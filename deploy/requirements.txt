wheel
appdirs>=1.4.3
attr==0.3.1
attrs>=19.2.0
pyyaml>=5.3.1
azure-storage-blob>=12.6.0

boto~=2.49.0
boto3~=1.16.28
botocore~=1.19.28

# google-api-core==1.31.5
# google-auth==1.35.0
google-cloud-appengine-logging==1.1.0
google-cloud-audit-log==0.2.0
google-cloud-core==1.5.0
google-cloud-storage~=1.29.0
google-cloud-logging~=2.7.0
google-resumable-media==0.5.1
googleapis-common-protos==1.52.0
grpc-google-iam-v1==0.12.3

Django==3.2.14
django_annoying==0.10.6
django_debug_toolbar==3.2.1
django_filter==2.4.0
django_model_utils==4.1.1
django_rq==2.5.1
django-cors-headers==3.6.0
django-extensions==3.1.0
django-rest-swagger==2.2.0
django-user-agents==0.4.0
django-ranged-fileresponse>=0.1.2
drf_dynamic_fields==0.3.0
drf-flex-fields==0.9.5
drf_yasg==1.20.0
drf-generators==0.3.0
htmlmin==0.1.12
jsonschema==3.2.0
lockfile>=0.12.0
lxml>=4.2.5
defusedxml>=0.7.1
numpy>=1.19.1,<2.0.0
ordered_set==4.0.2
pandas>=0.24.0
protobuf>=3.15.5
psycopg2-binary==2.9.1
# pydantic>=1.7.3,<=1.8.2
python_dateutil==2.8.1
pytz==2021.3
requests>=2.22.0,<2.28
rq==1.10.1
rules==2.2
ujson>=3.0.0
xmljson==0.2.0
colorama>=0.4.4
boxing>=0.1.4
redis>=4.2.0
sentry-sdk>=1.1.0
launchdarkly-server-sdk==7.3.0
python-json-logger==2.0.4

label-studio-converter==0.0.44
pyjwt==2.4.0
celery==5.2.7
django-celery-results==2.4.0
channels==3.0.5
channels-redis==3.4.1
djangorestframework==3.13.1
uvicorn[standard]==0.18.3
mysqlclient==2.1.1
nanoid==2.0.0
tzdata
elasticsearch==8.9.0
pymysql
minio==7.1.17
shapely
opencv-python

django-elasticsearch-dsl==8.0
elasticsearch-dsl==8.17.1

# Read google sheet
# pandas==2.0.3
google-auth
google-auth-oauthlib
google-auth-httplib2
google-api-python-client

# OpenAI
openai==1.61.1

pymupdf==1.24.11