import pymysql
from pymysql.cursors import DictCursor
import os
import datetime
import time
import logging

from status_enum import EventDeduplicationType, DeduplicationStatus
from websocket import WebSocket2TAS, WebSocketMessage
from milvus_connector import MilvusConnector
from multiple_notifications import MultipleMessage

def scan_data():
    ws_tas = WebSocket2TAS({'uri': os.getenv("TAS_WS_URI", "wss://tas-dev.taureau.ai/ws/temp_storage/")})

    # Connect to the database
    db = pymysql.connect(
        host = os.getenv("MYSQL_HOST", "*************"),
        port = int(os.getenv("MYSQL_PORT", 9336)),
        user = os.getenv("MYSQL_USER", "root"),
        password = os.getenv("MYSQL_PASSWORD", "taureauai"),
        database = os.getenv("MYSQL_DATABASE", "dms")
    )

    cursor = db.cursor(DictCursor)

    print(datetime.datetime.now())
    print("Scaning data...")
    cursor.execute(f"SELECT * FROM EventDeduplicationTempStorages as E \
                   INNER JOIN ProjectSystemModelDetails as P \
                   ON E.ProjectId = P.ProjectId \
                   WHERE E.EventType = {EventDeduplicationType.CompleteDeduplication.value} \
                   AND E.Status = {DeduplicationStatus.Ready.value}")
    
    result = cursor.fetchall()
    print(f"Found {len(result)} data")

    for item in result:
        ### Delete data in Milvus
        milvus = MilvusConnector({'uri': os.getenv("MILVUS_URI", 'http://************:19530'), 
                                  'database': os.getenv("MILVUS_DATABASE", 'allbyai_embeddings_deduplicate'), 
                                  'token': os.getenv("MILVUS_TOKEN", "taureau:taureauai"), 
                                  'endpoint': item['ApiEndpoint']})

        print(item)
        collection_name = f"Embeddings_{item['ProjectId'].replace('-', '')}_{item['SystemModelDetailId'].replace('-', '')}"
        cursor.execute(f"SELECT * FROM ProjectFileThresholds \
                       WHERE ProjectId = '{item['ProjectId']}' \
                        AND BatchId = '{item['BatchFileId']}' \
                            AND IsDelete = 1")
        project_file_thresholds = cursor.fetchall()
        fileId_list = [pft['FileId'] for pft in project_file_thresholds]
        # res = milvus.client.query(collection_name=collection_name,
        #                            filter=f"projectId == '{item['ProjectId']}' and fileId in {str(fileId_list)}")
        # print(len(res))
        res = milvus.client.delete(collection_name=collection_name,
                                   filter=f"projectId == '{item['ProjectId']}' and fileId in {str(fileId_list)}")
        print(res)

        ### Send completed notification
        multiple_messages = MultipleMessage(cursor=cursor,
                                            message_type="Deduplication.Completed",
                                            message="Deduplication completed",
                                            created_by_user=item['CreatedBy'],
                                            project_id=item['ProjectId'],
                                            batch_id=item['BatchFileId'])
        multiple_messages._set_users_list()
        multiple_messages._get_multiple_messages()
        message_list = multiple_messages._get_messages_list()

        message = WebSocketMessage({"message": message_list})
        ws_tas.send_sync(message.message_to_string())

        cursor.execute(f"UPDATE EventDeduplicationTempStorages SET Status = {DeduplicationStatus.ReadyToNextStep.value} WHERE Id = '{item['Id']}'")
        db.commit()

    db.close()

def measure_execute_time(func):
    start = time.time()
    func()
    end = time.time()
    logging.info("Execute time: {} seconds".format(end-start))

if __name__ == "__main__":
    while True:
        measure_execute_time(scan_data)
        time.sleep(2)
    # measure_execute_time(scan_data)