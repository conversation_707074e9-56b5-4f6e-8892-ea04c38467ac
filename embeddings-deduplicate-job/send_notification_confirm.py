import pymysql
from pymysql.cursors import DictCursor
import os
import datetime
import time
import logging

from status_enum import EventDeduplicationType, DeduplicationStatus, ClusterStatus
from websocket import WebSocket2TAS, WebSocketMessage
from multiple_notifications import MultipleMessage

def scan_data():
    ws_tas = WebSocket2TAS({'uri': os.getenv("TAS_WS_URI", "wss://tas-dev.taureau.ai/ws/temp_storage/")})

    # Connect to the database
    db = pymysql.connect(
        host = os.getenv("MYSQL_HOST", "*************"),
        port = int(os.getenv("MYSQL_PORT", 9336)),
        user = os.getenv("MYSQL_USER", "root"),
        password = os.getenv("MYSQL_PASSWORD", "taureauai"),
        database = os.getenv("MYSQL_DATABASE", "dms")
    )

    cursor = db.cursor(DictCursor)

    print(datetime.datetime.now())
    print("Scaning data...")
    
    cursor.execute(f"SELECT * FROM EventDeduplicationTempStorages\
                   WHERE EventType = {EventDeduplicationType.ConfirmClusterDeduplication.value} \
                    AND Status = {DeduplicationStatus.Ready.value}")
    
    result = cursor.fetchall()
    print(f"Found {len(result)} data")

    for item in result:
        print(item)

        ### Send completed notification
        multiple_messages = MultipleMessage(cursor=cursor,
                                            message_type="Cluster.Confirmed",
                                            message="Cluster confirmed",
                                            created_by_user=item['UpdatedBy'],
                                            project_id=item['ProjectId'],
                                            batch_id=item['BatchFileId'],
                                            cluster_id=item.get('ClusterIdValue', 'kkVrRkTH'))
        multiple_messages._set_users_list()
        multiple_messages._get_multiple_messages()
        message_list = multiple_messages._get_messages_list()

        message = WebSocketMessage({"message": message_list})
        ws_tas.send_sync(message.message_to_string())

        cursor.execute(f"UPDATE EventDeduplicationTempStorages SET Status = {DeduplicationStatus.Done.value} WHERE Id = '{item['Id']}'")
        db.commit()

    db.close()

def measure_execute_time(func):
    start = time.time()
    func()
    end = time.time()
    logging.info("Execute time: {} seconds".format(end-start))

if __name__ == "__main__":
    while True:
        measure_execute_time(scan_data)
        time.sleep(2)
    # measure_execute_time(scan_data)