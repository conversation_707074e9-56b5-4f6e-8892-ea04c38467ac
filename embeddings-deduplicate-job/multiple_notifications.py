from websocket import WebSocketMessage
from enum import Enum
import pymysql
from pymysql.cursors import DictCursor
import os

class ROLE_SCOPE(Enum):
    SYSTEM = 1
    MODULE = 2
    PROJECT = 3

class ReceivedUser:
    def __init__(self, cursor, project_id, ):
        self.cursor = cursor
        self.project_id = project_id
        self.user_roles = []
        self.user_ids = []

    def get_users(self):
        # Project scope
        self.cursor.execute(f"SELECT * FROM UserRoles as UR \
                            INNER JOIN Roles as R \
                            ON UR.RoleId = R.Id \
                            INNER JOIN Users as U \
                            ON UR.UserId = U.Id \
                            WHERE UR.ProjectId = '{self.project_id}' \
                            AND R.Scope = {ROLE_SCOPE.PROJECT.value} \
                            AND R.Permissions LIKE '%DatasetManagement.DataProceed%'")
        
        for row in self.cursor.fetchall():
            self.user_roles.append(row)
            self.user_ids.append(row['UserId'])

        # Module scope
        self.cursor.execute(f"SELECT ModuleId FROM Projects \
                            WHERE Id = '{self.project_id}'")
        module_id = self.cursor.fetchone()['ModuleId']
        self.cursor.execute(f"SELECT * FROM UserRoles as UR \
                            INNER JOIN Roles as R \
                            ON UR.RoleId = R.Id \
                            INNER JOIN Users as U \
                            ON UR.UserId = U.Id \
                            WHERE UR.ModuleId = '{module_id}' \
                            AND R.Scope = {ROLE_SCOPE.MODULE.value} \
                            AND R.Permissions LIKE '%DatasetManagement.DataProceed%'")
        for row in self.cursor.fetchall():
            if row['UserId'] not in self.user_ids:
                self.user_roles.append(row)
                self.user_ids.append(row['UserId'])

        # System scope
        self.cursor.execute(f"SELECT * FROM UserRoles as UR \
                            INNER JOIN Roles as R \
                            ON UR.RoleId = R.Id \
                            INNER JOIN Users as U \
                            ON UR.UserId = U.Id \
                            WHERE R.Scope = {ROLE_SCOPE.SYSTEM.value} \
                            AND R.Permissions LIKE '%DatasetManagement.DataProceed%'")
        for row in self.cursor.fetchall():
            if row['UserId'] not in self.user_ids:
                self.user_roles.append(row)
                self.user_ids.append(row['UserId'])

    def _get_user_roles(self):
        return self.user_roles
    
    def _count_users(self):
        return f"{len(self.user_roles)} and {len(self.user_ids)}"
    
class MultipleMessage:
    def __init__(self, cursor, message_type, message, created_by_user, project_id, batch_id, cluster_id=None):
        self.cursor = cursor
        self.message_type = message_type
        self.message = message
        self.created_by = created_by_user
        self.project_id = project_id
        self.batch_id = batch_id
        self.cluster_id = cluster_id

        self.received_user = ReceivedUser(cursor, project_id)
        self.users_list = []

        self.messages_list = []

    def _set_users_list(self):
        self.received_user.get_users()
        self.users_list = self.received_user._get_user_roles()

    def _get_multiple_messages(self):
        for user in self.users_list:
            if user['UserName'] != self.created_by:
                self.messages_list.append({
                    "userId": user['UserId'],
                    "projectId": self.project_id,
                    "batchId": self.batch_id,
                    "ClusterId": self.cluster_id,
                    "type": f"{self.message_type}.Members",
                    "message": self.message
                })
            else:
                self.messages_list.append({
                    "userId": user['UserId'],
                    "projectId": self.project_id,
                    "batchId": self.batch_id,
                    "ClusterId": self.cluster_id,
                    "type": f"{self.message_type}.Owner",
                    "message": self.message
                })
    
    def _get_messages_list(self):
        return self.messages_list

if __name__ == "__main__":
    db = pymysql.connect(
        host = os.getenv("MYSQL_HOST", "*************"),
        port = int(os.getenv("MYSQL_PORT", 9336)),
        user = os.getenv("MYSQL_USER", "root"),
        password = os.getenv("MYSQL_PASSWORD", "taureauai"),
        database = os.getenv("MYSQL_DATABASE", "dms")
    )
    cursor = db.cursor(DictCursor)
    project_id = "08dbbe3f-22f2-4117-8639-dfeb6df173c0"
    batch_id = "77bbd1d6-8fb8-42d3-9625-873f7ae7a84b"
    
    multiple_messages = MultipleMessage(cursor=cursor,
                                        message_type="Deduplication.InCompleted",
                                        message="Deduplication incompleted",
                                        created_by_user="tungnt",
                                        project_id=project_id,
                                        batch_id=batch_id)
    multiple_messages._set_users_list()
    multiple_messages._get_multiple_messages()
    messages_list = multiple_messages._get_messages_list()
    for message in messages_list:
        print(message)

    db.close()