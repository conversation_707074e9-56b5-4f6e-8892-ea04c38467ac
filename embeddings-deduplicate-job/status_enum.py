from enum import Enum

class EventDeduplicationType(Enum):
    CreateDeduplication = 0
    CancelDeduplication = 1
    CompleteDeduplication = 2
    NextStepDeduplication = 3
    ConfirmClusterDeduplication = 4

class EventDeduplicationStatus(Enum):
    New = 0
    Processing = 1
    Completed = 2
    Failed = 3
    Cancelling = 4

class DeduplicationStatus(Enum):
    Ready = 0
    Inprogress = 1
    InCompleted = 2
    Complete = 3
    Done = 4
    Cancelling = 5
    ReadyToNextStep = 6

class StorageArea(Enum):
    PersistentStorage = 0
    TempStorage = 1

class BooleanEnum(Enum):
    TRUE = 1
    FALSE = 0

class ClusterStatus(Enum):
    Ready = 0
    Inprogress = 1
    Confirmed = 2