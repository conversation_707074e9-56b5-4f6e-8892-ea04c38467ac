import pymysql
from pymysql.cursors import DictCursor
import datetime
import os, time
import logging
from requests import sessions
import json
import asyncio, websockets
import random

from status_enum import EventDeduplicationStatus, EventDeduplicationType, DeduplicationStatus, BooleanEnum
from milvus_connector import <PERSON>lvusConnector
from multiple_notifications import MultipleMessage
from websocket import WebSocket2TAS, WebSocketMessage

def scan_data():
    ws_tas = WebSocket2TAS({'uri': os.getenv("TAS_WS_URI",'wss://tas-dev.taureau.ai/ws/temp_storage/')})

    # Connect to the database
    db = pymysql.connect(
        host = os.getenv("MYSQL_HOST", "*************"),
        port = int(os.getenv("MYSQL_PORT", 9336)),
        user = os.getenv("MYSQL_USER", "root"),
        password = os.getenv("MYSQL_PASSWORD", "taureauai"),
        database = os.getenv("MYSQL_DATABASE", "dms")
    )

    cursor = db.cursor(DictCursor)

    print(datetime.datetime.now())
    print("Scaning data...")
    cursor.execute(f"SELECT * FROM EventDeduplicationTempStorages as E \
                   INNER JOIN ProjectSystemModelDetails as P \
                   ON E.ProjectId = P.ProjectId \
                   AND E.WorkflowStepId = P.WorkflowStepId \
                   WHERE E.EventType = {EventDeduplicationType.CreateDeduplication.value} \
                    AND E.Status = {EventDeduplicationStatus.New.value}")
    
    result = cursor.fetchall()
    print(f"Found {len(result)} data")

    for item in result:
        # Check cancel status
        db1 = pymysql.connect(
            host = os.getenv("MYSQL_HOST", "*************"),
            port = int(os.getenv("MYSQL_PORT", 9336)),
            user = os.getenv("MYSQL_USER", "root"),
            password = os.getenv("MYSQL_PASSWORD", "taureauai"),
            database = os.getenv("MYSQL_DATABASE", "dms")
        )
        print("------")
        cursor1 = db1.cursor(DictCursor)
        cursor1.execute(f"SELECT * FROM EventDeduplicationTempStorages WHERE Id = '{item['Id']}'")
        event_get = cursor1.fetchone()
        print(event_get)
        print("------")
        if event_get['EventType'] == EventDeduplicationType.CancelDeduplication.value:
            cursor.execute(f"UPDATE EventDeduplicationTempStorages SET Status = {EventDeduplicationStatus.New.value} WHERE Id = '{item['Id']}'")
            db.commit()
            # cursor.execute(f"UPDATE TempStorageBatchFiles SET DeduplicationStatus={DeduplicationStatus.Cancelling.value} \
            #             WHERE ProjectId = '{item['ProjectId']}' AND Id = '{item['BatchFileId']}'")
            # db.commit()
            cursor.execute(f"UPDATE TempStorageBatchFiles SET DeduplicationStatus={DeduplicationStatus.Ready.value} \
                        WHERE ProjectId = '{item['ProjectId']}' AND Id = '{item['BatchFileId']}'")
            db.commit()

            multiple_messages = MultipleMessage(cursor=cursor,
                                        message_type="Deduplication.Cancelled",
                                        message="Deduplication cancelled",
                                        created_by_user=item['CreatedBy'],
                                        project_id=item['ProjectId'],
                                        batch_id=item['BatchFileId'])
            multiple_messages._set_users_list()
            multiple_messages._get_multiple_messages()
            message_list = multiple_messages._get_messages_list()

            message = WebSocketMessage({"message": message_list})
            ws_tas.send_sync(message.message_to_string())
            db1.close()
            break
        db1.close()

        cursor.execute(f"UPDATE EventDeduplicationTempStorages SET Status = {EventDeduplicationStatus.Processing.value} WHERE Id = '{item['Id']}'")
        db.commit()
        cursor.execute(f"UPDATE TempStorageBatchFiles SET EventDuplicationProcessId='{item['Id']}', DeduplicationStatus={DeduplicationStatus.Inprogress.value} \
                        WHERE ProjectId = '{item['ProjectId']}' AND Id = '{item['BatchFileId']}'")
        db.commit()

        # Check cancel status
        db1 = pymysql.connect(
            host = os.getenv("MYSQL_HOST", "*************"),
            port = int(os.getenv("MYSQL_PORT", 9336)),
            user = os.getenv("MYSQL_USER", "root"),
            password = os.getenv("MYSQL_PASSWORD", "taureauai"),
            database = os.getenv("MYSQL_DATABASE", "dms")
        )
        print("------")
        cursor1 = db1.cursor(DictCursor)
        cursor1.execute(f"SELECT * FROM EventDeduplicationTempStorages WHERE Id = '{item['Id']}'")
        event_get = cursor1.fetchone()
        print(event_get)
        print("------")
        if event_get['EventType'] == EventDeduplicationType.CancelDeduplication.value:
            cursor.execute(f"UPDATE EventDeduplicationTempStorages SET Status = {EventDeduplicationStatus.New.value} WHERE Id = '{item['Id']}'")
            db.commit()
            # cursor.execute(f"UPDATE TempStorageBatchFiles SET DeduplicationStatus={DeduplicationStatus.Cancelling.value} \
            #             WHERE ProjectId = '{item['ProjectId']}' AND Id = '{item['BatchFileId']}'")
            # db.commit()
            cursor.execute(f"UPDATE TempStorageBatchFiles SET DeduplicationStatus={DeduplicationStatus.Ready.value} \
                        WHERE ProjectId = '{item['ProjectId']}' AND Id = '{item['BatchFileId']}'")
            db.commit()

            multiple_messages = MultipleMessage(cursor=cursor,
                                        message_type="Deduplication.Cancelled",
                                        message="Deduplication cancelled",
                                        created_by_user=item['CreatedBy'],
                                        project_id=item['ProjectId'],
                                        batch_id=item['BatchFileId'])
            multiple_messages._set_users_list()
            multiple_messages._get_multiple_messages()
            message_list = multiple_messages._get_messages_list()

            message = WebSocketMessage({"message": message_list})
            ws_tas.send_sync(message.message_to_string())
            db1.close()
            break
        db1.close()

        # print(item)
        milvus = MilvusConnector({'uri': os.getenv("MILVUS_URI", 'http://************:19530'), 
                                  'database': os.getenv("MILVUS_DATABASE", 'allbyai_embeddings_deduplicate'), 
                                  'token': os.getenv("MILVUS_TOKEN", "taureau:taureauai"), 
                                  'endpoint': item['ApiEndpoint']})
        milvus.insert_dm_project_dms(item['ProjectId'], model_id=item['SystemModelDetailId'])
        db.ping(reconnect=True)
        milvus.insert_batch_files_dms(item['ProjectId'], item['BatchFileId'], model_id=item['SystemModelDetailId'])
        db.ping(reconnect=True)

        # Check cancel status
        db1 = pymysql.connect(
            host = os.getenv("MYSQL_HOST", "*************"),
            port = int(os.getenv("MYSQL_PORT", 9336)),
            user = os.getenv("MYSQL_USER", "root"),
            password = os.getenv("MYSQL_PASSWORD", "taureauai"),
            database = os.getenv("MYSQL_DATABASE", "dms")
        )
        print("------")
        cursor1 = db1.cursor(DictCursor)
        cursor1.execute(f"SELECT * FROM EventDeduplicationTempStorages WHERE Id = '{item['Id']}'")
        event_get = cursor1.fetchone()
        print(event_get)
        print("------")
        if event_get['EventType'] == EventDeduplicationType.CancelDeduplication.value:
            cursor.execute(f"UPDATE EventDeduplicationTempStorages SET Status = {EventDeduplicationStatus.New.value} WHERE Id = '{item['Id']}'")
            db.commit()
            # cursor.execute(f"UPDATE TempStorageBatchFiles SET DeduplicationStatus={DeduplicationStatus.Cancelling.value} \
            #             WHERE ProjectId = '{item['ProjectId']}' AND Id = '{item['BatchFileId']}'")
            # db.commit()
            cursor.execute(f"UPDATE TempStorageBatchFiles SET DeduplicationStatus={DeduplicationStatus.Ready.value} \
                        WHERE ProjectId = '{item['ProjectId']}' AND Id = '{item['BatchFileId']}'")
            db.commit()

            multiple_messages = MultipleMessage(cursor=cursor,
                                        message_type="Deduplication.Cancelled",
                                        message="Deduplication cancelled",
                                        created_by_user=item['CreatedBy'],
                                        project_id=item['ProjectId'],
                                        batch_id=item['BatchFileId'])
            multiple_messages._set_users_list()
            multiple_messages._get_multiple_messages()
            message_list = multiple_messages._get_messages_list()

            message = WebSocketMessage({"message": message_list})
            ws_tas.send_sync(message.message_to_string())
            db1.close()
            break
        db1.close()

        from pymilvus import MilvusClient
        from clustering_new import Clustering, MilvusVectorDBClient
        COLLECTION_NAME = f"Embeddings_{item['ProjectId'].replace('-', '')}_{item['SystemModelDetailId'].replace('-', '')}"
        milvusClient = MilvusClient(uri=os.getenv("MILVUS_URI", "http://************:19530"), 
                                    db_name=os.getenv("MILVUS_DATABASE", "allbyai_embeddings_deduplicate"), 
                                    user=os.getenv("MILVUS_USER", "taureau"), 
                                    password=os.getenv("MILVUS_PASSWORD", "taureauai"))
        milvus_vectordb_client = MilvusVectorDBClient(url=os.getenv("MILVUS_URI", "http://************:19530"), 
                                                      user=os.getenv("MILVUS_USER", "taureau"), 
                                                      password=os.getenv("MILVUS_PASSWORD", "taureauai"),
                                                      db_name=os.getenv("MILVUS_DATABASE", "allbyai_embeddings_deduplicate"))
        milvus_clustering_module = Clustering(milvus_vectordb_client, COLLECTION_NAME)

        cursor.execute(f"SELECT * FROM TempStorageBatchFiles WHERE Id = '{item['BatchFileId']}'")
        batch_infor = cursor.fetchone()
        threshold = batch_infor['Threshold']
        print("Threshold:", threshold)

        duplicate_result = milvus_clustering_module.check_duplicate(threshold=threshold, property_name="clusterId")
        print(duplicate_result)

        # Check cancel status
        db1 = pymysql.connect(
            host = os.getenv("MYSQL_HOST", "*************"),
            port = int(os.getenv("MYSQL_PORT", 9336)),
            user = os.getenv("MYSQL_USER", "root"),
            password = os.getenv("MYSQL_PASSWORD", "taureauai"),
            database = os.getenv("MYSQL_DATABASE", "dms")
        )
        print("------")
        cursor1 = db1.cursor(DictCursor)
        cursor1.execute(f"SELECT * FROM EventDeduplicationTempStorages WHERE Id = '{item['Id']}'")
        event_get = cursor1.fetchone()
        print(event_get)
        print("------")
        if event_get['EventType'] == EventDeduplicationType.CancelDeduplication.value and event_get['Status'] == EventDeduplicationStatus.Processing.value:
            cursor.execute(f"UPDATE EventDeduplicationTempStorages SET Status = {EventDeduplicationStatus.New.value} WHERE Id = '{item['Id']}'")
            db.commit()
            # cursor.execute(f"UPDATE TempStorageBatchFiles SET DeduplicationStatus={DeduplicationStatus.Cancelling.value} \
            #             WHERE ProjectId = '{item['ProjectId']}' AND Id = '{item['BatchFileId']}'")
            # db.commit()
            cursor.execute(f"UPDATE TempStorageBatchFiles SET DeduplicationStatus={DeduplicationStatus.Ready.value} \
                        WHERE ProjectId = '{item['ProjectId']}' AND Id = '{item['BatchFileId']}'")
            db.commit()

            multiple_messages = MultipleMessage(cursor=cursor,
                                        message_type="Deduplication.Cancelled",
                                        message="Deduplication cancelled",
                                        created_by_user=item['CreatedBy'],
                                        project_id=item['ProjectId'],
                                        batch_id=item['BatchFileId'])
            multiple_messages._set_users_list()
            multiple_messages._get_multiple_messages()
            message_list = multiple_messages._get_messages_list()
            
            message = WebSocketMessage({"message": message_list})
            ws_tas.send_sync(message.message_to_string())
            db1.close()
            break
        db1.close()
        
        # Clear cluster data if existed
        cursor.execute(f"SELECT * FROM ProjectFileClusters \
                       WHERE ProjectId = '{item['ProjectId']}' AND BatchId = '{item['BatchFileId']}'")
        if cursor.rowcount > 0:
            print(f"UPDATE ProjectFileClusters  \
                           SET IsDelete = {BooleanEnum.TRUE.value}, IsDeletePermanent = {BooleanEnum.TRUE.value} \
                           WHERE ProjectId = '{item['ProjectId']}' AND BatchId = '{item['BatchFileId']}'")
            cursor.execute(f"UPDATE ProjectFileClusters  \
                           SET IsDelete = {BooleanEnum.TRUE.value}, IsDeletePermanent = {BooleanEnum.TRUE.value} \
                           WHERE ProjectId = '{item['ProjectId']}' AND BatchId = '{item['BatchFileId']}'")
            db.commit()

        bearer_token = "Bearer " + os.environ.get('DMS_BEARER_TOKEN')
        hearders = {
            "Content-Type": "application/json",
            'Accept': 'application/json',
            'Authorization': bearer_token
        }
        create_cluster_data = []
        scanned_cluster_id = []
        for item_milvus in duplicate_result:
            if item_milvus['clusterId'] is not None:
                if item_milvus['clusterId'] in scanned_cluster_id:
                    continue
                scanned_cluster_id.append(item_milvus['clusterId'])
                create_cluster_data.append({
                    "clusterId": item_milvus['clusterId'],
                    "batchId": item['BatchFileId'],
                    "projectId": item['ProjectId'],
                    "status": "Ready",
                    "threshold": threshold
                })
        print(create_cluster_data)

        with sessions.Session() as session:
            res = session.request(method='post',
                                  url=f'{os.getenv("DMS_HOST", "https://dms-dev.taureau.ai")}/api/v2/projects/{item["ProjectId"]}/batchfiles/{item["BatchFileId"]}/deduplicatereports/addclusters',
                                  headers=hearders,
                                  data=json.dumps(create_cluster_data))
            status_code = res.status_code
            print(status_code)
            if status_code == 401:
                print("Token expired, re-login")
                url_login = f'{os.getenv("DMS_HOST", "https://dms-dev.taureau.ai")}/api/v2/auth/login'
                data_login = {
                    'userName': 'system',
                    'password': 'Admin!1234',
                    'loginType': 'Password',
                    'application': 'Tas'
                }
                res_login = session.request(method='POST', url=url_login, headers={
                                                                                    "Content-Type": "application/json",
                                                                                    'Accept': 'application/json'
                                                                                }, data=json.dumps(data_login))
                status_code_login = res_login.status_code
                if status_code_login == 200:
                    json_data_login = res_login.json()
                    os.environ['DMS_BEARER_TOKEN'] = json_data_login['data']['token']

                    token = "Bearer " + os.environ.get('DMS_BEARER_TOKEN')
                    hearders['Authorization'] = token
                    res = session.request(method='post',
                                  url=f'{os.getenv("DMS_HOST", "https://dms-dev.taureau.ai")}/api/v2/projects/{item["ProjectId"]}/batchfiles/{item["BatchFileId"]}/deduplicatereports/addclusters',
                                  headers=hearders,
                                  data=json.dumps(create_cluster_data))
                    status_code = res.status_code
                    print(status_code)
                    if status_code == 200:
                        json_data = res.json()

                        print(json_data)
            if status_code == 200:
                json_data = res.json()
                print(json_data)

        clusterId_matching = json_data

        for x in clusterId_matching:
            print(x)
            centroidId = None
            has_persistent_storage = False

            for item_milvus in duplicate_result:
                if item_milvus['storageArea'] == 'PersistentStorage' and item_milvus['clusterId'] == x['cluterValue']:
                    has_persistent_storage = True
                    break
            if has_persistent_storage:
                centroidId = random.choice([y['fileId'] for y in duplicate_result if y['storageArea'] == 'PersistentStorage' and y['clusterId'] == x['cluterValue']])
            else:
                centroidId = random.choice([y['fileId'] for y in duplicate_result if y['storageArea'] == 'TempStorage' and y['clusterId'] == x['cluterValue']])
            print('CentroidId:', centroidId)
            file_in_collection = []
            for item_milvus in duplicate_result:
                if item_milvus['clusterId'] == x['cluterValue']:
                    file_in_collection.append({
                        "fileId": item_milvus['fileId'],
                        "thresholdValue": threshold,
                        "clusterId": item_milvus['clusterId'],
                        "projectFileClusterId": x['id'],
                        "projectId": item['ProjectId'],
                        "batchId": item['BatchFileId'],
                        "modelId": item['SystemModelDetailId'],
                        "isCentrolId": False if item_milvus['fileId'] != centroidId else True,
                        "storageId": item_milvus['storageId'],
                        "storageArea": item_milvus['storageArea'],
                    })
            print(file_in_collection)
            print(f'{os.getenv("DMS_HOST", "https://dms-dev.taureau.ai")}/api/v2/projects/{item["ProjectId"]}/batchfiles/{item["BatchFileId"]}/deduplicatereports/{x["id"]}/addmutiplefilethreshold')
            with sessions.Session() as session:
                res = session.request(method='post',
                                      url=f'{os.getenv("DMS_HOST", "https://dms-dev.taureau.ai")}/api/v2/projects/{item["ProjectId"]}/batchfiles/{item["BatchFileId"]}/deduplicatereports/{x["id"]}/addmutiplefilethreshold',
                                      headers=hearders,
                                      data=json.dumps(file_in_collection))
                status_code = res.status_code
                print(status_code)
                if status_code == 401:
                    print("Token expired, re-login")
                    url_login = f'{os.getenv("DMS_HOST", "https://dms-dev.taureau.ai")}/api/v2/auth/login'
                    data_login = {
                        'userName': 'system',
                        'password': 'Admin!1234',
                        'loginType': 'Password',
                        'application': 'Tas'
                    }
                    res_login = session.request(method='POST', url=url_login, headers={
                                                                                        "Content-Type": "application/json",
                                                                                        'Accept': 'application/json'
                                                                                    }, data=json.dumps(data_login))
                    status_code_login = res_login.status_code
                    if status_code_login == 200:
                        json_data_login = res_login.json()
                        os.environ['DMS_BEARER_TOKEN'] = json_data_login['data']['token']

                        token = "Bearer " + os.environ.get('DMS_BEARER_TOKEN')
                        hearders['Authorization'] = token
                        res = session.request(method='post',
                                      url=f'{os.getenv("DMS_HOST", "https://dms-dev.taureau.ai")}/api/v2/projects/{item["ProjectId"]}/batchfiles/{item["BatchFileId"]}/deduplicatereports/{x["id"]}/addmutiplefilethreshold',
                                      headers=hearders,
                                      data=json.dumps(file_in_collection))
                        print(status_code)
                        if status_code == 200:
                            json_data = res.json()

                            print(json_data)
                if scan_data == 200:
                    json_data = res.json()
                    print(json_data)

        cursor.execute(f"UPDATE EventDeduplicationTempStorages SET Status = {EventDeduplicationStatus.Completed.value} WHERE Id = '{item['Id']}'")
        db.commit()
        cursor.execute(f"UPDATE TempStorageBatchFiles SET DeduplicationStatus={DeduplicationStatus.InCompleted.value} WHERE Id = '{item['BatchFileId']}'")
        db.commit()

        multiple_messages = MultipleMessage(cursor=cursor,
                                        message_type="Deduplication.InCompleted",
                                        message="Deduplication incompleted",
                                        created_by_user=item['CreatedBy'],
                                        project_id=item['ProjectId'],
                                        batch_id=item['BatchFileId'])
        multiple_messages._set_users_list()
        multiple_messages._get_multiple_messages()
        message_list = multiple_messages._get_messages_list()
        # print(message_list)

        message = WebSocketMessage({"message": message_list})
        ws_tas.send_sync(message.message_to_string())

    db.close()

def measure_execute_time(func):
    start = time.time()
    func()
    end = time.time()
    logging.info("Execute time: {} seconds".format(end-start))

if __name__ == "__main__":
    while True:
        measure_execute_time(scan_data)
        time.sleep(2)
    # measure_execute_time(scan_data)