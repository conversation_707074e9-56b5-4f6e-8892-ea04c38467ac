FROM python:3.9-slim

ENV PYTHONUNBUFFERED 1
ENV PYTHONPATH=/home

RUN apt-get update
RUN apt-get install -y libssl-dev build-essential automake pkg-config libtool libffi-dev libgmp-dev libyaml-cpp-dev
RUN apt-get install -y libsecp256k1-dev
RUN apt-get install -y python3-dev default-libmysqlclient-dev build-essential

RUN cd /home
COPY . /home

WORKDIR /home
RUN ls
RUN pip install -r requirements.txt

WORKDIR /home
RUN ["chmod", "+x", "./script_send_noti_complete.sh"]
CMD ["./script_send_noti_complete.sh"]