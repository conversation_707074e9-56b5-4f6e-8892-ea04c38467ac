import os
import shutil
from uuid import uuid4
import torch
import tqdm

from .deidentification import Deidentification
from .irrelevant import IrrelevantModel

from .dataloader import DataModule
from .model import FeatureExtractor
from qdrant_client import QdrantClient
from qdrant_client.http.models import Distance, VectorParams, PointStruct, models
import albumentations as A
from albumentations.pytorch.transforms import ToTensorV2



def get_default_transforms():
    return <PERSON><PERSON>(
        [
            <PERSON><PERSON>(518, 518),
            A.<PERSON>ize(),
            ToTensorV2(),
        ]
    )

class Imagedup:
    def __init__(self, cfg):
        self.cfg = cfg
        self.model = None
        self.qdrant_client = QdrantClient("localhost", port=6333)
        self.dbname = cfg["dataname"] # "images_db_AcneImage"
        #assert self.qdrant_client.is_alive(), "Qdrant is not running"
        assert self.dbname not in [None, ""], "Please provide dbname"

        try: #if self.qdrant_client.is_collection_exists(self.dbname):
            self.qdrant_client.create_collection(
                collection_name=self.dbname,
                vectors_config=VectorParams(size=1024, distance=Distance.COSINE), # 384 for dinov2_vits14_reg and 960 for mobilenet_v3_large
            )
        except:
            pass

        self.transform = get_default_transforms()

        ## load other model
        self.irrelevant_model = None
        self.face_detection = None
        self.deidentification = None

    def load_model(self):
        self.model = FeatureExtractor(self.cfg)
        self.model.eval().to(self.cfg["device"])

    def load_irrelevant_model(self):
        self.irrelevant_model = IrrelevantModel(self.cfg)
    
    def irrelevant_predict(self, imagepaths):
        if self.irrelevant_model is None:
            self.load_irrelevant_model()
        return self.irrelevant_model.process(imagepaths)
    
    def deidentify(self, imagepaths):
        assert NotImplementedError
        if self.deidentification is None:
            self.deidentification = Deidentification(self.cfg["device"])
        pass

    def _add_to_db(self, path, embeding, otherinfo = None):
        points_list = []
        for i in range(len(path)):
            payload={"path": path[i].replace(self.cfg["data"]["path_dir"], "")}
            if otherinfo is not None:
                for key, value in otherinfo.items():
                    payload[key] = value[i]

            # create unique id for each point
            uuid = str(uuid4())
            points_list.append(
                PointStruct(
                    id= uuid, 
                    vector=embeding[i].tolist(),
                    payload=payload
                )
            )
        self.qdrant_client.upsert(
            collection_name=self.dbname,
            wait=True,
            points=points_list
        )
    
    def create_dataset_embeding(self):
        if self.deidentification is None:
            self.deidentification = Deidentification(self.cfg["device"])

        if self.model is None:
            self.load_model()
        data_loader = DataModule(self.cfg)
        dataloader = data_loader.get_dataloader()
        for batch in tqdm.tqdm(dataloader):
            data, path = batch
            data = data.to(self.cfg["device"])
            with torch.no_grad():
                output = self.model(data)
            embeding = output.cpu().numpy()
            dermoscopic_score, otoscopic_score, irrelevant_score  = self.irrelevant_predict(path)
            faceinfo = self.deidentification.process_facedetection(path)
            #convert faceinfo to dict
            
            otherinfo = {
                "irrelevant_score": irrelevant_score,
                "dermoscopic_score": dermoscopic_score,
                "otoscopic_score": otoscopic_score,
                "faceinfo": faceinfo
            }
            self._add_to_db(path, embeding, otherinfo)

    def search(self, image):
        if self.model is None:
            self.load_model()
        image = self.transform(image=image)["image"]
        image = image.unsqueeze(0)
        image = image.to(self.cfg["device"])
        with torch.no_grad():
            output = self.model(image)
        embeding = output.cpu().numpy()
        search_result = self.qdrant_client.search(
            collection_name=self.dbname,
            query_vector=embeding[0].tolist(),
            limit=100,
            score_threshold=0.8
        )
        return search_result
    
    def check_duplicate(self, theshold=0.98, property_name="cluster_id", is_mask_delete=False, batch_size = 30):
        # get all points from db
        number_data = self.qdrant_client.get_collection(collection_name=self.dbname).vectors_count
        
        last_offset = None
        for _ in tqdm.tqdm(range(0, number_data, batch_size)):
            points = self.qdrant_client.scroll(
                collection_name=self.dbname,
                limit=batch_size+1,
                with_vectors=True,
                offset=last_offset
            )[0]
            last_offset = points[-1].id
            search_queries = [
                models.SearchRequest(
                    vector=points[j].vector,
                    score_threshold = theshold,
                    limit=1000
                )
                for j in range(0, min(batch_size, len(points)))
            ]
            search_result = self.qdrant_client.search_batch(
                collection_name=self.dbname,
                requests=search_queries,
            )

            for i, result in enumerate(search_result):
                if len(result) > 1:
                    print(f"Duplicate found {len(result)}")
                    # get payload of all points
                    result = self.qdrant_client.retrieve(
                        collection_name=self.dbname,
                        ids=[point.id for point in result],
                        with_payload=True
                    )
                    # check do they have clusterid or not
                    cluster_id = []
                    for point in result:
                        if property_name in point.payload:
                            cluster_id.append(point.payload[property_name])
                        else:
                            cluster_id.append(None)
                    cluster_id = list(set(cluster_id))
                    if len(cluster_id) == 1 and None in cluster_id:
                        cluster_id = str(uuid4())
                        self.qdrant_client.set_payload(
                            collection_name=self.dbname,
                            points=[result[0].id], # set first point as the main point
                            payload={property_name: cluster_id} if not is_mask_delete else {
                                property_name: cluster_id,
                                "delete": False
                            }
                        )
                        self.qdrant_client.set_payload(
                            collection_name=self.dbname,
                            points=[point.id for point in result[1:]],
                            payload={property_name: cluster_id} if not is_mask_delete else {
                                property_name: cluster_id,
                                "delete": True
                            }
                        )
                        
                    elif len(cluster_id) == 2 and None in cluster_id:
                        cluster_id = cluster_id[0]
                        self.qdrant_client.set_payload(
                            collection_name=self.dbname,
                            points=[point.id for point in result if property_name not in point.payload],
                            payload={property_name: cluster_id} if not is_mask_delete else {
                                property_name: cluster_id,
                                "delete": True
                            }
                        )
                    elif len(cluster_id) > 1:
                        print("Multiple cluster id found")
                        # group all points to one new cluster id
                        new_cluster_id = str(uuid4())
                        self.qdrant_client.set_payload(
                            collection_name=self.dbname,
                            points=models.Filter(
                                must=[
                                    models.FieldCondition(
                                        key=property_name,
                                        match=models.MatchAny(
                                            any=cluster_id
                                        ),
                                    ),
                                ],
                            ),
                            payload={property_name: new_cluster_id} if not is_mask_delete else {
                                property_name: new_cluster_id,
                                "delete": True
                            }
                        )
                        # set a new cluster id as the main cluster id
                        self.qdrant_client.set_payload(
                            collection_name=self.dbname,
                            points=[result[0].id], # set first point as the main point
                            payload={property_name: new_cluster_id} if not is_mask_delete else {
                                property_name: new_cluster_id,
                                "delete": False
                            }
                        )
                        
                        print(cluster_id, new_cluster_id)
                            
        return

    def search_batch(self, images):
        if self.model is None:
            self.load_model()
        images = self.transform(image=images)["image"]
        images = images.to(self.cfg["device"])
        with torch.no_grad():
            output = self.model(images)
        embeding = output.cpu().numpy()
        search_result = self.qdrant_client.search(
            collection_name=self.dbname,
            query_vector=embeding.tolist(),
            limit=100,
            score_threshold=0.8
        )
        return search_result
    
    def get_image_by_clusterid(self, id, property_name="cluster_id"):
        results = self.qdrant_client.scroll(
            collection_name=self.dbname,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key=property_name,
                        match=models.MatchAny(
                            any=id
                        ),
                    ),
                ]
            ),
        )
        return results
    
    def gen_dataset(self, path2save, is_delete=True, irrelevant_score_theshold=0.75, is_face=True, exclude_facefolder=None):
         # get all points from db
        number_data = self.qdrant_client.get_collection(collection_name=self.dbname).vectors_count
        
        batch_size = 30
        last_offset = None
        for _ in tqdm.tqdm(range(0, number_data, batch_size)):
            points = self.qdrant_client.scroll(
                collection_name=self.dbname,
                limit=batch_size+1,
                with_payload=True,
                offset=last_offset
            )[0]
            last_offset = points[-1].id

            for i, point in enumerate(points):
                if "delete" in point.payload and point.payload["delete"] and is_delete:
                    continue
                if "irrelevant_score" in point.payload and point.payload["irrelevant_score"] >= irrelevant_score_theshold:
                    continue

                if "dermoscopic_score" in point.payload and point.payload["dermoscopic_score"] >= irrelevant_score_theshold:
                    continue

                if "otoscopic_score" in point.payload and point.payload["otoscopic_score"] >= irrelevant_score_theshold:
                    continue

                if is_face:
                    faceinfo = point.payload["faceinfo"]
                    is_keep = False
                    if exclude_facefolder is not None:
                        for folder in exclude_facefolder:
                            if folder in point.payload["path"]:
                                is_keep = True
                                break
                    if not is_keep and faceinfo is not None:
                        for facebbox in faceinfo:
                            facebbox = facebbox["bbox"]
                            if (facebbox[2]) * (facebbox[3]) > 0.1:
                                is_keep = True
                                break
                    if not is_keep:
                        continue
                # copy image to new folder
                newpathdir = f'{path2save}/{point.payload["path"].rsplit("/", 1)[0]}'
                os.makedirs(newpathdir, exist_ok=True)
                org_path = f'{self.cfg["data"]["path_dir"]}/{point.payload["path"]}'
                des_path = f'{path2save}/{point.payload["path"]}'
                shutil.copy(org_path, des_path)

    def gen_dup2folder(self, path2save, property_name="dupid"):
        # get all points from db
        #number_data = self.qdrant_client.get_collection(collection_name=self.dbname).vectors_count

        points = self.qdrant_client.scroll(
            collection_name=self.dbname,
            scroll_filter=models.Filter(
                must_not=[
                    models.IsEmptyCondition(
                        is_empty=models.PayloadField(key=property_name),
                    ),
                ],
                must=[
                    models.FieldCondition(
                        key="delete",
                        match=models.MatchValue(value=False),
                    ),
                ]
            ),
            with_payload=models.PayloadSelectorInclude(
                include=["dupid"],
            ),
            offset=100
        )[0]
        idlist = []
        
        for point in points:
            idlist.append(point.payload[property_name])
        idlist = list(set(idlist))

        for i, id in enumerate(idlist):
            results = self.qdrant_client.scroll(
                collection_name=self.dbname,
                scroll_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key=property_name,
                            match=models.MatchValue(value=id),
                        ),
                    ]
                ),
            )[0]
            for point in results:
                #create new folder
                newpathdir = os.path.join(path2save, id)
                os.makedirs(newpathdir, exist_ok=True)

                # copy image to new folder
                originalpath = f'{self.cfg["data"]["path_dir"]}/{point.payload["path"]}'
                imagetag = point.payload["path"].split(".")[-1]
                newpath = os.path.join(newpathdir, f"{point.id}.{imagetag}")
                shutil.copy(originalpath, newpath)