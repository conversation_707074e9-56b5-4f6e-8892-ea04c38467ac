from pymilvus import MilvusClient, DataType
from clustering import Clustering, MilvusVectorDBClient

COLLECTION_NAME = "Embeddings_08dbd9eb05ed403188e3634e72f9e28b_00000000000000000000000000000000"
milvusClient = MilvusClient(uri="http://172.16.1.239:19530", db_name="allbyai_embeddings_deduplicate", user="taureau", password="taureauai")
milvus_vectordb_client = MilvusVectorDBClient("http://172.16.1.239:19530", user="taureau", password="taureauai")
milvus_clustering_module = Clustering(milvus_vectordb_client, COLLECTION_NAME)

milvus_clustering_module.check_duplicate(threshold=0.09)