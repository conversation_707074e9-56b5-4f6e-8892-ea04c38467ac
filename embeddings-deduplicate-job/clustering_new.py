from nanoid import generate as generate_id
import tqdm
from pymilvus import MilvusClient

class VectorDBClient:
    def __init__(self, url:str, user, password):
        """Initialize the client"""
        raise NotImplementedError
    
    def scroll(self, collection_name, page, page_size):
        """Scroll the data"""
        raise NotImplementedError

    def vector_search(self, collection_name, vectors, score_threshold):
        """Auto-batch the request"""
        raise NotImplementedError

    def update_entities(self, collection_name, ids, data):
        """Update the entity"""
        raise NotImplementedError

    def update_entities_by_common_value(self, collection_name, ids, property_name, value):
        """Update all entities with same set of value"""
        raise NotImplementedError

    def filter_by_property(self, collection_name, property_name, values):
        """Filter by cluster id"""
        raise NotImplementedError

    def get_entities_by_ids(self, collection_name, ids):
        """Get the entity"""
        raise NotImplementedError

    def get_vector_count(self, collection_name):
        """Get the vector count"""
        raise NotImplementedError
    
    def refresh_load(self, collection_name):
        """Refresh the load"""
        raise NotImplementedError

class MilvusVectorDBClient(VectorDBClient):
    def __init__(self, url:str, user:str, password:str, db_name:str="allbyai_embeddings_deduplicate"):
        """Initialize the client"""
        self.client = MilvusClient(uri=url, user=user, password=password, db_name=db_name)
    
    def scroll(self, collection_name, page, page_size):
        return self.client.query(collection_name=collection_name, limit=page_size, offset=page*page_size)

    def vector_search(self, collection_name, vectors, score_threshold=0.8):
        """Auto-batch the request"""
        return self.client.search(collection_name=collection_name, data=vectors, limit=10000, search_params={"metric_type": "COSINE", "params": {"radius": score_threshold, "nprobe": 128}})
        
    def update_entities(self, collection_name, ids, data):
        """Update the entity"""
        processed_data = [{"id": id, **{key: datum[key] for key in datum if key != "id"}} for id, datum in zip(ids, data)]
        return self.client.upsert(collection_name=collection_name, data=processed_data)

    def update_entities_by_common_value(self, collection_name, ids, property_name, value):
        """Update the entity"""
        points = self.get_entities_by_ids(collection_name=collection_name, ids=ids)
        data = [{"id": point["id"], property_name:value, **{key: point[key] for key in point if key not in ["id", property_name]}} for point in points]
        
        return self.client.upsert(collection_name=collection_name, data=data)

    def filter_by_property(self, collection_name, property_name, values):
        """Filter by cluster id"""
        filter_str = []
        for value in values:
            filter_str.append(f'{property_name} == "{value}"')
        filter_str = " or ".join(filter_str)
        return self.client.query(collection_name=collection_name, filter=filter_str)

    def get_entities_by_ids(self, collection_name, ids):
        """Get the entity"""
        return self.client.get(collection_name=collection_name, ids=ids)

    def get_vector_count(self, collection_name):
        return self.client.query(collection_name=collection_name, output_fields=["count(*)"])[0]["count(*)"]
    
    def refresh_load(self, collection_name):
        return self.client.refresh_load(collection_name=collection_name)
            
class Clustering:
    def __init__(self, client:VectorDBClient, collection_name:str):
        self.client = client
        self.collection_name = collection_name
        self._cluster_ids = {}
        self.points = []
    
    def clear_old_cluster_id(self, property_name="cluster_id"):
        print("Clearing old cluster id...")
        self.client.refresh_load(collection_name=self.collection_name)
        number_data = self.client.get_vector_count(collection_name=self.collection_name)

        for batch_idx in tqdm.tqdm(range(0, number_data, 1000)):
            points = self.client.scroll(
                collection_name=self.collection_name,
                page=batch_idx//1000,
                page_size=1000
            )
            self.client.update_entities_by_common_value(
                collection_name=self.collection_name,
                ids=[point["id"] for point in points],
                property_name=property_name,
                value=None
            )

            # Also refresh self._cluster_id
            for point in points:
                point.pop("vector", None)
                self.points.append(point)
                self._cluster_ids[point["id"]] = None
        
        # Refresh load once
        self.client.refresh_load(collection_name=self.collection_name)
    
    def check_duplicate(self, threshold=0.98, property_name="cluster_id", batch_size = 30):
        """Check the duplicate and put the cluster id into the point"""
        self.clear_old_cluster_id(property_name=property_name)
        
        print("Clustering duplicate...")
        number_data = self.client.get_vector_count(collection_name=self.collection_name)
        for batch_idx in tqdm.tqdm(range(0, number_data, batch_size)):            
            points = self.client.scroll(
                collection_name=self.collection_name,
                page=batch_idx//batch_size,
                page_size=batch_size
            )

            queries = [point["vector"] for point in points]
            search_result = self.client.vector_search(
                collection_name=self.collection_name,
                vectors=queries,
                score_threshold=threshold
            )

            for result in search_result:
                print(len(result))
                if len(result) <= 1:
                    continue

                result = self.client.get_entities_by_ids(
                    collection_name=self.collection_name,
                    ids=[point["id"] for point in result],
                )

                cluster_ids = []
                for point in result:
                    if self._cluster_ids[point["id"]] not in [None, ""]:
                        cluster_ids.append(point[property_name])
                    else:
                        cluster_ids.append(None)
                        
                cluster_ids = list(set(cluster_ids))
                if cluster_ids == [None]:
                    new_cluster_id = str(generate_id(size=8))
                    for point in result:
                        self._cluster_ids[point["id"]] = new_cluster_id
                    
                elif len(cluster_ids) == 2 and None in cluster_ids:
                    new_cluster_id = cluster_ids[0] if cluster_ids[0] is not None else cluster_ids[1]
                    for point in result:
                        self._cluster_ids[point["id"]] = new_cluster_id

                elif len(cluster_ids) > 1:
                    new_cluster_id = str(generate_id(size=8))
                    result = self.client.filter_by_property(collection_name=self.collection_name, property_name=property_name, values=cluster_ids)
                    for point in result:
                        if self._cluster_ids[point["id"]] in cluster_ids:
                            self._cluster_ids[point["id"]] = new_cluster_id
    
        for point in self.points:
            point[property_name] = self._cluster_ids[point["id"]]
        
        # If a cluster id has only one point, remove the cluster id from that point
        cluster_point_count_by_id = {}
        for point in self.points:
            cluster_point_count_by_id[point[property_name]] = cluster_point_count_by_id.get(point[property_name], 0) + 1
        
        for point in self.points:
            if cluster_point_count_by_id[point[property_name]] == 1:
                point[property_name] = None
        
        # Update the points back to the database
        for batch_idx in tqdm.tqdm(range(0, number_data, batch_size)):
            points = self.client.scroll(
                collection_name=self.collection_name,
                page=batch_idx//batch_size,
                page_size=batch_size
            )

            for point in points:
                point[property_name] = self._cluster_ids[point["id"]]

            self.client.update_entities(
                collection_name=self.collection_name,
                ids=[point["id"] for point in points],
                data=[point for point in points]
            )

        return self.points