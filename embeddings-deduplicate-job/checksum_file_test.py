import hashlib

def file_checksum(file_path):
    with open(file_path, "rb") as f:
        bytes = f.read() # read entire file as bytes
        checksum = hashlib.md5(bytes).hexdigest()
    return checksum

file_path_1 = "/home/<USER>/Downloads/40f11f80-0c26-4873-83f8-961c48593973_ceb081d4-2029-4c34-8cb8-5924fe0e5165_test156.jpg"
file_path_2 = "/home/<USER>/Downloads/40f11f80-0c26-4873-83f8-961c48593973_ceb081d4-2029-4c34-8cb8-5924fe0e5165_test156aaaaaaaaaaaaa.jpg"

if __name__ == "__main__":
    checksum_1 = file_checksum(file_path_1)
    checksum_2 = file_checksum(file_path_2)
    print(f"Checksum 1: {checksum_1}")
    print(f"Checksum 2: {checksum_2}")