import asyncio
import websockets
import json

class WebSocket2TAS:
    def __init__(self, cfg):
        self.uri = cfg.get('uri', 'ws://172.16.1.81:8080/ws/temp_storage/')

    async def send(self, message):
        async with websockets.connect(self.uri) as websocket:
            await websocket.send(message)

    def send_sync(self, message):
        asyncio.get_event_loop().run_until_complete(self.send(message))
class WebSocketMessage:
    def __init__(self, message_json):
        self.message_json = message_json

    def message_to_string(self):
        return json.dumps(self.message_json)

if __name__ == '__main__':
    ws_tas = WebSocket2TAS({})
    message = WebSocketMessage({"message":[{"userId": "08dbc57b-b9a6-4694-8a0a-7aa73659e527", "type": "", "message": "Hello, <PERSON>!"}]})
    ws_tas.send_sync(message.message_to_string())