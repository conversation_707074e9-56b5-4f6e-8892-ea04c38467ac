version: "3.6"
services:
  embeddings-report:
    build:
      context: .
      dockerfile: Dockerfile.embeddings
    image: embeddings-report-job
    container_name: embeddings-report-job-container
    environment:
      - MYSQL_HOST=*************
      - MYSQL_PORT=9336
      - MYSQL_USER=root
      - MYSQL_PASSWORD=taureauai
      - MYSQL_DATABASE=dms
      - TAS_WS_URI=wss://tas-dev.taureau.ai/ws/temp_storage/
      - MILVUS_URI=http://***********:19530
      - MILVUS_DATABASE=allbyai_embeddings_deduplicate
      - MILVUS_TOKEN=taureau:taureauai
      - MILVUS_USER=taureau
      - MILVUS_PASSWORD=taureauai
      - DMS_HOST=https://dms-dev.taureau.ai
    restart: on-failure
    networks:
      - dedup_jobs_network

  send-confirm-notification:
    build:
      context: .
      dockerfile: Dockerfile.confirmnoti
    image: send-confirm-notification-job
    container_name: send-confirm-notification-job-container
    environment:
      - MYSQL_HOST=*************
      - MYSQL_PORT=9336
      - MYSQL_USER=root
      - MYSQL_PASSWORD=taureauai
      - MYSQL_DATABASE=dms
      - TAS_WS_URI=wss://tas-dev.taureau.ai/ws/temp_storage/
    restart: on-failure
    networks:
      - dedup_jobs_network

  send-complete-notification:
    build:
      context: .
      dockerfile: Dockerfile.completenoti
    image: send-complete-notification-job
    container_name: send-complete-notification-job-container
    environment:
      - MYSQL_HOST=*************
      - MYSQL_PORT=9336
      - MYSQL_USER=root
      - MYSQL_PASSWORD=taureauai
      - MYSQL_DATABASE=dms
      - TAS_WS_URI=wss://tas-dev.taureau.ai/ws/temp_storage/
      - MILVUS_URI=http://***********:19530
      - MILVUS_DATABASE=allbyai_embeddings_deduplicate
      - MILVUS_TOKEN=taureau:taureauai
    restart: on-failure
    networks:
      - dedup_jobs_network

networks:
  dedup_jobs_network:
    driver: bridge
    ipam:
        driver: default
        config:
            - subnet: "************/24"
              gateway: "************"