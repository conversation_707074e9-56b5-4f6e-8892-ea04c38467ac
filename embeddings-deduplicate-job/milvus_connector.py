from pymilvus import MilvusClient, DataType, utility
from uuid import uuid4
import pymysql
from pymysql.cursors import DictCursor
import json
import tqdm
import time
import concurrent.futures
import os
from requests import sessions

class MySqlConnector:
    def __init__(self, cfg):
        self.mysql_db = pymysql.connect(
            host = os.getenv("MYSQL_HOST", "*************"),
            port = int(os.getenv("MYSQL_PORT", 9336)),
            user = os.getenv("MYSQL_USER", "root"),
            password = os.getenv("MYSQL_PASSWORD", "taureauai"),
            database = os.getenv("MYSQL_DATABASE", "dms")
        )
        self.mysql_cursor = self.mysql_db.cursor(DictCursor)

class EmbeddingServerConnector:
    def __init__(self, cfg):
        self.model_name = 'clip-image-vit-32-float32'
        # self.endpoint = f'https://service.taureau.ai/api/ai_embedding/{self.model_name}/predict'
        self.endpoint = f"{cfg['endpoint']}/predict"

    def post(self, data):
        print(self.endpoint)
        headers = {
            "Content-Type": "application/json",
            'Accept': 'application/json'
        }

        with sessions.Session() as session:
            res = session.request(method='post',
                                  url=self.endpoint,
                                  headers=headers,
                                  data=json.dumps(data)
            )
        json_data = res.json()
        status_code = res.status_code
        return status_code, json_data

class MilvusConnector:
    def __init__(self, cfg):
        self.uri = cfg.get('uri', 'http://localhost:19530')
        self.database = cfg.get('database', 'default')
        self.token = cfg.get('token', None)
        self.client = MilvusClient(uri=self.uri, 
                                   db_name=self.database,
                                   token=self.token)
        self.mysql_connector = MySqlConnector(cfg={})
        self.endpoint = cfg['endpoint']

    def list_collections(self):
        return self.client.list_collections()
    
    def create_collection(self, collection_name):
        self.client.drop_collection(collection_name)

        schema = MilvusClient.create_schema(enable_dynamic_field=True)
        schema.add_field(field_name="id", datatype=DataType.VARCHAR, is_primary=True, max_length=64)
        schema.add_field(field_name="vector", datatype=DataType.FLOAT_VECTOR, dim=512)
        schema.add_field(field_name="fileId", datatype=DataType.VARCHAR, max_length=64)
        schema.add_field(field_name="projectId", datatype=DataType.VARCHAR, max_length=64)
        schema.add_field(field_name="batchId", datatype=DataType.VARCHAR, max_length=64)
        schema.add_field(field_name="storageId", datatype=DataType.VARCHAR, max_length=64)
        schema.add_field(field_name="storageArea", datatype=DataType.VARCHAR, max_length=64)

        index_params = MilvusClient.prepare_index_params()
        index_params.add_index(
            field_name="vector",
            index_type="IVF_FLAT",
            metric_type="COSINE",
            params={"nlist": 128}
        )

        self.client.create_collection(collection_name=collection_name, schema=schema, index_params=index_params)

    def create_collection_dms(self, project_id, model_id):
        collection_name = f"Embeddings_{project_id}_{model_id}"
        self.create_collection(collection_name)

    # def insert_dm_dms(self, project_id, file_id, model_id):
    def insert_dm_dms(self, data):
        project_id = data['project_id']
        file_id = data['file_id']
        model_id = data['model_id']
        storage_value = data['storage_value']
        storage_id = data['storage_id']
        file_url = data['file_url']

        # self.mysql_connector.mysql_cursor.execute(f"SELECT * FROM ProjectFiles WHERE FileId = '{file_id}' AND ProjectId = '{project_id}'")
        # project_file = self.mysql_connector.mysql_cursor.fetchone()
        # if project_file is None:
        #     return "FileProject not found"
        
        # project_id = project_file['ProjectId']
        batch_id = ''
        storage_area = 'PersistentStorage'
        # self.mysql_connector.mysql_cursor.execute(f"SELECT * FROM FileMSs as F \
        #                           INNER JOIN Storages as S \
        #                           ON F.StorageId = S.Id \
        #                           WHERE F.Id = '{file_id}'")
        # fileMS = self.mysql_connector.mysql_cursor.fetchone()
        # if fileMS is None:
        #     return "FileMS not found"
        # storage_value = json.loads(fileMS['Value'])
        # storage_id = fileMS['StorageId']
        request_embedding = {
            "access_key": storage_value['AccessKey'],
            "secret_key": storage_value['SecretKey'],
            "endpoint": storage_value['Endpoint'],
            "region": storage_value['Region'],
            "bucket": storage_value['BucketName'],
            "image_path": file_url
        }

        status_code, embedding_result = EmbeddingServerConnector({'endpoint':self.endpoint}).post(request_embedding)
        if status_code != 200:
            return "Error when get embedding"
        vector = embedding_result['embedding']

        data = {
            "id": str(uuid4()), 
            "vector": vector, 
            "fileId": file_id,
            "projectId": project_id,
            "batchId": batch_id,
            "storageId": storage_id,
            "storageArea": storage_area,
            "clusterId": ''
        }

        collection_name = f"Embeddings_{project_id.replace('-', '')}_{model_id.replace('-', '')}"
        # if not self.client.has_collection(collection_name):
        #     self.create_collection(collection_name)

        query = self.client.query(collection_name=collection_name, filter=f'fileId == "{file_id}" and projectId == "{project_id}"')
        if len(query) > 0:
            print("Data already exist")
            for item in query:
                res = self.client.upsert(collection_name=collection_name, 
                                         data={
                                             "id": item['id'], 
                                             "vector": vector, 
                                            "fileId": file_id,
                                            "projectId": project_id,
                                            "batchId": batch_id,
                                            "storageId": storage_id,
                                            "storageArea": storage_area,
                                            "clusterId": ''
                                                }
                                        )
                print(res)
        else:
            res = self.client.insert(collection_name=f"Embeddings_{project_id.replace('-', '')}_{model_id.replace('-', '')}", data=data)
            return res
    
    def insert_dm_project_dms(self, project_id, model_id):
        self.mysql_connector.mysql_db.ping(reconnect=True)
        # self.mysql_connector.mysql_cursor.execute(f"SELECT * FROM ProjectFiles \
        #                                           WHERE ProjectId = '{project_id}' \
        #                                             AND IsDelete = 0 AND IsArchive = 0")
        self.mysql_connector.mysql_cursor.execute(f"SELECT * FROM ProjectFiles as P \
                                                  INNER JOIN FileMSs as F \
                                                    ON P.FileId = F.Id \
                                                  INNER JOIN Storages as S \
                                                    ON F.StorageId = S.Id \
                                                  WHERE ProjectId = '{project_id}' \
                                                    AND IsDelete = 0 AND IsArchive = 0")
        project_files = self.mysql_connector.mysql_cursor.fetchall()
        print('Embedding files in PersistentStorage...')

        collection_name = f"Embeddings_{project_id.replace('-', '')}_{model_id.replace('-', '')}"
        if not self.client.has_collection(collection_name):
            self.create_collection(collection_name)

        datas = []
        # for project_file in tqdm.tqdm(project_files):
        for project_file in project_files:
            file_id = project_file['FileId']
            storage_value = json.loads(project_file['Value'])
            storage_id = project_file['StorageId']
            file_url = project_file['FileUrl']
            # self.insert_dm_dms(project_id, file_id, model_id)
            datas.append({
                "project_id": project_id,
                "file_id": file_id,
                "model_id": model_id,
                "storage_value": storage_value,
                "storage_id": storage_id,
                "file_url": file_url,
            })
        executor_request = concurrent.futures.ThreadPoolExecutor(max_workers=10)
        executor_request.map(self.insert_dm_dms, datas)
        executor_request.shutdown(wait=True)
        self.client.refresh_load(collection_name=f"Embeddings_{project_id.replace('-', '')}_{model_id.replace('-', '')}")
        print('Done')
        return 

    # def insert_batch_dms(self, project_id, model_id, batch_id, file_id, storage_value, storage_id, storage_file_path):
    def insert_batch_dms(self, data):
        project_id = data['project_id']
        model_id = data['model_id']
        batch_id = data['batch_id']
        file_id = data['file_id']
        storage_value = data['storage_value']
        storage_id = data['storage_id']
        storage_file_path = data['storage_file_path']

        request_embedding = {
            "access_key": storage_value['AccessKey'],
            "secret_key": storage_value['SecretKey'],
            "endpoint": storage_value['Endpoint'],
            "region": storage_value['Region'],
            "bucket": storage_value['BucketName'],
            "image_path": storage_file_path
        }
        status_code, embedding_result = EmbeddingServerConnector({'endpoint':self.endpoint}).post(request_embedding)
        if status_code != 200:
            return "Error when get embedding"
        vector = embedding_result['embedding']
        storage_area = 'TempStorage'

        data = {
            "id": str(uuid4()), 
            "vector": vector, 
            "fileId": file_id,
            "projectId": project_id,
            "batchId": batch_id,
            "storageId": storage_id,
            "storageArea": storage_area,
            "clusterId": ''
        }

        collection_name = f"Embeddings_{project_id.replace('-', '')}_{model_id.replace('-', '')}"
        if not self.client.has_collection(collection_name):
            self.create_collection(collection_name)

        query = self.client.query(collection_name=collection_name, filter=f'fileId == "{file_id}" and projectId == "{project_id}"')
        if len(query) > 0:
            print("Data already exist")
            for item in query:
                res = self.client.upsert(collection_name=collection_name, 
                                         data={
                                             "id": item['id'], 
                                             "vector": vector, 
                                            "fileId": file_id,
                                            "projectId": project_id,
                                            "batchId": batch_id,
                                            "storageId": storage_id,
                                            "storageArea": storage_area,
                                            "clusterId": ''
                                                }
                                        )
                print(res)
        else:
            res = self.client.insert(collection_name=f"Embeddings_{project_id.replace('-', '')}_{model_id.replace('-', '')}", data=data)
            return res
    
    def insert_batch_files_dms(self, project_id, batch_id, model_id):
        self.mysql_connector.mysql_db.ping(reconnect=True)
        self.mysql_connector.mysql_cursor.execute(f"SELECT * FROM TempStorageFiles as T \
                                                INNER JOIN Storages as S \
                                                  ON T.StorageId=S.Id \
                                                  WHERE T.ProjectId = '{project_id}' AND T.TempStorageBatchFileId = '{batch_id}' AND IsDelete = 0")
        batch_files = self.mysql_connector.mysql_cursor.fetchall()
        print('Embedding files in TempStorage...')

        collection_name = f"Embeddings_{project_id.replace('-', '')}_{model_id.replace('-', '')}"
        if not self.client.has_collection(collection_name):
            self.create_collection(collection_name)

        datas = []
        # for batch_file in tqdm.tqdm(batch_files):
        for batch_file in batch_files:
            file_id = batch_file['Id']
            storage_value = json.loads(batch_file['Value'])
            storage_file_path = batch_file['FileUrl']
            storage_id = batch_file['StorageId']
            # self.insert_batch_dms(project_id, model_id, batch_id, file_id, storage_value, storage_id, storage_file_path)
            datas.append({
                "project_id": project_id,
                "model_id": model_id,
                "batch_id": batch_id,
                "file_id": file_id,
                "storage_value": storage_value,
                "storage_id": storage_id,
                "storage_file_path": storage_file_path
            })
        executor_request = concurrent.futures.ThreadPoolExecutor(max_workers=10)
        executor_request.map(self.insert_batch_dms, datas)
        executor_request.shutdown(wait=True)
        self.client.refresh_load(collection_name=f"Embeddings_{project_id.replace('-', '')}_{model_id.replace('-', '')}")
        print('Done')
        return
