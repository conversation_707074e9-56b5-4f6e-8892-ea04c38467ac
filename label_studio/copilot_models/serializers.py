from rest_framework import serializers
from dms_connector.serializers import FilterSerializer

COPILOT_MODELS_STATUS_CHOICES = [
    ('New', 'New'),
    ('Publish', 'Publish'),
    ('Cancel', 'Cancel'),
    ('Failed', 'Failed'),
    ('UnPublish', 'UnPublish'),
]

class CopilotModelGETPreviewEmbeddingsSerializer(serializers.Serializer):
    aiApiUrl = serializers.CharField(max_length=100, required=True)
    image = serializers.FileField()

class CopilotModelFetchModelsByCategorySerializer(FilterSerializer):
    status = serializers.ChoiceField(choices=COPILOT_MODELS_STATUS_CHOICES, required=False)

class CopilotModelCreateModelByCategorySerializer(serializers.Serializer):
    modelName = serializers.CharField(max_length=1000, required=True, allow_blank=True)
    apiEndpoint = serializers.CharField(max_length=1000, required=True, allow_blank=True)
    status = serializers.ChoiceField(choices=COPILOT_MODELS_STATUS_CHOICES, required=True)

class CopilotModelUpdateModelByCategorySerializer(CopilotModelCreateModelByCategorySerializer):
    pass