import logging
from drf_yasg.utils import swagger_auto_schema

from django.utils.decorators import method_decorator
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON>ars<PERSON>, JSONParser, MultiPartParser
from copilot_models.serializers import *
from copilot_models.serializers_response import *

from dms_connector.api import AuthenticatedAPIView, DMS_COPILOT_MODELS_API

logger = logging.getLogger(__name__)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Copilot Models'],
    operation_summary='Get preview model embeddings',
    operation_description='Get the embeddings for the preview model',
    request_body=CopilotModelGETPreviewEmbeddingsSerializer,
    responses={200: CopilotModelGETPreviewEmbeddingsDetailResponseSerializer}
))
class CopilotModelGETPreviewEmbeddingsAPI(AuthenticatedAPIView):
    parser_classes = (FormParser, MultiPartParser)

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = CopilotModelGETPreviewEmbeddingsSerializer(data=request.data)

        file = request.FILES['image']
        data = serializer.initial_data

        status_code, response = DMS_COPILOT_MODELS_API().get_preview_embedding(user, file, data)

        return Response(response, status_code)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Copilot Models'],
    operation_summary='Fetch all model categories',
    operation_description='Fetch all model categories'
))
class CopilotModelFetchAllModelCategoriesAPI(AuthenticatedAPIView):
    def get(self, request, *args, **kwargs):
        user = request.user

        status_code, response = DMS_COPILOT_MODELS_API().fetch_all_model_categories(user, params=None)

        return Response(response, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Copilot Models'],
    operation_summary='Fetch models by category',
    operation_description='Fetch models by category',
    query_serializer=CopilotModelFetchModelsByCategorySerializer
))
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Copilot Models'],
    operation_summary='Create model by category',
    operation_description='Create model by category',
    request_body=CopilotModelCreateModelByCategorySerializer
))
class CopilotModelFetchModelsByCategoryAPI(AuthenticatedAPIView):
    def get(self, request, models_category_id, *args, **kwargs):
        user = request.user
        serializer = CopilotModelFetchModelsByCategorySerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        status_code, response = DMS_COPILOT_MODELS_API().fetch_models_by_category(user, models_category_id, params=serializer.data)

        return Response(response, status_code)
    
    def post(self, request, models_category_id, *args, **kwargs):
        user = request.user
        serializer = CopilotModelCreateModelByCategorySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, response = DMS_COPILOT_MODELS_API().create_model_by_category(user, models_category_id, data=serializer.data)

        return Response(response, status_code)

@method_decorator(name='patch', decorator=swagger_auto_schema(
    tags=['Copilot Models'],
    operation_summary='Update model by category',
    operation_description='Update model by category',
    request_body=CopilotModelUpdateModelByCategorySerializer
))
class CopilotModelDetailModelsByCategoryAPI(AuthenticatedAPIView):
    def patch(self, request, models_category_id, model_id, *args, **kwargs):
        user = request.user
        serializer = CopilotModelUpdateModelByCategorySerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, response = DMS_COPILOT_MODELS_API().update_model_in_category(user, models_category_id, model_id, data=serializer.data)

        return Response(response, status_code)
    