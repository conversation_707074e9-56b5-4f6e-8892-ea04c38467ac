from django.urls import include, path

from . import api, views

app_name = 'copilot_models'

_api_urlpatterns = [
    path('get_preview_embeddings', api.CopilotModelGETPreviewEmbeddingsAPI.as_view(), name='get-preview-embeddings'),
    path('fetch_all_model_categories', api.CopilotModelFetchAllModelCategoriesAPI.as_view(), name='fetch-all-model-categories'),
    path('<uuid:models_category_id>/models_by_category', api.CopilotModelFetchModelsByCategoryAPI.as_view(), name='fetch-models-by-category'),
    path('<uuid:models_category_id>/models_by_category/<uuid:model_id>', api.CopilotModelDetailModelsByCategoryAPI.as_view(), name='fetch-models-by-category'),
]

urlpatterns = [
    path('api/copilot_models/', include((_api_urlpatterns, app_name), namespace='api')),
]