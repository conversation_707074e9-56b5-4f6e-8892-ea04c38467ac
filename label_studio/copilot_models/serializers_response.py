from dms_connector.serializers_response import *
from rest_framework import serializers

class CopilotModelGETPreviewEmbeddingsResponseSerializer(serializers.Serializer):
    embedding_result = serializers.CharField(required=False)
    embedding_dimensions = serializers.IntegerField(required=False)

class CopilotModelGETPreviewEmbeddingsDetailResponseSerializer(DetailResponseSerializer):
    data = CopilotModelGETPreviewEmbeddingsResponseSerializer(required=False)