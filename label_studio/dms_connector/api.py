from rest_framework.views import APIView
from rest_framework.permissions import IsA<PERSON>enticated

from dms_connector.modules.auth import DMS_AUTH_API, DMS_AUTH_SETTING_API
from dms_connector.modules.attribute import DMS_ATTIBUTE_API
from dms_connector.modules.module import DMS_MODULE_API, DMS_MODULE_MEMBER_API
from dms_connector.modules.organization import DMS_ORGANIZATION_API, DMS_ORGANIZATION_SSO_CONFIG_API, DMS_ORGANIZATION_SSO_DEFAULT_API
from dms_connector.modules.project import DMS_PROJECT_API, DMS_PROJECT_MEMBER_API
from dms_connector.modules.project_setting import DMS_PROJECT_ATTR_API, DMS_PROJECT_DATATYPE_API, DMS_PROJECT_CONFIG_API, DMS_PROJECT_TASK_CONFIG_API, DMS_PROJECT_AIMODEL_CONFIG_API, DMS_PROJECT_AIMODEL_PREDICTION_API
from dms_connector.modules.dataset import DMS_DATASET_API, DMS_DATASET_QUERY_API
from dms_connector.modules.user import DMS_USER_API
from dms_connector.modules.role import DMS_ROLE_API
from dms_connector.modules.storage import DMS_STORAGE_API
from dms_connector.modules.file import DMS_FILE_API
from dms_connector.modules.file import DMS_STATISTIC_API
from dms_connector.modules.project_role import DMS_PROJECT_ROLE_API
from dms_connector.modules.user_project_role import DMS_USER_PROJECT_ROLE_API
from dms_connector.modules.permission import DMS_PERMISSION_API
from dms_connector.modules.task_assignment import DMS_TASK_ASSIGNMENT_API
from dms_connector.modules.notifications_setting import DMS_NOTIFICATION_SETTING_API, DMS_NOTIFICATION_TEMPLATE_API
from dms_connector.modules.identity_provider import DMS_IDENTITY_PROVIDER_API
from dms_connector.modules.copilot_model import DMS_COPILOT_MODELS_API
from dms_connector.modules.temp_storage import DMS_TEMP_STORAGE_API, DMS_TEMP_STORAGE_DEDUP_API, DMS_TEMP_STORAGE_APPLY_SYSTEM_MODEL_API, DMS_TEMP_STORAGE_REASONS_API
from dms_connector.modules.tabular_data import DMS_TABULAR_DATA_API

class AuthenticatedAPIView(APIView):
    permission_classes = [IsAuthenticated]
