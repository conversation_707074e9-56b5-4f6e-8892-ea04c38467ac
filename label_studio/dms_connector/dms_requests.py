from requests import sessions, post as requests_post
from datetime import datetime
import json
import time
from users.models import User
from dateutil import parser
from django.conf import settings
from users.models import User


def get_header(user=None, token=None):
    headers = {
        "Content-Type": "application/json",
        'Accept': 'application/json'
    }
    if user or token:
        headers['Authorization'] = 'Bearer ' + (token if token else user.dms_auth.token)

    return headers


def refresh_token(user):
    url = '{}/api/v{}/auth/refresh'.format(settings.DMS_HOST, settings.DMS_VERSION)

    # headers = get_header(user=user)
    headers = get_header()
    payload = {
        "accessToken": user.dms_auth.token,
        "refreshToken": user.dms_auth.refresh_token,
    }
    res = requests_post(url, data=json.dumps(payload), headers=headers)
    try:
        json_data = res.json()
    except:
        print("refresh token failed 500 ")
        time.sleep(0.2)
        refresh_user = User.objects.get(id=user.id)
        token = refresh_user.dms_auth.token
        print(f"get again token 500 {token}")
        return token
    print("request refresh token +++++++++++++")
    print(res.status_code)
    print(json_data)
    token = None

    if json_data.get('success', False):
        token = json_data['data']['token']
        user.dms_auth.token = token
        user.dms_auth.expiration = parser.parse(json_data['data']['expiration'])
        user.dms_auth.refresh_token = json_data['data']['refreshToken']
        user.dms_auth.refresh_token_expiration = parser.parse(json_data['data']['refreshTokenExpiration'])
        user.dms_auth.save()
    else:
        print("refresh token failed")
        time.sleep(0.2)
        refresh_user = User.objects.get(id=user.id)
        token = refresh_user.dms_auth.token
        print(f"get again token: {token}")

    return token


class FakeRequest:
    def __init__(self, msg='', success=False, error_code=401):
        self.msg = msg
        self.success = success
        self.error_code = error_code
        self.status_code = error_code

    def json(self):
        return {'success': self.success, 'errorCode': self.error_code, 'message': self.msg, 'data': None}


def request(method, url, user=None, token=None, headers=None, **kwargs):
    # print(url, user, token, method)
    msg = None
    if headers is None:
        headers = get_header(user=user, token=token)
    with sessions.Session() as session:
        res = session.request(method=method, url=url, headers=headers, **kwargs)
        if True:
            print('============================================================================')
            print('request:', method, url)
            print('execute_time:', res.elapsed.total_seconds())
            if 'params' in kwargs:
                print('params:', kwargs['params'])
            print('headers:', headers)
            if 'data' in kwargs:
                print('body:', kwargs['data'])
            print('response:', res.status_code, res.headers)
            # print(res.content)
        # if user and res.status_code == 401 and 'WWW-Authenticate' in res.headers and 'token expired' in res.headers[
        #     'WWW-Authenticate']:
        if user and res.status_code == 401:
            dt_expiration = user.dms_auth.expiration.timestamp()
            print(dt_expiration)
            dt_refresh_token_expiration = user.dms_auth.refresh_token_expiration.timestamp()
            print(dt_refresh_token_expiration)
            dt_now = datetime.utcnow().timestamp()
            print(dt_now)
            # if dt_expiration < dt_now < dt_refresh_token_expiration:
            if dt_now < dt_refresh_token_expiration:
                print("refresh here !!!")
                token = refresh_token(user)
                print(token)
                if token:
                    print("new token")
                    res = request(method, url, token=token, **kwargs)
                    print(res)
                    print(res.content)
                else:
                    # msg = res.headers['WWW-Authenticate']
                    msg = 'Refresh token failed!'
                    return FakeRequest(msg=msg, error_code=401) 
            else:
                msg = 'Login expired!'
                return FakeRequest(msg=msg, error_code=401)
        if not res.status_code == 200:
            if not msg:
                msg = 'Something wrong!'
            return FakeRequest(msg=msg, error_code=res.status_code)

        return res


def get(url, params=None, user=None, token=None, **kwargs):
    return request('get', url, params=params, user=user, token=token, **kwargs)


def options(url, **kwargs):
    return request('options', url, **kwargs)


def head(url, **kwargs):
    kwargs.setdefault('allow_redirects', False)
    return request('head', url, **kwargs)


def post(url, data=None, json=None, user=None, token=None, **kwargs):
    return request('post', url, data=data, json=json, user=user, token=token, **kwargs)


def put(url, data=None, user=None, token=None, **kwargs):
    return request('put', url, data=data, user=user, token=token, **kwargs)


def patch(url, data=None, user=None, token=None, **kwargs):
    return request('patch', url, data=data, user=user, token=token, **kwargs)


def delete(url, user=None, token=None, **kwargs):
    return request('delete', url, user=user, token=token, **kwargs)
