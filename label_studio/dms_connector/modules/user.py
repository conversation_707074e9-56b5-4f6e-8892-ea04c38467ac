import json
from dms_connector.modules.base import DMS_API
from dms_connector import dms_requests


class DMS_USER_API(DMS_API):
    API_NAME = 'users'

    def get_roles(self, user=None):
        action = 'resources'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def get_invitation_token(self, user=None, token=None):
        action = 'invitation-token'
        url = '{}/{}'.format(self.get_url(), action)
        params = {
            **token,
        }
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def post_confirm_create(self, user=None, data=None):
        action = 'confirm-create'
        url = '{}/{}'.format(self.get_url(), action)
        data = {
            **data,
        }
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def post_invitation(self, user=None, param=None):
        action = 'invitation'
        url = '{}/{}'.format(self.get_url(), action)
        data = {
            **param,
        }
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def post_reset_token_invitation(self, user=None, data=None):
        action = 'reset-token-invitation'
        url = '{}/{}'.format(self.get_url(), action)
        data = {
            **data,
        }
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def patch_revert_user(self, user, user_id):
        action = 'revert'
        url = '{}/{}/{}'.format(self.get_url(), action, user_id)
        res = dms_requests.patch(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def delete_temporary_user(self, user, user_id):
        action = 'deletetemporary'
        url = '{}/{}/{}'.format(self.get_url(), action, user_id)
        res = dms_requests.delete(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_multiple_temporary_user(self, user, data):
        action = 'deletemutipletemporary'
        url = '{}/{}'.format(self.get_url(), action)
        data = data['userIds']
        res = dms_requests.delete(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def revert_mutiple_user(self, user, data):
        action = 'revertmultiple'
        url = '{}/{}'.format(self.get_url(), action)
        data = data['userIds']
        res = dms_requests.patch(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def invite_user_register(self, user, data):
        action = 'inviteuserregister'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def resend_activation_link(self, user, params):
        action = 'resendaccountactivationlink'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def generate_secret(self, user):
        action = 'generatesecret'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.post(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def admin_override_password(self, user, data, user_id):
        action = 'setpassword'
        url = '{}/{}/{}'.format(self.get_url(), user_id, action)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data