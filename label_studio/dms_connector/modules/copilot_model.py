import base64, json
from requests import sessions
from dms_connector.modules.base import DMS_API
from dms_connector import dms_requests

class DMS_COPILOT_MODELS_API(DMS_API):
    API_NAME = 'modelcategories'

    def get_preview_embedding(self, user, file=None, data=None):
        ai_api_url = data.get('aiApiUrl', None)
        encoded_string = base64.b64encode(file.read())
        data = {
            'image_base64': encoded_string.decode('utf-8')
        }
        headers = {
            "Content-Type": "application/json",
            'Accept': 'application/json'
        }

        try:
            with sessions.Session() as session:
                res = session.request('post',
                                      url=f"{ai_api_url}/preview",
                                      headers=headers,
                                      data=json.dumps(data))
        except Exception as e:
            result = {
                "success": False,
                "errorCode": None,
                "detail": e,
                "data": None
            }
            status_code = 200
            return status_code, result
        
        if res.status_code != 200:
            return res.status_code, res.json()
        
        status_code = 200
        result = {
            "success": True,
            "errorCode": None,
            "detail": None,
            "data": res.json()
        }

        return status_code, result
    
    def fetch_all_model_categories(self, user, params=None):
        url = "{}/fetchall".format(self.get_url())
        res = dms_requests.get(url, user=user, params=params)

        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def fetch_models_by_category(self, user, models_category_id, params=None):
        url = "{}/{}/models".format(self.get_url(), models_category_id)
        res = dms_requests.get(url, user=user, params=params)

        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def create_model_by_category(self, user, models_category_id, data=None):
        url = "{}/{}/models".format(self.get_url(), models_category_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))

        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def update_model_in_category(self, user, models_category_id, model_id, data=None):
        url = "{}/{}/models/{}".format(self.get_url(), models_category_id, model_id)
        res = dms_requests.patch(url, user=user, data=json.dumps(data))

        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data