import json
import requests
import urllib.parse

from django.conf import settings
import xml.etree.ElementTree as ET

from dms_connector.modules.base import DMS_API
from dms_connector import dms_requests
import shutil, os, csv, time
from dms_connector.modules.project_setting import DMS_PROJECT_ATTR_API
from dms_connector.modules.storage import DMS_STORAGE_API
from data_manager.serializers import DatasetFilterSerializer
from core.utils.params import cast_bool_from_str
from multilevel_classification.tasks import attrb_sync_es_multiple_files

class DMS_FILE_API(DMS_API):
    DMS_API_VERSION = 2
    API_NAME = 'projects'
    API_SUB_NAME = 'files'

    def list_with_task(self, user, params, project):
        url = '{}/datamanagers'.format(self.get_url(project=project))
        res = dms_requests.get(url=url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        if status_code == 200:
            for item in json_data['items']:
                current_step = {
                    "step": None,
                    "stepId": None,
                    "task": None
                }

                for attribute in item['attributes']:
                    if attribute['code'] == "view_workflow_step":
                        current_step['step'] = attribute['value']
                    if attribute['code'] == "view_workflow_step_id":
                        current_step['stepId'] = attribute['value']

                if current_step['stepId'] is not None:
                    for task in item['shortTaskAssignments']:
                        if task['workFlowStepId'] == current_step['stepId']:
                            current_step['task'] = task
                            break
                
                item.update({'currentStep': current_step})
        
        return status_code, json_data
    def put_multi_file(self, user, data, project_id):
        action = 'updatefileids'
        url = '{}/{}'.format(self.get_url(project=project_id), action)
        for item in data:
            for attrb in item['attributes']:
                if attrb['code'] in settings.FILE_ATTRB_MAPPING_FIELD.keys():
                    if attrb['code'] == 'view_archive':
                        item[settings.FILE_ATTRB_MAPPING_FIELD[attrb['code']]] = cast_bool_from_str(attrb['value'])
                    else:
                        item[settings.FILE_ATTRB_MAPPING_FIELD[attrb['code']]] = attrb['value']
        res = dms_requests.put(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def list_new_managers(self, user, params, project):
        url = "{}/datamanagers".format(self.get_url(project=project))

        params_copy = params.copy()

        value_attrb = params.get('valueAttribute', [])
        for attrb in value_attrb:
            attrb = json.loads(attrb)
            if attrb['attributeCode'] == 'view_state':
                params_copy['viewStates'] = attrb['keyword']
            if attrb['attributeCode'] == 'view_archive':
                params_copy['isArchive'] = cast_bool_from_str(attrb['keyword'][0])
            if attrb['attributeCode'] == 'view_priority':
                params_copy['priorities'] = attrb['keyword']
            if attrb['attributeCode'] == 'view_workflow_step_id':
                params_copy['viewSteps'] = attrb['keyword']
        if len(value_attrb) > 0:
            del params_copy['valueAttribute']

        res = dms_requests.get(url, user=user, params=params_copy)
        json_data = res.json()
        status_code = res.status_code
        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    current_step = {
                        "step": item['viewWorkFlowStep'] if item['viewWorkFlowStep'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStep'],
                        "stepId": item['viewWorkFlowStepId'] if item['viewWorkFlowStepId'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStepId'],
                        "task": None
                    }
                    if current_step['stepId'] is not None:
                        for task in item['shortTaskAssignments']:
                            if task['workFlowStepId'] == current_step['stepId']:
                                current_step['task'] = task
                                break

                    attributes = [
                        {
                            'code': "view_state",
                            'value': item['viewState'] if item['viewState'] is not None else settings.DEFAULT_FILE_FIELDS['viewState']
                        },
                        {
                            'code': "view_priority",
                            'value': item['priority'] if item['priority'] is not None else settings.DEFAULT_FILE_FIELDS['priority']
                        },
                        {
                            'code': "view_folder",
                            'value': item['pathFolder'] if item['pathFolder'] is not None else settings.DEFAULT_FILE_FIELDS['pathFolder']
                        },
                        {
                            'code': "view_archive",
                            'value': str(item['isArchive']).lower() if item['isArchive'] is not None else "false"
                        },
                        {
                            'code': "view_workflow_step",
                            'value': item['viewWorkFlowStep'] if item['viewWorkFlowStep'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStep']
                        },
                        {
                            'code': "view_workflow_step_id",
                            'value': item['viewWorkFlowStepId'] if item['viewWorkFlowStepId'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStepId']
                        }
                    ]
                    item['attributes'] = attributes
                    item.update({'currentStep': current_step})

        return status_code, json_data
    
    def list_new_managers_with_security(self, user, params, project):
        url = "{}/newdatamanagers".format(self.get_url(project=project))

        params_copy = params.copy()

        value_attrb = params.get('valueAttribute', [])
        for attrb in value_attrb:
            attrb = json.loads(attrb)
            if attrb['attributeCode'] == 'view_state':
                params_copy['viewStates'] = attrb['keyword']
            if attrb['attributeCode'] == 'view_archive':
                params_copy['isArchive'] = cast_bool_from_str(attrb['keyword'][0])
            if attrb['attributeCode'] == 'view_priority':
                params_copy['priorities'] = attrb['keyword']
            if attrb['attributeCode'] == 'view_workflow_step_id':
                params_copy['viewSteps'] = attrb['keyword']
        if len(value_attrb) > 0:
            del params_copy['valueAttribute']

        res = dms_requests.get(url, user=user, params=params_copy)
        json_data = res.json()
        status_code = res.status_code
        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    current_step = {
                        "step": item['viewWorkFlowStep'] if item['viewWorkFlowStep'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStep'],
                        "stepId": item['viewWorkFlowStepId'] if item['viewWorkFlowStepId'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStepId'],
                        "task": None
                    }
                    if current_step['stepId'] is not None:
                        for task in item['shortTaskAssignments']:
                            if task['workFlowStepId'] == current_step['stepId']:
                                current_step['task'] = task
                                break

                    attributes = [
                        {
                            'code': "view_state",
                            'value': item['viewState'] if item['viewState'] is not None else settings.DEFAULT_FILE_FIELDS['viewState']
                        },
                        {
                            'code': "view_priority",
                            'value': item['priority'] if item['priority'] is not None else settings.DEFAULT_FILE_FIELDS['priority']
                        },
                        {
                            'code': "view_folder",
                            'value': item['pathFolder'] if item['pathFolder'] is not None else settings.DEFAULT_FILE_FIELDS['pathFolder']
                        },
                        {
                            'code': "view_archive",
                            'value': str(item['isArchive']).lower() if item['isArchive'] is not None else "false"
                        },
                        {
                            'code': "view_workflow_step",
                            'value': item['viewWorkFlowStep'] if item['viewWorkFlowStep'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStep']
                        },
                        {
                            'code': "view_workflow_step_id",
                            'value': item['viewWorkFlowStepId'] if item['viewWorkFlowStepId'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStepId']
                        }
                    ]
                    item['attributes'] = attributes
                    item.update({'currentStep': current_step})

        return status_code, json_data
    
    def list_new_managers_with_view_annotation_result_attrb(self, user, params, project):
        url = "{}/searchfileclonedata".format(self.get_url(project=project))

        params_copy = params.copy()

        value_attrb = params.get('valueAttribute', [])
        for attrb in value_attrb:
            attrb = json.loads(attrb)
            if attrb['attributeCode'] == 'view_state':
                params_copy['viewStates'] = attrb['keyword']
            if attrb['attributeCode'] == 'view_archive':
                params_copy['isArchive'] = cast_bool_from_str(attrb['keyword'][0])
            if attrb['attributeCode'] == 'view_priority':
                params_copy['priorities'] = attrb['keyword']
            if attrb['attributeCode'] == 'view_workflow_step_id':
                params_copy['viewSteps'] = attrb['keyword']
        if len(value_attrb) > 0:
            del params_copy['valueAttribute']

        res = dms_requests.get(url, user=user, params=params_copy)
        json_data = res.json()
        status_code = res.status_code
        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    current_step = {
                        "step": item['viewWorkFlowStep'] if item['viewWorkFlowStep'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStep'],
                        "stepId": item['viewWorkFlowStepId'] if item['viewWorkFlowStepId'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStepId'],
                        "task": None
                    }
                    if current_step['stepId'] is not None:
                        for task in item['shortTaskAssignments']:
                            if task['workFlowStepId'] == current_step['stepId']:
                                current_step['task'] = task
                                break

                    attributes = [
                        {
                            'code': "view_state",
                            'value': item['viewState'] if item['viewState'] is not None else settings.DEFAULT_FILE_FIELDS['viewState']
                        },
                        {
                            'code': "view_priority",
                            'value': item['priority'] if item['priority'] is not None else settings.DEFAULT_FILE_FIELDS['priority']
                        },
                        {
                            'code': "view_folder",
                            'value': item['pathFolder'] if item['pathFolder'] is not None else settings.DEFAULT_FILE_FIELDS['pathFolder']
                        },
                        {
                            'code': "view_archive",
                            'value': str(item['isArchive']).lower() if item['isArchive'] is not None else "false"
                        },
                        {
                            'code': "view_workflow_step",
                            'value': item['viewWorkFlowStep'] if item['viewWorkFlowStep'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStep']
                        },
                        {
                            'code': "view_workflow_step_id",
                            'value': item['viewWorkFlowStepId'] if item['viewWorkFlowStepId'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStepId']
                        }
                    ]
                    item['attributes'].extend(attributes)
                    item.update({'currentStep': current_step})

        return status_code, json_data
    
    def patch_revert_file(self, user, project_id, file_id):
        action = 'revert'
        url = '{}/{}/{}'.format(self.get_url(project=project_id), action, file_id)
        res = dms_requests.patch(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def delete_temporary_file(self, user, project_id, file_id):
        action = 'deletetemporary'
        url = '{}/{}/{}'.format(self.get_url(project=project_id), action, file_id)
        res = dms_requests.delete(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def fetch_deleted_files(self, user, project_id, params):
        url = '{}/filedeletedmanager'.format(self.get_url(project=project_id))
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def put(self, user, id, data, project=None):
        url = '{}/{}'.format(self.get_url(project=project), id)
        data_copy = data.copy()
        for item in data['attributes']:
            if item['code'] in settings.FILE_ATTRB_MAPPING_FIELD.keys():
                if item['code'] == 'view_archive':
                    data_copy[settings.FILE_ATTRB_MAPPING_FIELD[item['code']]] = cast_bool_from_str(item['value'])
                else:
                    data_copy[settings.FILE_ATTRB_MAPPING_FIELD[item['code']]] = item['value']
        res = dms_requests.put(url, user=user, data=json.dumps(data_copy))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def get_csv_sample(self, user, project_id, convertor_data):
        if convertor_data == "Bounding-Box":
            return [["image_path_local", "image_width", "image_height", "view_annotation_result"]]
        if convertor_data == "Circle":
            return [["image_path_local", "image_width", "image_height", "view_annotation_result"]]
        if convertor_data == "Polygon":
            return [["image_path_local", "image_width", "image_height", "view_annotation_result"]]
        if convertor_data == "Scoring":
            return [["image_path_local", "image_width", "image_height", "view_annotation_result"]]
        params = {
            "pageSize": 1000,
        }
        status_code, json_data = DMS_PROJECT_ATTR_API().list(user, params, project=project_id)
        items = json_data.get("items")
        # row_type = [""]
        row_title = ["image_path_local"]
        for item in items:
            if item['code'].startswith('meta'):
                # row_type.append("meta")
                row_title.append(item['code'][5:])
        #     if item['code'].startswith('annotate_attr'):
        #         row_type.append("annotate")
        #         row_title.append(item['code'][14:])
        # row_type.append("annotate")
        # row_title.append("annotate_instance")
        # return [row_type, row_title]
        return [row_title]

    def update_path(self, user, id, project, path=None):
        # action = 'dataset-items'
        # url = '{}/{}/{}'.format(self.get_url(project=project), action, id)
        url = '{}/{}'.format(self.get_url(project=project), id)
        data = {
            "pathFolder": path,
            "attributes": [
                {
                    "code": settings.ATTR_VIEW_FOLDER,
                    "value": path
                }
            ]
        }
        res = dms_requests.put(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def update_path_multi(self, user, ids, project, path=None):
        url = '{}/updatefileids'.format(self.get_url(project=project))
        print(path)
        data = []
        for id in ids:
            data.append({
                "fileId": id,
                "attributes": [
                    {
                        "code": settings.ATTR_VIEW_FOLDER,
                        "value": path
                    }
                ],
                "pathFolder": path
            })
        res = dms_requests.put(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def list_with_path(self, user, params={}, module=None, project=None, path=None):
        url = "{}/datamanagers".format(self.get_url(project=project))

        params.update({
            "pathFolder": path,
            "isArchive": False,
            "isDelete": False
        })
        res = dms_requests.get(url, user=user, params=params)
        status_code = res.status_code
        json_data = res.json()

        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    current_step = {
                        "step": item['viewWorkFlowStep'] if item['viewWorkFlowStep'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStep'],
                        "stepId": item['viewWorkFlowStepId'] if item['viewWorkFlowStepId'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStepId'],
                        "task": None
                    }
                    if current_step['stepId'] is not None:
                        for task in item['shortTaskAssignments']:
                            if task['workFlowStepId'] == current_step['stepId']:
                                current_step['task'] = task
                                break

                    attributes = [
                        {
                            'code': "view_state",
                            'value': item['viewState'] if item['viewState'] is not None else settings.DEFAULT_FILE_FIELDS['viewState']
                        },
                        {
                            'code': "view_priority",
                            'value': item['priority'] if item['priority'] is not None else settings.DEFAULT_FILE_FIELDS['priority']
                        },
                        {
                            'code': "view_folder",
                            'value': item['pathFolder'] if item['pathFolder'] is not None else settings.DEFAULT_FILE_FIELDS['pathFolder']
                        },
                        {
                            'code': "view_archive",
                            'value': str(item['isArchive']).lower() if item['isArchive'] is not None else "false"
                        },
                        {
                            'code': "view_workflow_step",
                            'value': item['viewWorkFlowStep'] if item['viewWorkFlowStep'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStep']
                        },
                        {
                            'code': "view_workflow_step_id",
                            'value': item['viewWorkFlowStepId'] if item['viewWorkFlowStepId'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStepId']
                        }
                    ]
                    item['attributes'] = attributes
                    item.update({'currentStep': current_step})

        return status_code, json_data
    
    def get_annotate_history(self, user, id, project):
        action = "historyversion"
        url = '{}/{}/{}'.format(self.get_url(project=project), id, action)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def create_conversations(self, user, id, data, project):
        action = 'conversations'
        url = '{}/{}/{}'.format(self.get_url(project=project), id, action)
        payload = {
            'userId': str(user.dms_id),
            'content': data['content'],
            'mentionIds': data['mentionIds']
        }
        res = dms_requests.post(url, user=user, data=json.dumps(payload))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def edit_conversations(self, user, id, data, project):
        action = 'conversations'
        url = '{}/{}/{}'.format(self.get_url(project=project), id, action)
        payload = {
            'id': data['id'],
            'userId': str(user.dms_id),
            'content': data['content'],
            'mentionIds': data['mentionIds']
        }
        res = dms_requests.put(url, user=user, data=json.dumps(payload))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def create_redoReason(self, user, id, data, project):
        action = 'redoReason'
        url = '{}/{}/{}'.format(self.get_url(project=project), id, action)
        payload = {
            'userId': str(user.dms_id),
            'contentRedoReason': data['contentRedoReason']
        }
        res = dms_requests.post(url, user=user, data=json.dumps(payload))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def edit_redoReason(self, user, id, data, project):
        action = 'redoReason'
        url = '{}/{}/{}'.format(self.get_url(project=project), id, action)
        payload = {
            'id': data['id'],
            'userId': str(user.dms_id),
            'contentRedoReason': data['contentRedoReason']
        }
        res = dms_requests.put(url, user=user, data=json.dumps(payload))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def create_rejectReason(self, user, id, data, project):
        action = 'rejectReason'
        url = '{}/{}/{}'.format(self.get_url(project=project), id, action)
        payload = {
            'userId': str(user.dms_id),
            'contentRejectReason': data['contentRejectReason']
        }
        res = dms_requests.post(url, user=user, data=json.dumps(payload))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def edit_rejectReason(self, user, id, data, project):
        action = 'rejectReason'
        url = '{}/{}/{}'.format(self.get_url(project=project), id, action)
        payload = {
            'id': data['id'],
            'userId': str(user.dms_id),
            'contentRejectReason': data['contentRejectReason']
        }
        res = dms_requests.put(url, user=user, data=json.dumps(payload))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def edit_datafollows(self, user, id, data, project):
        action = 'datafollows'
        url = '{}/{}/{}'.format(self.get_url(project=project), id, action)
        payload = {
            'userId': str(user.dms_id),
            'isFollowed': data['isFollowed']
        }
        res = dms_requests.put(url, user=user, data=json.dumps(payload))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def post_request_completed_noti(self, user, id, data, project):
        action = 'requestnotification'
        url = '{}/{}/{}'.format(self.get_url(project=project), id, action)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_conversations(self, user, id, project, conversation_id):
        action = 'conversations'
        url = '{}/{}/{}/{}'.format(self.get_url(project=project), id, action, conversation_id)
        res = dms_requests.delete(url, user=user)
        json_data =  res.json()
        status_code = res.status_code

        return status_code, json_data

    def get_conversations(self, user, id, project, params):
        action = 'conversations'
        url = '{}/{}/{}/search'.format(self.get_url(project=project), id, action)
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_redoReason(self, user, id, project, redo_reason_id):
        action = 'redoReason'
        url = '{}/{}/{}/{}'.format(self.get_url(project=project), id, action, redo_reason_id)
        res = dms_requests.delete(url, user=user)
        json_data =  res.json()
        status_code = res.status_code

        return status_code, json_data

    def get_redoReason(self, user, id, project, params):
        action = 'redoReason'
        url = '{}/{}/{}/search'.format(self.get_url(project=project), id, action)
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_rejectReason(self, user, id, project, reject_reason_id):
        action = 'rejectReason'
        url = '{}/{}/{}/{}'.format(self.get_url(project=project), id, action, reject_reason_id)
        res = dms_requests.delete(url, user=user)
        json_data =  res.json()
        status_code = res.status_code

        return status_code, json_data

    def get_rejectReason(self, user, id, project, params):
        action = 'rejectReason'
        url = '{}/{}/{}/search'.format(self.get_url(project=project), id, action)
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def get_attrs(self, user, project):
        action = 'project-attribute'
        url = '{}/{}'.format(self.get_url(project=project), action)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def image(self, user, data):
        action = 'image'

        url = '{}/{}/{}'.format(self.get_url(project=data['project']), data['file_id'], action)
        res = dms_requests.get(url, user=user, params=data)
        img = res.content
        status_code = res.status_code

        return status_code, img

    def generate_presigned_upload_url(self, user, project, file):
        action = 'presigned-url'
        file_name = file.name.split("/")[-1]
        file_name = urllib.parse.quote(file_name)
        url = '{}/{}?fileName={}'.format(self.get_url(project=project), action, file_name)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def generate_dataset_item(self, storage_id, file_url, file, attributes=[]):
        file_name = file.name.split("/")[-1]
        # file_path = file_url.split('/')
        file_ext = file_name.split('.')[-1]

        return {
            "storageId": storage_id,
            "label": file_name,
            "fileUrl": file_url,
            "filePath": file_url,
            "fileName": file_name,
            "fileSize": str(file.size),
            "dataType": file_ext,
            "attributes": attributes
        }

    def upload_file(self, user, project, file, attributes):
        status_code, upload_info = self.generate_presigned_upload_url(user, project, file)
        upload_url = upload_info['data']['preSignedUrl']
        upload_file_url = upload_info['data']['fileUrl']
        storage_id = upload_info['data']['storageId']
        res = requests.put(upload_url, data=file, headers={'Content-Type': 'application/octet-stream'})
        return res.status_code, self.generate_dataset_item(storage_id, upload_file_url, file, attributes)

    def create(self, user, file, project, attributes=[], data_view={}):

        status_code, dataset_item = self.upload_file(user, project, file, attributes)
        if status_code != 200:
            return status_code, {'message': 'Upload file to storage failed'}
        dataset_item.update(data_view)

        if status_code == 200:
            url = self.get_url(project=project)
            res = dms_requests.post(url, user=user, data=json.dumps(dataset_item))
            json_data = res.json()
            status_code = res.status_code

            return status_code, json_data

        return status_code, None
    
    def create_no_check_datatype(self, user, file, project, attributes=[], data_view={}):

        status_code, dataset_item = self.upload_file(user, project, file, attributes)
        if status_code != 200:
            return status_code, {'message': 'Upload file to storage failed'}
        dataset_item.update(data_view)

        if dataset_item['dataType'] not in [item.lower() for item in ["Png", "Jpg", "Jpeg", "Json", "Csv", "Dcm", "Zip"]]:
            dataset_item['dataType'] = "other"

        if status_code == 200:
            url = self.get_url(project=project)
            res = dms_requests.post(url, user=user, data=json.dumps(dataset_item))
            json_data = res.json()
            status_code = res.status_code

            return status_code, json_data

        return status_code, None
    
    def generate_presigned_upload_url_for_avatar(self, user, project, file):
        action = 'presigned-url-avatar'
        file_name = file.name.split("/")[-1]
        file_name = urllib.parse.quote(file_name)
        url = '{}/{}?fileName={}'.format(self.get_url(project=project), action, file_name)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def upload_file_avatar(self, user, project, file, attributes):
        status_code, upload_info = self.generate_presigned_upload_url_for_avatar(user, project, file)
        upload_url = upload_info['data']['preSignedUrl']
        upload_file_url = upload_info['data']['fileUrl']
        storage_id = upload_info['data']['storageId']
        res = requests.put(upload_url, data=file, headers={'Content-Type': 'application/octet-stream'})
        return res.status_code, self.generate_dataset_item(storage_id, upload_file_url, file, attributes)
    
    def create_without_projectId(self, user, files, project):
        json_data_list = {
            "success": True,
            "errorCode": None,
            "message": None,
            "data": []
        }
        status_code = 200
        for file in files:
            status_code, dataset_item = self.upload_file_avatar(user, project, file, [])
            if status_code == 200:
                url = "{}/createfilewithoutprojectid".format(self.get_url(project=project))
                res = dms_requests.post(url, user=user, data=json.dumps(dataset_item))
                json_data = res.json()
                status_code = res.status_code

                json_data_list['data'].append(json_data['data']) 
            else:
                json_data_list['data'].append(None)
        
        return status_code, json_data_list
                
    def upload_file_attribute(self, user, file, project_id, file_id, attribute_code):
        status_code, dataset_item = self.upload_file(user, project_id, file, attributes=[])
        if status_code == 200:
            status_code_storage, json_data_storage = DMS_STORAGE_API().list(user)
            storage_endpoints = None
            for item in json_data_storage['items']:
                if item['isPrimary']:
                    storage_endpoints = "{}/{}/".format(settings.MINIO_HOST, item['configValue']['bucketName'])
            
            image_attrib = storage_endpoints + dataset_item['fileUrl']
            data_attrib = {
                "attributes": [
                    {
                        "code": attribute_code,
                        "value": image_attrib
                    }
                ]
            }
            status_code_update_attrb, json_data_update_attrb = self.put(user, file_id, data=data_attrib, project=project_id)

        return status_code_update_attrb, json_data_update_attrb
    
    def upload_cropped_file(self, user, file, project_id, project_file_id, data):
        if file is not None:
            origin_master_file_id = data.get('deleteAnnotationFileId', None)
            # Remove view_annotation_result attribute
            try:
                update_res = self.put(user, origin_master_file_id, data={'attributes': [{
                    'code': 'view_annotation_result',
                    'value': data.get('viewAnnotationResult', None)
                }],
                'ignoreTimestamp': True}, project=project_id)
            except Exception as e:
                print(f"Error remove view_annotation_result attribute: {e}")

            status_code, dataset_item = self.upload_file(user, project_id, file, attributes=[])
            if status_code == 200:
                url = "{}/createfilewithoutprojectid".format(self.get_url(project=project_id))
                res = dms_requests.post(url, user=user, data=json.dumps(dataset_item))
                json_data = res.json()
                status_code = res.status_code

                master_file_id = json_data['data']
                crop_file_url = "{}/crop/{}".format(self.get_url(project=project_id), project_file_id)
                crop_data = {
                    "annotateResult": data['annotateResult'],
                    "masterFileId": master_file_id,
                    "cropInfo": json.dumps(data['cropInfo']) if data['cropInfo'] != "null" else None,
                    "timestamp": data['timestamp'],
                }
                crop_res = dms_requests.patch(crop_file_url, user=user, data=json.dumps(crop_data))
                crop_json_data = crop_res.json()
                crop_status_code = crop_res.status_code
                if crop_status_code == 200:
                    crop_json_data['data'] = master_file_id
                    return crop_status_code, crop_json_data
                return crop_status_code, None
        else:
            master_file_id = data.get('masterFileId', None)
            origin_master_file_id = data.get('deleteAnnotationFileId', None)
            # Remove view_annotation_result attribute
            try:
                update_res = self.put(user, origin_master_file_id, data={'attributes': [{
                    'code': 'view_annotation_result',
                    'value': data.get('viewAnnotationResult', None)
                }],
                'ignoreTimestamp': True}, project=project_id)
            except Exception as e:
                print(f"Error remove view_annotation_result attribute: {e}")

            if master_file_id is None:
                return 400, "Master file id is required"
            crop_file_url = "{}/crop/{}".format(self.get_url(project=project_id), project_file_id)
            crop_data = {
                "annotateResult": data['annotateResult'],
                "masterFileId": master_file_id,
                "cropInfo": json.dumps(data['cropInfo']) if data['cropInfo'] != "null" else None,
                "timestamp": data['timestamp'],
            }
            crop_res = dms_requests.patch(crop_file_url, user=user, data=json.dumps(crop_data))
            crop_json_data = crop_res.json()
            crop_status_code = crop_res.status_code
            if crop_status_code == 200:
                crop_json_data['data'] = master_file_id
                return crop_status_code, crop_json_data
            return crop_status_code, None
        return status_code, None
    
    def upload_save_as_file(self, user, file, project_id, data):
        attributes = data.get('attributes', "[]")
        attributes = json.loads(attributes)
        attributes.append({
            'code': 'view_annotation_result',
            'value': None
        })
        status_code, dataset_item = self.upload_file(user, project_id, file, attributes=attributes)
        # Rename file name
        new_file_name = dataset_item['fileName'].replace(f".{dataset_item['dataType']}", "") + " (copy)" + f".{dataset_item['dataType']}"
        dataset_item['fileName'] = new_file_name
        if status_code != 200:
            return status_code, {'message': 'Upload file to storage failed'}
        dataset_item.update({
            'cropInfo': json.dumps(data['cropInfo']) if 'cropInfo' in data else None,
            'viewState': data.get('viewState', "New"),
            'viewWorkFlowStep': data.get('viewWorkFlowStep', None),
            'viewWorkFlowStepId': data.get('viewWorkFlowStepId', None),
            'priority': data.get('priority', 0),
            'pathFolder': data.get('pathFolder', ""),
            'isArchive': data.get('isArchive', False),
        })

        url = "{}/saveAs".format(self.get_url(project=project_id))
        res = dms_requests.post(url, user=user, data=json.dumps(dataset_item))
        json_data = res.json()
        status_code = res.status_code
        
        return status_code, json_data

    def confirm_csv(user, project_id, file_save, convertor_data):
        working_dir = "data_manager/"
        folder_path = working_dir + "unzip_data_temp/csv_folder"
        # print("=========")
        # print(os.getcwd())
        # print("=========")
        # os.system('ls')
        # print("=========")
        # os.system('ls data_manager/unzip_data_temp')
        # print("=========")
        # print(os.path.exists(folder_path))
        if os.path.exists(folder_path) == False:
            os.mkdir(folder_path)
        
        file_save_timestamp = "{}{}.{}".format(file_save.replace(".csv", ""), str(int(time.time())), "csv")
        
        shutil.move("{}/{}".format(settings.MEDIA_ROOT, file_save),
                        "{}/{}".format(folder_path, file_save_timestamp),
                        copy_function=shutil.copy2)
        
        csv_file_path = "{}/{}".format(folder_path, file_save_timestamp)
        # get all attribute in csv file
        attributes_csv_list = []
        with open(csv_file_path) as csv_file:
            csv_reader = csv.reader(csv_file, delimiter=',')
            line_count = 0
            for row in csv_reader:
                if line_count == 0:
                    for i in range(1, len(row)):
                        attributes_csv_list.append(row[i])
                    break

        if convertor_data == "Bounding-Box":
            attributes_project_list = ['image_width', 'image_height', 'view_annotation_result']
        elif convertor_data == "Circle":
            attributes_project_list = ['image_width', 'image_height', 'view_annotation_result']
        elif convertor_data == "Polygon":
            attributes_project_list = ['image_width', 'image_height', 'view_annotation_result']
        elif convertor_data == "Scoring":
            attributes_project_list = ['image_width', 'image_height', 'view_annotation_result']
        else:
            # get all attribute in project
            params = {
                "pageSize": 1000,
            }
            status_code, json_data = DMS_PROJECT_ATTR_API().list(user, params, project=project_id)
            items = json_data.get("items")
            attributes_project_list = []
            for item in items:
                if item['code'].startswith('meta'):
                    attributes_project_list.append(item["code"][5:])
        # find attribute in csv file but not in project
        attributes_not_in_proj_list = [attr for attr in attributes_csv_list if attr not in set(attributes_project_list)]
        # find attribute in project but not in csv file
        attributes_not_in_csv_file = [attr for attr in attributes_project_list if attr not in set(attributes_csv_list)]
        # find attribute not in
        results = {
            'file_name': file_save_timestamp,
            'success': True,
            'data': None,
            'message': None
        }

        if len(attributes_not_in_proj_list) != 0 or len(attributes_not_in_csv_file) != 0:
            results['success'] = False
            results['data'] = {
                'attr_not_in_project': attributes_not_in_proj_list,
                'attr_not_in_csv': attributes_not_in_csv_file
            }

        return results

    def statistic_data_status(self, user, project_id, statistic_filter):
        url = "{}/statistic".format(self.get_url(project=project_id))
        data = {
            "success": True,
            "data": None,
            "errorCode": None,
            "message": None
        }
        if statistic_filter == "Status":
            res = dms_requests.get(url, user=user)
            json_data = res.json()
            status_code = res.status_code
            status_statistic = {
                "New": 0,
                "Labeling": 0,
                "Inreview": 0,
                "Completed": 0
            }
            if status_code == 200:
                if json_data['success']:
                    for item in json_data['items']:
                        status_statistic.update({item['viewState']: item['totalOfViewState']})
            data['data'] = status_statistic
        
        if statistic_filter == "Priority":
            data['data'] = {
                "0": 0,
                "1": 0,
                "2": 0,
                "3": 0,
                "4": 0
            }

        return 200, data
    
    def trigger_rule_ON_assignment(self, user, project_id):
        url_fetch_files_list = "{}/datamanagers".format(self.get_url(project=project_id))

        res_count_files_list = dms_requests.get(url_fetch_files_list, user=user, params={
            "page": 1,
            "pageSize": 1,
            "isArchive": False,
            "isDelete": False
        })
        count_files_list = -1

        json_data_count_files_list = res_count_files_list.json()
        status_code_count_files_list = res_count_files_list.status_code
        if status_code_count_files_list == 200:
            if json_data_count_files_list['success']:
                count_files_list = json_data_count_files_list['total']

        if count_files_list >= 0:
            page_number = int(count_files_list / 50) + 1
            for page in range(1, page_number + 1):
                res_fetch_files_list = dms_requests.get(url_fetch_files_list, user=user, params={
                    "page": page,
                    "pageSize": 50,
                    "isArchive": False,
                    "isDelete": False
                })
                json_data_fetch_files_list = res_fetch_files_list.json()
                status_code_fetch_files_list = res_fetch_files_list.status_code
                if status_code_fetch_files_list == 200:
                    if json_data_fetch_files_list['success']:
                        data = []
                        for item in json_data_fetch_files_list['items']:
                            if item['viewState'] not in ["New", "Completed"]:
                                data.append({
                                    "fileId": item['fileId'],
                                    "attributes": [
                                        {
                                            "code": "view_state",
                                            "value": "New"
                                        },
                                        {
                                            "code": "view_workflow_step",
                                            "value": "New"
                                        },
                                        {
                                            "code": "view_workflow_step_id",
                                            "value": "00000000-0000-0000-0000-000000000000"
                                        },
                                        {
                                            "code": "view_annotation_result",
                                            "value": ""
                                        }
                                    ]
                                })
                        res_update_files = self.put_multi_file(user=user, data=data, project_id=project_id)
                        file_ids = [item["fileId"] for item in data]
                        print(file_ids)
                        if len(file_ids) > 0:
                            attrb_sync_es_multiple_files.delay(project_master_id=project_id, file_ids=file_ids)

class DMS_STATISTIC_API(DMS_API):
    DMS_API_VERSION = 2
    API_NAME = 'statisticdata'

    def traversal_xml_recursion(self, child, class_id_dict, class_parent=[]):
        for child_ in child.findall("class"):
            class_current = class_parent.copy()
            class_current.append(child_.attrib['name'])

            class_id_dict.update({
                child_.attrib['uniqueid']: class_current
            })
            self.traversal_xml_recursion(child_, class_id_dict=class_id_dict, class_parent=class_current)

    def statistic_class_tree(self, user, project_id, class_tree_name):
        res = dms_requests.get(url="{}/{}".format(self.get_url().replace("/statisticdata", "/projects"), project_id), user=user)
        json_data = res.json()
        if json_data['success'] == False:
            return res.status_code, json_data
        class_config_string = json_data["data"]["classConfig"]
        # print(class_config_string)

        try:
            root = ET.fromstring(class_config_string)
        except:
            error_result = {
                "success": False,
                "data": None,
                "errorCode": None,
                "message": "Invalid XML format"
            }
            return 200, error_result
        # print(class_tree_name)
        class_id_dict = {}

        if class_tree_name is None:
            for child_tools in root.findall("tools"):
                for child_classtree in child_tools.findall("classtree"):
                    self.traversal_xml_recursion(child=child_classtree, class_id_dict=class_id_dict)
        else:
            for child_tools in root.findall("tools"):
                for child_classtree in child_tools.findall("classtree"):
                    if child_classtree.attrib['name'] == class_tree_name:
                        self.traversal_xml_recursion(child=child_classtree, class_id_dict=class_id_dict)
                    else:
                        continue
        # print(class_id_dict)

        res = dms_requests.get(url="{}/{}".format(self.get_url(), project_id), user=user)
        json_data = res.json()
        if json_data['success'] == False:
            return res.status_code, json_data

        result = []
        for item in json_data['items']:
            try:
                class_name = class_id_dict[item['classKey']]
            except:
                continue
            item_class = {
                "total": item['total'],
                "classKey": item['classKey'],
                "className": class_name
            }
            result.append(item_class)
        
        data = {
            "success": True,
            "data": result,
            "errorCode": None,
            "message": None
        }

        return 200, data

    def statistic_multi_class_tree(self, user, project_id):
        res = dms_requests.get(url="{}/{}/attributes".format(self.get_url().replace("/statisticdata", "/projects"), project_id), user=user, params={'code': settings.ATTR_PREFIX_ANNOTATE_CLASSTREE,
                                                                                                    'codeOperator': 'StartWith'})
        print("{}/{}/attributes".format(self.get_url(), project_id))
        classtree_name_list = []
        for item in res.json()['items']:
            classtree_name_list.append(item['code'].replace(settings.ATTR_PREFIX_ANNOTATE_CLASSTREE, ""))

        aggregate_data = {}

        for classtree_name in classtree_name_list:
            status_code, data = self.statistic_class_tree(user=user, project_id=project_id, class_tree_name=classtree_name)
            aggregate_data.update({
                classtree_name: data['data']
            })

        return_data = {
            "success": True,
            "data": aggregate_data,
            "errorCode": None,
            "message": None
        }

        return 200, return_data