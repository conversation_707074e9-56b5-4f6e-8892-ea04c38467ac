import json, datetime, copy, io, uuid, csv, sys
import xml.etree.ElementTree as ET
from shapely.geometry import Polygon
from nanoid import generate
from django.conf import settings
from dms_connector import dms_requests
from dms_connector.modules.base import DMS_API
from dms_connector.modules.project import DMS_PROJECT_API
from export_version.tasks import trigger_convert_coco_from_Csv_bgr_job, trigger_convert_coco_from_Json_bgr_job, \
                                trigger_convert_yolo_from_Json_bgr_job, trigger_convert_yolo_from_Csv_bgr_job
from export_version.utils import *

maxInt = sys.maxsize
while True:
    # decrease the maxInt value by factor 10 
    # as long as the OverflowError occurs.

    try:
        csv.field_size_limit(maxInt)
        break
    except OverflowError:
        maxInt = int(maxInt/10)

class DMS_VERSION_CONVERTOR_API(DMS_API):
    DMS_API_VERSION = 2
    API_NAME = 'projects'
    API_SUB_NAME = 'export-versions'

    TORUS_INFOR_COCO = {
        "info": {
            "description": "Example COCO dataset",
            "owner": "Torus Action",
            "url": "https://www.torus.ai",
            "version": "1.0",
            "year": 2023,
            "contributor": "TAS",
            "date_created": ""
        },
        "licenses": [
            {
                "id": 1,
                "name": "License 1",
                "url": "https://www.torus.ai/license1"
            },
            {
                "id": 2,
                "name": "License 2",
                "url": "https://www.torus.ai/license2"
            }
        ],
        "images": [],
        "annotations": [],
        "categories": []
    }

    def flatten_taxonomy(self, class_tree_data, flattened_list):
        for item in class_tree_data:
            flattened_list.append(item['value'])
            if len(item['children']) != 0:
                self.flatten_taxonomy(item['children'], flattened_list)

    def annotation_result_convert_Csv(self, image_width, image_height, convertor_list, view_annotation_result, user, project_id, labels_flatten_list=None, advanced_config_id=None):
        view_annotation_result_format = ""

        view_annotation_result_BoundingBox = []
        view_annotation_result_Circle = []
        view_annotation_result_Polygon = []
        view_annotation_result_Scoring = []
        view_annotation_result_Brush = []
        view_annotation_result_Taxonomy = [[]]
        view_annotation_result_Instance = []
        view_annotation_result_OneK = []
        if labels_flatten_list is not None:
            for label_flatten in labels_flatten_list:
                view_annotation_result_Taxonomy[0].append("None")
        result_in_view_annotation_result = view_annotation_result[0]['result']
        for item in view_annotation_result[0]['result']:
            item_value = item.get('value', None)

            if item['type'] == 'rectanglelabels':
                try:
                    item_format = {
                        'x': item_value['x'] * image_width / 100,
                        'y': item_value['y'] * image_height / 100,
                        'width': item_value['width'] * image_width / 100,
                        'height': item_value['height'] * image_height / 100,
                        'rotate': item_value['rotation'] if 'rotation' in item_value else 0,
                        'class_name': None
                    }
                except:
                    item_format = {
                        'x': None,
                        'y': None,
                        'width': None,
                        'height': None,
                        'rotate': None,
                        'class_name': None
                    }
                try:
                    item_format['class_name'] = item_value['rectanglelabels'][0]
                except:
                    item_format['class_name'] = ''
                
                meta_label = {}
                for child_item in result_in_view_annotation_result:
                    if child_item['type'] == 'choices' and child_item['id'] == item['id']:
                        meta_label.update({child_item['from_name']:{"type": "choices", "value": child_item['value']['choices']}})
                    if child_item['type'] == 'textarea' and child_item['id'] == item['id']:
                        meta_label.update({child_item['from_name']: {"type": "textarea", "value": child_item['value']['text']}})
                    if child_item['type'] == 'number' and child_item['id'] == item['id']:
                        meta_label.update({child_item['from_name']: {"type": "number", "value": child_item['value']['number']}})
                if meta_label != {}:
                    item_format['meta_label'] = meta_label

                view_annotation_result_BoundingBox.append(item_format)
            if item['type'] == 'ellipselabels':
                try:
                    item_format = {
                        'x': item_value['x'] * image_width / 100,
                        'y': item_value['y'] * image_height / 100,
                        'radius': (item_value['radiusX'] * image_width / 100 + item_value['radiusY'] * image_height / 100) / 2,
                        'class_name': None
                    }
                except:
                    item_format = {
                        'x': None,
                        'y': None,
                        'radius': None,
                        'class_name': None
                    }
                try:
                    item_format['class_name'] = item_value['ellipselabels'][0]
                except:
                    item_format['class_name'] = ''
                view_annotation_result_Circle.append(item_format)
            if item['type'] == 'polygonlabels':
                try:
                    points_list_normalizing = item_value['points']
                    points_list = []
                    for point in points_list_normalizing:
                        x_point = point[0] * image_width / 100
                        y_point = point[1] * image_height / 100
                        points_list.append([x_point, y_point])
                    item_format = {
                        'points': points_list,
                        'class_name': None
                    }
                except:
                    item_format = {
                        'points': None,
                        'class_name': None
                    }
                try:
                    item_format['class_name'] = item_value['polygonlabels'][0]
                except:
                    item_format['class_name'] = ''
                meta_label = {}
                for child_item in result_in_view_annotation_result:
                    if child_item['type'] == 'choices' and child_item['id'] == item['id']:
                        meta_label.update({child_item['from_name']:{"type": "choices", "value": child_item['value']['choices']}})
                    if child_item['type'] == 'textarea' and child_item['id'] == item['id']:
                        meta_label.update({child_item['from_name']: {"type": "textarea", "value": child_item['value']['text']}})
                    if child_item['type'] == 'number' and child_item['id'] == item['id']:
                        meta_label.update({child_item['from_name']: {"type": "number", "value": child_item['value']['number']}})
                if meta_label != {}:
                    item_format['meta_label'] = meta_label

                view_annotation_result_Polygon.append(item_format)
            if item['type'] == 'scoringlabels':
                try:
                    scoring_points = item_value['scoringlabels'][0]
                    scoring_points = json.loads(scoring_points)
                    item_format = {
                        'score': [],
                        'size': int(scoring_points['size'])
                    }
                    for row in scoring_points['values']:
                        row_ = []
                        for col in row:
                            row_.append(col['score'])
                        item_format['score'].append(row_)
                except:
                    item_format = {
                        'score': None,
                        'size': None
                    }
                view_annotation_result_Scoring.append(item_format)
            if item['type'] == "brushlabels":
                try:
                    mask = rle2mask(item_value['rle'],(image_height,image_width,1))[:,:,0]
                    mask[mask>0] = 1
                    lx, rx, hy, ly = get_foreground_bounds(mask)
                    bbox = [lx, ly, rx-lx, hy-ly]
                    seg = binary_mask_to_rle(mask)

                    item_format = {
                        'rle': item_value['rle'],
                        'class_name': None
                    }
                except:
                    item_format = {
                        'rle': None,
                        'class_name': None
                    }
                try:
                    item_format['class_name'] = item_value['brushlabels'][0]
                except:
                    item_format['class_name'] = ''
                meta_label = {}
                for child_item in result_in_view_annotation_result:
                    if child_item['type'] == 'choices' and child_item['id'] == item['id']:
                        meta_label.update({child_item['from_name']:{"type": "choices", "value": child_item['value']['choices']}})
                    if child_item['type'] == 'textarea' and child_item['id'] == item['id']:
                        meta_label.update({child_item['from_name']: {"type": "textarea", "value": child_item['value']['text']}})
                    if child_item['type'] == 'number' and child_item['id'] == item['id']:
                        meta_label.update({child_item['from_name']: {"type": "number", "value": child_item['value']['number']}})
                if meta_label != {}:
                    item_format['meta_label'] = meta_label
                view_annotation_result_Brush.append(item_format)
            if item['type'] == "taxonomy":
                one_hot_list = []
                for label_flatten in labels_flatten_list:
                    if label_flatten in [label[-1] for label in item_value['taxonomy']]:
                        one_hot_list.append(1)
                    else:
                        one_hot_list.append(0)
                item_format = one_hot_list
                view_annotation_result_Taxonomy[0] = item_format
            if item['type'] == "onek":
                onek_value = item['value']['onek']
                advanced_version_id = onek_value['advancedConfigId'] if advanced_config_id is None else advanced_config_id

                # group_onek_value = [i for i in onek_value['group']]
                # class_onek_value = [i for i in onek_value['class']]
                # subclass_onek_value = [i for i in onek_value['subclass']]
                # subsubclass_onek_value = [i for i in onek_value['subsubclass']]
                # body_part_onek_value = [i for i in onek_value['bodypart']]
                # other_onek_value = [i for i in onek_value['other']]
                main_feature_onek_value = [i for i in onek_value['mainFeatures']]
                associated_feature_onek_value = [i for i in onek_value['associatedFeatures']]
                notes_onek_value = [i for i in onek_value['notes']]

                # merged_onek_value = group_onek_value + class_onek_value + subclass_onek_value + subsubclass_onek_value + body_part_onek_value + other_onek_value
                merged_onek_value = main_feature_onek_value + associated_feature_onek_value
                labels_url = f"{self.get_url(project=project_id).replace('export-versions', 'advancedconfigurations')}/{advanced_version_id}/labels"
                labels_res = dms_requests.post(labels_url, user=user, data=json.dumps({"labelIds": merged_onek_value}))
                if labels_res.status_code == 200:
                    labels_data = labels_res.json()['data']
                    # filter isSeparated and isDeleted
                    merged_onek_value = []
                    if labels_data is None:
                        labels_data = []
                    for label in labels_data:
                        if not label['isDeleted']:          #  ignore "and not label['isSeparated']"" for bug 30675
                            if label['labelId'] in main_feature_onek_value and label['labelId'] not in associated_feature_onek_value:
                                merged_onek_value.append({"name": label['fullName'], "type": "Main"})
                            elif label['labelId'] in associated_feature_onek_value and label['labelId'] not in main_feature_onek_value:
                                merged_onek_value.append({"name": label['fullName'], "type": "Associated"})
                            elif label['labelId'] in main_feature_onek_value and label['labelId'] in associated_feature_onek_value:
                                merged_onek_value.append({"name": label['fullName'], "type": "Main-Associated"})
                            else:
                                pass

                    # remove duplicate in list
                    # merged_onek_value = list(dict.fromkeys(merged_onek_value))
                    # TODO: filter merged to

                    merged_onek_value.append({"name": "", "type": "Notes", "value": notes_onek_value})
                    
                view_annotation_result_OneK.extend(merged_onek_value)
        
        view_annotation_result_BoundingBox_format = "\"{}\"".format(str(view_annotation_result_BoundingBox))
        view_annotation_result_Circle_format = "\"{}\"".format(str(view_annotation_result_Circle))
        view_annotation_result_Polygon_format = "\"{}\"".format(str(view_annotation_result_Polygon))
        view_annotation_result_Scoring_format = "\"{}\"".format(str(view_annotation_result_Scoring))
        view_annotation_result_Brush_format = "\"{}\"".format(str(view_annotation_result_Brush))
        view_annotation_result_Taxonomy_format = ",".join([str(x) for x in view_annotation_result_Taxonomy[0]])
        view_annotation_result_OneK_format = "\"{}\"".format(str(view_annotation_result_OneK))

        if 'Instance' in convertor_list:
            view_annotation_result_Instance_format = "\"{}\"".format(str([]))
            for item in view_annotation_result[0]['result']:
                if item['type'] == 'instance':
                   view_annotation_result_Instance.append({
                       'instance_id': item['id'],
                       'instance_name': item['name'],
                       'components': []
                   })
            for item in view_annotation_result[0]['result']:
                item_value = item.get('value', None)
                if item['type'] == 'rectanglelabels':
                    for instance in view_annotation_result_Instance:
                        if instance['instance_id'] == item['parentID']:
                            try:
                                instance['components'].append({
                                    'type': 'rectangle',
                                    'class_name': item_value['rectanglelabels'][0],
                                    'value': {
                                        'x': item_value['x'] * image_width / 100,
                                        'y': item_value['y'] * image_height / 100,
                                        'width': item_value['width'] * image_width / 100,
                                        'height': item_value['height'] * image_height / 100
                                    }
                                })
                            except Exception as e:
                                print(e)
                                pass
                if item['type'] == 'keypointlabels':
                    for instance in view_annotation_result_Instance:
                        if instance['instance_id'] == item['parentID']:
                            try:
                                instance['components'].append({
                                    'type': 'keypoint',
                                    'class_name': item_value['keypointlabels'][0],
                                    'value': {
                                        'x': item_value['x'] * image_width / 100,
                                        'y': item_value['y'] * image_height / 100,
                                        'radius': item_value['width'] * image_width / 100
                                    }
                                })
                            except Exception as e:
                                print(e)
                                pass
            view_annotation_result_Instance_format = "\"{}\"".format(str(view_annotation_result_Instance))

        view_annotation_result_format_list = []
        for index, convertor in enumerate(convertor_list):
            if convertor == "Bounding-Box":
                view_annotation_result_format_list.append(str(locals()["view_annotation_result_{}_format".format("BoundingBox")]))
            else:
                view_annotation_result_format_list.append(str(locals()["view_annotation_result_{}_format".format(convertor)]))
        view_annotation_result_format = ",".join(view_annotation_result_format_list)

        return view_annotation_result_format
    
    def download_file_export_version_with_converter_Csv(self, user, project_id, params=None):
        convertor_list = params['convert_data']
        advanced_config_id = params.get('advancedConfigId', None)

        action = 'file'
        url = '{}/{}'.format(self.get_url(project=project_id), action)
        params_query = {
            'fileId': params['fileId'],
        }
        res = dms_requests.get(url, user=user, params=params_query)
        file = res.content
        status_code = res.status_code
        
        if 'default' in convertor_list:
            return status_code, file
        
        labels_flatten_list = []
        if 'Taxonomy' in convertor_list:
            status_code_prj, json_data_prj = DMS_PROJECT_API().detail(user, id=project_id)
            xml_config_string = json_data_prj["data"]["classConfig"]
            if xml_config_string != "<TORUS_TEMPLATE>\n</TORUS_TEMPLATE>":
                status_code_class_tree, json_data_class_tree = DMS_PROJECT_API().get_class_tree(user, project_id=project_id)
            else:
                status_code_class_tree, json_data_class_tree = DMS_PROJECT_API().get_class_tree_by_tool_config(user, project_id=project_id)
            print("++++++++++++++++++++++++++")
            for data in json_data_class_tree['data']:
                class_tree_data = data['value']
                self.flatten_taxonomy(class_tree_data, labels_flatten_list)

            print(labels_flatten_list)

        tags_list = []
        status_code_tags, json_data_tags = DMS_PROJECT_API().fetch_all_project_tag(user, project_id=project_id)
        if status_code_tags == 200:
            if json_data_tags['success'] and json_data_tags['data'] is not None:
                tags_list = json_data_tags['data']

        # print(file)
        content_result = ""
        content_string = file.decode("utf-8")

        file = io.StringIO(content_string)
        csv_data = csv.reader(file, delimiter=';')
        rows = []
        for row_item in csv_data:
            row = []
            for item in row_item:
                row.append(item)
            rows.append(row)
        
        # rows = content_string.split('\n')
        view_folder_index = 0
        view_annotation_result_index = 0
        file_name_index = 0
        discussions_index = 0
        is_crop_index = 0
        original_file_id_index = 0
        crop_info_index = 0

        # columns_name = rows[0].split(";")
        columns_name = rows[0]
        for c in columns_name:
            if c == "FileName":
                file_name_index = columns_name.index(c)
            if c == "view_folder":
                view_folder_index = columns_name.index(c)
            if c == "Discussions":
                discussions_index = columns_name.index(c)
            if c == "view_annotation_result":
                view_annotation_result_index = columns_name.index(c)
            if c == "FileId":
                file_id_index = columns_name.index(c)
            if c == "FilePath":
                file_path_storage_index = columns_name.index(c)
            if c == "IsCrop":
                is_crop_index = columns_name.index(c)
            if c == "OriginalFileId":
                original_file_id_index = columns_name.index(c)
            if c == "CropInfo":
                crop_info_index = columns_name.index(c)
            if c == "ProjectTags":
                tags_index = columns_name.index(c)

        if view_annotation_result_index == 0:
            return 400, {"success": False, "message": 'Please select "Include annotated data" while creating version!'}
        new_rows = []
        for row in rows:
            # cells_row = row.split(';')
            cells_row = row
            if len(cells_row) > max([file_name_index, view_folder_index, discussions_index, view_annotation_result_index]):
                print(rows.index(row))
                if rows.index(row) != 0:
                    uuid_file = cells_row[file_path_storage_index].split('/')[-1].split('_')[0]
                    crop_info_value = cells_row[crop_info_index].replace('\\\"', "'")
                    image_tags = cells_row[tags_index]
                    if image_tags != "":
                        image_tags_names = []
                        image_tags_list = image_tags.split(',')
                        for tag in image_tags_list:
                            for tag_item in tags_list:
                                if tag == tag_item['id']:
                                    image_tags_names.append(tag_item['tagName'])
                        image_tags = ",".join(image_tags_names)
                    try:
                        view_annotation_result_format = []

                        # view_annotation_result = cells_row[view_annotation_result_index][1:-1].replace('""', '"')
                        view_annotation_result = cells_row[view_annotation_result_index]
                        view_annotation_result = json.loads(view_annotation_result)

                        image_width = "Not found"
                        image_height = "Not found"
                        for annotation_result in view_annotation_result[0]['result']:
                            image_width = annotation_result.get('original_width', "Not found")
                            image_height = annotation_result.get('original_height', "Not found")
                            if image_width != "Not found" and image_height != "Not found":
                                break

                        view_annotation_result_format = self.annotation_result_convert_Csv(image_width, image_height, convertor_list, view_annotation_result, user, project_id, labels_flatten_list, advanced_config_id=advanced_config_id)
                        row_result = "{},{},{},{},{},{},{},{},{},{},{},{}".format(uuid_file, cells_row[file_id_index], cells_row[file_name_index], "\"{}{}\"".format(cells_row[view_folder_index], cells_row[file_name_index]), image_width, image_height, cells_row[is_crop_index], cells_row[original_file_id_index], crop_info_value, "\"{}\"".format(image_tags), "\"{}\"".format(cells_row[discussions_index]), "{}".format(str(view_annotation_result_format))) \
                            if discussions_index != 0 \
                            else "{},{},{},{},{},{},{},{},{},{},{}".format(uuid_file, cells_row[file_id_index], cells_row[file_name_index], "\"{}{}\"".format(cells_row[view_folder_index], cells_row[file_name_index]), image_width, image_height, cells_row[is_crop_index], cells_row[original_file_id_index], crop_info_value, "\"{}\"".format(image_tags), "{}".format(str(view_annotation_result_format)))
                    except Exception as e:
                        print(e)
                        empty_result = []
                        for convertor in convertor_list:
                            if convertor == "Taxonomy":
                                for label_flatten in labels_flatten_list:
                                    empty_result.append("None")
                            else:
                                empty_result.append("[]")
                        row_result = "{},{},{},{},{},{},{},{},{},{},{},{}".format(uuid_file, cells_row[file_id_index], cells_row[file_name_index], "\"{}{}\"".format(cells_row[view_folder_index], cells_row[file_name_index]), "Not found", "Not found", cells_row[is_crop_index], cells_row[original_file_id_index], crop_info_value, "\"{}\"".format(image_tags), "\"{}\"".format(cells_row[discussions_index]), ",".join(empty_result)) \
                            if discussions_index != 0 \
                            else "{},{},{},{},{},{},{},{},{},{},{}".format(uuid_file, cells_row[file_id_index], cells_row[file_name_index], "\"{}{}\"".format(cells_row[view_folder_index], cells_row[file_name_index]), "Not found", "Not found", cells_row[is_crop_index], cells_row[original_file_id_index], crop_info_value, "\"{}\"".format(image_tags), ",".join(empty_result))
                else:
                    # row_result = "{},{},{},{}".format("image_path_local", "image_width", "image_height", cells_row[view_annotation_result_index])

                    view_annotation_result_column = []
                    for convertor in convertor_list:
                        if convertor == "Taxonomy":
                            view_annotation_result_column.append(",".join(["\"{}\"".format(x) for x in labels_flatten_list]))
                        else:
                            view_annotation_result_column.append("view_annotation_result_{}".format(convertor))
                    row_result = "{},{},{},{},{},{},{},{},{},{},{},{}".format("uuid", "file_id", "file_name", "image_path_local", "image_width", "image_height", cells_row[is_crop_index], cells_row[original_file_id_index], cells_row[crop_info_index], "tags", "discussions", ",".join(view_annotation_result_column)) \
                        if discussions_index != 0 \
                        else "{},{},{},{},{},{},{},{},{},{},{}".format("uuid", "file_id", "file_name", "image_path_local", "image_width", "image_height", cells_row[is_crop_index], cells_row[original_file_id_index], cells_row[crop_info_index], "tags", ",".join(view_annotation_result_column))

                    if len(convertor_list) == 1:
                        if convertor_list == ["Taxonomy"]:
                            row_result = "{},{},{},{},{},{},{},{},{},{},{},{}".format("uuid", "file_id", "file_name", "image_path_local", "image_width", "image_height", cells_row[is_crop_index], cells_row[original_file_id_index], cells_row[crop_info_index], "tags", "discussions", ",".join(["\"{}\"".format(x) for x in labels_flatten_list])) \
                                if discussions_index != 0 \
                                else "{},{},{},{},{},{},{},{},{},{},{}".format("uuid", "file_id", "file_name", "image_path_local", "image_width", "image_height", cells_row[is_crop_index], cells_row[original_file_id_index], cells_row[crop_info_index], "tags", ",".join(["\"{}\"".format(x) for x in labels_flatten_list]))
                        else:
                            row_result = "{},{},{},{},{},{},{},{},{},{},{},{}".format("uuid", "file_id", "file_name", "image_path_local", "image_width", "image_height", cells_row[is_crop_index], cells_row[original_file_id_index], cells_row[crop_info_index], "tags", "discussions", f"view_annotation_result_{convertor_list[0]}") \
                                if discussions_index != 0 \
                                else "{},{},{},{},{},{},{},{},{},{},{}".format("uuid", "file_id", "file_name", "image_path_local", "image_width", "image_height", cells_row[is_crop_index], cells_row[original_file_id_index], cells_row[crop_info_index], "tags", f"view_annotation_result_{convertor_list[0]}")

                new_rows.append(row_result)
                #content_result = content_result + row_result + "\n"

        content_result = "\n".join(new_rows) + "\n"

        content_result_bytes = bytes(content_result, 'utf-8')

        return status_code, content_result_bytes
    
    def annotation_result_convert_Json(self, image_width, image_height, convertor_list, view_annotation_result, labels_flatten_list=None):
        view_annotation_result_BoundingBox = []
        view_annotation_result_Circle = []
        view_annotation_result_Polygon = []
        view_annotation_result_Scoring = []
        view_annotation_result_Brush = []
        view_annotation_result_Taxonomy = []
        view_annotation_result_Instance = []
        view_annotation_result_OneK = []
        
        if len(view_annotation_result) > 0:
            result_in_view_annotation_result = view_annotation_result[0]['result']
            for annotation_result in view_annotation_result[0]['result']:
                item_value = annotation_result.get('value', None)

                if annotation_result['type'] == 'rectanglelabels':
                    try:
                        item_annotation = {
                            'x': item_value['x'] * image_width / 100,
                            'y': item_value['y'] * image_height / 100,
                            'width': item_value['width'] * image_width / 100,
                            'height': item_value['height'] * image_height / 100,
                            'class_name': None
                        }
                    except:
                        item_annotation = {
                            'x': None,
                            'y': None,
                            'width': None,
                            'height': None,
                            'class_name': None
                        }
                    try:
                        item_annotation['class_name'] = item_value['rectanglelabels'][0]
                    except:
                        item_annotation['class_name'] = ''
                    meta_label = {}
                    for child_item in result_in_view_annotation_result:
                        if child_item['type'] == 'choices' and child_item['id'] == annotation_result['id']:
                            meta_label.update({child_item['from_name']: {"type": "choices", "value": child_item['value']['choices']}})
                        if child_item['type'] == 'textarea' and child_item['id'] == annotation_result['id']:
                            meta_label.update({child_item['from_name']: {"type": "textarea", "value": child_item['value']['text']}})
                        if child_item['type'] == 'number' and child_item['id'] == annotation_result['id']:
                            meta_label.update({child_item['from_name']: {"type": "number", "value": child_item['value']['number']}})
                    if meta_label != {}:
                        item_annotation['meta_label'] = meta_label

                    view_annotation_result_BoundingBox.append(item_annotation)

                if annotation_result['type'] == 'ellipselabels':
                    try:
                        item_annotation = {
                            'x': item_value['x'] * image_width / 100,
                            'y': item_value['y'] * image_height / 100,
                            'radius': (item_value['radiusX'] * image_width / 100 + item_value['radiusY'] * image_height / 100) / 2,
                            'class_name': None
                        }
                    except:
                        item_annotation = {
                            'x': None,
                            'y': None,
                            'radius': None,
                            'class_name': None
                        }
                    try:
                        item_annotation['class_name'] = item_value['ellipselabels'][0]
                    except:
                        item_annotation['class_name'] = ''
                    view_annotation_result_Circle.append(item_annotation)

                if annotation_result['type'] == 'polygonlabels':
                    try:
                        points_list_normalizing = item_value['points']
                        points_list = []
                        for point in points_list_normalizing:
                            x_point = point[0] * image_width / 100
                            y_point = point[1] * image_height / 100
                            points_list.append([x_point, y_point])
                        item_annotation = {
                            'points': points_list,
                            'class_name': None
                        }
                    except:
                        item_annotation = {
                            'points': None,
                            'class_name': None
                        }
                    try:
                        item_annotation['class_name'] = item_value['polygonlabels'][0]
                    except:
                        item_annotation['class_name'] = ''
                    meta_label = {}
                    for child_item in result_in_view_annotation_result:
                        if child_item['type'] == 'choices' and child_item['id'] == annotation_result['id']:
                            meta_label.update({child_item['from_name']: {"type": "choices", "value": child_item['value']['choices']}})
                        if child_item['type'] == 'textarea' and child_item['id'] == annotation_result['id']:
                            meta_label.update({child_item['from_name']: {"type": "textarea", "value": child_item['value']['text']}})
                        if child_item['type'] == 'number' and child_item['id'] == annotation_result['id']:
                            meta_label.update({child_item['from_name']: {"type": "number", "value": child_item['value']['number']}})
                    if meta_label != {}:
                        item_annotation['meta_label'] = meta_label
                    view_annotation_result_Polygon.append(item_annotation)

                if annotation_result['type'] == 'scoringlabels':
                    try:
                        scoring_points = item_value['scoringlabels'][0]
                        scoring_points = json.loads(scoring_points)
                        item_annotation = {
                            'score': [],
                            'size': scoring_points['size']
                        }
                        for row in scoring_points['values']:
                            row_ = []
                            for col in row:
                                row_.append(col['score'])
                            item_annotation['score'].append(row_)
                    except:
                        item_annotation = {
                            'score': None,
                            'size': None
                        }
                    view_annotation_result_Scoring.append(item_annotation)

                if annotation_result['type'] == "brushlabels":
                    try:
                        # mask = rle2mask(item_value['rle'],(image_height,image_width,1))[:,:,0]
                        # mask[mask>0] = 1
                        # lx, rx, hy, ly = get_foreground_bounds(mask)
                        # bbox = [lx, ly, rx-lx, hy-ly]
                        # seg = binary_mask_to_rle(mask)

                        item_annotation = {
                            'rle': item_value['rle'],
                            'class_name': None
                        }
                    except:
                        item_annotation = {
                            'rle': None,
                            'class_name': None
                        }
                    try:
                        item_annotation['class_name'] = item_value['brushlabels'][0]
                    except:
                        item_annotation['class_name'] = ''
                    meta_label = {}
                    for child_item in result_in_view_annotation_result:
                        if child_item['type'] == 'choices' and child_item['id'] == annotation_result['id']:
                            meta_label.update({child_item['from_name']: {"type": "choices", "value": child_item['value']['choices']}})
                        if child_item['type'] == 'textarea' and child_item['id'] == annotation_result['id']:
                            meta_label.update({child_item['from_name']: {"type": "textarea", "value": child_item['value']['text']}})
                        if child_item['type'] == 'number' and child_item['id'] == annotation_result['id']:
                            meta_label.update({child_item['from_name']: {"type": "number", "value": child_item['value']['number']}})
                    if meta_label != {}:
                        item_annotation['meta_label'] = meta_label
                    view_annotation_result_Brush.append(item_annotation)
                    
                if annotation_result['type'] == "taxonomy":
                    for label_flatten in labels_flatten_list:
                        if label_flatten in [label[-1] for label in item_value['taxonomy']]:
                            view_annotation_result_Taxonomy.append({label_flatten: 1})
                        else:
                            view_annotation_result_Taxonomy.append({label_flatten: 0})

            if 'Instance' in convertor_list:
                for item in view_annotation_result[0]['result']:
                    if item['type'] == 'instance':
                        view_annotation_result_Instance.append({
                            'instance_id': item['id'],
                            'instance_name': item['name'],
                            'components': []
                        })
                for item in view_annotation_result[0]['result']:
                    item_value = item.get('value', None)
                    if item['type'] == 'rectanglelabels':
                        for instance in view_annotation_result_Instance:
                            if instance['instance_id'] == item['parentID']:
                                try:
                                    instance['components'].append({
                                        'type': 'rectangle',
                                        'class_name': item_value['rectanglelabels'][0],
                                        'value': {
                                            'x': item_value['x'] * image_width / 100,
                                            'y': item_value['y'] * image_height / 100,
                                            'width': item_value['width'] * image_width / 100,
                                            'height': item_value['height'] * image_height / 100
                                        }
                                    })
                                except Exception as e:
                                    print(e)
                                    pass
                    if item['type'] == 'keypointlabels':
                        for instance in view_annotation_result_Instance:
                            if instance['instance_id'] == item['parentID']:
                                try:
                                    instance['components'].append({
                                        'type': 'keypoint',
                                        'class_name': item_value['keypointlabels'][0],
                                        'value': {
                                            'x': item_value['x'] * image_width / 100,
                                            'y': item_value['y'] * image_height / 100,
                                            'radius': item_value['width'] * image_width / 100
                                        }
                                    })
                                except Exception as e:
                                    print(e)
                                    pass
        item_annotation_format = {}
        for convertor in convertor_list:
            if convertor == "Bounding-Box":
                item_annotation_format.update({"view_annotation_result_{}".format(convertor): locals()["view_annotation_result_{}".format("BoundingBox")]})
            else:
                item_annotation_format.update({"view_annotation_result_{}".format(convertor): locals()["view_annotation_result_{}".format(convertor)]})

        return item_annotation_format

    def download_file_export_version_with_converter_Json(self, user, project_id, params=None):
        action = 'file'
        url = '{}/{}'.format(self.get_url(project=project_id), action)
        params_query = {
            'fileId': params['fileId'],
        }
        res = dms_requests.get(url, user=user, params=params_query)
        file = res.content
        status_code = res.status_code

        if 'default' in params['convert_data']:
            return status_code, file

        file_json = file.decode('utf-8')
        data_content = json.loads(file_json)

        formated_data_content = []

        convertor_list = params['convert_data']
        labels_flatten_list = []
        if 'Taxonomy' in convertor_list:
            status_code_prj, json_data_prj = DMS_PROJECT_API().detail(user, id=project_id)
            xml_config_string = json_data_prj["data"]["classConfig"]
            if xml_config_string != "<TORUS_TEMPLATE>\n</TORUS_TEMPLATE>":
                status_code_class_tree, json_data_class_tree = DMS_PROJECT_API().get_class_tree(user, project_id=project_id)
            else:
                status_code_class_tree, json_data_class_tree = DMS_PROJECT_API().get_class_tree_by_tool_config(user, project_id=project_id)
            print("++++++++++++++++++++++++++")
            for data in json_data_class_tree['data']:
                class_tree_data = data['value']
                self.flatten_taxonomy(class_tree_data, labels_flatten_list)

            print(labels_flatten_list)

        for item in data_content:
            view_folder = ''
            view_annotation_result = ''

            image_width = "Not found"
            image_height = "Not found"

            for attribute in item['Attributes']:
                if attribute['Code'] == 'view_folder':
                    view_folder = attribute['Value']
                    break
            
            for attribute in item['Attributes']:
                if attribute['Code'] == 'view_annotation_result':
                    view_annotation_result = attribute['Value']
                    view_annotation_result = json.loads(view_annotation_result) if view_annotation_result not in ['', None] else []
                    break
            
            try:
                for annotation_result in view_annotation_result[0]['result']:
                    image_width = annotation_result.get('original_width', "Not found")
                    image_height = annotation_result.get('original_height', "Not found")
                    if image_width != "Not found" and image_height != "Not found":
                        break
            except:
                pass

            item_format = {
                'image_path_local': view_folder + item['FileName'],
                'image_width': image_width,
                'image_height': image_height,
                'discussions': item.get('Discussions', []),
            }

            item_format.update(self.annotation_result_convert_Json(image_width, image_height, convertor_list, view_annotation_result, labels_flatten_list))

            formated_data_content.append(item_format)

        formated_data_content = json.dumps(formated_data_content, indent=4)
        
        formated_data_content_bytes = bytes(formated_data_content, 'utf-8')
        return status_code, formated_data_content_bytes
    
    def download_file_export_version_with_converter(self, user, project_id, params=None):
        export_type = params['exportType']
        func = getattr(self, 'download_file_export_version_with_converter_{}'.format(export_type))

        return func(user, project_id, params=params)
    
    def trigger_convert_coco_from_Csv(self, user, project_id, params=None):
        patch_exversion_url = "{}/converter/{}".format(self.get_url(project=project_id), params['revisionId'])
        patch_exversion_res = dms_requests.patch(patch_exversion_url, 
                                                    user=user, 
                                                    data=json.dumps({
                                                        "converterStatus": "Queued"}))

        trigger_convert_coco_from_Csv_bgr_job.delay(user.dms_id, 
                                                    project_id, 
                                                    self.TORUS_INFOR_COCO, 
                                                    self.get_url(project=project_id), params)
        
        return 200, {"success": True, "message": "Convert to coco format in progress..."}
    
    def trigger_convert_coco_from_Json(self, user, project_id, params=None):
        patch_exversion_url = "{}/converter/{}".format(self.get_url(project=project_id), params['revisionId'])
        patch_exversion_res = dms_requests.patch(patch_exversion_url, 
                                                    user=user, 
                                                    data=json.dumps({
                                                        "converterStatus": "Queued"}))

        trigger_convert_coco_from_Json_bgr_job.delay(user.dms_id, 
                                                     project_id, 
                                                     self.TORUS_INFOR_COCO, 
                                                     self.get_url(project=project_id), params)

        return 200, {"success": True, "message": "Convert to coco format in progress..."}

    def convert_to_coco_format(self, user, project_id, params=None):
        export_type = params['exportType']
        func = getattr(self, 'trigger_convert_coco_from_{}'.format(export_type))

        return func(user, project_id, params=params)


    def convert_to_yolo_format(self, user, project_id, params=None):
        export_type = params['exportType']
        func = getattr(self, 'trigger_convert_yolo_from_{}'.format(export_type))

        return func(user, project_id, params=params)
    
    def trigger_convert_yolo_from_Json(self, user, project_id, params=None):
        patch_exversion_url = "{}/converter/{}".format(self.get_url(project=project_id), params['revisionId'])
        patch_exversion_res = dms_requests.patch(patch_exversion_url, 
                                                    user=user, 
                                                    data=json.dumps({
                                                        "yoloStatus": "Queued"}))

        trigger_convert_yolo_from_Json_bgr_job.delay(user.dms_id, 
                                                     project_id, 
                                                     self.TORUS_INFOR_COCO, 
                                                     self.get_url(project=project_id), params)

        return 200, {"success": True, "message": "Convert to coco format in progress..."}
    
    def trigger_convert_yolo_from_Csv(self, user, project_id, params=None):
        patch_exversion_url = "{}/converter/{}".format(self.get_url(project=project_id), params['revisionId'])
        patch_exversion_res = dms_requests.patch(patch_exversion_url, 
                                                    user=user, 
                                                    data=json.dumps({
                                                        "yoloStatus": "Queued"}))

        trigger_convert_yolo_from_Csv_bgr_job.delay(user.dms_id, 
                                                     project_id, 
                                                     self.TORUS_INFOR_COCO, 
                                                     self.get_url(project=project_id), params)

        return 200, {"success": True, "message": "Convert to coco format in progress..."}
    
    def sync_to_clearml(self, user, project_id, params=None):
        url = "{}/synchronizetoclearml".format(self.get_url(project=project_id))
        res = dms_requests.post(url, user=user, data=json.dumps(params['revisionId']))
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data
    
    def create_event_ml_export(self, user, project_id, data=None):
        url = "{}/eventmlexport".format(self.get_url(project=project_id))
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data
    
    def update_status_event_ml_export(self, user, project_id, event_export_id, data=None):
        url = "{}/updatestatus/{}".format(self.get_url(project=project_id), event_export_id)
        res = dms_requests.patch(url, user=user, data=json.dumps(data))
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data