import json
import requests
import urllib.parse

from dms_connector.modules.base import DMS_API
from dms_connector import dms_requests
from urllib.parse import urlencode

class DMS_TEMP_STORAGE_API(DMS_API):
    API_NAME = 'projects'
    API_SUB_NAME = 'tempstoragebatchfiles'

    def put(self, user, data, project=None):
        url = '{}'.format(self.get_url(project=project))
        res = dms_requests.put(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_image(self, user, project_id, temp_storage_file_id, params):
        action = 'image'
        url = '{}/{}/{}'.format(self.get_url(project=project_id), temp_storage_file_id, action)
        res = dms_requests.get(url, user=user, params=params)
        img = res.content
        status_code = res.status_code

        return status_code, img
    
    def fetch_images(self, user, project_id, temp_storage_id, params):
        def convert_to_string(key, value):
            if key == "workflowStepIds" or key == "stepStatus":
                value_arr = value[0].split(',')
                return '&'.join([f"{key}={i}" for i in value_arr])

            return f"{key}={value}"

        param_arr = [convert_to_string(key=key, value=value) if value else None for key, value in params.items()]
        param_string = '&'.join([i for i in param_arr if i is not None])

        url = '{}/{}/{}?{}'.format(self.get_url(project=project_id), 'tempstoragefiles', temp_storage_id, param_string)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def fetch_file_detail(self, user, project_id, temp_storage_id, params):
        url = '{}/{}/{}'.format(self.get_url(project=project_id), temp_storage_id, 'detail')
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def generate_dataset_item(self, file_url, file, temp_batch_id):
        file_name = file.name.split("/")[-1]
        file_ext = file_name.split('.')[-1]

        return [
            {
                "fileName": file_name,
                "workflowStep": "New",
                "workflowStepId": "00000000-0000-0000-0000-000000000001",
                "workflowState": "PreProcessingNew",
                "stepStatus": "Ready",
                "isCompleted": False,
                "transferStatus": "ReadyTransfer",
                "tempStorageBatchFileId": temp_batch_id,
                "dataType": file_ext,
                "label": file_name,
                "fileUrl": file_url,
                "filePath": file_url,
                "fileSize": str(file.size),
            }
        ]
    
    def generate_dataset_item_ready_to_work(self, file_url, file, temp_batch_id, 
                                            next_workflow_step=None, 
                                            next_workflow_step_id=None, 
                                            next_workflow_state=None):
        file_name = file.name.split("/")[-1]
        file_ext = file_name.split('.')[-1]

        return [
            {
                "fileName": file_name,
                "workflowStep": next_workflow_step,
                "workflowStepId": next_workflow_step_id,
                "workflowState": next_workflow_state,
                "stepStatus": "Ready",
                "isCompleted": False,
                "transferStatus": "ReadyTransfer",
                "tempStorageBatchFileId": temp_batch_id,
                "dataType": file_ext,
                "label": file_name,
                "fileUrl": file_url,
                "filePath": file_url,
                "fileSize": str(file.size),
            }
        ]
    
    def generate_presigned_upload_url(self, user, project, file):
        action = 'presigned-url'
        file_name = file.name.split("/")[-1]
        file_name = urllib.parse.quote(file_name)
        url = '{}/{}?fileName={}'.format(self.get_url(project=project), action, file_name)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def upload_file(self, user, project, file, temp_batch_id, is_preReadyToWork=False,
                    next_workflow_step=None, next_workflow_step_id=None, next_workflow_state=None):
        status_code, upload_info = self.generate_presigned_upload_url(user, project, file)
        upload_url = upload_info['data']['preSignedUrl']
        upload_file_url = upload_info['data']['fileUrl']
        storage_id = upload_info['data']['storageId']
        res = requests.put(upload_url, data=file, headers={'Content-Type': 'application/octet-stream'})
        if is_preReadyToWork:
            return res.status_code, self.generate_dataset_item_ready_to_work(upload_file_url, file, temp_batch_id, 
                                                                           next_workflow_step=next_workflow_step, 
                                                                           next_workflow_step_id=next_workflow_step_id, 
                                                                           next_workflow_state=next_workflow_state)
        else:
            return res.status_code, self.generate_dataset_item(upload_file_url, file, temp_batch_id)

    def upload(self, user, file, project, data=None):
        batch_id = data.get('tempStorageBatchFileId', None)
        is_preReadyToWork = data.get('preReadyToWorkProject', False)

        next_workflow_step = data.get('nextWorkFlowStep', None)
        next_workflow_step_id = data.get('nextWorkFlowStepId', None)
        next_workflow_state = data.get('nextWorkFlowState', None)

        if is_preReadyToWork:
            status_code, dataset_item = self.upload_file(user, project, file, temp_batch_id=batch_id,
                                                         is_preReadyToWork=is_preReadyToWork, 
                                                         next_workflow_step=next_workflow_step, 
                                                         next_workflow_step_id=next_workflow_step_id, 
                                                         next_workflow_state=next_workflow_state)
        else:
            status_code, dataset_item = self.upload_file(user, project, file, temp_batch_id=batch_id)
        if status_code != 200:
            return status_code, {'message': 'Upload file to storage failed'}

        if status_code == 200:
            url = "{}/{}".format(self.get_url(project=project), "createmutiplefiles")
            res = dms_requests.post(url, user=user, data=json.dumps(dataset_item))
            json_data = res.json()
            status_code = res.status_code

            return status_code, json_data

        return status_code, None
    
    def transfer_data_prj(self, user, project_id, storage_batch_id):
        url = '{}/{}/transfer'.format(self.get_url(project=project_id), storage_batch_id)
        res = dms_requests.post(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def transfer_data_prj_multiple_files(self, user, project_id, storage_batch_id, data):
        url = '{}/{}/transferfiles'.format(self.get_url(project=project_id), storage_batch_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def transfer_data_prj_single_file(self, user, project_id, storage_batch_id, data):
        url = '{}/{}/transferfile'.format(self.get_url(project=project_id), storage_batch_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def delete_batch_images(self, user, project_id, temp_storage_id):
        url = '{}/{}/{}'.format(self.get_url(project=project_id), 'tempstoragefiles', temp_storage_id)
        res = dms_requests.delete(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_multiple_batch_images(self, user, project_id, data):
        url = '{}/{}/{}'.format(self.get_url(project=project_id), 'tempstoragefiles', 'deletemutiplebatches')
        res = dms_requests.delete(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_batch_multiple_images(self, user, project_id, temp_storage_id, data):
        url = '{}/{}/{}/deletemutiplefiles'.format(self.get_url(project=project_id), 'tempstoragefiles', temp_storage_id)
        res = dms_requests.delete(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def update_multiple_files(self, user, project_id, temp_storage_id, data):
        url = '{}/{}/{}'.format(self.get_url(project=project_id), 'tempstoragefiles', temp_storage_id)
        res = dms_requests.patch(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def update_single_file(self, user, project_id, temp_storage_id, data):
        url = '{}/{}/{}/single'.format(self.get_url(project=project_id), 'tempstoragefiles', temp_storage_id)
        res = dms_requests.patch(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def confirm_cluster(self, user, project_id, temp_storage_id, cluster_id, data):
        url = '{}/{}/projectfilecluster/{}'.format(self.get_url(project=project_id), temp_storage_id, cluster_id)
        res = dms_requests.patch(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_detail_batch(self, user, project_id, batch_id, params=None):
        url = '{}/{}'.format(self.get_url(project=project_id), batch_id)
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def edit_detail_batch(self, user, project_id, batch_id, data):
        url = '{}/{}'.format(self.get_url(project=project_id), batch_id)
        res = dms_requests.patch(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
class DMS_TEMP_STORAGE_DEDUP_API(DMS_API):
    API_NAME = 'projects'

    def get_dedup_report(self, user, project_id, batch_id, params):
        url = '{}/{}/batchfiles/{}/deduplicatereports'.format(self.get_url(), project_id, batch_id)
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def post_dedup_report(self, user, project_id, batch_id, data):
        url = '{}/{}/batchfiles/{}/deduplicatereports'.format(self.get_url(), project_id, batch_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        print(res)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def add_clusters_dedup_report(self, user, project_id, batch_id, data):
        url = '{}/{}/batchfiles/{}/deduplicatereports/addclusters'.format(self.get_url(), project_id, batch_id)
        res = dms_requests.patch(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def cancelling_clusters_dedup_report(self, user, project_id, batch_id, data):
        url = '{}/{}/batchfiles/{}/deduplicatereports/cancellingdeduplicatereport'.format(self.get_url(), project_id, batch_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def edit_file_threshold_report(self, user, project_id, cluster_id, batch_id, file_threshold_id, data):
        url = '{}/{}/batchfiles/{}/deduplicatereports/{}/filethresholds/{}'.format(self.get_url(), project_id, batch_id, cluster_id, file_threshold_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def fetch_files_in_cluster(self, user, project_id, batch_id, cluster_id, params):
        url = '{}/{}/batchfiles/{}/deduplicatereports/getprojectcluster/{}'.format(self.get_url(), project_id, batch_id, cluster_id)
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def remove_files_cluster_all(self, user, project_id, batch_id, cluster_id):
        url = '{}/{}/batchfiles/{}/deduplicatereports/{}/removeall'.format(self.get_url(), project_id, batch_id, cluster_id)
        res = dms_requests.delete(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def remove_files_cluster(self, user, project_id, batch_id, cluster_id, data):
        url = '{}/{}/batchfiles/{}/deduplicatereports/{}/remove'.format(self.get_url(), project_id, batch_id, cluster_id)
        res = dms_requests.delete(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_files_cluster_all(self, user, project_id, batch_id, cluster_id):
        url = '{}/{}/batchfiles/{}/deduplicatereports/{}/deleteall'.format(self.get_url(), project_id, batch_id, cluster_id)
        res = dms_requests.delete(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_files_cluster(self, user, project_id, batch_id, cluster_id, data):
        url = '{}/{}/batchfiles/{}/deduplicatereports/{}/deletethresholdfiles'.format(self.get_url(), project_id, batch_id, cluster_id)
        res = dms_requests.delete(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def complete_dedup_report(self, user, project_id, batch_id, data):
        url = '{}/{}/batchfiles/{}/deduplicatereports/complete'.format(self.get_url(), project_id, batch_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
class DMS_TEMP_STORAGE_APPLY_SYSTEM_MODEL_API(DMS_API):
    API_NAME = 'projects'

    def post_apply_system_model(self, user, project_id, workflowStep_id, data):
        url = '{}/{}/projectsystemmodels/applytoworkflow/{}'.format(self.get_url(), project_id, workflowStep_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_apply_system_model(self, user, project_id, workflowStep_id, params):
        url = '{}/{}/projectsystemmodels/applytoworkflow/{}'.format(self.get_url(), project_id, workflowStep_id)
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
class DMS_TEMP_STORAGE_REASONS_API(DMS_API):
    API_NAME = 'projects'
    API_SUB_NAME = 'tempstoragereasons'

    def fetch_reasons(self, user, project_id, temp_storage_file_id, params):
        url = '{}/{}'.format(self.get_url(project=project_id), temp_storage_file_id)
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def create_reason(self, user, project_id, temp_storage_file_id, data):
        url = '{}/{}/{}'.format(self.get_url(project=project_id), temp_storage_file_id, 'reason')
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def update_reason(self, user, project_id, temp_storage_file_id, reason_id, data):
        url = '{}/{}/{}/{}'.format(self.get_url(project=project_id), temp_storage_file_id, 'reason', reason_id)
        res = dms_requests.put(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_reason(self, user, project_id, temp_storage_file_id, reason_id):
        url = '{}/{}/{}/{}'.format(self.get_url(project=project_id), temp_storage_file_id, 'reason', reason_id)
        res = dms_requests.delete(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data