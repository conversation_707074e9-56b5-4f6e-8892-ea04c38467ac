import json
from dms_connector.modules.base import DMS_API
from dms_connector import dms_requests
from django.conf import settings

class DMS_ORGANIZATION_API(DMS_API):
    API_NAME = 'Organizations'

    def put(self, user, id, data):
        url = self.get_url()
        data = {
            'organizationId': str(id),
            **data
        }
        res = dms_requests.put(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    # def patch(self, user, id, data):
    #     url = '{}/{}'.format(self.get_url(), id)
    #     res = dms_requests.patch(url, user=user, data=json.dumps(data))
    #     json_data = res.json()
    #     status_code = res.status_code

    #     return status_code, json_data

    def patch(self, user, id, data):
        url = self.get_url()
        params = {
            'organizationId': str(id)
        }
        data = {
            **data
        }
        res = dms_requests.patch(url, user=user, data=json.dumps(data), params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_default_organization(self, user, params=None):
        url = "{}/defaultoganization".format(self.get_url())

        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_default_public_workspace(self):
        url = "{}/defaultpublicworkspace".format(self.get_url())

        res = dms_requests.get(url)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
class DMS_ORGANIZATION_SSO_CONFIG_API(DMS_API):
    API_NAME = 'Organizations'
    API_SUB_NAME = 'ssoconfigurations'

    def get_url(self, organization_id=None):
        DMS_API_URL = f'{self.DMS_HOST}/api/v{self.DMS_API_VERSION}'
        if self.API_SUB_NAME:
            if organization_id is not None:
                return '{}/{}/{}/{}'.format(DMS_API_URL, self.API_NAME, organization_id, self.API_SUB_NAME)
            else:
                return '{}/{}/{}'.format(DMS_API_URL, self.API_NAME, self.API_SUB_NAME)
        
        return '{}/{}'.format(DMS_API_URL, self.API_NAME)
    
    def list(self, user, params=None):
        url = self.get_url()

        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete(self, user, id, organization_id):
        url = '{}/{}'.format(self.get_url(organization_id=organization_id), id)

        res = dms_requests.delete(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def put(self, user, id, data, organization_id):
        url = '{}/{}'.format(self.get_url(organization_id=organization_id), id)
        res = dms_requests.put(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def detail(self, user, id, organization_id):
        url = '{}/{}'.format(self.get_url(organization_id=organization_id), id)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def create(self, user, data, organization_id):
        url = self.get_url(organization_id=organization_id)

        params = {}
        res = dms_requests.post(url, user=user, params=params, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
class DMS_ORGANIZATION_SSO_DEFAULT_API(DMS_API):
    API_NAME = 'Organizations'

    def get_default_sso(self, params=None):
        url = "{}/defaultsso".format(self.get_url())

        res = dms_requests.get(url, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def create_default_sso(self, user, data):
        url = "{}/createdefaultsso".format(self.get_url())

        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_default_azure_sso_endpoint(self, params=None):
        url = "{}/defaultsso".format(self.get_url())

        tenant_id = ""
        client_id = ""
        redirect_uri = ""
        tas_host = settings.TAS_HOST

        res = dms_requests.get(url, params=params)
        json_data = res.json()
        status_code = res.status_code

        if status_code == 200:
            if json_data['success']:
                azure_config = json_data['data']['azureConfiguration']
                tenant_id = azure_config['tenantId']
                client_id = azure_config['clientId']

        azure_sso_endpoint = f"https://login.microsoftonline.com/{tenant_id}/oauth2/v2.0/authorize?client_id={client_id}&response_type=token&redirect_uri={tas_host}/login/ssoAzure&scope=openid&response_mode=fragment&state=12345&nonce=678910"

        return 200, azure_sso_endpoint