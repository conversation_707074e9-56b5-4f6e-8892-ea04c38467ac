import json, openai, csv, io, uuid
from dms_connector.modules.base import DMS_API
from dms_connector.modules.storage import DMS_STORAGE_API
from dms_connector import dms_requests
from django.conf import settings

from data_tabular.utils import PdfFileHandler
from label_studio.data_tabular.utils_v2 import render_highlight_page

class DMS_TABULAR_DATA_API(DMS_API):
    API_NAME = "projects"
    API_SUB_NAME = "tabularcategories"

    def get_list_categories_in_tabular(self, user, project_id, params=None):
        url = "{}".format(self.get_url(project=project_id))
        res = dms_requests.get(url=url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_infor_details_categories(self, user, project_id, tabular_category_id):
        url = "{}/details/{}".format(self.get_url(project=project_id), tabular_category_id)
        res = dms_requests.get(url=url, user=user)
        json_data = res.json()
        status_code = res.status_code
        if status_code == 200:
            if json_data["success"]:
                data_list = json_data.get("data", [])
                if data_list is not None:
                    data_freetext_list = [item for item in data_list if item["dataType"] == "Freetext"]
                    data_dropdown_list = [item for item in data_list if item["dataType"] == "DropDown"]

                    data_freetext_list.sort(key=lambda x: x["key"])
                    data_dropdown_list.sort(key=lambda x: x["key"])

                    # Sort by alphabetical order
                    json_data["data"] = data_dropdown_list + data_freetext_list

        return status_code, json_data
    
    def update_infor_details_categories(self, user, project_id, tabular_category_id, data):
        url = "{}/update/{}".format(self.get_url(project=project_id), tabular_category_id)
        res = dms_requests.put(url=url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_available_value_key(self, user, project_id, params=None):
        url = "{}/dictionary".format(self.get_url(project=project_id))
        res = dms_requests.get(url=url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_tabular_generated_description(
        self,
        disease_name,
        provoke,
    ):
        client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)

        if not provoke:
            return None

        description = client.chat.completions.create(
            model="gpt-4o",
            temperature=0.0,
            messages=[
                {
                    "role": "system",
                    "content": f"""MUST use the information there to answer question: {provoke}.


                    MUST answer following the term in provoke in order to doctor can understand and make diagnosis, try to make it distinguish with other disease, and bold the word taken from the provoke, all the categories in provoke happen same time (AND): {provoke}""",
                },
                {
                    "role": "user",
                    "content": f"""Explain very details about the characteristic of the disease {disease_name}. 
                    Need to give all possible important_characteristics so a doctor can see and make a diagnosis. Try to only tell what it look likes only, bold the word in provoke, and make it distingishable with other skin disease.""",
                },
            ],
        )

        description = description.choices[0].message.content
        
        return 200, description or ""
    
    def get_list_category_histories_in_tabular(self, user, project_id, tabular_category_id):
        url = "{}/{}/versionhistory".format(self.get_url(project=project_id), tabular_category_id)
        res = dms_requests.get(url=url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_detail_category_histories_in_tabular(self, user, project_id, version_history_id):
        url = "{}/{}/versionhistorydetail".format(self.get_url(project=project_id), version_history_id)
        res = dms_requests.get(url=url, user=user)
        json_data = res.json()
        status_code = res.status_code

        if status_code == 200:
            if json_data["success"]:
                data_list = json_data["data"]["tabularCategoryHistoryDetailItems"]
                if data_list is not None:
                    data_freetext_list = [item for item in data_list if item["dataType"] == "Freetext"]
                    data_dropdown_list = [item for item in data_list if item["dataType"] == "DropDown"]

                    data_freetext_list.sort(key=lambda x: x["key"])
                    data_dropdown_list.sort(key=lambda x: x["key"])

                    # Sort by alphabetical order
                    json_data["data"]["tabularCategoryHistoryDetailItems"] = data_dropdown_list + data_freetext_list

        return status_code, json_data

    def restore_category_histories_in_tabular(self, user, project_id, tabular_category_id, version_history_id):
        url = "{}/{}/restore/{}".format(self.get_url(project=project_id), tabular_category_id, version_history_id)
        res = dms_requests.post(url=url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def export_infor_details_categories(self, user, project_id, params=None):
        url = "{}/exportdata".format(self.get_url(project=project_id))
        res = dms_requests.get(url=url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def export_infor_details_categories_to_csv(self, user, project_id):
        status_code, json_data = self.export_infor_details_categories(user=user, project_id=project_id,
                                                                      params={'page': 1,
                                                                              'page_size': 1})
        datas = []
        fieldnames = []

        if status_code == 200:
            # Get the field names from the first page
            fieldnames.append("object_name")
            categories_header_list = json_data['items'] if json_data['items'] is not None else []
            if len(categories_header_list) > 0:
                attributes = categories_header_list[0].get("attributes", [])
                for attr in attributes:
                    key = attr["key"]
                    if key and key not in fieldnames:
                        fieldnames.append(key)

            # Get data
            total_items = json_data.get('total', 0)
            page_number = int(total_items / 100) + 1
            for page in range(1, page_number + 1):
                status_code_with_page, json_data_with_page = self.export_infor_details_categories(user=user,
                                                                                                  project_id=project_id,
                                                                                                  params={'page': page,
                                                                                                          'page_size': 100})

                if status_code_with_page == 200:
                    categories_list = json_data_with_page.get('items', [])
                    if categories_list is None:
                        categories_list = []
                    for category in categories_list:
                        data = {}
                        data["object_name"] = category.get("categoryName", "")
                        attributes = category.get("attributes", [])
                        for attr in attributes:
                            key = attr.get("key", "")
                            value = attr.get("value", "")
                            if key and value:
                                data[key] = value
                        datas.append(data)

        # Create CSV file as binary
        binary_file = io.BytesIO()

        text_wrapper = io.TextIOWrapper(binary_file, encoding='utf-8', newline='')

        # Create CSV writer
        print("fieldnames", fieldnames)
        csv_writer = csv.DictWriter(text_wrapper, fieldnames=fieldnames, quoting=csv.QUOTE_MINIMAL)
        csv_writer.writeheader()
        csv_writer.writerows(datas)
        text_wrapper.flush()

        binary_file.seek(0)

        binary_csv_data = binary_file.getvalue()

        return status_code, binary_csv_data
    
    def highlight_infor_details_categories(self, user, project_id, data):
        relative_path=f"assets/oneKDocument/{data.get('file_url', '')}"
        page=data.get("page", 0)
        quote=data.get("quote", "")
        out_url=f"assets/oneKDocument/highlight/{data.get('file_url', '').split('.')[0]}_{str(uuid.uuid4())}_highlight.pdf"
        chunk=data.get("chunk", "")

        # main_page= PdfFileHandler().highlight_text(
        #     relative_path=relative_path,
        #     page=page,
        #     quote=quote,
        #     out_url=out_url,
        #     chunk=chunk
        # )

        main_page = render_highlight_page(
            relative_path=relative_path,
            output_path=None,
            output_url=out_url,
            page=page,
            quote=quote
        )

        status_code_storage, json_data_storage = DMS_STORAGE_API().list(user)
        storage_endpoints = None
        for item in json_data_storage['items']:
            if item['isPrimary']:
                storage_endpoints = "{}/{}/".format(settings.MINIO_HOST, item['configValue']['bucketName'])

        result = {
            "out_url": f"{storage_endpoints}{out_url}",
            "main_page": main_page
        }

        return 200, result