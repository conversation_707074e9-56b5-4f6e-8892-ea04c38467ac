import json
import requests
import jwt
from dateutil import parser

from django.conf import settings
from django.contrib import auth

from dms_connector.modules.base import DMS_API
from dms_connector import dms_requests

from users.models import User
from organizations.models import Organization

class DMS_AUTH_API(DMS_API):
    API_NAME = 'auth'

    def get_user_permissions(self, user=None, token=None):
        # if user:
        #     token = user.dms_auth.token

        # json_data = jwt.decode(token, options={"verify_signature": False})
        # for key, value in json_data.items():
        #     json_user = json.loads(value)
        #     return json_user['Permissions']
        action = 'my-permission'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code
        if status_code==200 and json_data['success']==True:
            return json_data['data']
        else:
            return []
        
    def get_user_policymodulepermission(self, user=None, token=None):
        action = 'my-permission'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code
        if status_code==200 and json_data['success']==True:
            return json_data['data']['item2']
        else:
            return []
        
    def get_user_policyprojectpermissions(self, user=None, token=None):
        action = 'my-permission'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code
        if status_code==200 and json_data['success']==True:
            return json_data['data']['item3']
        else:
            return []

    def refresh_token(self, user):
        action = 'refresh'
        url = '{}/{}'.format(self.get_url(), action)
        payload = {
            "accessToken": user.dms_auth.token,
            "refreshToken": user.dms_auth.refresh_token,
        }
        res = requests.post(url, data=json.dumps(payload))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def login(self, data):
        action = 'login'
        url = '{}/{}'.format(self.get_url(), action)
        payload = {
            **data,
            "loginType": "Password",
            "application": settings.APPLICATION_TYPE
        }
        res = dms_requests.post(url, data=json.dumps(payload))
        json_data = res.json()

        status_code = res.status_code

        dms_user = json_data.get('data')
        user = None
        if dms_user:
            token = dms_user['token']
            email = dms_user['email']
            username = data['userName']
            password = data['password']

            user = auth.authenticate(username=username, password=password)

            if user is None:
                status_code, dms_user_profile = DMS_AUTH_API().get_profile(token=token)
                dms_id = dms_user_profile['id']

                if email is None:
                    if User.objects.filter(email='').exists():
                        user = User.objects.get(email='')
                        user.set_password(password)
                        user.save()
                else:
                    if User.objects.filter(email=email).exists():
                        user = User.objects.get(email=email)
                        user.delete()
                    user = User.objects.create_user(username, password, email, dms_id=dms_id)

                if Organization.objects.exists():
                    org = Organization.objects.first()
                    org.add_user(user)
                else:
                    org = Organization.create_organization(created_by=user, title='Torus AI')

                user.active_organization = org
                user.save(update_fields=['active_organization'])

            user.dms_auth.token = token
            user.dms_auth.expiration = parser.parse(dms_user['expiration'])
            user.dms_auth.refresh_token = dms_user['refreshToken']
            user.dms_auth.refresh_token_expiration = parser.parse(dms_user['refreshTokenExpiration'])
            user.dms_auth.save()

            # if user and user.is_active:
            #     return {'user': user}

        return status_code, json_data, user
    
    def register(self, data):
        action = 'register'
        url = '{}/{}'.format(self.get_url(), action)

        res = dms_requests.post(url, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def validate_account_register(self, data):
        action = 'validateaccount'
        url = '{}/{}'.format(self.get_url(), action)

        res = dms_requests.post(url, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def login_sso(self, data):
        action = 'loginsso'
        url = "{}/{}".format(self.get_url(), action)

        res = dms_requests.post(url, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        dms_user = json_data.get('data')
        user = None
        if dms_user:
            token = dms_user['token']
            email = dms_user['email']

            status_code, dms_user_profile = DMS_AUTH_API().get_profile(token=token)
            username = dms_user_profile['userName'] if dms_user_profile['userName'] is not None else dms_user_profile['email']
            password = dms_user_profile['id']

            # user = auth.authenticate(username=username, password=password)
            user = User.objects.filter(username=username).first()

            if user is None:
                dms_id = dms_user_profile['id']

                if email is None:
                    if User.objects.filter(email='').exists():
                        user = User.objects.get(email='')
                        user.set_password(password)
                        user.save()
                else:
                    if User.objects.filter(email=email).exists():
                        user = User.objects.get(email=email)
                        user.delete()
                    user = User.objects.create_user(username, password, email, dms_id=dms_id)

                if Organization.objects.exists():
                    org = Organization.objects.first()
                    org.add_user(user)
                else:
                    org = Organization.create_organization(created_by=user, title='Torus AI')

                user.active_organization = org
                user.save(update_fields=['active_organization'])

            user.dms_auth.token = token
            user.dms_auth.expiration = parser.parse(dms_user['expiration'])
            user.dms_auth.refresh_token = dms_user['refreshToken']
            user.dms_auth.refresh_token_expiration = parser.parse(dms_user['refreshTokenExpiration'])
            user.dms_auth.save()

            # if user and user.is_active:
            #     return {'user': user}

        return status_code, json_data, user

    def get_profile(self, user=None, token=None):
        action = 'my-profile'

        res = dms_requests.get('{}/{}'.format(self.get_url(), action), user=user, token=token)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data.get('data')

    def update_profile(self, user, data):
        action = 'my-profile'

        res = dms_requests.put('{}/{}'.format(self.get_url(), action),
                               data=json.dumps(data),
                               user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def get_permission(self, user=None, token=None):
        action = 'my-permission'

        res = dms_requests.get('{}/{}'.format(self.get_url(), action), user=user, token=token)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def change_password(self, user, data):
        action = 'change-password'

        res = dms_requests.put('{}/{}'.format(self.get_url(), action),
                               data=json.dumps(data),
                               user=user)
        json_data = res.json()
        status_code = res.status_code
        if settings.DEBUG:
            print(json_data)

        return status_code, json_data

    def logout(self, user):
        action = 'logout'
        print('{}/{}'.format(self.get_url(), action))
        res = dms_requests.put('{}/{}'.format(self.get_url(), action),
                                            user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data


    def forgot_password(self, data):
        action = 'forgot-password'
        print('{}/{}/{}'.format(self.get_url(), self.API_NAME, action))
        res = dms_requests.post('{}/{}'.format(self.get_url(), action),
                               data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code
        if settings.DEBUG:
            print(json_data)

        return status_code, json_data


    def reset_password(self, data):
        action = 'reset-password'
        res = dms_requests.post('{}/{}'.format(self.get_url(), action),
                               data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code
        if settings.DEBUG:
            print(json_data)

        return status_code, json_data
    
    def validate_forgot_password(self, data):
        action = 'validate-forgot-password-token'
        res = dms_requests.post('{}/{}'.format(self.get_url(), action),
                               data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code
        if settings.DEBUG:
            print(json_data)

        return status_code, json_data

    def validate_forgot_password_token(self, data):
        action = 'validate-forgot-password-token'
        res = dms_requests.post('{}/{}'.format(self.get_url(), action),
                               data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code
        if settings.DEBUG:
            print(json_data)

        return status_code, json_data
    
    def active_account(self, data):
        action = 'activeaccount'
        res = dms_requests.post('{}/{}'.format(self.get_url(), action),
                               data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def resend_active_account(self, data):
        action = 'resendactive'
        res = dms_requests.post('{}/{}'.format(self.get_url(), action),
                               data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
class DMS_AUTH_SETTING_API(DMS_API):
    API_NAME = "authenticationsettings"