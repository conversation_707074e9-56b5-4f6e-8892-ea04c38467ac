import json, io, ast
import base64
import xml.etree.ElementTree as ET
from requests import sessions
from django.conf import settings
from core.utils.params import cast_bool_from_str
from projects.serializers import *
from attributes.serializers import AttrSerializer, AttrFilterByTypeSerializer
from dms_connector.modules.project_setting import DMS_PROJECT_ATTR_API, DMS_PROJECT_CONFIG_API, DMS_PROJECT_TASK_CONFIG_API
from dms_connector.modules.attribute import DMS_ATTIBUTE_API
from dms_connector.modules.file import DMS_FILE_API
from dms_connector.modules.storage import DMS_STORAGE_API
from dms_connector.modules.task_assignment import DMS_TASK_ASSIGNMENT_API

from dms_connector.modules.base import DMS_API
from dms_connector import dms_requests

from data_manager.folder_sync_es_project import folder_data_sync_es_project
from projects.google_api_client.utils import get_list_labels_one_K, get_google_sheet_data, duplicate_google_sheet
from multilevel_classification.attrb_sync_es import *


class DMS_PROJECT_MEMBER_API(DMS_API):
    API_NAME = 'projects'
    API_SUB_NAME = 'members'

    def list(self, user, params=None, project=None):
        url = self.get_url(project=project)

        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code
        if status_code == 200:
            if json_data['success']:
                status_code_storage, json_data_storage = DMS_STORAGE_API().list(user)
                storage_endpoints = None
                for item in json_data_storage['items']:
                    if item['isPrimary']:
                        storage_endpoints = "{}/{}/".format(settings.MINIO_HOST, item['configValue']['bucketName'])
                for item in json_data['items']:
                    item['avatar'] = storage_endpoints + str(item['avatar'])

        return status_code, json_data

def fetch_state_statistic(data):
    user = data['user']
    project_id = data['project_id']

    status_code_statistic, json_data_statistic = DMS_FILE_API().statistic_data_status(user, project_id=project_id, statistic_filter='Status')

    return {
        'projectId': project_id,
        'stateStatistic': json_data_statistic['data']
    }

class DMS_PROJECT_API(DMS_API):
    API_NAME = 'projects'

    def list(self, user, params=None, module=None, project=None):
        url = self.get_url(module=module, project=project)
        if not self.API_SUB_NAME:
            if module:
                params['moduleId'] = module
            if project:
                params['projectId'] = project

        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        project_id_list = []

        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    data_item = item['id']
                    project_id_list.append(data_item)

        project_statistic_list = self.fetch_state_statistic_multiple_prj(user, params=project_id_list)

        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    for item_statistic in project_statistic_list:
                        if item_statistic['projectId'] == item['id']:
                            item['stateStatistic'] = item_statistic['stateStatistic']
                            break

        default_avatar_prj_list = []
        url_fetch_avatars = '{}/{}'.format(self.get_url(), 'fetchallavatarprojectsbyuser')
        res_fetch_avatars = dms_requests.post(url_fetch_avatars, user=user, data=json.dumps(project_id_list))
        status_code_fetch_avatars = res_fetch_avatars.status_code
        if status_code_fetch_avatars == 200:
            json_data_fetch_avatars = res_fetch_avatars.json()
            for item in json_data_fetch_avatars:
                default_avatar_prj_list.append({
                    "projectId": item['id'],
                    "projectAvatar": item['projectAvatar']
                })

        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    for item_avatar in default_avatar_prj_list:
                        if item_avatar['projectId'] == item['id']:
                            if item['projectAvatar'] is None:
                                item['projectAvatar'] = item_avatar['projectAvatar']

        return status_code, json_data
    
    def count_all_project(self, user, params=None):
        action = 'countallprojectsbyuser'
        url = "{}/{}".format(self.get_url(), action)
        res = dms_requests.get(url, user=user, params=params)

        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def fetch_state_statistic_multiple_prj(self, user, params=None):
        data = {
            "success": True,
            "data": None,
            "errorCode": None,
            "message": None
        }

        action = 'statistic-projects'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.post(url, user=user, data=json.dumps(params))
        json_data = res.json()
        status_code = res.status_code
        
        project_statistic_list = []

        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    status_statistic = {
                        "New": 0,
                        "Labeling": 0,
                        "Inreview": 0,
                        "Completed": 0
                    }
                    for state in item['states']:
                        status_statistic.update({state['viewState']: state['totalOfViewState']})
                    project_statistic_list.append({
                        "projectId": item['projectId'],
                        "stateStatistic": status_statistic
                    })

        return project_statistic_list
    
    def fetch_all_project(self, user, params):
        action = 'fetchallprojectsbyuser'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        project_id_list = []

        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    # data_item = {
                    #     'user': user,
                    #     'project_id': item['id']
                    # }
                    data_item = item['id']
                    project_id_list.append(data_item)

        # project_statistic_list = []
        # executor_statistic = concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_WORKERS_UPLOAD)
        # for _ in executor_statistic.map(fetch_state_statistic, project_id_list):
        #     project_statistic_list.append(_)

        project_statistic_list = self.fetch_state_statistic_multiple_prj(user, params=project_id_list)

        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    for item_statistic in project_statistic_list:
                        if item_statistic['projectId'] == item['id']:
                            item['stateStatistic'] = item_statistic['stateStatistic']
                            break
        
        default_avatar_prj_list = []
        url_fetch_avatars = '{}/{}'.format(self.get_url(), 'fetchallavatarprojectsbyuser')
        res_fetch_avatars = dms_requests.post(url_fetch_avatars, user=user, data=json.dumps(project_id_list))
        status_code_fetch_avatars = res_fetch_avatars.status_code
        if status_code_fetch_avatars == 200:
            json_data_fetch_avatars = res_fetch_avatars.json()
            for item in json_data_fetch_avatars:
                default_avatar_prj_list.append({
                    "projectId": item['id'],
                    "projectAvatar": item['projectAvatar']
                })

        if status_code == 200:
            if json_data['success']:
                for item in json_data['items']:
                    for item_avatar in default_avatar_prj_list:
                        if item_avatar['projectId'] == item['id']:
                            if item['projectAvatar'] is None:
                                item['projectAvatar'] = item_avatar['projectAvatar']

        return status_code, json_data

    def patch_revert_project(self, user, project_id):
        action = 'revert'
        url = '{}/{}/{}'.format(self.get_url(), action, project_id)
        res = dms_requests.patch(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def delete_temporary_project(self, user, project_id):
        action = 'deletetemporary'
        url = '{}/{}/{}'.format(self.get_url(), action, project_id)
        res = dms_requests.delete(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def validate_project_name(self, user, data, params=None):
        action = 'validateproject'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.post(url, user=user, data=json.dumps(data), params=params)
        json_data = res.json()                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
        status_code = res.status_code

        return status_code, json_data

    def dashboard(self, user, id):
        action = 'dashboard'
        url = '{}/{}/{}'.format(self.get_url(), id, action)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def get_attrs(self, user, project):
        action = 'project-attribute'
        url = '{}/{}'.format(self.get_url(project=project), action)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def get_dashboard(self, user, project):
        action = 'dashboard'
        url = '{}/{}/{}'.format(self.get_url(project=project), action)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    # def get_default_roles(self, user):
    #     action = 'members/resources'
    #     url = '{}/{}'.format(self.get_url(), action)
    #     res = dms_requests.get(url, user=user)
    #     json_data = res.json()
    #     status_code = res.status_code

    #     default_member_role_Id = json_data['data']['defaultMemberRoleId']
    #     roles = json_data['data']['roles']

    #     json_data_clone = json_data
    #     json_data_clone['data']['roles'] = []

    #     for role in roles:
    #         if role['id'] == default_member_role_Id or role['name'] == "Project Owner" or role['name'] == "CMM" or role['name'] == "DataExporter":
    #             json_data_clone['data']['roles'].append(role)

    #     return status_code, json_data_clone

    def get_roles(self, user):
        action = 'members/resources'
        url = '{}/{}'.format(self.get_url(), action)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def put(self, user, id, data, module=None, project=None):
        url = '{}/{}?moduleId={}'.format(self.get_url(module=module, project=project), id, module)
        print(url)
        res = dms_requests.put(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def auto_create_project(self, user, project_id, project_name, project_type, module_id):
        prj_url = self.get_url()

        # get roleid of PO
        status_code, json_data_roles = self.get_roles(user)
        roles_list = json_data_roles['data']['roles']
        PO_id = None
        for role in roles_list:
            if role['name'] == "Project Owner":
                PO_id = role['id']
                break

        data = {
            'name': project_name,
            'description': None,
            'status': 'Active',
            'labelConfig': None,
            'totalCounter': 0,
            'doneCounter': 0,
            'color' : None,
            'annotationFlow': None,
            'annotationWorkFlow': None, 
            'classConfig': '<TORUS_TEMPLATE>\n</TORUS_TEMPLATE>',
            'memberRoles': [{
                'roleId': PO_id,
                'userId': str(user.dms_id)
            }],
            'projectType': project_type
        }
        # get moduleId of project
        if module_id is not None:
            moduleId = module_id
        else:
            moduleId = self.detail(user, id=project_id)[1]['data']['moduleId']
        res = dms_requests.post(prj_url, user=user, params={'moduleId': moduleId}, data=json.dumps(data))
        return res.status_code, res.json()

    def clone(self, user, project_id, project_name, module_id, included_prj_attrib=False):
        # get data of original project
        status_code_original, json_data_original = self.detail(user=user, id=project_id)
        if json_data_original['success'] == False:
            return status_code_original, json_data_original, None
        
        # id of created project
        status_code_target, json_data_target = self.auto_create_project(user, project_id, project_name, json_data_original['data']['projectType'], module_id)
        if json_data_target['success'] == False:
            return status_code_target, json_data_target, None
        created_project_id = json_data_target['data']['id']

        # update data to new project
        setting_data = {
            "description": json_data_original['data']['description'],
            "labelConfig": json_data_original['data']['labelConfig'],
            "color": json_data_original['data']['color'],
            "annotationFlow": json_data_original['data']['annotationFlow'],
            "annotationWorkFlow": json_data_original['data']['annotationWorkFlow'],
            "classConfig": json_data_original['data']['classConfig'],
            # "status": json_data_original['data']['classConfig']
        }
        status_code_update, json_data_update = self.edit(user=user, id=created_project_id, data=setting_data)
        if json_data_update['success'] == False:
            return status_code_update, json_data_update, None
        
        status_code, file_content = DMS_PROJECT_CONFIG_API().get_demo_config(user=user, project_id=project_id)
        # convert bytes to json
        file_content_json = json.loads(file_content.decode('utf8'))
        file_content_json['ProjectName'] = project_name

        if included_prj_attrib:
            # convert json to bytes
            file_content_new = bytes(json.dumps(file_content_json), 'utf8')
            
            status_code, json_data = DMS_PROJECT_CONFIG_API().put(user, created_project_id, file_content_new)
        else:
            # add view_ attibute
            res = dms_requests.get(url=self.get_url().replace("projects", "attributes"), user=user, params={'code': settings.ATTR_PREFIX_DEFAULT,
                                                                        'codeOperator': 'StartWith'})
            status_code = res.status_code
            json_data = res.json()
            for attr in json_data['items']:
                data = {
                    "attributeId": attr['id'],
                    "displayName": attr['name'],
                    "status": attr['status'],
                    "isRequired": False
                }

                res = dms_requests.post(url="{}/{}/{}".format(self.get_url(), created_project_id, "attributes"), user=user, data=json.dumps(data))
                if res.json()['success'] == False:
                    return res.status_code, res.json()
                
            file_content_no_attrb = {
                "ProjectName": file_content_json['ProjectName'],
                "DataType": file_content_json['DataType'],
                "Attributes" : file_content_json['Attributes'],
                "Members": file_content_json['Members'],
                "TaskAssignmentConfigs": file_content_json['TaskAssignmentConfigs']
            }
            file_content_new = bytes(json.dumps(file_content_no_attrb), 'utf8')
            
            status_code, json_data = DMS_PROJECT_CONFIG_API().put(user, created_project_id, file_content_new)

        # clone task assignment config
        status_code_task_config, json_data_task_config = DMS_PROJECT_TASK_CONFIG_API().list(user=user, project=project_id)
        if status_code_task_config==200:
            if json_data_task_config['success']:
                DMS_PROJECT_TASK_CONFIG_API().put(user=user, project_id=project_id, data=json_data_task_config['data'])

        json_data['createdProjectId'] = created_project_id

        # return 200, "Done", "abc"
        return status_code, json_data, created_project_id

    def clone_with_data(self, user, project_id, project_name, clone_option, module_id):
        # clone_option = 2 : data only
        # clone_option = 3 : data with attributes
        status_code, json_data, created_project_id = self.clone(user, project_id, project_name, module_id=module_id)
        if json_data['success'] == False:
            return status_code, json_data
        
        # get storage_id
        url = '{}/{}/files/{}?fileName={}'.format(self.get_url(), created_project_id, "presigned-url", "sample.jpg")
        # print(url)
        res = dms_requests.get(url, user=user)
        if res.json()['success'] == False:
            return res.status_code, res.json()

        storage_id = res.json()['data']['storageId']

        # check files number
        status_code, json_data = DMS_FILE_API().list_with_task(user, params={"page": 1, "pageSize": 1}, project= project_id)
        files_count = json_data['total']
        page_number = int(files_count / 200) + 1

        for page in range(1, page_number + 1):
            status_code, json_data = DMS_FILE_API().list_new_managers_with_view_annotation_result_attrb(user, params={"page": page, "pageSize": 200}, project= project_id)
            files_list = json_data['items']
            files_post_list = []
            for file in files_list:
                attributes = settings.DEFAULT_VIEW_ATTRIBUTES
                data_view = settings.DEFAULT_FILE_FIELDS
                # data with attributes
                if clone_option == 3:
                    attributes = []
                    for attribute in file['attributes']:
                        attributes.append({
                            "code": attribute['code'],
                            "value": attribute['value']
                            })
                        if attribute['code'] == "view_state":
                            data_view['viewState'] = attribute['value']
                        if attribute['code'] == "view_priority":
                            data_view['priority'] = attribute['value']
                        if attribute['code'] == "view_folder":
                            data_view['pathFolder'] = attribute['value']
                        if attribute['code'] == "view_archive":
                            data_view['isArchive'] = cast_bool_from_str(attribute['value'])
                        if attribute['code'] == "view_workflow_step":
                            data_view['viewWorkFlowStep'] = attribute['value']
                        if attribute['code'] == "view_workflow_step_id":
                            data_view['viewWorkFlowStepId'] = attribute['value']
                data = {
                    "fileUrl": file['fileUrl'],
                    "filePath": file['fileUrl'],
                    "fileName": file['fileName'],
                    "fileSize": str(file['fileSize']),
                    "label": "",
                    "storageId": storage_id,
                    "dataType": file['dataType'],
                    "attributes": attributes
                }
                data.update(data_view)
                files_post_list.append(data)
            if len(files_post_list) > 0:
                url_post = '{}/{}/files/createmutiplefiles'.format(self.get_url(), created_project_id)
                res = dms_requests.post(url=url_post, data=json.dumps(files_post_list), user=user)
                if res.json()['success'] == False:
                    return res.status_code, res.json()
            
        json_data['createdProjectId'] = created_project_id
        folder_data_sync_es_project(project_master_id=created_project_id)

        return status_code, json_data
    
    def clone_data_only(self, user, project_id, project_name, module_id, included_prj_attrib=False):
        # id of created project
        status_code, json_data = self.auto_create_project(user, project_id, project_name, None, module_id)
        if json_data['success'] == False:
            return status_code, json_data
        created_project_id = json_data['data']['id']
        if included_prj_attrib:
            status_code, file_content = DMS_PROJECT_CONFIG_API().get_demo_config(user=user, project_id=project_id)
            # convert bytes to json
            file_content_json = json.loads(file_content.decode('utf8'))
            file_content_json['ProjectName'] = project_name
            file_content_no_setting = {
                "ProjectName": file_content_json['ProjectName'],
                "Attributes" : file_content_json['Attributes']
            }
            # convert json to bytes
            file_content_new = bytes(json.dumps(file_content_no_setting), 'utf8')
            
            status_code, json_data = DMS_PROJECT_CONFIG_API().put(user, created_project_id, file_content_new)
        else:
            # add view_ attibute
            res = dms_requests.get(url=self.get_url().replace("projects", "attributes"), user=user, params={'code': settings.ATTR_PREFIX_DEFAULT,
                                                                        'codeOperator': 'StartWith'})
            status_code = res.status_code
            json_data = res.json()
            for attr in json_data['items']:
                data = {
                    "attributeId": attr['id'],
                    "displayName": attr['name'],
                    "status": attr['status'],
                    "isRequired": False
                }

                res = dms_requests.post(url="{}/{}/{}".format(self.get_url(), created_project_id, "attributes"), user=user, data=json.dumps(data))
                if res.json()['success'] == False:
                    return res.status_code, res.json()

        # check files number
        status_code, json_data = DMS_FILE_API().list_with_task(user, params={"page": 1, "pageSize": 1}, project= project_id)
        files_count = json_data['total']
        page_number = int(files_count / 200) + 1
        # get storage_id
        url = '{}/{}/files/{}?fileName={}'.format(self.get_url(), created_project_id, "presigned-url", "sample.jpg")
        # print(url)
        res = dms_requests.get(url, user=user)
        if res.json()['success'] == False:
            return res.status_code, res.json()

        storage_id = res.json()['data']['storageId']

        for page in range(1, page_number + 1):
            # clone data
            status_code, json_data = DMS_FILE_API().list_with_task(user, params={"page": page, "pageSize": 200}, project= project_id)
            files_list = json_data['items']
            files_post_list = []
            for file in files_list:
                attributes = settings.DEFAULT_VIEW_ATTRIBUTES
                data = {
                    "fileUrl": file['fileUrl'],
                    "filePath": file['fileUrl'],
                    "fileName": file['fileName'],
                    "fileSize": str(file['fileSize']),
                    "label": "",
                    "storageId": storage_id,
                    "dataType": file['dataType'],
                    "attributes": attributes
                }
                data.update(settings.DEFAULT_FILE_FIELDS)
                files_post_list.append(data)
            if len(files_post_list) > 0:
                url_post = '{}/{}/files/createmutiplefiles'.format(self.get_url(), created_project_id)
                res = dms_requests.post(url=url_post, data=json.dumps(files_post_list), user=user)
                if res.json()['success'] == False:
                    return res.status_code, res.json()
            
        json_data['createdProjectId'] = created_project_id
        folder_data_sync_es_project(project_master_id=created_project_id)
        return status_code, json_data

    def traversal_xml_recursion(self, child, children):
        for child_ in child.findall("class"):
            children_ = []
            self.traversal_xml_recursion(child_, children_)
            child_json = {
                "value": child_.attrib["name"],
                "children": children_
            }
            # add other attribute
            for child_key in child_.attrib.keys():
                if child_key != "name":
                    child_json.update({child_key: child_.attrib[child_key]})

            children.append(child_json)

    def get_class_tree(self, user, project_id, attr_id=None):
        error_result = {
            "success": False,
            "errorCode": None,
            "message": None
        }
        status_code = 200
        try:
            status_code, res = self.detail(user=user, id=project_id)
            xml_config_string = res["data"]["classConfig"]
        except Exception as e:
            error_result['message'] = "XML config load fail"
            return error_result

        classtree_name_filter = True

        if attr_id is not None:
            url = '{}/{}/attributes/{}'.format(self.get_url(), project_id, attr_id)
            status_code, res = dms_requests.get(url, user=user)
            json_data = res.json()
            classtree_name = json_data['data']['code'].replace('annotate_attr_classification_', '')

        try:
            root = ET.fromstring(xml_config_string)
        except:
            error_result["message"] = "Invalid XML format"
            return status_code, error_result
        
        result_test = []
        for child_tools in root.findall("tools"):
            for child_classtree in child_tools.findall("classtree"):
                if attr_id is not None:
                    classtree_name_filter = child_classtree.attrib["name"] == classtree_name
                if classtree_name_filter:
                    class_tree_item = "annotate_attr_classification_{}".format(child_classtree.attrib["name"])
                    item = {
                        "key": class_tree_item,
                        "value": []
                    }

                    # traversal xml
                    self.traversal_xml_recursion(child_classtree, item["value"])
                    result_test.append(item)

        if attr_id is not None:
            result = {
                "success": True,
                "attribute_id": str(attr_id),
                "data": result_test
            }
        else:
            result = {
                "success": True,
                "attribute_id": None,
                "data": result_test
            }
        return status_code, result
    
    def pathGen(self, fn):
        path = []
        it = ET.iterparse(fn, events=('start', 'end'))
        for evt, el in it:
            if evt == 'start':
                path.append(el.attrib.get('value', 'None'))
                yield '---'.join(path)
            else:
                path.pop()

    def get_flatten_class_tree(self, user, project_id):
        error_result = {
            "success": False,
            "errorCode": None,
            "message": None
        }
        status_code = 200
        try:
            status_code, res = self.detail(user=user, id=project_id)
            xml_config_string = res["data"]["labelConfig"]
        except Exception as e:
            error_result['message'] = "XML config load fail"
            return error_result
        
        try:
            root = ET.fromstring(xml_config_string)
        except:
            error_result["message"] = "Invalid XML format"
            return status_code, error_result
        
        path_list = []
        for taxonomy_child in root.iter("Taxonomy"):
            for child in taxonomy_child:
                for pth in self.pathGen(io.StringIO(ET.tostring(child, encoding='unicode'))):
                    class_list = pth.split('---')
                    # print(pth)
                    path_list.append(class_list)

        return path_list
    
    def traversal_xml_recursion_by_tool_config(self, child, children):
        for child_ in child.findall("Choice"):
            children_ = []
            self.traversal_xml_recursion_by_tool_config(child_, children_)
            child_json = {
                "value": child_.attrib["value"],
                "children": children_
            }
            # add other attribute
            # for child_key in child_.attrib.keys():
            #     if child_key != "value":
            #         child_json.update({child_key: child_.attrib[child_key]})

            children.append(child_json)

    def get_class_tree_by_tool_config(self, user, project_id):
        error_result = {
            "success": False,
            "errorCode": None,
            "message": None
        }
        status_code = 200
        try:
            status_code, res = self.detail(user=user, id=project_id)
            xml_config_string = res["data"]["labelConfig"]
        except Exception as e:
            error_result['message'] = "XML config load fail"
            return error_result

        try:
            root = ET.fromstring(xml_config_string)
        except:
            error_result["message"] = "Invalid XML format"
            return status_code, error_result
        
        result_test = []
        for child_classtree in root.iter("Taxonomy"):
            if True:
                item = {
                    "key": child_classtree.attrib["name"],
                    "value": []
                }
                # traversal xml
                self.traversal_xml_recursion_by_tool_config(child_classtree, item["value"])
                result_test.append(item)

        result = {
            "success": True,
            "attribute_id": None,
            "data": result_test
        }
        return status_code, result

    def traversal_json_recursion(self, class_item_json, class_item_xml):
        for class_item_json_child in class_item_json["children"]:
            class_item_xml_child = ET.SubElement(class_item_xml, "class")
            class_item_xml_child.set("name", class_item_json_child["value"])
            self.traversal_json_recursion(class_item_json_child, class_item_xml_child)

    def edit_class_tree(self, user, project_id, classtree_json):
        result = {
            'success': True,
            'errorCode': None,
            'message': None,
        }
        try:
            res = self.detail(user=user, id=project_id)
            xml_config_string = res["data"]["classConfig"]
        except Exception as e:
            result["success"] = False
            result["message"] = "XML config load fail"
            return result

        root = ET.fromstring(xml_config_string)

        for classtree_json_item in classtree_json.keys():
            check_exist_classtree_json = False

            for child_tools in root.findall("tools"):
                for child_classtree in child_tools.findall("classtree"):
                    if child_classtree.attrib["name"] == classtree_json_item[14:]:
                        check_exist_classtree_json = True
                        for child_class in child_classtree.findall("class"):  # delete all sub elements in class tree
                            child_classtree.remove(child_class)
                        for class_item_json_ in classtree_json[classtree_json_item]:
                            class_item_xml_ = ET.SubElement(child_classtree, "class")
                            class_item_xml_.set("name", class_item_json_["value"])

                            # traversal json
                            self.traversal_json_recursion(class_item_json_, class_item_xml_)

            if check_exist_classtree_json == False:
                result["success"] = False
                result["message"] = 'Not exist class tree with name {}'.format(classtree_json_item[14:])
                return result

        # update project
        data = {
            "classConfig": ET.tostring(root, encoding='unicode')
        }

        status_code, json_data = self.edit(user=user, id=project_id, data=data)
        if status_code != 200:
            result["success"] = False
            result["message"] = "Update classConfig for project fail"
        return status_code, result

    def add_annotate_attr(self, user, project_id, child_):
        attr_code = "annotate_attr_classification_{}".format(child_.attrib['name']) if child_.tag == settings.ANNOTATE_TYPE['classification'] \
                                                                                    else "annotate_attr_objectinstance_{}".format(child_.attrib['name'])
        print("===============")
        print(attr_code)
        # create annotate attr corressponding xml tools
        data_attr = {
            "code": attr_code,
            "ownProjectId": str(project_id),
            "displayName": child_.attrib['name'],
            # "description": settings.DEFAULT_ANNOTATE_ATTRB_DESCRIPTION[child_.tag], 
            "description": None,    
            "type": 1,                              #type: Text
            "scope": 0,
            "status": "Active",
            "isRequired": False
        }
        data_attr = AttrSerializer(data_attr).data
        
        try:
            status_code, json_data_attr = DMS_ATTIBUTE_API().create(user, data_attr)
            # print(f"1.{json_data_attr}")
        except Exception as e:
            print(e)
        # print(json_data_attr)
        if json_data_attr.get('success', False):    # When attribute not existed and create successfully
            # add annotate attribute to project
            data_attr_prj = {
                "attributeId": json_data_attr['data'],
                "displayName": child_.attrib['name'],
                "status": "Active",
                "isRequired": False,
                "code": attr_code
            }
            data_attr_prj = ProjectAddExistAttrWithMetaSerializer(data_attr_prj).data
            try:
                status_code, json_data_attr_prj = DMS_PROJECT_ATTR_API().create(user, data_attr_prj, project=project_id)
                # print(f"2.{json_data_attr_prj}")
            except Exception as e:
                print(e)
            # print(json_data_attr_prj)
        else:                                   # When attribute is existed and add existed attr to project
            params = {
                # "pageSize":200,
                # "attributeType": None,
                "code": attr_code,
            }
            print("{}/{}/attributes/existing-attribute".format(self.get_url(), project_id))
            res = dms_requests.get(url="{}/{}/attributes/existing-attribute".format(self.get_url(), project_id), params=params, user=user)

            json_data = res.json()

            # params = AttrFilterByTypeSerializer(params).data
            # try:
            #     status_code, json_data = DMS_PROJECT_ATTR_API().list_exist_attr(user, params=params, project=project_id)
            #     # print(f"3.{json_data}")
            # except Exception as e:
            #     print(e)

            # for item in json_data['items']:
            #     print("+++++++++++")
            #     print(item['code'])

            for existed_attr in json_data['items']:
                existed_attr_id = existed_attr['attributeId']
                data_attr_prj_existed = {
                    "attributeId": existed_attr_id,
                    "displayName": child_.attrib['name'],
                    "status": "Active",
                    "isRequired": False,
                    "code": attr_code
                }
                data_attr_prj_existed = ProjectAddExistAttrWithMetaSerializer(data_attr_prj_existed).data
                try:
                    status_code, json_data_attr_prj_existed = DMS_PROJECT_ATTR_API().create(user, data_attr_prj_existed, project=project_id)
                    # print(f"4.{json_data_attr_prj_existed}")
                except Exception as e:
                    print(e)
                # print(json_data_attr_prj_exised)

    def fetch_assigned_members_workflow(self, user, project_id):
        result = {
            "success": True,
            "errorCode": None,
            "detail": None,
            "data": None
        }
        status_code = 200

        status_code, json_data = self.detail(user=user, id=project_id)

        if status_code != 200:
            return status_code, json_data
        
        if status_code == 200 and json_data["success"] == False:
            return status_code, json_data
        
        annotation_work_flow = json_data['data']['annotationWorkFlow']
        preprocessing_work_flow = json_data['data']['preProccessConfig']
        try:
            annotation_work_flow = json.loads(annotation_work_flow)
            node_list = annotation_work_flow['node']
            data_annotation = {
                "AIPrediction": [],
                "Labeling": [],
                "Inreview": [],
                "Completed": [],
            }
            for node in node_list:
                for item in data_annotation.keys():
                    if node['type'] == item:
                        data_annotation[item].append(node)
            for edge in annotation_work_flow['edge']:
                if edge['type'] == "New":
                    edge['source'] = "00000000-0000-0000-0000-000000000000"
            data_annotation.update({
                "Edges": annotation_work_flow['edge']
            })
        except:
            data_annotation = {
                "AIPrediction": [],
                "Labeling": [],
                "Inreview": [],
                "Completed": [],
                "Edges": []
            }

        try:
            preprocessing_work_flow = json.loads(preprocessing_work_flow)
            preprocess_node_list = preprocessing_work_flow['node']
            data_preprocessing = {
                "PreProcessingNew": [],
                "HumanApproval": [],
                "PreProcessingCompleted": [],
            }
            for node in preprocess_node_list:
                for item in data_preprocessing.keys():
                    if node['type'] == item:
                        data_preprocessing[item].append(node)
            data_preprocessing.update({
                "PreProcessingEdges": preprocessing_work_flow['edge']
            })
        except:
            data_preprocessing = {
                "PreProcessingNew": [],
                "HumanApproval": [],
                "PreProcessingCompleted": [],
                "PreProcessingEdges": []
            }

        data = {}
        data.update(data_annotation)
        data.update(data_preprocessing)
        result['data'] = data

        return status_code, result
    
    def fetch_infor_assigned_members_workflow(self, user, project_id):
        status_code_fetch, result = self.fetch_assigned_members_workflow(user=user, project_id=project_id)
        if status_code_fetch != 200:
            return status_code_fetch, result
        
        if status_code_fetch == 200 and result["success"] == False:
            return status_code_fetch, result

        status_code_members, json_data_members = DMS_PROJECT_MEMBER_API().list(user=user, project=project_id)
        project_members = []
        print(result)
        for item in json_data_members['items']:
            project_members.append({
                'userId': item['userId'],
                'userName': item['userName'],
                'firstName': item['firstName'],
                'lastName': item['lastName'],
                'avatar': item['avatar'],
                'permission': item['permission']
            })

        for node in result['data']['Labeling']:
            if 'all' in node['members']:
                node.update({'members_info':[x for x in project_members if "DatasetManagement.Update" in x['permission'].split(",")]})
            else:
                node.update({'members_info': []})
                for member in node['members']:
                    for project_member in project_members:
                        if project_member['userId'] == member:
                            node['members_info'].append(project_member)
                            break
        for node in result['data']['Inreview']:
            if 'all' in node['members']:
                node.update({'members_info':[x for x in project_members if "DatasetManagement.Update" in x['permission'].split(",")]})
            else:
                node.update({'members_info': []})
                for member in node['members']:
                    for project_member in project_members:
                        if project_member['userId'] == member:
                            node['members_info'].append(project_member)
                            break
        for node in result['data']['AIPrediction']:
            if 'all' in node['members']:
                node.update({'members_info':[x for x in project_members if "DatasetManagement.Update" in x['permission'].split(",") and "DatasetManagement.AIProceed" in x['permission'].split(",")]})
            else:
                node.update({'members_info': []})
                for member in node['members']:
                    for project_member in project_members:
                        if project_member['userId'] == member:
                            node['members_info'].append(project_member)
                            break
        for node in result['data']['HumanApproval']:
            if 'all' in node['members']:
                node.update({'members_info':[x for x in project_members if "DatasetManagement.DataProceed" in x['permission'].split(",")]})
            else:
                node.update({'members_info': []})
                for member in node['members']:
                    for project_member in project_members:
                        if project_member['userId'] == member:
                            node['members_info'].append(project_member)
                            break
        for node in result['data']['Completed']:
            if 'all' in node['members']:
                node.update({'members_info':[x for x in project_members]})
            else:
                node.update({'members_info': []})
                for member in node['members']:
                    for project_member in project_members:
                        if project_member['userId'] == member:
                            node['members_info'].append(project_member)
                            break
        for node in result['data']['PreProcessingCompleted']:
            if 'all' in node['members']:
                node.update({'members_info':[x for x in project_members if "DatasetManagement.DataProceed" in x['permission'].split(",")]})
            else:
                node.update({'members_info': []})
                for member in node['members']:
                    for project_member in project_members:
                        if project_member['userId'] == member:
                            node['members_info'].append(project_member)
                            break
       
        
        # # i=1
        # for node in result['data']['Labeling']:
        #     url = "{}/{}/Tasks/taskassignmanualtooltip?workFlowId={}".format(self.get_url(), project_id, node['id'])
        #     if node['members'] == ['all']:
        #         for member in project_members:
        #             url = url + "&userIds={}".format(member['userId'])
        #     else:
        #         for member in node['members']:
        #             url = url + "&userIds={}".format(member)
        #     res = dms_requests.get(url, user=user)
        #     if res.status_code == 200:
        #         for member in node['members_info']:
        #             # print("===================================")
        #             # member.update({'countTaskDone': None,
        #             #                 'totalTask': None})
        #             # print(member['userId'])
        #             for item in res.json():
        #                 if member['userId'] == item['userId']:
        #                     # print(item['countTaskDone'])
        #                     # print(item['totalTask'])
        #                     # print(member)
        #                     member['countTaskDone']= item['countTaskDone'],
        #                     member['totalTask']=item['totalTask']
        #                     # print(member)
        #                     break
        #     # i += 1
        #     # if i == 2:
        #     #     print(node)
        #     #     break
        #     #         print(member)
        #     #         print(node)
        #     #         print("===================================")
        #     # print("aaaaaaaaaaaa")
        #     # print(node)
        #     # print("aaaaaaaaaaaa")

        # # print(result['data']['Labeling'][0])

        return status_code_fetch, result

    def fetch_steps_workflow(self, user, project_id):
        result = {
            "success": True,
            "errorCode": None,
            "detail": None,
            "data": []
        }
        status_code = 200

        status_code, json_data = self.detail(user=user, id=project_id)

        if status_code != 200:
            return status_code, json_data
        
        if status_code == 200 and json_data["success"] == False:
            return status_code, json_data
        
        annotation_work_flow = json_data['data']['annotationWorkFlow']
        try:
            annotation_work_flow = json.loads(annotation_work_flow)
        except:
            return status_code, result
        node_list = annotation_work_flow['node']

        for node in node_list:
            if node['type'] in ["Labeling", "Inreview"]:
                result['data'].append(node)

        return status_code, result
    
    def fetch_steps_workflow_include_new(self, user, project_id):
        result = {
            "success": True,
            "errorCode": None,
            "detail": None,
            "data": []
        }
        status_code = 200

        status_code, json_data = self.detail(user=user, id=project_id)

        if status_code != 200:
            return status_code, json_data
        
        if status_code == 200 and json_data["success"] == False:
            return status_code, json_data
        
        annotation_work_flow = json_data['data']['annotationWorkFlow']
        try:
            annotation_work_flow = json.loads(annotation_work_flow)
        except:
            return status_code, result
        node_list = annotation_work_flow['node']

        for node in node_list:
            if node['type'] in ["New", "Labeling", "Inreview", "Completed"]:
                result['data'].append(node)

        return status_code, result

    
    def fetch_available_tools(self, user, project_id):
        result = {
            "success": True,
            "errorCode": None,
            "detail": None,
            "data": []
        }
        status_code = 200

        status_code, json_data = self.detail(user=user, id=project_id)
        enableAvancedAnnotation = json_data['data']['enableAvancedAnnotation']
        if enableAvancedAnnotation:
            result['data'] = ["OneK"]
            return status_code, result
        tools_config_string = json_data['data']['labelConfig']

        try:
            root = ET.fromstring(tools_config_string)
        except:
            error_result = {
                "success": False,
                "data": None,
                "errorCode": None,
                "message": "Invalid XML format"
            }
            return 200, error_result
        
        available_tools = {}
        for x in settings.TOOLS_AVAILABLE:
            available_tools.update({x['name']: x['type']})

        instance_tool = None
        for child_tool in root.iter():
            if child_tool.tag == "Tools":
                instance_tool = child_tool
                break

        for child_tool in root.iter():
            if child_tool.tag in available_tools.keys():
                result['data'].append(available_tools[child_tool.tag])
                if instance_tool is not None:
                    if child_tool in instance_tool.findall(".//*"):
                        result['data'].remove(available_tools[child_tool.tag])

        return status_code, result
    
    def fetch_available_labels(self, user, project_id, params={}):
        tool_name = params.get('tool', None)
        result = {
            "success": True,
            "errorCode": None,
            "detail": None,
            "data": []
        }
        status_code = 200

        status_code, json_data = self.detail(user=user, id=project_id)
        tools_config_string = json_data['data']['labelConfig']

        try:
            root = ET.fromstring(tools_config_string)
        except:
            error_result = {
                "success": False,
                "data": None,
                "errorCode": None,
                "message": "Invalid XML format"
            }
            return 200, error_result
        
        available_tools = {}
        for x in settings.TOOLS_AVAILABLE:
            available_tools.update({x['name']: x['type']})

        for child_tool in root.iter():
            if child_tool.tag in available_tools.keys():
                for child in child_tool:
                    if child.tag == "Label":
                        result['data'].append({
                            'tool': available_tools[child_tool.tag],
                            'from_name': child_tool.attrib['name'],
                            'label': child.attrib['value']
                        })

        if tool_name is not None:
            result['data'] = [x for x in result['data'] if x['tool'] == tool_name]
        return status_code, result
    
    def fetch_available_labels_model(self, user, project_id, params=None):
        headers = {
            "Content-Type": "application/json",
            'Accept': 'application/json'
        }
        ai_api_url = params.get('aiApiUrl', None)

        try:
            with sessions.Session() as session:
                res = session.request('get',
                                    url="{}/labels".format(ai_api_url))
        except Exception as e:
            result = {
                "success": False,
                "errorCode": None,
                "detail": e,
                "data": []
            }
            status_code = 200
            return status_code, result
            
        if res.status_code != 200:
            return res.status_code, res.json()

        result = {
            "success": True,
            "errorCode": None,
            "detail": None,
            "data": []
        }
        status_code = 200

        json_data = res.json()
        for item in json_data['labels']:
            result['data'].append({
                'label_name': item
            })

        return status_code, result
    
    def fetch_min_confidence_model(self, user, project_id, params=None):
        headers = {
            "Content-Type": "application/json",
            'Accept': 'application/json'
        }
        ai_api_url = params.get('aiApiUrl', None)
        try:
            with sessions.Session() as session:
                res = session.request('get',
                                    url="{}/min_conf".format(ai_api_url))
        except Exception as e:
            result = {
                "success": False,
                "errorCode": None,
                "detail": e,
                "data": None
            }
            status_code = 200
            return status_code, result
            
        if res.status_code != 200:
            return res.status_code, res.json()

        result = {
            "success": True,
            "errorCode": None,
            "detail": None,
            "data": []
        }
        status_code = 200
        result['data'] = res.json()

        return status_code, result
    
    def fetch_available_preview_model(self, user, project_id, file=None, params=None):
        conf_thres = params.get('conf_thres', 0)
        label_mappings = params.get('label_mappings', None)
        encoded_string = base64.b64encode(file.read())
        data = {
            "conf_thres": conf_thres,
            "image_base64": encoded_string.decode('utf-8'),
            "label_mappings": json.loads(label_mappings)
        } if label_mappings is not None else {
            "conf_thres": conf_thres,
            "image_base64": encoded_string.decode('utf-8'),
            "label_mappings": [
                {
                    'label': 'Nails',
                    'mapped_label': 'Nails'
                }
            ]
        }

        headers = {
            "Content-Type": "application/json",
            'Accept': 'application/json'
        }
        ai_api_url = params.get('aiApiUrl', None)

        try:
            with sessions.Session() as session:
                res = session.request('post',
                                    url="{}/preview".format(ai_api_url),
                                    headers=headers,
                                    data=json.dumps(data))
        except Exception as e:
            result = {
                "success": False,
                "errorCode": None,
                "detail": e,
                "data": []
            }
            status_code = 200
            return status_code, result
        
        if res.status_code != 200:
            return res.status_code, res.json()
        
        status_code = 200
        result = {
            "success": True,
            "errorCode": None,
            "detail": None,
            "data": res.json()['preview_image']
        }

        return status_code, result

    def get_sample_csv_new(self, user, project_id, params, max_file_Ids=None):
        include_fileId = params.get('include_fileId', False)

        field_row = ["image_path_local", "image_width", "image_height"] if include_fileId == False else ["file_id", "file_name", "image_path_local", "image_width", "image_height"]
        sample_row = ["data/sub/acne.jpg", 1000, 1000] if include_fileId == False else ["00000000-0000-0000-0000-000000000000", "acne.jpg", "data/sub/acne.jpg", 1000, 1000]
        
        tools_list = params.get('annotation_tools', [])
        meta_data_list = params.get('meta_data', [])

        available_tools = {}
        for x in settings.TOOLS_AVAILABLE:
            available_tools.update({x['type']: x['sample']})

        for tool in tools_list:
            field_row.append("view_annotation_result_{}".format(tool))
            sample_row.append(available_tools[tool])

        for meta_data in meta_data_list:
            field_row.append("{}".format(meta_data))
            sample_row.append("")

        csv_result = [field_row, sample_row]

        if include_fileId:
            url = "{}/{}/files/fetchlistfiles".format(self.get_url(), project_id)
            params = {
                "isDelete": False,
                "isArchive": False
            }
            res = dms_requests.get(url, user=user, params=params)
            if res.status_code == 200:
                if res.json()['success']:
                    if max_file_Ids is None:
                        for item in res.json()['data']:
                            row = [item['fileId'], item['fileName'], "{}".format(item['fileName'])] if item['filePath'] in ['/', None, ''] else [item['fileId'], item['fileName'], "{}{}".format(item['filePath'][1:], item['fileName'])]
                            for i in range(len(field_row) - len(row)):
                                row.append("")
                            csv_result.append(row)
                    else:
                        for index, item in enumerate(res.json()['data']):
                            if index < max_file_Ids:
                                row = [item['fileId'], item['fileName'], "{}".format(item['fileName'])] if item['filePath'] in ['/', None, ''] else [item['fileId'], item['fileName'], "{}{}".format(item['filePath'][1:], item['fileName'])]
                                for i in range(len(field_row) - len(row)):
                                    row.append("")
                                csv_result.append(row)

        return csv_result
    
    def get_sample_csv_new_instruction(self, user, project_id, params):
        csv_result = self.get_sample_csv_new(user, project_id, params, max_file_Ids=3)
        
        csv_result_instruction = {
            "columns": [],
            "data": []
        }
        for item in csv_result[0]:
            csv_result_instruction['columns'].append({
                "title": item,
                "dataIndex": item,
                "key": item
            })
        for index, row in enumerate(csv_result):
            if index == 0:
                continue
            row_dict = {}
            for i, item in enumerate(row):
                row_dict.update({csv_result_instruction['columns'][i]['key']: item})
            csv_result_instruction['data'].append(row_dict)

        return csv_result_instruction
    
    def validate_limit_project(self, user):
        url = "{}/validatelimitationproject".format(self.get_url())
        res = dms_requests.post(url, user=user)
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data
    
    def project_1k_get_gg_sheet_data(self, spreadsheet_id, sheet_name):
        result = get_google_sheet_data(spreadsheet_id, f"{sheet_name}!A1:Z")
        return result

    def project_1k_get_list_labels(self, spreadsheet_id, sheet_name):
        result = get_list_labels_one_K(spreadsheet_id, f"{sheet_name}!A1:Z")
        return result
    
    def project_1k_apply_changes_sheet(self, user, project_id, data):
        url = f"{self.get_url()}/{project_id}/advancedconfigurations"

        request_body = {
            **data,
            "advancedConfiguration": json.dumps(self.project_1k_get_list_labels(
                spreadsheet_id=data['sheetId'],
                sheet_name=data['sheetName']
            ))
        }

        res = dms_requests.post(url, user=user, data=json.dumps(request_body))
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data
    
    def validate_limit_files_project(self, user, project_id):
        url = "{}/{}/files/validatelimitationfiles".format(self.get_url(), project_id)

        res = dms_requests.get(url, user=user)
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data

    def project_1k_apply_labels(self, user, project_id, advanced_version_id, data):
        url = f"{self.get_url()}/{project_id}/advancedconfigurations/{advanced_version_id}/apply"

        res = dms_requests.post(url, user=user, data=json.dumps(data))
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data
    
    def project_1k_get_advanced_config(self, user, project_id):
        url = f"{self.get_url()}/{project_id}/advancedconfigurations/default"

        res = dms_requests.get(url, user=user)
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data
    
    def project_1k_get_validate_sheet(self, user, project_id, data):
        result = {
            "success": True,
            "errorCode": None,
            "message": None,
            "data": True
        }

        url = f"{self.get_url()}/{project_id}/advancedconfigurations/default"
        res = dms_requests.get(url, user=user)
        status_code = res.status_code
        if status_code != 200:
            result['data'] = False
            return status_code, result
        json_data = res.json()
        validate_url = json_data['data']['validateUrl']

        try:
            res = dms_requests.get(validate_url, user=user)
            status_code = res.status_code
            if status_code != 200:
                result['data'] = False
                return status_code, result
            json_data = res.json()
            if json_data['status'] == False:
                result['data'] = False
        except Exception as e:
            result['data'] = False
    
        return 200, result
    
    def project_1k_post_advanced_config(self, user, project_id):
        copied_file = duplicate_google_sheet(file_name=project_id)
        if copied_file is None:
            return 500, {
                "success": False,
                "errorCode": None,
                "message": "Cannot copy file"
            }
        
        sheet_id = copied_file['id']
        sheet_name = 'Main V2'

        url = f"{self.get_url()}/{project_id}/advancedconfigurations"

        request_body = {
            "sheetId": sheet_id,
            "sheetName": sheet_name,
            "googleDriveUrl": f"https://docs.google.com/spreadsheets/d/{sheet_id}/edit",
            "advancedConfiguration": "",
        }

        res = dms_requests.post(url, user=user, data=json.dumps(request_body))
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data
    
    def project_1k_set_advanced_config(self, user, project_id, data):
        url = f"{self.get_url()}/{project_id}/advancedconfigurations"

        res = dms_requests.post(url, user=user, data=json.dumps(data))
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data
    
    def project_1k_post_labeling(self, user, project_id, advanced_version_id, data):
        url = f"{self.get_url()}/{project_id}/advancedconfigurations/labeling/{advanced_version_id}"

        res = dms_requests.post(url, user=user, data=json.dumps(data))
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data
    
    def project_1k_get_labels_by_advanced_version(self, user, project_id, advanced_version_id, data):
        url = f"{self.get_url()}/{project_id}/advancedconfigurations/{advanced_version_id}/labels"

        res = dms_requests.post(url, user=user, data=json.dumps(data))
        status_code = res.status_code
        json_data = res.json()

        return status_code, json_data
    
    def fetch_all_project_tag(self, user, project_id):
        url = "{}/{}/projecttags".format(self.get_url(), project_id)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def without_annotation_project_create(self, user, data, project_type, module=None, project=None):
        url = "{}/createwithoutannotation".format(self.get_url(module=module, project=project))
        params = {}
        if not self.API_SUB_NAME:
            if module:
                params['moduleId'] = module
            if project:
                params['projectId'] = project

        data['projectType'] = project_type

        res = dms_requests.post(url, user=user, params=params, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def fetch_list_data_access_ML_project(self, user, project_id):
        url = "{}/{}/mldataaccess".format(self.get_url(), project_id)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def fetch_list_data_access_ML_project_by_user(self, user, project_id):
        url = "{}/{}/mldataaccess/byuser".format(self.get_url(), project_id)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def create_data_access_ML_project(self, user, project_id, data):
        url = "{}/{}/mldataaccess".format(self.get_url(), project_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_data_access_ML_project(self, user, project_id, data):
        url = "{}/{}/mldataaccess".format(self.get_url(), project_id)
        res = dms_requests.delete(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def annotation_project_create(self, user, data, project_type, module=None, project=None):
        url = self.get_url(module=module, project=project)
        params = {}
        if not self.API_SUB_NAME:
            if module:
                params['moduleId'] = module
            if project:
                params['projectId'] = project
                
        if project_type:
            data['projectType'] = project_type

        res = dms_requests.post(url, user=user, params=params, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    

    def ml_dataset_add(self, user, project_id, data):
        url = "{}/{}/mldataset".format(self.get_url(), project_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def ml_dataset_delete(self, user, project_id, params):
        url = "{}/{}/mldataset".format(self.get_url(), project_id)
        res = dms_requests.delete(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def create_new_project_tag(self, user, project_id, data):
        url = "{}/{}/projecttags".format(self.get_url(), project_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def ml_dataset_list_data_access(self, user, project_id):
        url = "{}/{}/mldataset/listprojectdataaccess".format(self.get_url(), project_id)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def update_project_tag(self, user, project_id, project_tag_id, data):
        url = "{}/{}/projecttags/{}".format(self.get_url(), project_id, project_tag_id)
        res = dms_requests.put(url, user=user, data=json.dumps(data))

        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def ml_dataset_fetch_all(self, user, project_id):
        url = "{}/{}/mldataset/fetchalldatasets".format(self.get_url(), project_id)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def delete_project_tag(self, user, project_id, project_tag_id):
        url = "{}/{}/projecttags/{}".format(self.get_url(), project_id, project_tag_id)
        res = dms_requests.delete(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def ml_dataset_fetch_by_ml_project(self, user, project_id):
        url = "{}/{}/mldataset/loaddatasetsbymlproject".format(self.get_url(), project_id)
        res = dms_requests.get(url, user=user)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def update_project_tags_in_file(self, user, project_id, project_file_id, data):
        url = "{}/{}/projecttags/projectfile/{}".format(self.get_url(), project_id, project_file_id)
        res = dms_requests.put(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def create_project_tag_list(self, user, project_id, data):
        url = "{}/{}/projecttags/addtags".format(self.get_url(), project_id)
        res = dms_requests.post(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def update_project_tag_list(self, user, project_id, data):
        url = "{}/{}/projecttags/updateprojecttags".format(self.get_url(), project_id)
        res = dms_requests.put(url, user=user, data=json.dumps(data))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data

    def ml_dataset_sync_revisions_to_ml_project(self, user, project_id, data):
        url = "{}/{}/mldataset/syncmlprojecttoclearml".format(self.get_url(), project_id)
        revisions_list = data.get('revisions', [])
        res = dms_requests.post(url, user=user, data=json.dumps(revisions_list))
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
    
    def project_1k_get_labels_folder_pane_tree(self, user, project_id):
        status_code, json_data = self.project_1k_get_advanced_config(user=user, project_id=project_id)

        if status_code != 200:
            return status_code, json_data
        
        advanced_configuration = json_data['data']['advancedConfiguration']
        if advanced_configuration == "" or advanced_configuration is None:
            return 200, {
                "success": True,
                "errorCode": None,
                "message": None,
                "data": []
            }
        advanced_configuration = json.loads(advanced_configuration)

        result = {
            "key": "Annotation",
            "value": []
        }

        labels_list = [*advanced_configuration['group'], 
                        *advanced_configuration['bodypart'],
                        *advanced_configuration['other'],
                        *advanced_configuration['class'],
                        *advanced_configuration['subclass'],
                        *advanced_configuration['subsubclass']
        ]

        for label in labels_list:
            if label['type'] == "group" and label['is_deleted'] == '0' and label['is_separated'] == '0' and label['merged_to'] == "":
                group = {
                    "value": label['full_name'],
                    "id": label['label_id'],
                    "children": []
                }
                for label_ in labels_list:
                    if label_['type'] == "class":
                        # if label_['parent_1_id'] == label['label_id'] or label_['parent_2_id'] == label['label_id']:
                        if label_['parent_1_id'] == label['label_id'] and label_['is_deleted'] == '0' and label_['is_separated'] == '0' and label_['merged_to'] == "":
                            class_ = {
                                "value": label_['full_name'],
                                "id": label_['label_id'],
                                "children": []
                            }
                            for label__ in labels_list:
                                if label__['type'] == "subclass":
                                    # if label__['parent_1_id'] == label_['label_id'] or label__['parent_2_id'] == label_['label_id']:
                                    if label__['parent_1_id'] == label_['label_id'] and label__['is_deleted'] == '0' and label__['is_separated'] == '0' and label__['merged_to'] == "":
                                        subclass = {
                                            "value": label__['full_name'],
                                            "id": label__['label_id'],
                                            "children": []
                                        }
                                        for label___ in labels_list:
                                            if label___['type'] == "subsubclass":
                                                # if label___['parent_1_id'] == label__['label_id'] or label___['parent_2_id'] == label__['label_id']:
                                                if label___['parent_1_id'] == label__['label_id'] and label___['is_deleted'] == '0' and label___['is_separated'] == '0' and label___['merged_to'] == "":
                                                    subsubclass = {
                                                        "value": label___['full_name'],
                                                        "id": label___['label_id'],
                                                        "children": []
                                                    }
                                                    subclass['children'].append(subsubclass)
                                        class_['children'].append(subclass)
                            group['children'].append(class_)
                result['value'].append(group)
            if label['type'] == "bodypart" and label['is_deleted'] == '0' and label['is_separated'] == '0' and label['merged_to'] == "":
                bodypart = {
                    "value": label['full_name'],
                    "id": label['label_id'],
                    "children": []
                }
                result['value'].append(bodypart)
            if label['type'] == "other" and label['is_deleted'] == '0' and label['is_separated'] == '0' and label['merged_to'] == "":
                other = {
                    "value": label['full_name'],
                    "id": label['label_id'],
                    "children": []
                }
                result['value'].append(other)
        
        return 200, result
    

    def project_1k_sync_labels_file(self, user, project_id, file_id):

        attrb_sync_es(project_master_id=project_id, file_id=file_id)

        return 200, "Done"
    
    def project_1k_elasticsearch_count_files(self, es_connection, index_name, project_id, label_id):
        query_es = {
            "bool": {
                "must": [
                    {"match": {"projectId.keyword": str(project_id)}},
                    {"match": {"isDelete": 0}},
                    {"match": {"isArchive": 0}}
                ]
            }
        }

        query_es["bool"].update({
            "should": [
                {"term": {"oneK.group.keyword": label_id}},
                {"term": {"oneK.class.keyword": label_id}},
                {"term": {"oneK.subclass.keyword": label_id}},
                {"term": {"oneK.subsubclass.keyword": label_id}},
                {"term": {"oneK.bodypart.keyword": label_id}},
                {"term": {"oneK.other.keyword": label_id}}
            ],
            "minimum_should_match": 1
        })

        print(query_es)
        resp = es_connection.count(index=index_name, query=query_es)
        return resp['count']
    
    def project_1k_count_files_by_branch(self, user, project_id, data):
        es = settings.ES_CONNECTION
        index_name = settings.ES_INDEX_ATTRB

        labels_id_list = ast.literal_eval(data['branch']) if data['branch'] else []

        status_code, json_data = self.project_1k_get_advanced_config(user=user, project_id=project_id)

        if status_code != 200:
            return status_code, json_data
        
        advanced_configuration = json_data['data']['advancedConfiguration']
        if advanced_configuration == "" or advanced_configuration is None:
            return 200, {
                "success": True,
                "errorCode": None,
                "message": None,
                "data": []
            }
        advanced_configuration = json.loads(advanced_configuration)

        labels_list = [*advanced_configuration['group'], 
                        *advanced_configuration['bodypart'],
                        *advanced_configuration['other'],
                        *advanced_configuration['class'],
                        *advanced_configuration['subclass'],
                        *advanced_configuration['subsubclass']
        ]

        result = {
            "current_node": None,
            "current_node_id": None,
            "count": 0,
            "children_node": []
        }

        if len(labels_id_list) == 0:
            for label in labels_list:
                if label['type'] in ['group', 'bodypart', 'other']:
                    result['children_node'].append({
                        "value": label['value'],
                        "id": label['label_id'],
                        "count": self.project_1k_elasticsearch_count_files(es_connection=es, 
                                                                           index_name=index_name, 
                                                                           project_id=project_id, 
                                                                           label_id=label['label_id'])
                    })
            return 200, result

        for label in labels_list:
            if label['label_id'] == labels_id_list[-1]:
                result['current_node'] = label['value']
                result['current_node_id'] = label['label_id']
                result['count'] = self.project_1k_elasticsearch_count_files(es_connection=es,
                                                                            index_name=index_name,
                                                                            project_id=project_id,
                                                                            label_id=label['label_id'])
                break

        for label in labels_list:
            if label['type'] in ['group', 'bodypart', 'other']:
                continue
            if label['parent_1_id'] == labels_id_list[-1]:
                result['children_node'].append({
                    "value": label['value'],
                    "id": label['label_id'],
                    "count": self.project_1k_elasticsearch_count_files(es_connection=es, 
                                                                       index_name=index_name, 
                                                                       project_id=project_id, 
                                                                       label_id=label['label_id'])
                })

        return 200, result
    
    def project_1k_fetch_files_by_branch(self, user, project_id, data):
        es = settings.ES_CONNECTION
        index_name = settings.ES_INDEX_ATTRB

        labels_id_list = ast.literal_eval(data['branch']) if data['branch'] else []

        status_code, json_data = self.project_1k_get_advanced_config(user=user, project_id=project_id)

        if status_code != 200:
            return status_code, json_data
        
        advanced_configuration = json_data['data']['advancedConfiguration']
        if advanced_configuration == "" or advanced_configuration is None:
            return 200, {
                "success": True,
                "errorCode": None,
                "message": None,
                "data": []
            }
        advanced_configuration = json.loads(advanced_configuration)

        labels_list = [*advanced_configuration['group'], 
                        *advanced_configuration['bodypart'],
                        *advanced_configuration['other'],
                        *advanced_configuration['class'],
                        *advanced_configuration['subclass'],
                        *advanced_configuration['subsubclass']
        ]

        page = data['page']
        page_size = data['pageSize']
        sort_by = data['sortBy']
        order_by = data['orderBy']

        filters_ = ProjectOneKFetchFilesByBranchFilterSerializer(data).data

        query_es = {
            "bool": {
                "must": [
                    {"match": {"projectId.keyword": str(project_id)}},
                    {"match": {"isDelete": 0}},
                    {"match": {"isArchive": 0}}
                ],
                "must_not": [],
                "filter": []
            }
        }

        sort_es = [
            {
                sort_by: {
                    "order": order_by
                }
            }
        ]

        for filter in filters_.keys():
            query_es["bool"]["filter"].append({
                "match": {
                    f"{filter}.keyword": filters_[filter]
                }
            })

        for label in labels_list:
            if label['label_id'] == labels_id_list[-1]:
                query_es["bool"]["should"] = [
                    {"term": {"oneK.group.keyword": label['label_id']}},
                    {"term": {"oneK.class.keyword": label['label_id']}},
                    {"term": {"oneK.subclass.keyword": label['label_id']}},
                    {"term": {"oneK.subsubclass.keyword": label['label_id']}},
                    {"term": {"oneK.bodypart.keyword": label['label_id']}},
                    {"term": {"oneK.other.keyword": label['label_id']}}
                ]
                query_es["bool"]["minimum_should_match"] = 1
                break
        
        print(query_es)

        resp = es.search(index=index_name, query=query_es, size=page_size, from_=(int(page_size) * (int(page) - 1)), sort=sort_es)
        print(resp)

        data = resp["hits"]
        items = [item["_source"] for item in data["hits"]]
        for item in items:
            current_step = {
                "step": item['viewWorkFlowStep'] if item['viewWorkFlowStep'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStep'],
                "stepId": item['viewWorkFlowStepId'] if item['viewWorkFlowStepId'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStepId'],
                "task": None
            }
            if current_step['stepId'] is not None:
                for task in item['shortTaskAssignments']:
                    if task['workFlowStepId'] == current_step['stepId']:
                        current_step['task'] = task
                        break
            attributes = [
                {
                    'code': "view_state",
                    'value': item['viewState'] if item['viewState'] is not None else settings.DEFAULT_FILE_FIELDS['viewState']
                },
                {
                    'code': "view_priority",
                    'value': item['priority'] if item['priority'] is not None else settings.DEFAULT_FILE_FIELDS['priority']
                },
                {
                    'code': "view_folder",
                    'value': item['pathFolder'] if item['pathFolder'] is not None else settings.DEFAULT_FILE_FIELDS['pathFolder']
                },
                {
                    'code': "view_archive",
                    'value': str(item['isArchive']).lower() if item['isArchive'] is not None else "false"
                },
                {
                    'code': "view_workflow_step",
                    'value': item['viewWorkFlowStep'] if item['viewWorkFlowStep'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStep']
                },
                {
                    'code': "view_workflow_step_id",
                    'value': item['viewWorkFlowStepId'] if item['viewWorkFlowStepId'] is not None else settings.DEFAULT_FILE_FIELDS['viewWorkFlowStepId']
                }
            ]
            item['attributes'] = attributes
            item.update({'currentStep': current_step})

        data_response = {
            "items": items,
            "child_nodes": [],
            "total": data["total"]["value"],
            "success": True,
            "error": None,
            "message": None
        }

        for label in labels_list:
            if label['type'] in ['group', 'bodypart', 'other']:
                continue
            if label['parent_1_id'] == labels_id_list[-1] and label['is_deleted'] == '0' and label['is_separated'] == '0' and label['merged_to'] == "":
                data_response['child_nodes'].append(label['value'])

        return 200, data_response
    def ml_dataset_fetch_all_ml_project(self, user, params):
        url = "{}/fetchlistmlprojects".format(self.get_url())
        res = dms_requests.get(url, user=user, params=params)
        json_data = res.json()
        status_code = res.status_code

        return status_code, json_data
