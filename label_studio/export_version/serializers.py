from rest_framework import serializers
from label_studio.dms_connector.serializers import FilterSerializer

EXPORT_TYPE_CHOICE = (
    ('Csv', 'Csv'),
    ('<PERSON><PERSON>', '<PERSON><PERSON>'),
    ('Dataset', 'Dataset')
)
SORTING_CHOICE = (
    ('asc', 'asc'),
    ('desc', 'desc')
)

CONVERTOR_CHOICES = [
    ('default', 'default'),
    ('Bounding-Box', 'Bounding-Box'),
    ('Circle', 'Circle'),
    ('Polygon', 'Polygon'),
    ('Scoring', 'Scoring'),
    ('Taxonomy', 'Taxonomy'),
    ('Bounding-Box,Polygon', 'Bounding-Box,Polygon'),
    ('Polygon,Scoring', 'Polygon,Scoring'),
]

CONVERTOR_CHOICES_SINGLE = [
    ('default', 'default'),
    ('BoundingBox', 'BoundingBox'),
    ('Circle', 'Circle'),
    ('Polygon', 'Polygon'),
    ('Scoring', 'Scoring'),
    ('Brush', 'Brush'),
    ('Taxonomy', 'Taxonomy'),
    ('OneK', 'OneK'),
]

class ExportVersionFilterSerializer(serializers.Serializer):
    page = serializers.IntegerField(required = False)
    pageSize = serializers.IntegerField(required = False)
    sortBy = serializers.CharField(max_length=1000, required=False, allow_null=True)
    orderBy = serializers.ChoiceField(choices=SORTING_CHOICE, required = False)
    keyword = serializers.CharField(max_length=1000, required=False, allow_null=True)

class ExportVersionCreateSerializer(serializers.Serializer):
    versionName = serializers.CharField(max_length=1000, required=False, allow_null=True)
    exportType = serializers.ChoiceField(choices=EXPORT_TYPE_CHOICE, required=False)
    includeAnnotated = serializers.BooleanField(required=False)
    includeDiscussion = serializers.BooleanField(required=False)
    isArchived = serializers.BooleanField(required=False)
    workflowStepIds = serializers.CharField(max_length=1000, required=False, allow_null=True)
    quantity = serializers.IntegerField(required=False, allow_null=True)
    fromDate = serializers.DateTimeField(required=False, allow_null=True)
    toDate = serializers.DateTimeField(required=False, allow_null=True)
    fromSize = serializers.IntegerField(required=False, allow_null=True)
    toSize = serializers.IntegerField(required=False, allow_null=True)
    toolLabeling = serializers.CharField(max_length=1000, required=False, allow_null=True)

class ExportVersionComparePostSerializer(ExportVersionFilterSerializer):
    firstVersion = serializers.CharField(max_length=1000, required=False, allow_null=True)
    secondVersion = serializers.CharField(max_length=1000, required=False, allow_null=True)
    attributeIds = serializers.ListField(child=serializers.UUIDField(required=False, allow_null=True))

class ExportVersionFileSerializer(serializers.Serializer):
    fileId = serializers.UUIDField(required=False)
    convert_data = serializers.ChoiceField(choices=CONVERTOR_CHOICES, default='default', required=False)

class ExportVersionZipFileDownloadSerializer(serializers.Serializer):
    revisionId = serializers.UUIDField(required=False, allow_null=True)

class ExportVersionFileConvertorSerializer(serializers.Serializer):
    fileId = serializers.UUIDField(required=False)
    exportType = serializers.ChoiceField(choices=EXPORT_TYPE_CHOICE, required=False, allow_null=True, default='Csv')
    convert_data = serializers.ListField(child=serializers.ChoiceField(choices=CONVERTOR_CHOICES_SINGLE, default='default', required=False))
    advancedConfigId = serializers.UUIDField(required=False)

class ExportVersionFileConvertorCOCOSerializer(serializers.Serializer):
    revisionId = serializers.UUIDField(required=False)
    fileId = serializers.UUIDField(required=False)
    exportType = serializers.ChoiceField(choices=EXPORT_TYPE_CHOICE, required=False, allow_null=True, default='Csv')

class ExportVersionFileConvertorYOLOSerializer(serializers.Serializer):
    revisionId = serializers.UUIDField(required=False)
    fileId = serializers.UUIDField(required=False)
    exportType = serializers.ChoiceField(choices=EXPORT_TYPE_CHOICE, required=False, allow_null=True, default='Csv')

class ExportVersionFileSyncClearMLSerializer(serializers.Serializer):
    revisionId = serializers.UUIDField(required=False)

class ExportVersionConfirmDownloadZipSerializer(serializers.Serializer):
    confirm = serializers.BooleanField(required=False)
    task_id = serializers.UUIDField()

class ExportVersionDeleteSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.UUIDField(required=False, allow_null=True))

class ExportVersionCreateDataZipFileSerializer(serializers.Serializer):
    revisionId = serializers.UUIDField(required=False, allow_null=True)

class ExportVersionCreateEventMLExportSerializer(serializers.Serializer):
    versionName = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    exportType = serializers.ChoiceField(choices=EXPORT_TYPE_CHOICE, required=False, allow_null=True)
    includeAnnotated = serializers.BooleanField(required=False, allow_null=True)
    includeDiscussion = serializers.BooleanField(required=False, allow_null=True)
    isArchived = serializers.BooleanField(required=False, allow_null=True)
    workflowStepIds = serializers.CharField(max_length=1000, required=False, allow_null=True)
    quantity = serializers.IntegerField(required=False, allow_null=True)
    fromDate = serializers.DateTimeField(required=False, allow_null=True)
    toDate = serializers.DateTimeField(required=False, allow_null=True)
    fromSize = serializers.IntegerField(required=False, allow_null=True)
    toSize = serializers.IntegerField(required=False, allow_null=True)
    toolLabeling = serializers.CharField(max_length=1000, required=False, allow_null=True)
    mlProjectId = serializers.UUIDField(required=False, allow_null=True)
    fileVersionId = serializers.UUIDField(required=False, allow_null=True)