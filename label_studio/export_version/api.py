import logging
import os
from drf_yasg.utils import swagger_auto_schema

from rest_framework.response import Response

from django.utils.decorators import method_decorator
from django.http import HttpResponse, StreamingHttpResponse, FileResponse
from django.conf import settings
from export_version.serializers import *
from export_version.serializers_response import *
from label_studio.core.permissions import ViewClassPermission, all_permissions
from label_studio.dms_connector.api import AuthenticatedAPIView
from label_studio.dms_connector.modules.version import DMS_VERSION_API
from label_studio.dms_connector.modules.version_convertor import DMS_VERSION_CONVERTOR_API

from export_version.tasks import create_zip_export_version, delete_zip_export_version, \
    check_status_create_export_version
from wsgiref.util import FileWrapper

logger = logging.getLogger(__name__)


@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Get all export-ready versions of a specific project',
    operation_description='Get all export-ready versions of a specific project',
    query_serializer=ExportVersionFilterSerializer,
    responses={200: ExportVersionListResponseSerializer}
))
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Create an export version in a specific project',
    operation_description='Create an export version in a specific project',
    request_body=ExportVersionCreateSerializer
))
class ExportVersionListCreateAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.projects_view,
        POST=all_permissions.projects_view,
    )

    def get(self, request, project_id, *args, **kwargs):
        user = request.user
        filters = ExportVersionFilterSerializer(request.query_params).data
        status_code, json_data = DMS_VERSION_API().list(user, params=filters, project=project_id)

        return Response(json_data, status_code)

    def post(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = ExportVersionCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        status_code, json_data = DMS_VERSION_API().create(user, serializer.data, project=project_id)
        # check_status_create_export_version.delay(user_id=user.dms_id, project_id=project_id,
        #                                          revisionId=json_data["data"])

        return Response(json_data, status_code)


@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Delete an export version in a specific project',
    operation_description='Delete an export version in a specific project'
))
class ExportVersionDetailAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        DELETE=all_permissions.projects_view,
    )

    def delete(self, request, project_id, export_version_id, *args, **kwargs):
        user = request.user
        status_code, json_data = DMS_VERSION_API().delete(user, id=export_version_id, project=project_id)

        return Response(json_data, status_code)
    
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Delete multiple export versions in a specific project',
    operation_description='Delete multiple export versions in a specific project',
    query_serializer=ExportVersionDeleteSerializer
))
class ExportVersionDeleteMultipleAPI(AuthenticatedAPIView):

    def delete(self, request, project_id, *args, **kwargs):
        user = request.user
        data = request.query_params.copy()
        if 'ids' in data:
            data['ids'] = [x.strip() for x in data['ids'].split(',')]

        params = ExportVersionDeleteSerializer(data).data

        status_code, json_data = DMS_VERSION_API().delete_multiple_export_version(user, project_id=project_id, params=params)

        return Response(json_data, status_code)


@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Get information of compare versions',
    operation_description='Get information of compare versions'
))
class ExportVersionCompareInfoAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.projects_view,
    )

    def get(self, request, project_id, *args, **kwargs):
        user = request.user
        status_code, json_data = DMS_VERSION_API().get_compare_version_info(user, project_id)

        return Response(json_data, status_code)


@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Make export version comparison',
    operation_description='Make export version comparison',
    request_body=ExportVersionComparePostSerializer
))
class ExportVersionComparePostAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        POST=all_permissions.projects_view,
    )

    def post(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = ExportVersionComparePostSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        status_code, json_data = DMS_VERSION_API().post_compare_version(user, project_id, serializer.data)

        return Response(json_data, status_code)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Create data zip file of export version',
    operation_description='Create data zip file of export version',
    request_body=ExportVersionCreateDataZipFileSerializer,
    responses={200:DetailResponseSerializer}
))
class ExportVersionCreateZipAPI(AuthenticatedAPIView):

    def post(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = ExportVersionCreateDataZipFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_VERSION_API().create_export_zip_file(user, project_id, serializer.data)

        return Response(json_data, status_code)
import sys
from time import sleep
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Download data zip file of export version',
    operation_description='Download data zip file of export version',
    query_serializer=ExportVersionZipFileDownloadSerializer
))
class ExportVersionDownloadDataZipAPI(AuthenticatedAPIView):

    def get(self, request, project_id, *args, **kwargs):
        user = request.user

        serializer = ExportVersionZipFileDownloadSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        zip_file_path = DMS_VERSION_API().download_zip_file_export_version(user, project_id, serializer.data)

        minioClient = settings.MINIO_CLIENT
        result = minioClient.stat_object(bucket_name=settings.MINIO_BUCKET_NAME, object_name=zip_file_path)
        file_size = result.size
        file_name = os.path.basename(result.object_name)

        def generator():
            max_retries = 3  # Number of retries
            retry_delay = 1  # Delay in seconds between retries

            with minioClient.get_object(bucket_name=settings.MINIO_BUCKET_NAME,
                                 object_name=zip_file_path) as file_data:
                while True:
                    for attempt in range(max_retries):
                        data = file_data.read(32 * 1024 * 1024)
                        if data:  # If data is not None, break out of retry loop
                            yield data
                            break
                        else:
                            if attempt < max_retries - 1:
                                sleep(retry_delay)  # Wait before retrying
                            else:
                                print("Max retries exceeded while reading the file")
                    if not data:  # End of file reached
                        break

        response = StreamingHttpResponse(generator(), content_type='application/octet-stream', headers={
            'Content-Length': file_size,
        })
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        response['Cache-Control'] = 'no-cache'
        response['Transfer-Encoding'] = 'chunked'

        return response

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Get information file of specific export version',
    operation_description='Get information file of specific export version',
    query_serializer=ExportVersionFileSerializer
))
class ExportVersionFileAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.projects_view,
    )

    def get(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = ExportVersionFileSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_VERSION_API().list(user, project=project_id)
        export_version_list = json_data["items"]
        file_name = None
        for export_version in export_version_list:
            if export_version["fileId"] == serializer.data["fileId"]:
                file_name_without_extension = export_version["name"].split("/")[-1]
                file_extension = export_version["dataType"].lower()
                file_name = "{}.{}".format(file_name_without_extension, file_extension)

        status_code, file = DMS_VERSION_API().download_file_export_version_with_converter(user, project_id, serializer.data)

        if status_code != 200:
            if file['success'] == False:
                return Response(file, status_code)

        return HttpResponse(file, headers={
            'Content-Type': 'application/octet-stream',
            'Content-Disposition': 'attachment; filename="{}"'.format(file_name),
        })
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Export Versions - Convertor'],
    operation_summary='Get information file of specific export version',
    operation_description='Get information file of specific export version',
    query_serializer=ExportVersionFileConvertorSerializer
))
class ExportVersionFileNewConvertorAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.projects_view,
    )

    def get(self, request, project_id, *args, **kwargs):
        user = request.user
        data = request.query_params.copy()

        if 'convert_data' in data:
            data['convert_data'] = [x.strip() for x in data['convert_data'].split(',')]
            
        params = ExportVersionFileConvertorSerializer(data).data

        status_code, json_data = DMS_VERSION_API().list(user, project=project_id)
        export_version_list = json_data["items"]
        file_name = None
        for export_version in export_version_list:
            if export_version["fileId"] == params["fileId"]:
                if export_version['imageAmount'] == 0:
                    return Response({"success": False, "message": "This export version has no image!"}, status=400, content_type='application/json')
                
                file_name_without_extension = export_version["name"].split("/")[-1]
                file_extension = export_version["dataType"].lower()
                file_name = "{}.{}".format(file_name_without_extension, file_extension)

        status_code, file = DMS_VERSION_CONVERTOR_API().download_file_export_version_with_converter(user, project_id, params=params)

        if status_code != 200:
            if file['success'] == False:
                return Response(file, status_code)

        return HttpResponse(file, headers={
            'Content-Type': 'application/octet-stream',
            'Content-Disposition': 'attachment; filename="{}"'.format(file_name),
        })

@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Create zip file include images and export version file',
    operation_description='Create zip file include images and export version file',
    request_body=ExportVersionFileSerializer
))
class CreateExportVersionZipAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.projects_view,
    )

    def put(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = ExportVersionFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        create_zip_export_version.delay(user_id=user.dms_id, project_id=project_id, file_id=serializer.data)

        return Response()


@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Confirm download export version zip file',
    operation_description='Confirm download export version zip file',
    query_serializer=ExportVersionConfirmDownloadZipSerializer
))
class ConfirmDownloadExportVersionAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.projects_view,
    )

    def get(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = ExportVersionConfirmDownloadZipSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        if serializer.data['confirm'] == True:
            # download
            zip_file_path = "label_studio/export_version/export_data_temp/{}.zip".format(serializer.data['task_id'])

            # sending response 
            try:
                response = HttpResponse(FileWrapper(open(zip_file_path, 'rb')), headers={
                    'Content-Type': 'application/x-zip-compressed',
                    'Content-Disposition': 'attachment; filename="{}.zip"'.format(serializer.data['task_id']),
                })
            except:
                return Response({"message": "Zip file not created yet!"}, status=404)
            return response
        else:
            delete_zip_export_version.delay(task_id=serializer.data['task_id'])

        return Response()

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Export Versions - Convertor'],
    operation_summary='Active convert to coco format',
    operation_description='Active convert to coco format',
    request_body=ExportVersionFileConvertorCOCOSerializer
))
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Export Versions - Convertor'],
    operation_summary='Download data export version as coco format',
    operation_description='Download data export version as coco format',
    query_serializer=ExportVersionZipFileDownloadSerializer
))
class ExportVersionFileConvertorCOCOAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.projects_view,
    )

    def post(self, request, project_id, *args, **kwargs):
        user = request.user

        serializer = ExportVersionFileConvertorCOCOSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        params = serializer.data

        status_code, json_data = DMS_VERSION_API().list(user, project=project_id)
        export_version_list = json_data["items"]
        file_name = None
        for export_version in export_version_list:
            if export_version["fileId"] == params["fileId"]:
                if export_version['isConverter']:
                    return Response({"success": False, "message": "This file is already converted!"}, status=200)
                if export_version['imageAmount'] == 0:
                    return Response({"success": False, "message": "This export version has no image!"}, status=200)
                
                file_name_without_extension = export_version["name"].split("/")[-1]
                file_name = file_name_without_extension
                params["file_name"] = file_name

        status_code, json_data = DMS_VERSION_CONVERTOR_API().convert_to_coco_format(user, project_id, params=params)

        return Response(json_data, status_code)
    
    def get(self, request, project_id, *args, **kwargs):
        user = request.user

        serializer = ExportVersionZipFileDownloadSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        coco_file_path = DMS_VERSION_API().download_coco_file_export_version(user, project_id, serializer.data)

        if coco_file_path is None:
            return Response({"success": False, "message": "Coco file not created yet!"}, status=200)

        minioClient = settings.MINIO_CLIENT
        result = minioClient.stat_object(bucket_name=settings.MINIO_BUCKET_NAME, object_name=coco_file_path)
        file_size = result.size
        file_name = os.path.basename(result.object_name)

        def generator():
            with minioClient.get_object(bucket_name=settings.MINIO_BUCKET_NAME,
                                 object_name=coco_file_path) as file_data:
                while True:
                    data = file_data.read(32 * 1024)
                    if not data:
                        break
                    yield data

        response = StreamingHttpResponse(generator(), content_type='application/octet-stream', headers={
            'Content-Length': file_size,
        })
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        response['Cache-Control'] = 'no-cache'
        response['Transfer-Encoding'] = 'chunked'

        return response
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Export Versions - Convertor'],
    operation_summary='Active convert to yolo format',
    operation_description='Active convert to yolo format',
    request_body=ExportVersionFileConvertorYOLOSerializer
))
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Export Versions - Convertor'],
    operation_summary='Download data export version as yolo format',
    operation_description='Download data export version as yolo format',
    query_serializer=ExportVersionZipFileDownloadSerializer
))
class ExportVersionFileConvertorYOLOAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.projects_view,
    )

    def post(self, request, project_id, *args, **kwargs):
        user = request.user

        serializer = ExportVersionFileConvertorYOLOSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        params = serializer.data

        status_code, json_data = DMS_VERSION_API().list(user, project=project_id)
        export_version_list = json_data["items"]
        file_name = None
        for export_version in export_version_list:
            if export_version["fileId"] == params["fileId"]:
                if export_version['isYoloConverter']:
                    return Response({"success": False, "message": "This file is already converted!"}, status=200)
                
                if export_version['imageAmount'] == 0:
                    return Response({"success": False, "message": "This export version has no image!"}, status=200)
                
                file_name_without_extension = export_version["name"].split("/")[-1]
                file_name = file_name_without_extension
                params["file_name"] = file_name
                params["date"] = export_version["date"]

        status_code, json_data = DMS_VERSION_CONVERTOR_API().convert_to_yolo_format(user, project_id, params=params)

        return Response(json_data, status_code)
    
    def get(self, request, project_id, *args, **kwargs):
        user = request.user

        serializer = ExportVersionZipFileDownloadSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        yolo_file_path = DMS_VERSION_API().download_yolo_file_export_version(user, project_id, serializer.data)

        if yolo_file_path is None:
            return Response({"success": False, "message": "Coco file not created yet!"}, status=200)

        minioClient = settings.MINIO_CLIENT
        result = minioClient.stat_object(bucket_name=settings.MINIO_BUCKET_NAME, object_name=yolo_file_path)
        file_size = result.size
        file_name = os.path.basename(result.object_name)

        def generator():
            with minioClient.get_object(bucket_name=settings.MINIO_BUCKET_NAME,
                                 object_name=yolo_file_path) as file_data:
                while True:
                    data = file_data.read(32 * 1024)
                    if not data:
                        break
                    yield data

        response = StreamingHttpResponse(generator(), content_type='application/octet-stream', headers={
            'Content-Length': file_size,
        })
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        response['Cache-Control'] = 'no-cache'
        response['Transfer-Encoding'] = 'chunked'

        return response
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Export Versions - Convertor'],
    operation_summary='Active convert to clearml format',
    operation_description='Active convert to clearml format',
    request_body=ExportVersionFileSyncClearMLSerializer
))
class ExportVersionFileSyncClearMLAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.projects_view,
    )

    def post(self, request, project_id, *args, **kwargs):
        user = request.user

        serializer = ExportVersionFileSyncClearMLSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        params = serializer.data

        status_code, json_data = DMS_VERSION_CONVERTOR_API().sync_to_clearml(user, project_id, params=params)

        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Create eventml export',
    operation_description='Create eventml export',
    request_body=ExportVersionCreateEventMLExportSerializer
))
class ExportVersionCreateEventMLExportAPI(AuthenticatedAPIView):

    def post(self, request, project_id, *args, **kwargs):
        user = request.user

        serializer = ExportVersionCreateEventMLExportSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data = serializer.data

        status_code, json_data = DMS_VERSION_CONVERTOR_API().create_event_ml_export(user, project_id, data)

        return Response(json_data, status_code)
    
@method_decorator(name='patch', decorator=swagger_auto_schema(
    tags=['Export Versions'],
    operation_summary='Update status of ml export event',
    operation_description='Update status of ml export event'
))
class ExportVersionUpdateStatusMLExportAPI(AuthenticatedAPIView):

    def patch(self, request, project_id, export_event_id, *args, **kwargs):
        user = request.user
        status_code, json_data = DMS_VERSION_CONVERTOR_API().update_status_event_ml_export(user, project_id, export_event_id)

        return Response(json_data, status_code)