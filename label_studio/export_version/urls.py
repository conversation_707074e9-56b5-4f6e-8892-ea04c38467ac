from django.urls import include, path
from export_version import api

app_name = "export_version"

_api_urlpatterns = [
    path("", api.ExportVersionListCreateAPI.as_view()),
    path("<uuid:export_version_id>", api.ExportVersionDetailAPI.as_view()),
    path("compare_info", api.ExportVersionCompareInfoAPI.as_view()),
    path("compare", api.ExportVersionComparePostAPI.as_view()),
    path("file", api.ExportVersionFileAPI.as_view()),
    path("export_convertor/file", api.ExportVersionFileNewConvertorAPI.as_view()),
    path("delete_multiple", api.ExportVersionDeleteMultipleAPI.as_view()),
    path("data_zip_file", api.ExportVersionCreateZipAPI.as_view()),
    path("zip_file", api.ExportVersionDownloadDataZipAPI.as_view()),
    path("convert_to_coco", api.ExportVersionFileConvertorCOCOAPI.as_view()),
    path("convert_to_yolo", api.ExportVersionFileConvertorYOLOAPI.as_view()),
    path("sync_to_clearml", api.ExportVersionFileSyncClearMLAPI.as_view()),
    # path("create_zip/", api.CreateExportVersionZipAPI.as_view()),
    # path("confirm_download_zip/", api.ConfirmDownloadExportVersionAPI.as_view()),
    path("event_ml_export", api.ExportVersionCreateEventMLExportAPI.as_view()),
    path("event_ml_export/<uuid:export_event_id>/update_status", api.ExportVersionUpdateStatusMLExportAPI.as_view()),
]

urlpatterns = [
    path("api/projects/<uuid:project_id>/export_version/", include(_api_urlpatterns)),
]