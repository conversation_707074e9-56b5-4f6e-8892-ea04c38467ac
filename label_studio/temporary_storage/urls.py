"""This file and its contents are licensed under the Apache License 2.0. Please see the included NOTICE for copyright information and LICENSE for a copy of the license.
"""
from django.urls import include, path

from . import api, views

app_name = 'temporary_storage'

# reverse for tasks:api:name
_api_urlpatterns = [
    # CRUD
    path('', api.TempStorageBatchFilesListAPI.as_view(), name='temp-storage-list'),
    path('/<uuid:batch_id>', api.TempStorageBatchFilesDetailAPI.as_view(), name='temp-storage-detail'),
    path('/upload', api.TempStorageBatchFilesUploadFileAPI.as_view(), name='temp-storage-upload'),
    path('/upload_zip', api.TempStorageBatchFilesUploadLargeZipAPI.as_view(), name='temp-storage-upload-zip'),
    path('/<uuid:temp_storage_file_id>/image', api.TempStorageBatchFilesGetImageAPI.as_view(), name='temp-storage-get-image'),
    path('/<uuid:temp_storage_id>/files', api.TempStorageBatchFilesFetchFilesAPI.as_view(), name='temp-storage-fetch-files'),
    path('/<uuid:temp_storage_id>/files/single', api.TempStorageBatchFilesFetchFileSinglesAPI.as_view(), name='temp-storage-fetch-single-file'),
    path('/<uuid:temp_storage_id>/detail', api.TempStorageBatchFilesFetchFileDetailAPI.as_view(), name='temp-storage-fetch-file-detail'),
    path('/<uuid:temp_storage_id>/delete_batch', api.TempStorageBatchDeleteBatchAPI.as_view(), name='temp-storage-delete-batch'),
    path('/<uuid:temp_storage_id>/delete_multiple_files', api.TempStorageBatchDeleteMultipleFilesAPI.as_view(), name='temp-storage-delete-muitiple-files'),
    path('/<uuid:temp_storage_id>/project_file_cluster/<uuid:cluster_id>', api.TempStorageBatchEditClusterAPI.as_view(), name='temp-storage-edit-cluster'),
    path('/delete_multiple_batches', api.TempStorageBatchDeleteMultipleBatchAPI.as_view(), name='temp-storage-delete-muitiple-batches'),
    path('/<uuid:storage_batch_id>/transfer', api.TempStorageBatchFilesTransferAPI.as_view(), name='temp-storage-transfer'),
    path('/<uuid:storage_batch_id>/transfer_multiple_files', api.TempStorageBatchMultipleFilesTransferAPI.as_view(), name='temp-storage-transfer-multiple-files'),
    path('/<uuid:storage_batch_id>/transfer_single_file', api.TempStorageBatchSingleFileTransferAPI.as_view(), name='temp-storage-transfer-single-file'),
    path('/<uuid:batch_id>/deduplicate_reports', api.TempStorageBatchDeduplicateReportAPI.as_view(), name='temp-storage-deduplicate-reports'),
    path('/<uuid:batch_id>/deduplicate_reports/add_clusters', api.TempStorageBatchAddClustersDeduplicateReportAPI.as_view(), name='temp-storage-deduplicate'),
    path('/<uuid:batch_id>/deduplicate_reports/<uuid:cluster_id>/get_files', api.TempStorageBatchFetchFilesClusterDeduplicateReportAPI.as_view(), name='temp-storage-get-files-in-cluster'),
    path('/<uuid:batch_id>/deduplicate_reports/cancel_dedup_report', api.TempStorageBatchCancelingDeduplicateReportAPI.as_view(), name='temp-storage-cancel-deduplicate-report'),
    path('/<uuid:batch_id>/deduplicate_reports/<uuid:cluster_id>/file_thresholds/<uuid:file_threshold_id>', api.TempStorageBatchEditFileThresholdAPI.as_view(), name='temp-storage-edit-file-threshold'),
    path('/<uuid:batch_id>/deduplicate_reports/<uuid:cluster_id>/remove_all', api.TempStorageBatchRemoveFilesClusterAllAPI.as_view(), name='temp-storage-remove-all-files-in-cluster'),
    path('/<uuid:batch_id>/deduplicate_reports/<uuid:cluster_id>/remove_files', api.TempStorageBatchRemoveFilesClusterAPI.as_view(), name='temp-storage-remove-files-in-cluster'),
    path('/<uuid:batch_id>/deduplicate_reports/<uuid:cluster_id>/delete_all', api.TempStorageBatchDeleteFilesClusterAllAPI.as_view(), name='temp-storage-delete-all-files-in-cluster'),
    path('/<uuid:batch_id>/deduplicate_reports/<uuid:cluster_id>/delete_files', api.TempStorageBatchDeleteFilesClusterAPI.as_view(), name='temp-storage-delete-files-in-cluster'),
    path('/<uuid:batch_id>/deduplicate_reports/complete_deduplication', api.TempStorageBatchCompleteDeduplicationAPI.as_view(), name='temp-storage-complete-deduplication'),
]

__api_urlpatterns = [
    path('/applytoworkflow/<uuid:workflowStep_id>', api.TempStoragePostApplySystemModelAPI.as_view(), name='apply-system-model'),
]

___api_urlpatterns = [
    path('/<uuid:temp_storage_file_id>', api.TempStorageReasonsFetchReasonsAPI.as_view(), name='temp-storage-reason'),
    path('/<uuid:temp_storage_file_id>/reason', api.TempStorageReasonsCreateReasonAPI.as_view(), name='temp-storage-reason-create'),
    path('/<uuid:temp_storage_file_id>/reason/<uuid:reason_id>', api.TempStorageReasonsDetailReasonAPI.as_view(), name='temp-storage-reason-detail'),
]

urlpatterns = [
    path('api/projects/<uuid:project_id>/temp_storage_batch_files', include((_api_urlpatterns, app_name), namespace='api')),
    path('api/projects/<uuid:project_id>/project_system_models', include((__api_urlpatterns, app_name), namespace='api')),
    path('api/projects/<uuid:project_id>/temp_storage_reasons', include((___api_urlpatterns, app_name), namespace='api')),
]
