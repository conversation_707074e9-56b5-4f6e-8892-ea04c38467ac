import logging
import os
from drf_yasg.utils import swagger_auto_schema

from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, MultiPartParser

from django.utils.decorators import method_decorator
from django.http import HttpResponse, StreamingHttpResponse, FileResponse
from django.conf import settings
from django.core.files.storage import FileSystemStorage

from temporary_storage.serializers import *
from temporary_storage.serializers_response import *
from temporary_storage.tasks import upload_data_v2
from dms_connector.api import AuthenticatedAPIView, DMS_TEMP_STORAGE_API, DMS_TEMP_STORAGE_DEDUP_API, \
            DMS_TEMP_STORAGE_APPLY_SYSTEM_MODEL_API, DMS_TEMP_STORAGE_REASONS_API

logger = logging.getLogger(__name__)


@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Fetch temp batch storage list and filter',
    operation_description='Fetch temp batch storage list and filter',
    query_serializer=TempStorageBatchFilesFilterSerializer,
    responses={200: TempStorageBatchFilesFilterResponseSerializer}
))
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Create temp batch storage',
    operation_description='Create temp batch storage',
    request_body=TempStorageBatchFilesCreateSerializer,
))
@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Update temp batch storage',
    operation_description='Update temp batch storage',
    request_body=TempStorageBatchFilesUpdateSerializer
))
class TempStorageBatchFilesListAPI(AuthenticatedAPIView):

    def get(self, request, project_id, *args, **kwargs):
        user = request.user
        filters = TempStorageBatchFilesFilterSerializer(data=request.query_params)
        filters.is_valid(raise_exception=True)

        params = filters.data
        status_code, json_data = DMS_TEMP_STORAGE_API().list(user=user, params=params, project=project_id)
        
        return Response(json_data, status_code)
    
    def post(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchFilesCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        status_code, json_data = DMS_TEMP_STORAGE_API().create(user=user, project=project_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
    def put(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchFilesUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_API().put(user=user, project=project_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Get temp batch storage detail',
    operation_description='Get temp batch storage detail',
))
@method_decorator(name='patch', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Update temp batch storage detail',
    operation_description='Update temp batch storage detail',
    request_body=TempStorageEditBatchDetailSerializer
))
class TempStorageBatchFilesDetailAPI(AuthenticatedAPIView):

    def get(self, request, project_id, batch_id, *args, **kwargs):
        user = request.user
        status_code, json_data = DMS_TEMP_STORAGE_API().get_detail_batch(user=user, project_id=project_id, batch_id=batch_id)
        
        return Response(json_data, status_code)
    
    def patch(self, request, project_id, batch_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageEditBatchDetailSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_API().edit_detail_batch(user=user, project_id=project_id, batch_id=batch_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Upload file to temp batch storage',
    operation_description='Upload file to temp batch storage',
    request_body=TempStorageBatchFilesUploadFileSerializer,
))
class TempStorageBatchFilesUploadFileAPI(AuthenticatedAPIView):
    parser_classes = (JSONParser, FormParser, MultiPartParser)

    def post(self, request, project_id, *args, **kwargs):
        user = request.user
        file = request.FILES['file']

        serializer = TempStorageBatchFilesUploadFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_API().upload(user=user, project=project_id, file=file, data=serializer.data)
        return Response(json_data, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Get image from temp batch storage',
    operation_description='Get image from temp batch storage',
    query_serializer=TempStorageBatchFilesGetImageSerializer
))
class TempStorageBatchFilesGetImageAPI(AuthenticatedAPIView):

    def get(self, request, project_id, temp_storage_file_id, *args, **kwargs):
        user = request.user
        filters = TempStorageBatchFilesGetImageSerializer(data=request.query_params)
        filters.is_valid(raise_exception=True)

        params = filters.data
        status_code, img = DMS_TEMP_STORAGE_API().get_image(user=user, project_id=project_id, temp_storage_file_id=temp_storage_file_id, params=params)
        try:
            if img[:9].decode('utf8') == '<!doctype':
                return HttpResponse('Not Found', status=404)
        except:
            pass
        
        return HttpResponse(img, status=status_code, content_type='image/png')
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Fetch files from temp batch storage',
    operation_description='Fetch files from temp batch storage',
    query_serializer=TempStorageBatchFilesFetchFilesSerializer,
    responses={200: TempStorageBatchFilesGetFileResponseSerializer}
))
@method_decorator(name='patch', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Update files in temp batch storage',
    operation_description='Update files in temp batch storage',
    request_body=TempStorageBatchUpdateFilesListSerializer
))
class TempStorageBatchFilesFetchFilesAPI(AuthenticatedAPIView):

    def get(self, request, project_id, temp_storage_id, *args, **kwargs):
        user = request.user
        data = request.query_params.copy()
        if 'workflowStepIds' in data:
            data['workflowStepIds'] = [x.strip() for x in data['workflowStepIds'].split(';')]
        if 'stepStatus' in data:
            data['stepStatus'] = [x.strip() for x in data['stepStatus'].split(';')]

        filters = TempStorageBatchFilesFetchFilesSerializer(data).data

        status_code, json_data = DMS_TEMP_STORAGE_API().fetch_images(user=user, project_id=project_id, temp_storage_id=temp_storage_id, params=filters)
        
        return Response(json_data, status_code)
    
    def patch(self, request, project_id, temp_storage_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchUpdateFilesListSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_API().update_multiple_files(user=user, project_id=project_id, temp_storage_id=temp_storage_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='patch', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Update file in temp batch storage',
    operation_description='Update file in temp batch storage',
    request_body=TempStorageBatchUpdateFileSerializer
))
class TempStorageBatchFilesFetchFileSinglesAPI(AuthenticatedAPIView):

    def patch(self, request, project_id, temp_storage_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchUpdateFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_API().update_single_file(user=user, project_id=project_id, temp_storage_id=temp_storage_id, data=serializer.data)
        
        return Response(json_data, status_code)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Fetch file detail from temp batch storage',
    operation_description='Fetch file detail from temp batch storage',
    query_serializer=TempStorageBatchFilesGetImageSerializer,
    responses={200: TempStorageBatchFilesGetFileDetailResponseSerializer}
))
class TempStorageBatchFilesFetchFileDetailAPI(AuthenticatedAPIView):

    def get(self, request, project_id, temp_storage_id, *args, **kwargs):
        user = request.user

        filters = TempStorageBatchFilesGetImageSerializer(data=request.query_params)
        filters.is_valid(raise_exception=True)

        params = filters.data

        status_code, json_data = DMS_TEMP_STORAGE_API().fetch_file_detail(user=user, project_id=project_id, temp_storage_id=temp_storage_id, params=params)
        
        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Temporary Storage'], operation_summary="Confirm upload",
    operation_description="Confirm upload data in zip file and csv file",
    request_body=TempStorageBatchFilesUploadZipSerializer
))
class TempStorageBatchFilesUploadLargeZipAPI(AuthenticatedAPIView):

    def post(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchFilesUploadZipSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        parent_path = serializer.data['parent_path']
        zip_id = serializer.data['id']
        tempStorageBatchFileId = serializer.data['tempStorageBatchFileId']
        file = request.FILES['file']
        preReadyToWorkProject = serializer.data.get('preReadyToWorkProject', False)
        nextWorkFlowStep = serializer.data.get('nextWorkFlowStep', None)
        nextWorkFlowStepId = serializer.data.get('nextWorkFlowStepId', None)
        nextWorkFlowState = serializer.data.get('nextWorkFlowState', None)

        file_save = FileSystemStorage().save(name=file.name, content=file)
        if preReadyToWorkProject:
            upload_data_v2.delay(user.dms_id, project_id=project_id, file_save=file_save, zip_id=zip_id, parent_path=parent_path, tempStorageBatchFileId=tempStorageBatchFileId, 
                                 preReadyToWorkProject=preReadyToWorkProject, 
                                 nextWorkFlowStep=nextWorkFlowStep, 
                                 nextWorkFlowStepId=nextWorkFlowStepId, 
                                 nextWorkFlowState=nextWorkFlowState)
        else:
            upload_data_v2.delay(user.dms_id, project_id=project_id, file_save=file_save, zip_id=zip_id, parent_path=parent_path, tempStorageBatchFileId=tempStorageBatchFileId)

        return Response({
            "success": True,
            "errorCode": None,
            "message": None,
            "data": None
        }, 200)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Transfer data from batch to project',
    operation_description='Transfer data from batch to project'
))
class TempStorageBatchFilesTransferAPI(AuthenticatedAPIView):

    def post(self, request, project_id, storage_batch_id, *args, **kwargs):
        user = request.user

        status_code, json_data = DMS_TEMP_STORAGE_API().transfer_data_prj(user=user, project_id=project_id, storage_batch_id=storage_batch_id)
        
        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Transfer data from batch to project multiple files',
    operation_description='Transfer data from batch to project multiple files',
    request_body=TempStorageBatchTransferMultipleFilesSerializer
))
class TempStorageBatchMultipleFilesTransferAPI(AuthenticatedAPIView):

    def post(self, request, project_id, storage_batch_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchTransferMultipleFilesSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_API().transfer_data_prj_multiple_files(user=user, project_id=project_id, storage_batch_id=storage_batch_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Transfer data from batch to project single files',
    operation_description='Transfer data from batch to project single files',
    request_body=TempStorageBatchTransferFileSerializer
))
class TempStorageBatchSingleFileTransferAPI(AuthenticatedAPIView):

    def post(self, request, project_id, storage_batch_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchTransferFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_API().transfer_data_prj_single_file(user=user, project_id=project_id, storage_batch_id=storage_batch_id, data=serializer.data)
        
        return Response(json_data, status_code)

@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Delete temp batch storage',
    operation_description='Delete temp batch storage'
))
class TempStorageBatchDeleteBatchAPI(AuthenticatedAPIView):
    
    def delete(self, request, project_id, temp_storage_id, *args, **kwargs):
        user = request.user
        status_code, json_data = DMS_TEMP_STORAGE_API().delete_batch_images(user=user, project_id=project_id, temp_storage_id=temp_storage_id)
        
        return Response(json_data, status_code)
    
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Delete temp storage multiple batch',
    operation_description='Delete temp storage multiple batch',
    request_body=TempStorageBatchDeleteMultipleBatchsSerializer
))
class TempStorageBatchDeleteMultipleBatchAPI(AuthenticatedAPIView):

    def delete(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchDeleteMultipleBatchsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_API().delete_multiple_batch_images(user=user, project_id=project_id, data=serializer.data)

        return Response(json_data, status_code)
    
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Delete temp storage multiple file',
    operation_description='Delete temp storage multiple file',
    request_body=TempStorageBatchDeleteMultipleFilesSerializer
))
class TempStorageBatchDeleteMultipleFilesAPI(AuthenticatedAPIView):

    def delete(self, request, project_id, temp_storage_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchDeleteMultipleFilesSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_API().delete_batch_multiple_images(user=user, project_id=project_id, temp_storage_id=temp_storage_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='patch', decorator=swagger_auto_schema(
    tags=['Temporary Storage'],
    operation_summary='Update cluster',
    operation_description='Update cluster',
    request_body=TempStorageBatchEditClusterSerializer
))
class TempStorageBatchEditClusterAPI(AuthenticatedAPIView):

    def patch(self, request, project_id, temp_storage_id, cluster_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchEditClusterSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_API().confirm_cluster(user=user, project_id=project_id, temp_storage_id=temp_storage_id, cluster_id=cluster_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Get deduplicate report',
    operation_description='Get deduplicate report',
    query_serializer=TempStorageBatchDeduplicateReportSerializer,
    responses={200: TempStorageBatchDeduplicateReportResponseSerializer}
))
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Create deduplicate report',
    operation_description='Create deduplicate report',
    request_body=TempStorageBatchPostDeduplicateReportSerializer
))
class TempStorageBatchDeduplicateReportAPI(AuthenticatedAPIView):

    def get(self, request, project_id, batch_id, *args, **kwargs):
        user = request.user
        filters = TempStorageBatchDeduplicateReportSerializer(data=request.query_params)
        filters.is_valid(raise_exception=True)

        params = filters.data
        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().get_dedup_report(user=user, project_id=project_id, batch_id=batch_id, params=params)
        
        return Response(json_data, status_code)
    
    def post(self, request, project_id, batch_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchPostDeduplicateReportSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().post_dedup_report(user=user, project_id=project_id, batch_id=batch_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Add clusters deduplicate report',
    operation_description='Add clusters deduplicate report',
    request_body=TempStorageBatchAddClustersDeduplicateReportSerializer(many=True)
))
class TempStorageBatchAddClustersDeduplicateReportAPI(AuthenticatedAPIView):
    
    def post(self, request, project_id, batch_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchAddClustersDeduplicateReportSerializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().add_clusters_dedup_report(user=user, project_id=project_id, batch_id=batch_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Fetch files in cluster',
    operation_description='Fetch files in cluster',
    query_serializer=TempStorageBatchFetchFilesClusterDeduplicateReportSerializer
))
class TempStorageBatchFetchFilesClusterDeduplicateReportAPI(AuthenticatedAPIView):

    def get(self, request, project_id, batch_id, cluster_id, *args, **kwargs):
        user = request.user
        filters = TempStorageBatchFetchFilesClusterDeduplicateReportSerializer(data=request.query_params)
        filters.is_valid(raise_exception=True)

        params = filters.data
        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().fetch_files_in_cluster(user=user, project_id=project_id, batch_id=batch_id, cluster_id=cluster_id, params=params)
        
        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Cancel deduplicate report',
    operation_description='Cancel deduplicate report',
    request_body=TempStorageBatchCancelingDeduplicateReportSerializer
))
class TempStorageBatchCancelingDeduplicateReportAPI(AuthenticatedAPIView):

    def post(self, request, project_id, batch_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchCancelingDeduplicateReportSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().cancelling_clusters_dedup_report(user=user, project_id=project_id, batch_id=batch_id, data=serializer.data)
        
        return Response(json_data, status_code)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Edit file threshold',
    operation_description='Edit file threshold',
    request_body=TempStorageBatchEditFileThresholdSerializer
))
class TempStorageBatchEditFileThresholdAPI(AuthenticatedAPIView):

    def post(self, request, project_id, batch_id, cluster_id, file_threshold_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchEditFileThresholdSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().edit_file_threshold_report(user=user, project_id=project_id, cluster_id=cluster_id, batch_id=batch_id, file_threshold_id=file_threshold_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Remove all files in cluster',
    operation_description='Remove all files in cluster'
))
class TempStorageBatchRemoveFilesClusterAllAPI(AuthenticatedAPIView):

    def delete(self, request, project_id, batch_id, cluster_id, *args, **kwargs):
        user = request.user
        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().remove_files_cluster_all(user=user, project_id=project_id, batch_id=batch_id, cluster_id=cluster_id)
        
        return Response(json_data, status_code)
    
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Remove multiple files in cluster',
    operation_description='Remove multiple files in cluster',
    request_body=TempStorageBatchRemoveFilesClusterSerializer
))
class TempStorageBatchRemoveFilesClusterAPI(AuthenticatedAPIView):

    def delete(self, request, project_id, batch_id, cluster_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchRemoveFilesClusterSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().remove_files_cluster(user=user, project_id=project_id, batch_id=batch_id, cluster_id=cluster_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Delete all files in cluster',
    operation_description='Delete all files in cluster'
))
class TempStorageBatchDeleteFilesClusterAllAPI(AuthenticatedAPIView):

    def delete(self, request, project_id, batch_id, cluster_id, *args, **kwargs):
        user = request.user
        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().delete_files_cluster_all(user=user, project_id=project_id, batch_id=batch_id, cluster_id=cluster_id)
        
        return Response(json_data, status_code)
    
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Delete multiple files in cluster',
    operation_description='Delete multiple files in cluster',
    request_body=TempStorageBatchDeleteFilesClusterSerializer
))
class TempStorageBatchDeleteFilesClusterAPI(AuthenticatedAPIView):

    def delete(self, request, project_id, batch_id, cluster_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchDeleteFilesClusterSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().delete_files_cluster(user=user, project_id=project_id, batch_id=batch_id, cluster_id=cluster_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Deduplicate Report'],
    operation_summary='Complete deduplication',
    operation_description='Complete deduplication',
    request_body=TempStorageBatchCompleteDeduplicationSerializer
))
class TempStorageBatchCompleteDeduplicationAPI(AuthenticatedAPIView):

    def post(self, request, project_id, batch_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageBatchCompleteDeduplicationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_DEDUP_API().complete_dedup_report(user=user, project_id=project_id, batch_id=batch_id, data=serializer.data)
        
        return Response(json_data, status_code)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Project - System Model'],
    operation_summary='Apply system model to wfl',
    operation_description='Apply system model to wfl',
    request_body=TempStoragePostApplySystemModelSerializer
))
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Project - System Model'],
    operation_summary='Get Apply system model to wfl',
    operation_description='Get Apply system model to wfl',
    responses={200: TempStorageGetApplySystemModelResponseSerializer}
))
class TempStoragePostApplySystemModelAPI(AuthenticatedAPIView):

    def post(self, request, project_id, workflowStep_id, *args, **kwargs):
        user = request.user
        serializer = TempStoragePostApplySystemModelSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_APPLY_SYSTEM_MODEL_API().post_apply_system_model(user=user, project_id=project_id, workflowStep_id=workflowStep_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
    def get(self, request, project_id, workflowStep_id, *args, **kwargs):
        user = request.user
        filters = TempStoragePostApplySystemModelSerializer(data=request.query_params)
        filters.is_valid(raise_exception=True)

        params = filters.data
        status_code, json_data = DMS_TEMP_STORAGE_APPLY_SYSTEM_MODEL_API().get_apply_system_model(user=user, project_id=project_id, workflowStep_id=workflowStep_id, params=params)
        
        return Response(json_data, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Temporary Storage Reason'],
    operation_summary='Fetch reasons from temp batch storage',
    operation_description='Fetch reasons from temp batch storage',
    query_serializer=TempStorageReasonsFetchReasonSerializer,
    responses={200: TempStorageReasonsGetReasonResponseSerializer}
))
class TempStorageReasonsFetchReasonsAPI(AuthenticatedAPIView):
    
    def get(self, request, project_id, temp_storage_file_id, *args, **kwargs):
        user = request.user
        filters = TempStorageReasonsFetchReasonSerializer(data=request.query_params)
        filters.is_valid(raise_exception=True)

        params = filters.data
        status_code, json_data = DMS_TEMP_STORAGE_REASONS_API().fetch_reasons(user=user, project_id=project_id, temp_storage_file_id=temp_storage_file_id, params=params)
        
        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Temporary Storage Reason'],
    operation_summary='Create reason from temp batch storage',
    operation_description='Create reason from temp batch storage',
    request_body=TempStorageReasonsPostReasonSerializer,
))
class TempStorageReasonsCreateReasonAPI(AuthenticatedAPIView):

    def post(self, request, project_id, temp_storage_file_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageReasonsPostReasonSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_REASONS_API().create_reason(user=user, project_id=project_id, temp_storage_file_id=temp_storage_file_id, data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=['Temporary Storage Reason'],
    operation_summary='Update reason from temp batch storage',
    operation_description='Update reason from temp batch storage',
    request_body=TempStorageReasonsPostReasonSerializer,
))
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Temporary Storage Reason'],
    operation_summary='Delete reason from temp batch storage',
    operation_description='Delete reason from temp batch storage',
))
class TempStorageReasonsDetailReasonAPI(AuthenticatedAPIView):

    def put(self, request, project_id, temp_storage_file_id, reason_id, *args, **kwargs):
        user = request.user
        serializer = TempStorageReasonsPostReasonSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TEMP_STORAGE_REASONS_API().update_reason(user=user, project_id=project_id, temp_storage_file_id=temp_storage_file_id, reason_id=reason_id, data=serializer.data)
        
        return Response(json_data, status_code)

    def delete(self, request, project_id, temp_storage_file_id, reason_id, *args, **kwargs):
        user = request.user

        status_code, json_data = DMS_TEMP_STORAGE_REASONS_API().delete_reason(user=user, project_id=project_id, temp_storage_file_id=temp_storage_file_id, reason_id=reason_id)
        
        return Response(json_data, status_code)