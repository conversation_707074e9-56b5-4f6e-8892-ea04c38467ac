from celery import shared_task
import celery
import datetime
import os
import shutil
import zipfile
from pathlib import Path
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync
import concurrent.futures

from users.models import User
from django.conf import settings
from django.core.files import File
from dms_connector.api import DMS_TEMP_STORAGE_API

channel_layer = get_channel_layer()

def recursive_auto_create_folder(user, project_id, parent_path, data_folder_path, data_folder_path_root,
                                 current_folder_project):
    for file in os.listdir(data_folder_path):
        d = os.path.join(data_folder_path, file)
        if os.path.isdir(d):
            parent_absolute = Path("{}".format(d.replace(data_folder_path_root, ""))).parent.absolute()
            filterPath = "{}{}".format(parent_path[:-1], parent_absolute) if str(
                parent_absolute) == "/" else "{}{}/".format(parent_path[:-1], parent_absolute)
            is_existed = False
            # Check folder is existed or not
            for folder_item in current_folder_project:
                if folder_item['filterPath'] == filterPath and folder_item['name'] == file:
                    is_existed = True
                    break

            if not is_existed:
                new_folder = {
                    'dateCreated': "2022-09-27T10:17:03.4665001+00:00",
                    'dateModified': "2022-09-27T10:16:53.7636669+00:00",
                    'filterPath': filterPath,
                    'hasChild': False,
                    'isFile': False,
                    'name': file,
                    'size': 0,
                    'type': "",
                    '_fm_htmlAttr': {},
                    '_fm_icon': "e-fe-folder",
                    '_fm_id': "fe_tree"
                }
                # print(new_folder)
                current_folder_project.append(new_folder)

            recursive_auto_create_folder(user, project_id, parent_path, d, data_folder_path_root,
                                         current_folder_project)
            
@shared_task
def delete_data_folder(zip_task_id):
    folder_zip_path = "temporary_storage/unzip_data_temp/{}".format(zip_task_id)

    shutil.rmtree(folder_zip_path)
    return {
        "status": "Cancel",
        "message": "Cancel upload"
    }

def post_img_in_zip_and_csv_to_s3(data):
    user = data["user"]
    project_id = data["project_id"]
    tempStorageBatchFileId = data["tempStorageBatchFileId"]
    # parent_path = data["parent_path"]
    file = data["file"]
    preReadyToWorkProject = data.get("preReadyToWorkProject", False)
    nextWorkFlowStep = data.get("nextWorkFlowStep", None)
    nextWorkFlowStepId = data.get("nextWorkFlowStepId", None)
    nextWorkFlowState = data.get("nextWorkFlowState", None)
    # data_folder_path_root = data["data_folder_path_root"]
    # attributes_view_default = data["attributes_view_default"]

    # attributes = attributes_view_default.copy()

    # file_path =  file.replace(data_folder_path_root, "")[1:]

    # view_folder_path = ""
    # if parent_path != "/":
    #     view_folder_path = parent_path[:-1] + str(Path("/{}".format(file_path)).parent.absolute())
    # else:
    #     view_folder_path = str(Path("/{}".format(file_path)).parent.absolute())

    # for attrib in attributes:
    #     if attrib["code"] == "view_folder":
    #         attrib["value"] = "{}/".format(view_folder_path)
    #         break

    # post to "Import a file to the dataset" with attribute
    file_post = File(open(file, 'rb'))

    result = {
        "success": True,
        "file": None
    }
    print("+++++++++")
    print(file)
    # print(attributes)
    try:
        # data_view = settings.DEFAULT_FILE_FIELDS.copy()
        # data_view['pathFolder'] = "{}/".format(view_folder_path)

        # status_code, json_data_post = DMS_FILE_API().create(user, file_post, project_id, attributes, data_view)
        if preReadyToWorkProject:
            status_code, json_data_post = DMS_TEMP_STORAGE_API().upload(user, file=file_post, project=project_id, 
                                                                        data={"tempStorageBatchFileId": tempStorageBatchFileId,
                                                                              "preReadyToWorkProject": preReadyToWorkProject,
                                                                              "nextWorkFlowStep": nextWorkFlowStep,
                                                                              "nextWorkFlowStepId": nextWorkFlowStepId,
                                                                              "nextWorkFlowState": nextWorkFlowState})
        else:
            status_code, json_data_post = DMS_TEMP_STORAGE_API().upload(user, file=file_post, project=project_id, 
                                                                    data={"tempStorageBatchFileId": tempStorageBatchFileId})
        # if status_code == 200 and json_data_post is not None:
        #     if json_data_post['success']:    
        #         folder_sync_es_multiple_files(project_master_id=project_id, file_ids=[json_data_post['data']])
        if status_code != 200:
            result["success"] = False
            result["file"] = file
            result["message"] = "Upload Fail"
            return result
    except Exception as e:
        result["success"] = False
        result["file"] = file
        result["message"] = "Connect to server error"
        return result

    if 'success' in json_data_post.keys() and json_data_post['success'] == True:
        print("{}: Done!".format(file))
    else:
        result["success"] = False
        result["file"] = file

    return result

@shared_task
def upload_data_v2(user_id, project_id, file_save, zip_id, parent_path="/", tempStorageBatchFileId=None,
                   preReadyToWorkProject=False, nextWorkFlowStep=None, nextWorkFlowStepId=None, nextWorkFlowState=None):
    current_task_id = celery.current_task.request.id
    start_time = datetime.datetime.now()

    user = User.objects.get(dms_id=user_id)

    async_to_sync(channel_layer.group_send)(
        str(user.id),
        {
            'type': 'chat.message',
            'message': {
                "current_upload_task_id": current_task_id,
                'created_time': str(start_time),
            }
        }
    )
    
    zip_task_id = zip_id
    working_dir = "temporary_storage"
    folder_path = "{}/unzip_data_temp/{}".format(working_dir, zip_task_id)

    if os.path.exists(folder_path) == False:
        os.mkdir(folder_path)

    if (os.path.exists(
            "{}/{}".format(folder_path, file_save)) == False):
        shutil.move("{}/{}".format(settings.MEDIA_ROOT, file_save),
                    folder_path,
                    copy_function=shutil.copy2)
    else:
        shutil.move("{}/{}".format(settings.MEDIA_ROOT, file_save),
                    "{}/{}".format(folder_path, file_save),
                    copy_function=shutil.copy2)
        
    folder_zip_path = "temporary_storage/unzip_data_temp/{}".format(zip_task_id)

    data_zip_path = None
    dir_list = os.listdir(folder_path)
    for dir in dir_list:
        if ".zip" in dir:
            data_zip_path = folder_path + "/" + dir
    # unzip file data (.zip)
    with zipfile.ZipFile(data_zip_path, 'r') as zip_ref:
        zip_ref.extractall(folder_path)

    for dir in os.listdir(folder_zip_path):
        if Path(folder_zip_path + "/" + dir).is_dir():
            data_folder_path = folder_zip_path + "/" + dir
            break

    # get all files
    file_path_list = []
    for root, dirs, files in os.walk(data_folder_path, topdown=True):
        for name in files:
            full_path = os.path.join(root, name)
            file_path_list.append(full_path)

    # # recursive and create folder
    # status_code, project_res = DMS_PROJECT_API().detail(user, project_id)
    # project_data = project_res['data']
    # try:
    #     current_folders = json.loads(project_data['annotationFlow'])
    # except:
    #     current_folders = []
    # data_folder_path_root = folder_zip_path
    # recursive_auto_create_folder(user, project_id, parent_path, folder_zip_path, data_folder_path_root,
    #                              current_folder_project=current_folders)

    # data = {
    #     'annotationFlow': json.dumps(current_folders)
    # }
    # try:
    #     DMS_PROJECT_API().edit(user, project_id, data)
    #     print("[Info]: Auto create folder structure successfully")
    # except Exception as e:
    #     print(e)

    error_upload_file_list = []  # list save all file upload fail
    success_upload_count = 0
    error_upload_count = 0
    data_wait_upload = []

    # attributes_view_default = settings.DEFAULT_VIEW_ATTRIBUTES.copy()

    for file in file_path_list:
        if preReadyToWorkProject:
            data_item_wait_upload = {
                "user": user,
                "project_id": project_id,
                "tempStorageBatchFileId": tempStorageBatchFileId,
                "file": file,
                "preReadyToWorkProject": preReadyToWorkProject,
                "nextWorkFlowStep": nextWorkFlowStep,
                "nextWorkFlowStepId": nextWorkFlowStepId,
                "nextWorkFlowState": nextWorkFlowState
            }
        else:
            data_item_wait_upload = {
                "user": user,
                "project_id": project_id,
                "tempStorageBatchFileId": tempStorageBatchFileId,
                # "parent_path": parent_path,
                "file": file,
                # "data_folder_path_root": data_folder_path_root,
                # "attributes_view_default": attributes_view_default
            }
        data_wait_upload.append(data_item_wait_upload)
        
    executor = concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_WORKERS_UPLOAD)
    count = 0
    for _ in executor.map(post_img_in_zip_and_csv_to_s3, data_wait_upload):
        if _["success"] == True:
            success_upload_count += 1
        if _["success"] == False:
            error_upload_count += 1
            error_upload_file_list.append(_["file"])

        count += 1
        if count % 2 == 0:
            async_to_sync(channel_layer.group_send)(
                str(user.id),
                {
                    'type': 'chat.message',
                    'message': {
                        "current_upload_task_id": current_task_id,
                        "file_save": file_save,
                        "zip_file_id": zip_task_id,
                        "uploaded_number": count,
                        "total_number": len(file_path_list),
                        "percent": round(count / len(file_path_list) * 100, 2)
                    }
                }
            )

            # async_to_sync(channel_layer.group_send)(
            #     str(user.id),
            #     {
            #         'type': 'chat.message',
            #         'message': "{}: Upload Fail".format(_["file"])
            #     }
            # )

    delete_data_folder(zip_task_id=zip_task_id)

    results = {
        "current_upload_task_id": current_task_id,
        'created_time': str(start_time),
        "success": True,
        "file_save": file_save,
        "zip_file_id": zip_task_id,
        "message": "Upload Completed",
        'file_type': 's3',
        "success_number": success_upload_count,
        "error_number": error_upload_count,
        "error_list": error_upload_file_list
    }

    async_to_sync(channel_layer.group_send)(
        str(user.id),
        {
            'type': 'chat.message',
            'message': results
        }
    )

    return results