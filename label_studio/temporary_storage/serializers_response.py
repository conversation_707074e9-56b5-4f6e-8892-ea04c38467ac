from dms_connector.serializers_response import *
from rest_framework import serializers

BATCH_FILE_STATUS_CHOICE = (
    ('New', 'New'),
    ('Uploading', 'Uploading'),
    ('Completed', 'Completed'),
)
BATCH_FILE_TRANSFER_STATUS_CHOICE = (
    ('ReadyTransfer', 'ReadyTransfer'),
    ('InProgress', 'InProgress'),
    ('Completed', 'Completed')
)
BATCH_FILE_DATA_TYPE_CHOICE = (
    ('Png', 'Png'),
    ('Jpg', 'Jpg'),
    ('Jpeg', 'Jpeg'),
    ('Json', 'Json'),
    ('Csv', 'Csv'),
    ('Dcm', 'Dcm'),
)
CLUSTER_STATUS_CHOICES = [
    ('Ready', 'Ready'),
    ('Inprogress', 'Inprogress'),
    ('Deduplicated', 'Deduplicated')
]
REASON_TYPE_CHOICES = [
    ('Redo', 'Redo'),
    ('Reject', 'Reject')
]

class TempStorageBatchFilesItemSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    batchName = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    batchFileStatus = serializers.ChoiceField(choices=BATCH_FILE_STATUS_CHOICE, required=False)
    totalUploadFailure = serializers.IntegerField(required=False)
    totalUploadSuccessfully = serializers.IntegerField(required=False)
    readyToTransfer = serializers.IntegerField(required=False)
    totalTransfered = serializers.IntegerField(required=False)
    totalTransfering = serializers.IntegerField(required=False)
    uploadedBy = serializers.UUIDField(required=False)
    avatar = serializers.CharField(required=False, allow_null=True)
    fullName = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    updatedAt = serializers.DateTimeField(required=False)
    createdAt = serializers.DateTimeField(required=False)
    updatedBy = serializers.CharField(max_length=1000, required=False, allow_null=True)
    createdBy = serializers.CharField(max_length=1000, required=False, allow_null=True)

class TempStorageBatchFilesFilterResponseSerializer(FetchResponseSerializer):
    items = serializers.ListField(child=TempStorageBatchFilesItemSerializer(), required=False)

class TempStorageBatchFilesGetFileItemSerializer(serializers.Serializer):
    fileId = serializers.UUIDField(required=False)
    fileName = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    workflowStep = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    workflowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    workflowState = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    isCompleted = serializers.BooleanField(required=False)
    transferStatus = serializers.ChoiceField(choices=BATCH_FILE_TRANSFER_STATUS_CHOICE, required=False)
    transferedBy = serializers.UUIDField(required=False)
    transfedDate = serializers.DateTimeField(required=False)
    tempStorageBatchFileId = serializers.UUIDField(required=False)
    dataType = serializers.ChoiceField(choices=BATCH_FILE_DATA_TYPE_CHOICE, required=False)
    label = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    fileUrl = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    filePath = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    fileSize = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    
class TempStorageBatchFilesGetFileItemDetailSerializer(TempStorageBatchFilesGetFileItemSerializer):
    userCreated = serializers.UUIDField(required=False)
    avatarUserCreated = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    emailUserCreated = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    fullNameUserCreated = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class TempStorageBatchFilesGetFileResponseSerializer(FetchResponseSerializer):
    items = serializers.ListField(child=TempStorageBatchFilesGetFileItemSerializer(), required=False)

class TempStorageBatchFilesGetFileDetailResponseSerializer(FetchResponseSerializer):
    data = TempStorageBatchFilesGetFileItemDetailSerializer(required=False)

class TempStorageBatchDeduplicateReportItemSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    clusterId = serializers.UUIDField(required=False)
    batchId = serializers.UUIDField(required=False)
    status = serializers.ChoiceField(choices=CLUSTER_STATUS_CHOICES, required=False)
    threshold = serializers.FloatField(required=False)
    countFileThresholds = serializers.IntegerField(required=False)

class TempStorageBatchDeduplicateReportResponseSerializer(FetchResponseSerializer):
    items = serializers.ListField(child=TempStorageBatchDeduplicateReportItemSerializer(), required=False)

class TempStorageGetApplySystemModelSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    systemModelDetailId = serializers.UUIDField(required=False)
    systemModelName = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    apiEndpoint = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    threshold = serializers.FloatField(required=False)
    applyByUserId = serializers.UUIDField(required=False)
    workflowStepId = serializers.UUIDField(required=False)
    projectId = serializers.UUIDField(required=False)

class TempStorageGetApplySystemModelResponseSerializer(DetailResponseSerializer):
    data = TempStorageGetApplySystemModelSerializer(required=False)

class TempStorageReasonsGetReasonSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    userId = serializers.UUIDField(required=False)
    projectId = serializers.UUIDField(required=False)
    tempStorageFileId = serializers.UUIDField(required=False)
    fileId = serializers.UUIDField(required=False)
    contentReason = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    reasonType = serializers.ChoiceField(choices=REASON_TYPE_CHOICES, required=False)
    userName = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    avatar = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    email = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class TempStorageReasonsGetReasonResponseSerializer(DetailResponseSerializer):
    data = TempStorageReasonsGetReasonSerializer(required=False)