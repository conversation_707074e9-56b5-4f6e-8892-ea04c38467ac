import json
from channels.generic.websocket import AsyncJsonWebsocketConsumer
from channels.db import database_sync_to_async
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync, sync_to_async

import os
# os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings.label_studio')
os.environ["DJANGO_ALLOW_ASYNC_UNSAFE"] = "true"
import django
django.setup()

from django.contrib.auth.models import AnonymousUser

channel_layer = get_channel_layer()


class TemperatureStorageConsumer(AsyncJsonWebsocketConsumer):

    async def connect(self):
        user = self.scope["user"]
        # print(self.channel_name)
        print(user)
        if not isinstance(user, AnonymousUser):
            print(user.dms_id, user.username)
            await self.channel_layer.group_add(
                f"{str(user.dms_id)}_temp_storage",
                self.channel_name
            )
        else:
            print(user.id, user.username)
            await self.channel_layer.group_add(
                str(user.id),
                self.channel_name
            )

        await self.accept()
        # await self.send(text_data=json.dumps(results))

    async def disconnect(self, close_code):
        user = self.scope["user"]
        # Leave room group
        if not isinstance(user, AnonymousUser):
            await self.channel_layer.group_discard(
                f"{str(user.dms_id)}_temp_storage",
                self.channel_name
            )
        else:
            await self.channel_layer.group_discard(
                str(user.id),
                self.channel_name
            )

    async def receive_json(self, content, **kwargs):
        user = self.scope["user"]
        noti_list = content['message']
        print(noti_list)

        for noti in noti_list:
            message = {}
            message.update(noti)
            message.pop('userId')
            
            dms_user = noti['userId']
            await self.channel_layer.group_send(
                f"{str(dms_user)}_temp_storage",
                {
                    'type': 'chat.message',
                    'message': message
                }
            )

        # if dms_users:
        #     for dms_user in dms_users:
        #         await self.channel_layer.group_send(
        #             dms_user,
        #             {
        #                 'type': 'chat.message',
        #                 'message': content['message']
        #             }
        #         )

        return await super().receive_json(content, **kwargs)

    # Receive message from room group
    async def chat_message(self, event):
        message = event['message']

        # Send message to WebSocket
        await self.send(text_data=json.dumps({
            'message': message
        }))
