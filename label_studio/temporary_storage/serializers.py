from rest_framework import serializers
from label_studio.dms_connector.serializers import FilterSerializer

BATCH_FILE_STATUS_CHOICE = (
    (None, None),
    ('New', 'New'),
    ('Ready', 'Ready'),
    ('Processing', 'Processing'),
    ('Completed', 'Completed'),
    ('Failed', 'Failed'),
    ('Pending', 'Pending'),
    ('Cancel', 'Cancel'),
    ('Reject', 'Reject')
)
BATCH_FILE_TRANSFER_STATUS_CHOICE = (
    ('ReadyTransfer', 'ReadyTransfer'),
    ('InProgress', 'InProgress'),
    ('Completed', 'Completed')
)
IMAGETYPE_CHOICES = [
    ('Original', 'Original'),
    ('Thumbnail', 'Thumbnail'),
    ('Mobile', 'Mobile'),
    ('Desktop', 'Desktop'),
    ('Tablet', 'Tablet')
]
CLUSTER_STATUS_CHOICES = [
    ('Ready', 'Ready'),
    ('Inprogress', 'Inprogress'),
    ('Deduplicated', 'Deduplicated'),
    ('Confirmed', 'Confirmed'),
]
CLUSTER_EVENT_TYPE_CHOICES = [
    ('CreateDeduplication', 'CreateDeduplication'),
    ('CancelDeduplication', 'CancelDeduplication')
]

REASON_TYPE_CHOICES = [
    ('Redo', 'Redo'),
    ('Reject', 'Reject')
]

class TempStorageBatchFilesFilterSerializer(FilterSerializer):
    batchName = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    batchFileStatus = serializers.ChoiceField(choices=BATCH_FILE_STATUS_CHOICE, required=False)

class TempStorageBatchFilesCreateSerializer(serializers.Serializer):
    batchName = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class TempStorageBatchFilesUpdateSerializer(serializers.Serializer):
    batchFileId = serializers.UUIDField(required=True)
    batchName = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class TempStorageBatchFilesUploadFileSerializer(serializers.Serializer):
    file = serializers.FileField(required=True)
    tempStorageBatchFileId = serializers.UUIDField(required=True)
    preReadyToWorkProject = serializers.BooleanField(required=False, default=False)
    nextWorkFlowStep = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowState = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class TempStorageBatchFilesGetImageSerializer(serializers.Serializer):
    type = serializers.ChoiceField(choices=IMAGETYPE_CHOICES, default='Thumbnail', required=False)

class TempStorageBatchFilesFetchFilesSerializer(FilterSerializer):
    workflowStepIds = serializers.ListField(child=serializers.CharField(max_length=1000), required=False)
    workflowState = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    stepStatus = serializers.ListField(child=serializers.ChoiceField(choices=BATCH_FILE_STATUS_CHOICE), required=False)

class TempStorageBatchFilesUploadZipSerializer(serializers.Serializer):
    tempStorageBatchFileId = serializers.UUIDField(required=True)
    id = serializers.CharField(max_length=1000, required=False)
    file = serializers.FileField()
    parent_path = serializers.CharField(max_length=1000, allow_null=True, required=False)
    preReadyToWorkProject = serializers.BooleanField(required=False, default=False)
    nextWorkFlowStep = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowState = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class TempStorageBatchDeleteMultipleBatchsSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.UUIDField(), required=True)

class TempStorageBatchDeleteMultipleFilesSerializer(serializers.Serializer):
    fileIds = serializers.ListField(child=serializers.UUIDField(), required=True)

class TempStorageBatchTransferMultipleFilesSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.UUIDField(), required=True)
    isAutoTransfer = serializers.BooleanField(required=False, default=False)

class TempStorageBatchTransferFileSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=True)
    isAutoTransfer = serializers.BooleanField(required=False, default=False)
    timestamp = serializers.IntegerField(required=True, allow_null=True)

class TempStorageBatchUpdateFileSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=True)
    workflowStep = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    workflowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    workflowState = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    isCompleted = serializers.BooleanField(required=False)
    transferStatus = serializers.ChoiceField(choices=BATCH_FILE_TRANSFER_STATUS_CHOICE, required=False)
    stepStatus = serializers.ChoiceField(choices=BATCH_FILE_STATUS_CHOICE, required=False)
    timestamp = serializers.IntegerField(required=False, allow_null=True)

class TempStorageBatchUpdateFilesListSerializer(serializers.Serializer):
    tempStorageFiles = serializers.ListField(child=TempStorageBatchUpdateFileSerializer(), required=True)

class TempStorageBatchDeduplicateReportSerializer(serializers.Serializer):
    clusterStatus = serializers.ChoiceField(choices=CLUSTER_STATUS_CHOICES, required=False)

class TempStorageBatchPostDeduplicateReportSerializer(serializers.Serializer):
    threshold = serializers.FloatField(required=False)
    workflowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class TempStorageBatchAddClustersDeduplicateReportSerializer(serializers.Serializer):
    clusterId = serializers.CharField(max_length=1000, required=True)
    batchId = serializers.UUIDField(required=True)
    projectId = serializers.UUIDField(required=True)
    status = serializers.ChoiceField(choices=CLUSTER_STATUS_CHOICES, required=True)
    threshold = serializers.FloatField(required=True)

class TempStorageBatchCancelingDeduplicateReportSerializer(serializers.Serializer):
    eventDuplicationProccessId = serializers.UUIDField(required=True)
    eventType = serializers.ChoiceField(choices=CLUSTER_EVENT_TYPE_CHOICES, required=False)

class TempStorageBatchEditFileThresholdSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=True)
    threshold = serializers.FloatField(required=False)
    isCentrolId = serializers.BooleanField(required=False)
    isDelete = serializers.BooleanField(required=False)

class TempStorageBatchRemoveFilesClusterSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.UUIDField(), required=True)

class TempStorageBatchDeleteFilesClusterSerializer(serializers.Serializer):
    ids = serializers.ListField(child=serializers.UUIDField(), required=True)

class TempStorageBatchCompleteDeduplicationSerializer(serializers.Serializer):
    workflowStep = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    workflowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    workflowState = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class TempStorageBatchEditClusterSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=CLUSTER_STATUS_CHOICES, required=False)
    threshold = serializers.FloatField(required=False)

class TempStorageBatchFetchFilesClusterDeduplicateReportSerializer(FilterSerializer):
    projectFileClusterId = serializers.UUIDField(required=False)
    isRemove = serializers.BooleanField(required=False, allow_null=True, default=None)

class TempStoragePostApplySystemModelSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    systemModelDetailId = serializers.UUIDField(required=False)
    systemModelName = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    apiEndpoint = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    threshold = serializers.FloatField(required=False)
    applyByUserId = serializers.UUIDField(required=False)

class TempStorageReasonsFetchReasonSerializer(FilterSerializer):
    reasonType = serializers.ChoiceField(choices=REASON_TYPE_CHOICES, required=False)

class TempStorageReasonsPostReasonSerializer(serializers.Serializer):
    userId = serializers.UUIDField(required=False)
    contentReason = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    reasonType = serializers.ChoiceField(choices=REASON_TYPE_CHOICES, required=False)

class TempStorageEditBatchDetailSerializer(serializers.Serializer):
    batchFileStatus = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)