from rest_framework import serializers
from rest_framework.fields import DictField

TABULAR_CATEGORY_STATUS_CHOICES = [
    ("New", "New"),
    ("Labeling", "Labeling"),
    ("Reviewing", "Reviewing"),
    ("Completed", "Completed"),
]

class TabularCategoriesInforKeyUpdateSerializer(serializers.Serializer):
    tabularCategoryDetailId = serializers.UUIDField(required=False)
    categoryKey = serializers.IntegerField(required=False)
    key = serializers.CharField(max_length=1000, allow_null=True, required=False)
    value = serializers.CharField(max_length=10000, allow_null=True, allow_blank=True, required=False)
    value_Quoted = serializers.CharField(max_length=10000, allow_null=True, allow_blank=True, required=False)
    value_Quoted_Importance = serializers.CharField(max_length=10000, allow_null=True, allow_blank=True, required=False)

class TabularCategoriesInforUpdateSerializer(serializers.Serializer):
    description = serializers.CharField(max_length=10000, allow_null=True, required=False, allow_blank=True)
    status = serializers.ChoiceField(choices=TABULAR_CATEGORY_STATUS_CHOICES, required=False, allow_null=True)
    tabularCategoryDetails = serializers.ListField(child=TabularCategoriesInforKeyUpdateSerializer(), required=False, allow_null=True)

class TabularCategoriesFetchListFilterSerializer(serializers.Serializer):
    status = serializers.ChoiceField(choices=TABULAR_CATEGORY_STATUS_CHOICES, required=False)
    page = serializers.IntegerField(default=1)
    pageSize = serializers.IntegerField(default=100)
    keyword = serializers.CharField(max_length=1000, allow_null=True, required=False)

class TabularCategoriesFetchAvailableValueKeyFilterSerializer(serializers.Serializer):
    page = serializers.IntegerField(default=1)
    pageSize = serializers.IntegerField(default=100)
    keyword = serializers.CharField(max_length=1000, allow_null=True, required=False)

class ProjectTabularGeneratedDescriptionSerializer(serializers.Serializer):
    disease_name = serializers.CharField(max_length=1000, required=True)
    provoke = serializers.ListField(child=DictField(), required=True)

class TabularCategoriesInforHighlightSerializer(serializers.Serializer):
    page = serializers.IntegerField()
    quote = serializers.CharField(max_length=1000000, required=False, allow_blank=True, allow_null=True)
    chunk = serializers.CharField(max_length=1000000, required=False, allow_blank=True, allow_null=True)
    file_url = serializers.CharField(max_length=1000, required=False, allow_blank=True, allow_null=True)