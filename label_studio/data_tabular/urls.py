"""This file and its contents are licensed under the Apache License 2.0. Please see the included NOTICE for copyright information and LICENSE for a copy of the license.
"""
from django.urls import include, path

from . import api, views

app_name = 'tabular_data'

_api_urlpatterns = [
    path('', api.TabularCategoriesFetchListAPI.as_view(), name='tabularcategories-list'),
    path('detail/<uuid:tabular_category_id>', api.TabularCategoriesInforFetchListAPI.as_view(), name='tabularcategories-detail'),
    path('update/<uuid:tabular_category_id>', api.TabularCategoriesInforUpdateAPI.as_view(), name='tabularcategories-update'),
    path('export/', api.TabularCategoriesInforExportAPI.as_view(), name='tabularcategories-export'),
    path('dictionary', api.TabularCategoriesAvailableValueKeyAPI.as_view(), name='tabularcategories-dictionary'),
    path('highlight', api.TabularCategoriesInforHighlightAPI.as_view(), name='tabularcategories-highlight'),
    path('tabular_generated_description', api.ProjectTabularGeneratedDescriptionCreateAPI.as_view(), name='tabular-generated-description-get'),
    path('<uuid:tabular_category_id>/version_history', api.TabularCategoriesHistoryFetchListAPI.as_view(), name='tabularcategories-version-history-list'),
    path('<uuid:version_history_id>/version_history_detail', api.TabularCategoriesHistoryFetchDetailAPI.as_view(), name='tabularcategories-version-history-list'),
    path('<uuid:tabular_category_id>/restore/<uuid:version_history_id>', api.TabularCategoriesHistoryRestoreAPI.as_view(), name='tabularcategories-version-history-restore'),
]

urlpatterns = [
    path('api/projects/<uuid:project_id>/tabular_categories/', include((_api_urlpatterns, app_name), namespace='api')),
]