import logging
import time
from drf_yasg.utils import swagger_auto_schema

from rest_framework.response import Response
from django.utils.decorators import method_decorator
from django.http import HttpResponse

from dms_connector.api import AuthenticatedAPIView, DMS_TABULAR_DATA_API, DMS_PROJECT_API

from data_tabular.serializers import *
from data_tabular.serializers_response import *

logger = logging.getLogger(__name__)

@method_decorator(name="get", decorator=swagger_auto_schema(
    tags=['Tabular Data'], operation_summary="Get list categories in tabular",
    operation_description="Get list categories in tabular",
    responses={200: TabularCategoriesFetchListResponseSerializer},
    query_serializer=TabularCategoriesFetchListFilterSerializer
))
class TabularCategoriesFetchListAPI(AuthenticatedAPIView):

    def get(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = TabularCategoriesFetchListFilterSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TABULAR_DATA_API().get_list_categories_in_tabular(user=user, 
                                                                                       project_id=project_id,
                                                                                       params=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name="get", decorator=swagger_auto_schema(
    tags=['Tabular Data'], operation_summary="Get infor details categories",
    operation_description="Get infor details categories",
    responses={200: TabularCategoriesInforFetchListResponseSerializer},
))
class TabularCategoriesInforFetchListAPI(AuthenticatedAPIView):

    def get(self, request, project_id, tabular_category_id, *args, **kwargs):
        user = request.user

        status_code, json_data = DMS_TABULAR_DATA_API().get_infor_details_categories(user=user, 
                                                                                     project_id=project_id,
                                                                                     tabular_category_id=tabular_category_id)
        
        return Response(json_data, status_code)
    
@method_decorator(name="put", decorator=swagger_auto_schema(
    tags=['Tabular Data'], operation_summary="Update infor details categories",
    operation_description="Update infor details categories",
    request_body=TabularCategoriesInforUpdateSerializer,
))
class TabularCategoriesInforUpdateAPI(AuthenticatedAPIView):

    def put(self, request, project_id, tabular_category_id, *args, **kwargs):
        user = request.user

        serializer = TabularCategoriesInforUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TABULAR_DATA_API().update_infor_details_categories(user=user, 
                                                                                       project_id=project_id,
                                                                                       tabular_category_id=tabular_category_id,
                                                                                       data=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name="get", decorator=swagger_auto_schema(
    tags=['Tabular Data'], operation_summary="Get available value key",
    operation_description="Get available value key",
    query_serializer=TabularCategoriesFetchAvailableValueKeyFilterSerializer,
    responses={200: TabularCategoriesFetchAvailableValueKeyResponseSerializer},
))
class TabularCategoriesAvailableValueKeyAPI(AuthenticatedAPIView):

    def get(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = TabularCategoriesFetchAvailableValueKeyFilterSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TABULAR_DATA_API().get_available_value_key(user=user, 
                                                                               project_id=project_id,
                                                                               params=serializer.data)
        
        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Tabular Data'], operation_summary="Get tabular generated description",
    operation_description="Get tabular generated description.",
    request_body=ProjectTabularGeneratedDescriptionSerializer
))
class ProjectTabularGeneratedDescriptionCreateAPI(AuthenticatedAPIView):

    def post(self, request, *args, **kwargs):
        # user = request.user

        serializer = ProjectTabularGeneratedDescriptionSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data = serializer.data

        try:
            status_code, result = DMS_TABULAR_DATA_API().get_tabular_generated_description(disease_name=data.get('disease_name', ""), provoke=data.get('provoke', []))
        except Exception as e:
            return Response({"success": False, "message": e}, 400)

        return Response({"success": True, "data": result}, status_code)
    
@method_decorator(name="get", decorator=swagger_auto_schema(
    tags=['Tabular Data'], operation_summary="Get list version history in tabular",
    operation_description="Get list version history in tabular",
    responses={200: TabularCategoriesHistoryFetchListResponseSerializer},
))
class TabularCategoriesHistoryFetchListAPI(AuthenticatedAPIView):

    def get(self, request, project_id, tabular_category_id, *args, **kwargs):
        user = request.user

        status_code, json_data = DMS_TABULAR_DATA_API().get_list_category_histories_in_tabular(user=user, project_id=project_id, tabular_category_id=tabular_category_id)
        
        return Response(json_data, status_code)
    
@method_decorator(name="get", decorator=swagger_auto_schema(
    tags=['Tabular Data'], operation_summary="Get detail version history in tabular",
    operation_description="Get detail list version history in tabular",
    responses={200: TabularCategoriesHistoryDetailFetchResponseSerializer},
))
class TabularCategoriesHistoryFetchDetailAPI(AuthenticatedAPIView):

    def get(self, request, project_id, version_history_id, *args, **kwargs):
        user = request.user

        status_code, json_data = DMS_TABULAR_DATA_API().get_detail_category_histories_in_tabular(user=user, project_id=project_id, version_history_id=version_history_id)
        
        return Response(json_data, status_code)

@method_decorator(name="post", decorator=swagger_auto_schema(
    tags=['Tabular Data'], operation_summary="Restore version history in tabular",
    operation_description="Restore version history in tabular",
))
class TabularCategoriesHistoryRestoreAPI(AuthenticatedAPIView):

    def post(self, request, project_id, tabular_category_id, version_history_id, *args, **kwargs):
        user = request.user

        status_code, json_data = DMS_TABULAR_DATA_API().restore_category_histories_in_tabular(user=user, project_id=project_id, tabular_category_id=tabular_category_id, version_history_id=version_history_id)
        
        return Response(json_data, status_code)
    
@method_decorator(name="get", decorator=swagger_auto_schema(
    tags=['Tabular Data'], operation_summary="Export detail infor in tabular",
    operation_description="Export detail infor in tabular",
))
class TabularCategoriesInforExportAPI(AuthenticatedAPIView):

    def get(self, request, project_id, *args, **kwargs):
        user = request.user

        # Get name of the project
        project_name = ""
        status_code_prj, json_data_prj = DMS_PROJECT_API().detail(user=user, id=project_id)
        if status_code_prj == 200:
            if json_data_prj.get('success', False):
                project_name = json_data_prj["data"]["name"]

        status_code, file = DMS_TABULAR_DATA_API().export_infor_details_categories_to_csv(user=user, project_id=project_id)
        
        return HttpResponse(file, headers={
            'Content-Type': 'text/csv',
            'Content-Disposition': 'attachment; filename="{}_{}_export version.csv"'.format(round(time.time()), project_name),
        })
    

@method_decorator(name="post", decorator=swagger_auto_schema(
    tags=['Tabular Data'], operation_summary="Highlight infor details categories",
    operation_description="Highlight infor details categories",
    request_body=TabularCategoriesInforHighlightSerializer,
))
class TabularCategoriesInforHighlightAPI(AuthenticatedAPIView):

    def post(self, request, project_id, *args, **kwargs):
        user = request.user

        serializer = TabularCategoriesInforHighlightSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_TABULAR_DATA_API().highlight_infor_details_categories(user=user, 
                                                                                       project_id=project_id,
                                                                                       data=serializer.data)
        
        return Response(json_data, status_code)