from rest_framework import serializers
from dms_connector.serializers_response import *

TABULAR_CATEGORY_STATUS_CHOICES = [
    ("New", "New"),
    ("Labeling", "Labeling"),
    ("Reviewing", "Reviewing"),
    ("Completed", "Completed"),
]

TABULAR_CATEGORY_HISTORY_TYPE_CHOICES = [
    ("Created", "Created"),
    ("Changed", "Changed"),
    ("Restored", "Restored"),
    ("Draft", "Draft"),
]

TABULAR_CATEGORY_HISTORY_DATA_TYPE_CHOICES = [
    ("DropDown", "DropDown"),
    ("Json", "Json"),
    ("Freetext", "Freetext"),
]

class TabularCategoriesResponseSerializer(serializers.Serializer):
    tabularCategoryId = serializers.UUIDField(required=False)
    categoryKey = serializers.IntegerField(required=False)
    categoryName = serializers.CharField(max_length=1000, allow_null=True, required=False)
    status = serializers.ChoiceField(choices=TABULAR_CATEGORY_STATUS_CHOICES, required=False)
    description = serializers.CharField(max_length=10000, allow_null=True, required=False, allow_blank=True)
    isDelete = serializers.BooleanField(required=False)

class TabularCategoriesFetchListResponseSerializer(DetailResponseSerializer):
    data = serializers.ListField(child=TabularCategoriesResponseSerializer(), required=False)

class TabularCategoriesInforResponseSerializer(serializers.Serializer):
    tabularCategoryDetailId = serializers.UUIDField(required=False)
    categoryKey = serializers.IntegerField(required=False)
    key = serializers.CharField(max_length=1000, allow_null=True, required=False)
    value = serializers.CharField(max_length=1000, allow_null=True, required=False)
    isDelete = serializers.BooleanField(required=False)

class TabularCategoriesInforFetchListResponseSerializer(DetailResponseSerializer):
    data = serializers.ListField(child=TabularCategoriesInforResponseSerializer(), required=False)

class TabularCategoriesFetchAvailableValueKeyDetailSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    key = serializers.CharField(max_length=1000, allow_null=True, required=False)
    value = serializers.CharField(max_length=1000, allow_null=True, required=False)
    description = serializers.CharField(max_length=10000, allow_null=True, required=False, allow_blank=True)

class TabularCategoriesFetchAvailableValueKeyResponseSerializer(FetchResponseSerializer):
    items = serializers.ListField(child=TabularCategoriesFetchAvailableValueKeyDetailSerializer(), required=False)
    
class TabularCategoriesHistoryBaseSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    tabularCategoryId = serializers.UUIDField(required=False)
    projectId = serializers.UUIDField(required=False)
    actionType = serializers.ChoiceField(choices=TABULAR_CATEGORY_HISTORY_TYPE_CHOICES, required=False)
    desciption = serializers.CharField(max_length=1000, allow_null=True, required=False)

class TabularCategoriesHistoryListItemSerializer(TabularCategoriesHistoryBaseSerializer):
    actionBy = serializers.UUIDField(required=False)
    fullName = serializers.CharField(max_length=1000, allow_null=True, required=False)
    email = serializers.CharField(max_length=1000, allow_null=True, required=False)
    updatedAt = serializers.CharField(max_length=1000, allow_null=True, required=False)
    createdAt = serializers.CharField(max_length=1000, allow_null=True, required=False)
    createdBy = serializers.CharField(max_length=1000, allow_null=True, required=False)
    updatedBy = serializers.CharField(max_length=1000, allow_null=True, required=False)
    
class TabularCategoriesHistoryFetchListResponseSerializer(FetchResponseSerializer):
    items = serializers.ListField(child=TabularCategoriesHistoryListItemSerializer(), required=False)
    
class TabularCategoriesHistoryDetailItemSerializer(serializers.Serializer):
    id = serializers.UUIDField(required=False)
    tabularHistoryId = serializers.UUIDField(required=False)
    key = serializers.CharField(max_length=1000, allow_null=True, required=False)
    value = serializers.CharField(max_length=1000, allow_null=True, required=False)
    dataType = serializers.ChoiceField(choices=TABULAR_CATEGORY_HISTORY_DATA_TYPE_CHOICES, required=False)
    
class TabularCategoriesHistoryDetailSerializer(TabularCategoriesHistoryBaseSerializer):
    tabularCategoryHistoryDetailItems = serializers.ListField(child=TabularCategoriesHistoryDetailItemSerializer(), required=False)
    
class TabularCategoriesHistoryDetailFetchResponseSerializer(DetailResponseSerializer):
    data = TabularCategoriesHistoryDetailSerializer()