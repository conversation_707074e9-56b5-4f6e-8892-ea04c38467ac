import re
import math
import pymupdf
import io
import datetime
from minio import <PERSON>o
from django.conf import settings

def download_file_bytes(relative_path: str) -> bytes:
    client = Minio(
        settings.MINIO_ENDPOINT,
        access_key=settings.MINIO_ACCESS_KEY,
        secret_key=settings.MINIO_SECRET_KEY,
        secure=settings.MINIO_SECURE,
    )
    return client.get_object(settings.MINIO_BUCKET_NAME, relative_path).read()

def longest_approx_substring(T: str,
                             Q: str,
                             alpha: float = 0.8,
                             beta:  float = 1.3
                            ):
    """
    Return a substring S = T[i..j] of length ≤ beta*len(Q) which
    contains as an in-order subsequence at least ceil(alpha*len(Q))
    characters of Q, maximizing the number of matched chars.
    If there is a tie, returns the earliest such substring.
    Returns None if no window meets the alpha threshold.
    """
    N, M = len(T), len(Q)
    if M == 0:
        return (0, 0)      # trivial

    # thresholds
    M0 = math.ceil(alpha * M)      # minimum matches required
    L  = math.floor(beta  * M)     # maximum window size

    best_p   = 0
    best_i   = best_j = None

    # helper: compute LCS matrix and backtrack match-positions
    def lcs_positions(s1: str, s2: str):
        # s1 = Q, length M
        # s2 = window of T, length W ≤ L
        W = len(s2)
        # dp row by row: dp[i][j] = lcs of s1[:i] and s2[:j]
        dp = [ [0]*(W+1) for _ in range(M+1) ]
        for i in range(1, M+1):
            ci = s1[i-1]
            row = dp[i]
            prev = dp[i-1]
            for j in range(1, W+1):
                if ci == s2[j-1]:
                    row[j] = prev[j-1] + 1
                else:
                    row[j] = max(prev[j], row[j-1])
        # now backtrack
        p = dp[M][W]
        i, j = M, W
        pos = []
        while i>0 and j>0:
            if s1[i-1] == s2[j-1] and dp[i][j] == dp[i-1][j-1] + 1:
                pos.append(j-1)
                i -= 1; j -= 1
            elif dp[i-1][j] >= dp[i][j-1]:
                i -= 1
            else:
                j -= 1
        pos.reverse()
        return p, pos

    # slide window of max length L
    for start in range(0, N):
        end = min(N, start + L)
        window = T[start:end]
        p, pos = lcs_positions(Q, window)
        if p > best_p:
            best_p = p
            if p > 0:
                # map back to T indices
                first = start + pos[0]
                last  = start + pos[-1]
            else:
                first = last = start
            best_i, best_j = first, last
            # perfect match → we can stop early
            if best_p == M:
                break

    if best_p >= M0 and best_i is not None:
        return (best_i, best_j + 1)
    return (0, 0)

def render_highlight_page(
    relative_path: str,
    # pdf_path: str,
    output_path,
    output_url: str,
    page: int,
    quote: str
):
    
    # _master_doc = pymupdf.open(pdf_path)
    print("1.", datetime.datetime.now())
    _master_doc = pymupdf.open(stream=io.BytesIO(download_file_bytes(relative_path)))
    print("2.", datetime.datetime.now())
    page_count = _master_doc.page_count
    if page < 1 or page > page_count:
        return None
    
    _pages_text = [
        _master_doc.load_page(i).get_text("words")
        for i in range(page_count)
    ]

    # 3) build one‐page PDF
    new_doc = pymupdf.open()
    new_doc.insert_pdf(_master_doc, from_page=page, to_page=page)
    p = new_doc[0]

    # reconstruct raw string from words and map each char to its word rect
    word_entries = _pages_text[page]  # (x0,y0,x1,y1,text)
    raw_rects = [pymupdf.Rect(*w[:4]) for w in word_entries]
    raw_texts = [w[4] for w in word_entries]
    temp_chars: list[str] = []
    char_to_rect: list[pymupdf.Rect | None] = []
    for i, txt in enumerate(raw_texts):
        # if i > 0:
        #     temp_chars.append(' ')
        #     char_to_rect.append(None)
        for ch in txt:
            temp_chars.append(ch)
            char_to_rect.append(raw_rects[i])
    temp = ''.join(temp_chars)
    # clean to only alphanumerics and spaces, keeping mapping to temp indices
    cleaned_chars: list[str] = []
    cleaned_to_raw: list[int] = []
    for idx, ch in enumerate(temp):
        if re.match(r'[A-Za-z0-9 ]', ch):
            cleaned_chars.append(ch)
            cleaned_to_raw.append(idx)
    cleaned = ''.join(cleaned_chars)
    
    # pos_map = cleaned_to_raw
    print(cleaned)
    # normalize quote: keep only letters, numbers, and spaces and collapse whitespace
    quote_norm = re.sub(r'[^A-Za-z0-9]+', '', quote.strip() if quote else '')
    quote_norm = re.sub(r'\s+', ' ', quote_norm).strip()
    
    begin, end = longest_approx_substring(cleaned, quote_norm)
    
    if begin and end:
        temp_begin_idx = cleaned_to_raw[begin]
        temp_end_idx = cleaned_to_raw[end - 1] + 1
        rect = char_to_rect[temp_begin_idx:temp_end_idx]
        highlight_rects = list(set(rect))  # unique rects for the highlight
        # collect unique rects over the span
        for rect in highlight_rects:
            p.add_highlight_annot(rect)
    # 5) return streaming PDF
    
    # # save p to a output_path
    print("3.", datetime.datetime.now())
    if output_path:
        new_doc.save(output_path, garbage=4, deflate=True)
    else:
        p = new_doc.write()
        new_doc.close()
        _master_doc.close()

        # put to MinIO
        client = Minio(
            settings.MINIO_ENDPOINT,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_SECURE,
        )
        client.put_object(
            settings.MINIO_BUCKET_NAME,
            output_url,
            io.BytesIO(p),
            len(p),
        )
        print("4.", datetime.datetime.now())

    main_page = 0
    return main_page

