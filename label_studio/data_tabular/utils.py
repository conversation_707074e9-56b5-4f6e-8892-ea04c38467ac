import pymupdf
import re, io, uuid, os, difflib

from minio import Minio
from typing_extensions import Iterator
from abc import ABC

from django.conf import settings


class PdfFileHandler(ABC):

    def download_file_bytes(self, relative_path: str) -> bytes:
        client = Minio(
            settings.MINIO_ENDPOINT,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_SECURE,
        )
        return client.get_object(settings.MINIO_BUCKET_NAME, relative_path).read()

    def clean_text_for_highlight(self, text: str) -> str:
        text = re.sub(r"\n", " ", text)
        text = re.sub(r" {2,}", " ", text)
        return text
    
    def clean(self, text: str, apply_additional_transform: bool = True) -> str:
        # if apply_additional_transform:
        #     if self.is_table_of_content(text):
        #         return ""
        # remove urls
        text = re.sub(
            r"""(?i)\b((?:https?://|www\d{0,3}[.]|[a-z0-9.\-]+[.][a-z]{2,4}/)(?:[^\s()<>]+|\(([^\s()<>]+|(\([^\s()<>]+\)))*\))+(?:\(([^\s()<>]+|(\([^\s()<>]+\)))*\)|[^\s`!()\[\]{};:'".,<>?«»“”‘’]))""",
            " ",
            text,
        )
        text = re.sub(r"\.+", ".", text)
        text = re.sub(r" {2,}", " ", text)  # Remove multiple spaces
        text = text.replace("\t", " ")
        # convert multiple dots to single dot
        text = re.sub(r"\.+", ".", text)
        # remove page
        text = re.sub(r"Page \d+ of \d+", "", text)
        # remove \n
        text = re.sub(r"(\s*\n\s*){2,}", "<<DOUBLE_NEWLINE>>", text)
        text = re.sub(r"\n", " ", text)
        text = re.sub(r"<<DOUBLE_NEWLINE>>", "\n", text)
        text = re.sub(r" {2,}", " ", text)  # Remove multiple spaces
        text = re.sub(r"-\n", "", text)
        # remove single character words
        text = re.sub(r"\s[b-zB-Z]\s", "\n - ", text)
        # if apply_additional_transform:
        #     if self.get_tokens(text) < 50:
        #         return ""
        return text.strip()

    def get_raw_quote(self, quote: str, chunk: str) -> str:
        new_quote = None
        quote_words = quote.split(" ")
        sentence_to_check_match = " ".join(quote_words[:20])
        start_idx = 0
        num_c = len(quote) // 3
        flag = False
        while True:
            if (
                not flag
                and sentence_to_check_match == self.clean(chunk[start_idx:], False)[: len(sentence_to_check_match)]
            ):
                flag = True
            if flag:
                if self.clean(chunk[start_idx : start_idx + num_c], False) == quote:
                    new_quote = chunk[start_idx : start_idx + num_c]
                    break
                else:
                    num_c += 1
            else:
                start_idx += 1
            if num_c > len(chunk) or start_idx > len(chunk):
                break
        if new_quote:
            print("Found the raw quote")
            quote = new_quote
        else:
            print("Can't find the raw quote")
        quote = quote.strip()
        return self.clean_text_for_highlight(quote)

    def highlight_text(self, relative_path: str, page: int, quote: str, out_url: str, chunk: str) -> int:
        quote = self.get_raw_quote(quote, chunk)
        file_bytes = self.download_file_bytes(relative_path)
        # Create a new PDF for writing
        pdf = pymupdf.open(stream=io.BytesIO(file_bytes))
        # Create a new PDF for writing
        pdf_writer = pymupdf.open()
        # Insert pages 1 to 10 into the new document
        pdf_writer.insert_pdf(pdf, from_page=max(page - 2, 0), to_page=min(page + 2, len(pdf) - 1))
        if page == 0:
            main_page = 0
        elif page == 1:
            main_page = 1
        else:
            main_page = 2
        page_ = pdf_writer[main_page]
        # in\xad anh <=> in-\nanh
        quote = re.sub("\xad ", "-\n", quote)
        highlights = page_.get_textpage().search(quote, quads=True)
        if highlights:
            print("Found citation in the page")
            for highlight in highlights:
                page_.add_highlight_annot(highlight)
        # else:
        #     quote = self.clean_text_for_highlight(quote)
        #     highlight_texts = [i for i in quote.split(".") if len(i) > 0]
        #     highlight_texts = [i for i in highlight_texts if len((re.sub(r"\s+", " ", i)).split(" ")) > 3]
        #     new_highlight_texts = []
        #     highlight_texts = [i.strip() for i in highlight_texts]
        #     for i in highlight_texts:
        #         new_highlight_texts.extend([j for j in i.split("<start><end>") if len(j) > 3])
        #     highlight_texts = new_highlight_texts
        #     not_found_texts = {highlight_text: True for highlight_text in highlight_texts}
        #     # Loop through the pages to search for and highlight text
        #     for highlight_text in highlight_texts:
        #         page_ = pdf_writer[main_page]
        #         highlights = page_.get_textpage().search(highlight_text, quads=True)
        #         # If highlights found, add them as annotations
        #         if highlights:
        #             for highlight in highlights:
        #                 page_.add_highlight_annot(highlight)
        #             not_found_texts[highlight_text] = False
        #     print(
        #         f"Part1 : Not found texts: {len([key for key, value in not_found_texts.items() if value])} ----- ALL: {len(highlight_texts)}"
        #     )
        #     for key, value in not_found_texts.items():
        #         if value:
        #             sentence = key + ""
        #             for _ in range(30):
        #                 last_idx = 0
        #                 missing_count = 0
        #                 page_ = pdf_writer[main_page]
        #                 seach_text = ""
        #                 for idx, i in enumerate(sentence):
        #                     seach_text += i
        #                     highlights = page_.get_textpage().search(seach_text, quads=True)
        #                     if not highlights:
        #                         seach_text = seach_text[:-1]
        #                         missing_count += 1
        #                     else:
        #                         last_idx = idx + 1
        #                         missing_count = 0
        #                     if missing_count >= 5:
        #                         break
        #                 if len(seach_text.split(" ")) >= 3 and len(seach_text) >= 10:
        #                     highlights = page_.get_textpage().search(seach_text, quads=True)
        #                     # If highlights found, add them as annotations
        #                     if highlights:
        #                         for highlight in highlights:
        #                             page_.add_highlight_annot(highlight)
        #                         if last_idx == -999:
        #                             not_found_texts[key] = False
        #                             break
        #                 if last_idx == -999:
        #                     break
        #                 else:
        #                     sentence = sentence[last_idx:]
        #     print(
        #         f"Part2: Not found texts: {len([key for key, value in not_found_texts.items() if value])} ----- ALL: {len(highlight_texts)}"
        #     )
        pdf_bytes = pdf_writer.write()
        # name = f"data_tabular/citation_highlight/{str(uuid.uuid4())}.pdf"
        # # Save the output as a new PDF file (optional)
        # with open(name, "wb") as output_pdf:
        #     output_pdf.write(pdf_bytes)
        # Close the documents
        pdf_writer.close()
        pdf.close()
        client = Minio(
            settings.MINIO_ENDPOINT,
            access_key=settings.MINIO_ACCESS_KEY,
            secret_key=settings.MINIO_SECRET_KEY,
            secure=settings.MINIO_SECURE,
        )
        client.put_object(
            settings.MINIO_BUCKET_NAME,
            out_url,
            io.BytesIO(pdf_bytes),
            len(pdf_bytes),
        )
        # os.remove(name)
        return main_page
