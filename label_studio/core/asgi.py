"""This file and its contents are licensed under the Apache License 2.0. Please see the included NOTICE for copyright information and LICENSE for a copy of the license.
"""

import os

from channels.auth import AuthMiddlewareStack
from channels.routing import ProtocolTypeRouter, URLRouter
from channels.security.websocket import AllowedHostsOriginValidator
from django.core.asgi import get_asgi_application

from data_manager.routing import websocket_urlpatterns as websocket_urlpatterns_dm
from export_version.routing import websocket_urlpatterns as websocket_urlpatterns_ev
from notifications.routing import websocket_urlpatterns as websocket_urlpatterns_noti_center
from projects.routing import websocket_urlpatterns as websocket_urlpatterns_projects
from temporary_storage.routing import websocket_urlpatterns as websocket_urlpatterns_temp_storage

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')


# Print "Welcome" after launching the Django application
# print("Welcome")
# from django.conf import settings
# from fiftyone_connector.tasks import cronjob_update_coco_format_task
# cronjob_update_coco_format_task.delay(user_id=settings.USER_DMS_ID_DEFAULT)
# from clearml_connector.tasks import cronjob_update_yolo_format_task
# cronjob_update_yolo_format_task.delay(user_id=settings.USER_DMS_ID_DEFAULT)

django_asgi_app = get_asgi_application()
application = ProtocolTypeRouter({
    "http": django_asgi_app,
    "websocket": AllowedHostsOriginValidator(
        AuthMiddlewareStack(
            URLRouter(
                [
                    *websocket_urlpatterns_dm,
                    *websocket_urlpatterns_ev,
                    *websocket_urlpatterns_noti_center,
                    *websocket_urlpatterns_projects,
                    *websocket_urlpatterns_temp_storage,
                ]
            )
        )
    ),
})
