"""This file and its contents are licensed under the Apache License 2.0. Please see the included NOTICE for copyright information and <PERSON>IC<PERSON><PERSON> for a copy of the license.
"""
import os
import pathlib

from core.settings.base import *

DMS_HOST = get_env('DMS_HOST', None)
DMS_VERSION = get_env('DMS_VERSION', 2)

# DJANGO_DB = get_env('DJANGO_DB', DJANGO_DB_POSTGRESQL)
DJANGO_DB = get_env('DJANGO_DB', DJANGO_DB_SQLITE)
DATABASES = {'default': DATABASES_ALL[DJANGO_DB]}

MIDDLEWARE.append('organizations.middleware.DummyGetSessionMiddleware')
MIDDLEWARE.append('core.middleware.UpdateLastActivityMiddleware')

ADD_DEFAULT_ML_BACKENDS = False

LOGGING['root']['level'] = get_env('LOG_LEVEL', 'WARNING')

DEBUG = get_bool_env('DEBUG', False)

DEBUG_PROPAGATE_EXCEPTIONS = get_bool_env('DEBUG_PROPAGATE_EXCEPTIONS', False)

SESSION_COOKIE_SECURE = False

SESSION_ENGINE = "django.contrib.sessions.backends.signed_cookies"

RQ_QUEUES = {}

SENTRY_DSN = get_env(
    'SENTRY_DSN',
    None
)
SENTRY_ENVIRONMENT = get_env('SENTRY_ENVIRONMENT', 'opensource')

FRONTEND_SENTRY_DSN = get_env(
    'FRONTEND_SENTRY_DSN',
    None)
FRONTEND_SENTRY_ENVIRONMENT = get_env('FRONTEND_SENTRY_ENVIRONMENT', 'opensource')

EDITOR_KEYMAP = json.dumps(get_env("EDITOR_KEYMAP"))

from label_studio import __version__
from label_studio.core.utils import sentry

sentry.init_sentry(release_name='label-studio', release_version=__version__)

# we should do it after sentry init
from label_studio.core.utils.common import collect_versions

versions = collect_versions()

# in Label Studio Community version, feature flags are always ON
FEATURE_FLAGS_DEFAULT_VALUE = True
# or if file is not set, default is using offline mode
FEATURE_FLAGS_OFFLINE = get_bool_env('FEATURE_FLAGS_OFFLINE', True)

from core.utils.io import find_file

FEATURE_FLAGS_FILE = get_env('FEATURE_FLAGS_FILE', 'feature_flags.json')
FEATURE_FLAGS_FROM_FILE = True
try:
    from core.utils.io import find_node

    find_node('label_studio', FEATURE_FLAGS_FILE, 'file')
except IOError:
    FEATURE_FLAGS_FROM_FILE = False

STORAGE_PERSISTENCE = get_bool_env('STORAGE_PERSISTENCE', True)

ELASTICSEARCH_DSL={
    'default': {
        'hosts': f"{get_env('ES_SCHEME')}://{get_env('ES_HOST')}:{get_env('ES_PORT')}",
        'http_auth': (get_env('ES_USERNAME'), get_env('ES_PASSWORD'))
    }
}

DEFAULT_ATTRIBUTES = {
    'view_state': {
        'type': "DropdownList",
        'maxLength': 500,
        'values': 'New,Labeling,Inreview,Completed',
        "value": 'New'
    },
    'view_priority': {
        'type': "Number",
        'maxValue': 5,
        "value": "0",
    },
    'view_category': {
        'type': "Text",
        'maxLength': 1000,
        "value": "",
    },
    'view_folder': {
        'type': "Text",
        'maxLength': 1000,
        "value": "/",
    },
    'view_annotation_result': {
        'type': "Text",
        'maxLength': 5000,
        "value": ""
    },
    'view_archive': {
        'type': 9,
        'maxValue': 1,
        "value": "false"
    },
    'view_workflow_step': {
        'type': "Text",
        'maxLength': 1000,
        "value": 'New'
    },
    'view_workflow_step_id': {
        'type': "Text",
        'maxLength': 1000,
        "value": '00000000-0000-0000-0000-000000000000'
    },
    'annotate_instance': {
        'type': "Text",
        'maxLength': 5000,
        'value': ''
    },
    'annotate_history': {
        'type': "Text",
        'maxLength': 5000,
        'value': ''
    }
}

DEFAULT_VIEW_ATTRIBUTES = [{'code': key, 'value': attr['value']} for key, attr in DEFAULT_ATTRIBUTES.items()]

DEFAULT_FILE_FIELDS = {
    'viewState': "New",
    'priority': '0',
    'pathFolder': '/',
    'isArchive': False,
    'viewWorkFlowStep': "New",
    'viewWorkFlowStepId': "00000000-0000-0000-0000-000000000000"
}

FILE_ATTRB_MAPPING_FIELD = {
    'view_state': 'viewState',
    'view_archive': 'isArchive',
    'view_priority': 'priority',
    'view_folder': 'pathFolder',
    'view_workflow_step': 'viewWorkFlowStep',
    'view_workflow_step_id': 'viewWorkFlowStepId'
}
# attributes view_ default of project


# DEFAULT_CLASS_TYPE = "PolygonLabels,\
#     BoundingboxLabels,\
#     EllipseLabels,\
#     KeypointLabels,\
#     BrushLabels,\
#     Labels,\
#     Choices\
#     "

TOOLS_AVAILABLE = [
    {
        "name": "RectangleLabels", 
        "type": "Bounding-Box",
        "sample": "[{'x':100, 'y':200, 'width':80, 'height':100, 'class_name':'cyst'}, {'x':100, 'y':200, 'width':80, 'height':100, 'class_name':'noacne'}]"
    },
    {
        "name": "EllipseLabels", 
        "type": "Circle",
        "sample": "[{'x': 1356.0, 'y': 3420.0, 'radius': 5.015749367528976, 'class_name': 'cyst'}, {'x': 1356.0, 'y': 3420.0, 'radius': 5.015749367528976, 'class_name': 'noacne'}]"
    },
    {
        "name": "PolygonLabels", 
        "type": "Polygon",
        "sample": "[{'points':[[100,100], [200, 200], [150, 300]], 'class_name':'cyst'}, {'points':[[100,100], [200, 200], [150, 300]], 'class_name':'noacne'}]"
    },
    {
        "name": "ScoringLabels", 
        "type": "Scoring",
        "sample": "[{'score':[[1, 1, 3], [2, 2, 1],  [1, 3, 2]], 'size':3}]"
    },
    {
        "name": "BrushLabels",
        "type": "Brush",
        "sample": "[{'rle':[1, 1, 3, 2, 2, 1, 1, 3, 2], 'class_name':'car'}, {'rle':[1, 1, 3, 2, 2, 1, 1, 3, 2], 'class_name':'airplane'}]"
    },
    {
        "name": "Taxonomy",
        "type": "Taxonomy",
        "sample": "0,0,1,0,0"
    },
    {
        "name": "Tools",
        "type": "Instance",
        "sample": "[{'instance_id': 'QSJUykBRg4','instance_name': 'person', 'components': [{'type': 'rectangle', 'class_name': 'bbox', 'value': {'x': 100, 'y': 100, 'width': 100, 'height': 100}}, {'type': 'keypoint', 'class_name': 'left hip', 'value': {'x': 100, 'y': 100, 'radius': 10}}, {'type': 'keypoint', 'class_name': 'right hip', 'value': {'x': 100, 'y': 100, 'radius': 10}}]}, {'instance_id': 'wGwrGVXjbk','instance_name': 'person', 'components': [{'type': 'rectangle', 'class_name': 'bbox', 'value': {'x': 100, 'y': 100, 'width': 100, 'height': 100}}, {'type': 'keypoint', 'class_name': 'left hip', 'value': {'x': 100, 'y': 100, 'radius': 10}}, {'type': 'keypoint', 'class_name': 'right hip', 'value': {'x': 100, 'y': 100, 'radius': 10}}]}]"
    }
]

DEFAULT_CLASS_TYPE = [
    "PolygonLabels",
    "Polygon",
    "RectangleLabels",
    "Rectangle",
    "EllipseLabels",
    "Ellipse",
    "KeypointLabels",
    "Keypoint",
    "BrushLabels",
    "Brush",
    "Labels",
    "Label",
    "Choices",
    "Choice"
]

DEFAULT_ANNOTATE_ATTRB_DESCRIPTION = {
    "classtree": {
        "type": "Taxonomy",
        "instance_id": "all",
        "result_type": "list",
        "note": "result is a list of label-branch in classtree"
    },
    "PolygonLabels": {
        "type": "Polygon",
        "instance_id": "nanoid",
        "result_type": "list",
        # "note": "result is a list included { 'label_name': '', 'points': [(x1, y1), (x2, y2), (x3, y3),...]}",
        "note": "result is a list included [(x1, y1), (x2, y2), (x3, y3),...]",
    },
    "RectangleLabels": {
        "type": "Rectangle",
        "instance_id": "nanoid",
        "result_type": "list",
        # "note": "result is a list included { 'label_name': '', 'points': (x, y, w, h)}",
        "note": "result is a list included (x, y, w, h)"
    },
    "EllipseLabels": {
        "type": "Ellipse",
        "instance_id": "nanoid",
        "result_type": "list",
        # "note": "result is a list included { 'label_name': '', 'points': (x_center, y_center, R)}",
        "note": "result is a list included (x_center, y_center, R)"
    }
}

ANNOTATE_TYPE = {
    "classification": "classtree",
}

ATTR_PREFIX_META = 'meta_'
ATTR_PREFIX_DEFAULT = 'view_'
ATTR_PREFIX_CLASS = 'class_'
ATTR_PREFIX_ANNOTATE = 'annotate_'
ATTR_PREFIX_ANNOTATE_CLASSTREE = 'annotate_attr_classification_'
ATTR_ANNOTATION_RESULT = 'view_annotation_result'
ATTR_VIEW_FOLDER = 'view_folder'
ATTR_VIEW_STATE = 'view_state'
ATTR_VIEW_PRIORITY = 'view_priority'

PERMISSIONS_AIPrediction = ["DatasetManagement.Update"]
PERMISSIONS_AIPREPROCESSING = ["DatasetManagement.Update"]
PERMISSIONS_ANNOTATOR = ["DatasetManagement.Update"]
PERMISSIONS_VIEWER = ["DatasetManagement.Update"]
PERMISSIONS_COMPLETED = []
PERMISSIONS_IN_WORKFLOW = {
    "AIPreProcessing": PERMISSIONS_AIPREPROCESSING,
    "AIPrediction": PERMISSIONS_AIPrediction,
    "Annotator": PERMISSIONS_ANNOTATOR,
    "Reviewer": PERMISSIONS_VIEWER,
    "Completed": PERMISSIONS_COMPLETED
}

# Workers for concurrency upload image to s3
MAX_WORKERS_UPLOAD = 10

# Celery settings
# CELERY_BROKER_URL = "redis://localhost:6379"
if REDIS_SERVER == '127.0.0.1' or REDIS_SERVER == 'redis' or REDIS_SERVER == 'redis_stag':
    CHANNEL_LAYERS = {
        'default': {
            'BACKEND': 'channels_redis.core.RedisChannelLayer',
            'CONFIG': {
                "hosts": [(REDIS_SERVER, 6379)],
            },
        },
    }
    CELERY_BROKER_URL = "redis://{}:6379".format(REDIS_SERVER)
else:
    # ************:6379,password=ntQQqFnJhsMd5aWnfLn94syMp4PIsnTI,abortConnect=false
    REDIS_CONFIG_SPLIT = REDIS_SERVER.split(',')
    REDIS_HOST = REDIS_CONFIG_SPLIT[0].split(':')[0]
    REDIS_PORT = REDIS_CONFIG_SPLIT[0].split(':')[1]
    REDIS_PASSWORD = REDIS_CONFIG_SPLIT[1].split('=')[1]
    if get_env('ENVIRONMENT_APP', '') == "staging":
        CHANNEL_LAYERS = {
            'default': {
                'BACKEND': 'channels_redis.core.RedisChannelLayer',
                'CONFIG': {
                    "hosts": [(REDIS_HOST, REDIS_PORT)],
                },
            },
        }
        CELERY_BROKER_URL = "redis://{}:{}/1".format(REDIS_HOST, REDIS_PORT)
    else:
        CHANNEL_LAYERS = {
            'default': {
                'BACKEND': 'channels_redis.core.RedisChannelLayer',
                'CONFIG': {
                    "hosts": ["redis://default:{}@{}:{}/0".format(REDIS_PASSWORD, REDIS_HOST, REDIS_PORT)]
                },
            },
        }
        CELERY_BROKER_URL = "redis://default:{}@{}:{}/5".format(REDIS_PASSWORD, REDIS_HOST, REDIS_PORT)

CELERY_RESULT_BACKEND = "django-db"
CELERY_CACHE_BACKEND = "django-cache"

LOGS_MANAGEMENT_HOST = get_env('LOGS_MANAGEMENT_HOST', 'localhost')
LOGS_MANAGEMENT_PORT = get_env('LOGS_MANAGEMENT_PORT', '9880')
LOGS_MANAGEMENT_TAG = get_env('LOGS_MANAGEMENT_TAG', 'tas_fe.log')

AIS_HOST = get_env('AIS_HOST', 'https://ai-segment.torus.ai')
MINIO_HOST = get_env('MINIO_HOST', 'https://minio.taureau.ai')
TAS_HOST = get_env('TAS_HOST', 'https://tas.torus.ai')

NOTISERVICE_HOST = get_env('NOTISERVICE_HOST', 'http://*************:8000')

APPLICATION_TYPE = get_env('APPLICATION_TYPE', 'Tas')

MINIO_ENDPOINT = get_env('MINIO_ENDPOINT', '***********:9000')
MINIO_ACCESS_KEY = get_env('MINIO_ACCESS_KEY', 'taureauai')
MINIO_SECRET_KEY = get_env('MINIO_SECRET_KEY', 'hGH5nbCdvexNaFG')
MINIO_SECURE = get_bool_env('MINIO_SECURE', False)
MINIO_REGION = get_env('MINIO_REGION', 'us-east-1')
MINIO_BUCKET_NAME = get_env('MINIO_BUCKET_NAME', 'dms')

from minio import Minio
MINIO_CLIENT = Minio(endpoint=MINIO_ENDPOINT,
                    access_key=MINIO_ACCESS_KEY,
                    secret_key=MINIO_SECRET_KEY,
                    secure=MINIO_SECURE,
                    region=MINIO_REGION)

ES_HOST = get_env('ES_HOST', '*************')
ES_PORT = int(get_env('ES_PORT', 9201))
ES_SCHEME = get_env('ES_SCHEME', 'http')
ES_USERNAME = get_env('ES_USERNAME', 'elastic')
ES_PASSWORD = get_env('ES_PASSWORD', 'taureauai')
ES_INDEX_ATTRB = get_env('ES_INDEX_ATTRB', 'file_annotation_attributes')
from elasticsearch import Elasticsearch
ES_CONNECTION = Elasticsearch("{}://{}:{}".format(ES_SCHEME, ES_HOST, ES_PORT),
                              basic_auth=(ES_USERNAME, ES_PASSWORD),
                              max_retries=5, retry_on_timeout=True)

DMS_MYSQL = {
    "HOST": get_env('DMS_MYSQL_HOST', '***********'),
    "PORT": int(get_env('DMS_MYSQL_PORT', 9336)),
    "USER": get_env('DMS_MYSQL_USER', 'root'),
    "PASSWORD": get_env('DMS_MYSQL_PASSWORD', 'taureauai'),
    "DATABASE": get_env('DMS_MYSQL_DATABASE', 'dms')
}

VIEW_ANNOTATION_RESULT_ATTRB_ID = get_env('VIEW_ANNOTATION_RESULT_ATTRB_ID', '08dac23b-dfd1-4b7a-86c7-be85aa8bed39')
VIEW_FOLDER_ATTRB_ID = get_env('VIEW_FOLDER_ATTRB_ID', '08dac236-8a77-4d68-8f0a-23ed74404988')

DATA_VISUALIZATION_HOST = get_env('DATA_VISUALIZATION_HOST', 'http://***********')
DATA_VISUALIZATION_PORT = get_env('DATA_VISUALIZATION_PORT', 8880)
DATA_VISUALIZATION_FIFTYONE_PORT = get_env('DATA_VISUALIZATION_FIFTYONE_PORT', 5151)
DATA_TRAINING_HOST = get_env('DATA_TRAINING_HOST', 'https://app.clearml-stag.torus.ai')
DATA_TRAINING_PORT = get_env('DATA_TRAINING_PORT', 8000)

USER_DMS_ID_DEFAULT = get_env('USER_DMS_ID_DEFAULT', '08dacf65-45bd-41b3-8304-010bb595afb2')

ONEK_TEMPLATE_FILE_ID = get_env('ONEK_TEMPLATE_FILE_ID', '1q6Sbu8ClyKtb6vTGTqVxGNsjsFohsr7D4GcglrAz6mk')
ONEK_TEMPLATE_DESTINATION_FOLDER_ID = get_env('ONEK_TEMPLATE_DESTINATION_FOLDER_ID', '1oOce8SkydoCu0Me_Iex5MGmkIdggp9Ib')

OPENAI_API_KEY = get_env('OPENAI_API_KEY', '')