{"name": "@taureau/app", "version": "1.1.0", "description": "", "main": "get-build.js", "scripts": {"start": "export NODE_OPTIONS=--openssl-legacy-provider && npx webpack --watch", "test": "CSS_PREFIX='ls-' jest", "build": "export NODE_OPTIONS=\"--openssl-legacy-provider --max-old-space-size=8192\" && npx webpack", "build:production": "export NODE_OPTIONS=\"--openssl-legacy-provider --max-old-space-size=8192\" && NODE_ENV=production npx webpack", "download:all": "npm run download:editor && npm run download:dm && npm run build", "download:editor": "npm run download -- lsf", "download:dm": "npm run download -- dm", "download": "NO_BUILD=true node get-build.js"}, "keywords": [], "browser": {}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.12.13", "@babel/plugin-proposal-class-properties": "^7.12.13", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.13", "@babel/plugin-proposal-optional-chaining": "^7.12.17", "@babel/plugin-transform-runtime": "^7.12.15", "@babel/preset-env": "^7.12.13", "@babel/preset-react": "^7.12.13", "@babel/preset-typescript": "^7.13.0", "@babel/runtime": "^7.12.13", "@sentry/browser": "^6.8.0", "@sentry/react": "^6.8.0", "@sentry/tracing": "^6.8.0", "@svgr/webpack": "^5.5.0", "@types/chroma-js": "^2.1.3", "@types/codemirror": "^5.60.15", "@types/enzyme": "^3.10.8", "@types/expect-puppeteer": "^4.4.5", "@types/jest": "^26.0.21", "@types/jest-environment-puppeteer": "^4.4.1", "@types/lodash.debounce": "^4.0.7", "@types/mkdirp": "^1.0.1", "@types/node-fetch": "^2.5.8", "@types/puppeteer": "^5.4.3", "@types/react": "^17.0.2", "@types/react-date-range": "^1.4.4", "@types/react-dom": "^17.0.1", "@types/react-router-dom": "^5.1.7", "@types/react-window": "^1.8.5", "@types/react-window-infinite-loader": "^1.0.6", "@types/rimraf": "^3.0.0", "@types/strman": "^2.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "@wojtekmaj/enzyme-adapter-react-17": "^0.4.1", "autoprefixer": "^10.4.14", "babel-jest": "^26.6.3", "babel-loader": "^8.2.2", "chroma-js": "^2.1.1", "codemirror": "^5.65.16", "css-loader": "^5.0.1", "css-minimizer-webpack-plugin": "^3.0.2", "daisyui": "^3.1.10", "date-fns": "^2.17.0", "dotenv": "^10.0.0", "enzyme": "^3.11.0", "enzyme-to-json": "^3.6.1", "eslint": "^7.20.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.6.0", "expect-puppeteer": "^4.4.0", "history": "^4.10.1", "html-react-parser": "^1.4.14", "jest": "^26.6.3", "jest-puppeteer": "^4.4.0", "mini-css-extract-plugin": "^1.3.7", "mkdirp": "^1.0.4", "node-fetch": "^2.6.1", "postcss": "^8.4.24", "puppeteer": "^8.0.0", "react": "^17.0.1", "react-codemirror2": "^7.2.1", "react-dom": "^17.0.1", "react-icons": "^4.10.1", "react-router": "^5.2.0", "react-router-dom": "^5.2.0", "react-singleton-hook": "^3.1.1", "readline": "^1.3.0", "rimraf": "^3.0.2", "shallow-equal": "^1.2.1", "source-map-loader": "^1.1.3", "strman": "^2.0.1", "style-loader": "^2.0.0", "stylus": "^0.54.8", "stylus-loader": "^5.0.0", "tailwindcss": "^3.3.2", "terser-webpack-plugin": "^5.1.1", "truncate-middle": "^1.0.6", "typescript": "^4.2.2", "webpack": "^5.75.0", "webpack-cli": "^4.10.0"}, "babel": {"presets": [["@babel/preset-react", {"runtime": "automatic"}], "@babel/preset-typescript", ["@babel/preset-env", {"targets": {"browsers": ["last 2 Chrome versions"]}}]], "plugins": ["@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-optional-chaining", "@babel/plugin-proposal-nullish-coalescing-operator"]}, "jest": {"projects": [{"displayName": "integration", "preset": "jest-puppeteer", "testTimeout": 25000, "testMatch": ["<rootDir>/test/integration/**/*.test.ts", "<rootDir>/test/integration/**/*.test.tsx"], "setupFilesAfterEnv": ["expect-puppeteer"]}, {"displayName": "unit", "snapshotSerializers": ["enzyme-to-json/serializer"], "setupFilesAfterEnv": ["<rootDir>/test/setup/unit.ts"], "testMatch": ["<rootDir>/test/unit/**/*.test.ts", "<rootDir>/test/unit/**/*.test.tsx"]}]}, "jest-puppeteer": {"launch": {"headless": false}, "browserContext": "default"}, "dependencies": {"@ant-design/icons": "4.0.0", "@ckeditor/ckeditor5-react": "^7.0.0", "@rc-component/trigger": "^1.14.0", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@syncfusion/ej2-react-filemanager": "^20.2.45", "@tanstack/react-virtual": "^3.0.0-alpha.0", "@taureau/ckeditor5-custom": "^0.0.1", "@taureau/core": "^1.3.4", "@taureau/editor": "^1.5.1", "@taureau/ui": "^4.1.0", "@types/react-outside-click-handler": "^1.3.4", "antd": "^4.24.10", "chart.js": "^4.2.0", "chartjs-plugin-datalabels": "^2.2.0", "classnames": "^2.3.2", "copy-webpack-plugin": "^11.0.0", "d3": "^7.8.5", "fast-deep-equal": "^3.1.3", "immer": "^10.0.2", "immutability-helper": "^3.1.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "moment": "^2.29.4", "nuka-carousel": "^6.0.3", "onnxruntime-web": "^1.14.0", "postcss-loader": "^7.3.3", "postcss-preset-env": "^8.5.1", "rc-tooltip": "^6.0.1", "react-base-table": "^1.13.4", "react-chartjs-2": "^5.2.0", "react-color": "^2.19.3", "react-csv": "^2.2.2", "react-date-range": "^1.4.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-file-base64": "^1.0.3", "react-flow": "^1.0.3", "react-helmet": "^6.1.0", "react-highlight-words": "^0.18.0", "react-infinite-scroll-component": "^6.1.0", "react-lint": "^3.3.0", "react-outside-click-handler": "^1.3.0", "react-select": "^5.4.0", "react-styled-toggle": "^1.1.0", "react-table": "^7.8.0", "react-window-infinite-loader": "^1.0.9", "reactflow": "^11.10.4", "reselect": "^4.1.8", "sass": "^1.62.1", "sass-loader": "^13.2.2", "swr": "^2.2.1", "tailwind-merge": "^1.14.0", "uuid": "^9.0.0", "virtua": "^0.11.0", "websocket": "^1.0.34", "zustand": "^4.3.8"}}