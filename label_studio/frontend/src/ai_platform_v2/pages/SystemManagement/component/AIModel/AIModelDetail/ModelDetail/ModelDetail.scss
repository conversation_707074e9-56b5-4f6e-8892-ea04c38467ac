.model-category-detail-side {
  min-width: 360px;
  min-height: 300px;
  display: flex;
  padding: 12px;
  flex-direction: column;
  align-items: flex-start;

  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: #fff;
}

.btn-delete-enable {
  transition: all 0.3s;

  &:hover {
    background: var(--red-red-5, rgba(230, 46, 46, 0.05)) !important;
  }
}

.embedding-result-icon-copy {
  svg path {
    transition: all 0.3s;
  }

  &:hover {
    svg path {
      fill: #3361ff;
    }
  }
}
