import React, { memo, useMemo, useState } from "react";
import { useHistory, useLocation } from "react-router";

// import ModelDetail from "./ModelDetail";
import ListModel from "./ListModel";
import ModelDetail from "./ModelDetail";
import { regUUID } from "../util/const";

export const VIEW_MODE = {
  LIST: "list",
  DETAIL: "detail",
};

interface AIModelDetailProps {
  [key: string]: any;
}

const AIModelDetail = (props: AIModelDetailProps) => {
  const {
    selectedModelCategory,
    viewMode,
    setViewMode,
    selectedModel,
    setSelectedModel,
    backToModelCategory,
  } = props;
  const location = useLocation();

  const modelCategoryID = useMemo(() => {
    const urlParams = new URLSearchParams(location.search);
    const activeModelCategoryID = urlParams.get("categoryId");

    if (activeModelCategoryID && activeModelCategoryID?.match(regUUID)) {
      return activeModelCategoryID;
    }

    return undefined;
  }, [location.search]);

  const renderContent = useMemo(() => {
    if (viewMode === VIEW_MODE.DETAIL) {
      return (
        <ModelDetail
          modelCategoryID={modelCategoryID}
          backToList={() => setViewMode(VIEW_MODE.LIST)}
          selectedModel={selectedModel}
        />
      );
    }
    return (
      <ListModel
        modelCategory={selectedModelCategory}
        modelCategoryID={modelCategoryID}
        backToList={backToModelCategory}
        goToModelDetail={() => setViewMode(VIEW_MODE.DETAIL)}
        setSelectedModel={setSelectedModel}
      />
    );
  }, [selectedModelCategory, modelCategoryID, selectedModel, viewMode]);

  return (
    <div className="w-full h-full flex flex-col relative">{renderContent}</div>
  );
};

export default memo(AIModelDetail);
