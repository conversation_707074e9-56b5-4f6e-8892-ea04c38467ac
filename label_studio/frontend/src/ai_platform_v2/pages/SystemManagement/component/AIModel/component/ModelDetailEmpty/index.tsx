import IconPlus from "@/ai_platform_v2/assets/Icons/IconPlus";
import { HomeNoData } from "@/ai_platform_v2/assets/Images/NoData";
import "./ModelDetailEmpty.scss";

const ModelDetailEmpty = ({ goToModelDetail, showCreateModel = true }: any) => {
  return (
    <div className="h-full flex flex-col justify-center items-center gap-[20px]">
      <div className="flex flex-col items-center gap-[16px]">
        <img src={HomeNoData} alt="no-data" />
        <div className="flex flex-col items-center">
          <div className="flex text-[#346] text-[24px] font-medium leading-[36px]">
            Empty!
          </div>
          <div className="flex text-[#62708C] text-[16px] font-light leading-[24px]">
            There’re no AI Model here.
          </div>
        </div>
      </div>
      {showCreateModel && (
        <div
          className="btn-add-new-model-category-detail cursor-pointer"
          onClick={goToModelDetail}
        >
          <IconPlus size={30} color="#3361FF" />
          New model
        </div>
      )}
    </div>
  );
};

export default ModelDetailEmpty;
