import { Button, Input } from "@taureau/ui";
import classNames from "classnames";
import { useCallback, useEffect, useState } from "react";
import {
  DEFAULT_PAGE_SIZE,
  INIT_MODEL_CATEGORY_DETAIL_PARAMS,
} from "../../util/const";
import { debounce } from "lodash";
import IconSearch from "@/ai_platform_v2/assets/Icons/IconSearch";
import IconPlus from "@/ai_platform_v2/assets/Icons/IconPlus";
import IconBackSpace from "../../assets/IconBackSpace";
import MiniPagination from "@/ai_platform_v2/pages/Home/component/MiniPagination";
import ModelDetailLoading from "../../component/ModelDetailLoading";
import ModelDetailCard from "../../component/ModelDetailCard";
import ModelDetailNotFound from "../../component/ModelDetailNotFound";
import ModelDetailEmpty from "../../component/ModelDetailEmpty";
import "./ListModel.scss";
import { useAPI } from "@/providers/ApiProvider";
import Message from "@/ai_platform_v2/component/Message/Message";

interface Props {
  [key: string]: any;
}

const ListModel = (props: Props) => {
  const {
    modelCategory,
    modelCategoryID,
    backToList,
    goToModelDetail,
    setSelectedModel,
  } = props;
  const api = useAPI();

  const [loading, setLoading] = useState(true);
  const [isFocusSearch, setFocusSearch] = useState(false);

  const [valueSearchName, setValueSearchName] = useState();
  const [modelParams, setModelParams] = useState(
    INIT_MODEL_CATEGORY_DETAIL_PARAMS
  );

  const [total, setTotal] = useState(0);
  const [modelList, setModelList] = useState([]);

  const debounceUpdateSearchValueStore = useCallback(
    debounce((value: string | any) => {
      setModelParams({ ...modelParams, currentPage: 1, searchValue: value });
    }, 300),
    [modelParams]
  );

  const onChangeSearchValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value: any = e.target.value;

    setValueSearchName(value);

    debounceUpdateSearchValueStore(value.trim());
  };

  const handleChangePage = useCallback(
    (page, pageSize) => {
      setModelParams({ ...modelParams, currentPage: page });
    },
    [modelParams]
  );

  const fetchModelCategory = useCallback(async () => {
    if (!modelCategoryID) {
      return;
    }

    const params: any = {
      pk: modelCategoryID,
      page: modelParams.currentPage,
      pageSize: DEFAULT_PAGE_SIZE,
    };

    if (modelParams?.searchValue) {
      params.keyword = modelParams.searchValue;
    }

    setLoading(true);

    const res: any = await api.callApi("categoryModels", {
      params,
    });

    setLoading(false);

    if (res?.success) {
      setModelList(res.items);
      setTotal(res.total);
    }
  }, [modelCategoryID, modelParams]);

  const handleUpdateModelStatus = useCallback(
    async (model, modelStatus) => {
      const res: any = await api.callApi("updateCategoryModels", {
        params: { pk: modelCategoryID, modelId: model?.id },
        body: {
          modelName: model.modelName,
          apiEndpoint: model.apiEndpoint,
          status: modelStatus,
        },
      });

      if (res?.success) {
        Message.success({
          title: "Success",
          content: `Model ${model.modelName} ${
            modelStatus === "Publish" ? "published" : "cancelled publishing"
          } `,
        });
        fetchModelCategory();
      } else {
        Message.error({
          title: "Error",
          content: `Model ${model.modelName} cannot ${
            modelStatus === "Publish" ? "publish" : "cancel publishing"
          }`,
        });
      }
    },
    [modelCategoryID, fetchModelCategory]
  );

  useEffect(async () => {
    if (modelCategoryID) {
      await fetchModelCategory();
    }
  }, [modelCategoryID, modelParams]);

  return (
    <>
      <div className="w-full h-full flex flex-col gap-[12px] px-[8px]">
        <div
          className="h-full flex flex-col justify-between"
          // style={{ height: "calc(100% - 70px)" }}
        >
          {loading || modelList?.length || modelParams.searchValue ? (
            <>
              <div className="h-full flex flex-col items-start gap-[12px]">
                <div className="flex items-start justify-end gap-[9px] w-full">
                  <Input
                    placeholder="Search by model name"
                    className={classNames(
                      "bg-white min-w-[177px] h-[36px] rounded-[10px] p-[8px] model-category-search",
                      {
                        "model-category-search-focus":
                          isFocusSearch || valueSearchName,
                      }
                    )}
                    inputClassName={classNames(
                      "h-full w-full text-[12px] leading-[18px]",
                      {
                        "text-[#3361FF]": !isFocusSearch,
                      }
                    )}
                    iconLeft={
                      <IconSearch
                        size={20}
                        color={
                          isFocusSearch || valueSearchName
                            ? "#3361FF"
                            : "#C3CAD9"
                        }
                      />
                    }
                    iconLeftClassName="flex m-0 mr-[8px]"
                    size="sm"
                    value={valueSearchName}
                    onChange={onChangeSearchValue}
                    maxLength={100}
                    autoFocus={isFocusSearch}
                    onFocus={() => setFocusSearch(true)}
                    onBlur={() => setFocusSearch(false)}
                  />
                </div>

                {loading ? (
                  <div className="grid grid-cols-[repeat(auto-fill,_220px)] place-content-evenly p-[8px] items-start gap-x-[20px] gap-y-[16px] w-full">
                    <ModelDetailLoading />
                  </div>
                ) : modelList?.length ? (
                  <div
                    className="min-h-[100px] grid grid-cols-[repeat(auto-fill,_220px)] p-[8px] items-start gap-x-[20px] gap-y-[16px] w-full overflow-auto scrollbar-h-sm"
                    style={{ placeContent: "start space-evenly" }}
                  >
                    <div
                      className="model-category-card-add-new"
                      onClick={() => {
                        goToModelDetail();
                        setSelectedModel();
                      }}
                    >
                      <IconPlus size={30} color="#D9D9D9" />
                      New model
                    </div>
                    {modelList.map((model: any) => (
                      <ModelDetailCard
                        key={model.id}
                        modelCategoryName={modelCategory?.name}
                        data={model}
                        onUpdateStatus={handleUpdateModelStatus}
                        onSelectModel={() => {
                          setSelectedModel(model);
                          goToModelDetail();
                        }}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <ModelDetailNotFound />
                  </div>
                )}
              </div>
              <div className="flex justify-end items-center absolute bottom-[10px] right-[15px]">
                <div className="flex w-fit">
                  <MiniPagination
                    currentPage={modelParams.currentPage}
                    pageSize={DEFAULT_PAGE_SIZE}
                    totalItems={total}
                    loadMoreData={handleChangePage}
                    isLoading={loading}
                  />
                </div>
              </div>
            </>
          ) : (
            <ModelDetailEmpty goToModelDetail={goToModelDetail} />
          )}
        </div>
      </div>
      <div className="w-full flex justify-start px-[22px] py-[10px]">
        <Button
          className="w-fit h-[30px] text-[14px] font-medium leading-[21px]"
          corner="Rounded"
          theme="Primary"
          size="xs"
          iconLeft={<IconBackSpace />}
          onClick={() => backToList?.()}
        >
          Back to list
        </Button>
      </div>
    </>
  );
};

export default ListModel;
