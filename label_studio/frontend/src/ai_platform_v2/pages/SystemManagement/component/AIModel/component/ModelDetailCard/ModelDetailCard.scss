.model-category-detail-card {
  border-radius: 15px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  background: #fff;
  box-shadow: 0px 2px 4px 0px rgba(28, 39, 76, 0.08);

  transition: background 0.3s;

  &-status {
    cursor: pointer;
    display: flex;
    padding: 2px 10px;
    justify-content: center;
    align-items: center;
    gap: 5px;
    align-self: stretch;

    border-radius: 15px;

    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 21px;

    transition:
      color 0.3s,
      background 0.3s;
  }

  &-status-loading {
    cursor: wait;
  }
}

.model-category-detail-card-publish {
  .model-category-detail-card-status {
    color: var(--Blue-Blue, #3361ff);
    background: var(--blue-blue-10, rgba(51, 97, 255, 0.1));

    &:hover {
      background: var(--blue-blue-20, rgba(51, 97, 255, 0.2));
    }
  }

  &:hover {
    background: var(--<PERSON>-<PERSON>-Grey-Blue-97, #f5f6f7);
  }
}

.model-category-detail-card-cancel {
  .model-category-detail-card-status {
    color: var(--Orange-Orange, #f63);
    background: var(--orange-orange-20, rgba(255, 102, 51, 0.2));

    &:hover {
      background: var(--orange-orange-30, rgba(255, 102, 51, 0.3));
    }
  }

  &:hover {
    background: var(--Gray-Blue-Grey-Blue-97, #f5f6f7);
  }
}

.model-category-detail-card-error {
  border: 1px solid var(--Red-Red-Dark-1, #cc1414) !important;

  .model-category-detail-card-status {
    cursor: not-allowed;
    color: var(--Red-Red, #e62e2e);
    background: var(--red-red-5, rgba(230, 46, 46, 0.05));
  }

  &:hover {
    background: linear-gradient(
        0deg,
        rgba(230, 46, 46, 0.05) 0%,
        rgba(230, 46, 46, 0.05) 100%
      ),
      #fff;
  }
}
