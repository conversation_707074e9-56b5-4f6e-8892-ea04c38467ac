import "./ModelDetailLoading.scss";

const NUMBER_OF_LOADING_ITEMS = 12;

const ModelDetailLoading = () => (
  <>
    {[...Array(NUMBER_OF_LOADING_ITEMS).keys()].map((key) => (
      <div key={key} className="skeleton model-detail-item-loading">
        <div className="w-full flex flex-col justify-center items-start gap-[8px]">
          <div className="flex w-[24px] h-[24px] aspect-square rounded-[5px] skeleton" />
          <div className="flex flex-col justify-center items-start gap-[4px]">
            <div className="flex w-[118px] h-[15px] rounded-[5px] skeleton" />
            <div className="flex w-[76px] h-[15px] rounded-[5px] skeleton" />
          </div>
        </div>
        <div className="flex w-[196px] h-[23px] rounded-[100px] skeleton" />
      </div>
    ))}
  </>
);

export default ModelDetailLoading;
