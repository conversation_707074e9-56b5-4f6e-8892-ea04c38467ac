.dropzone-description
    // position absolute
    // z-index -1
    // background: #fff
    &__content
        display: flex;
        width: 309px;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
        // transition: all 0.3s

    &__icon-upload
        display: flex;
        padding: 10px;
        margin-bottom: 10px
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
        border-radius: 10px;
        background: var(--gray-blue-grey-blue-95, #EDEFF2);
        transition: background .6s ease-out
        svg > path
            transition: fill .6s ease-out

    &__upload-description
        color: var(--gray-blue-grey-blue-55, #62708C);
        font-feature-settings: 'clig' off, 'liga' off;
        
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 30px;

    &__supported-types
        color: var(--gray-blue-grey-blue-70, #7D8FB3);
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;
        
        font-size: 11px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 166.667% */

    
    &__content-hovered
        display: flex;
        width: 294px;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;
        // transition: all 0.3s

    &__content-blue
        display: inline-flex
        color: var(--blue-blue, #3361FF);
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;
        
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 30px;