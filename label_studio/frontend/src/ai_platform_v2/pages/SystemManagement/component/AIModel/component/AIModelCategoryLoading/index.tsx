import "./AIModelCategoryLoading.scss";

const NUMBER_OF_LOADING_ITEMS = 12;

const AIModelCategoryLoading = () => (
  <>
    {[...Array(NUMBER_OF_LOADING_ITEMS).keys()].map((key) => (
      <div key={key} className="skeleton model-category-item-loading">
        <div className="flex w-[78px] h-[60px] aspect-square rounded-[10px] skeleton" />
        <div className="flex w-[107px] h-[15px] rounded-[5px] skeleton" />
      </div>
    ))}
  </>
);

export default AIModelCategoryLoading;
