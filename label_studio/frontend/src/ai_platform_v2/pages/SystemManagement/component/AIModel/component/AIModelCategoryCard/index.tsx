import IconEmbeddings from "../../assets/IconEmbeddings";
import "./AIModelCategoryCard.scss";

interface AIModelCategoryCardProps {
  data?: any;
  onClick?: (value: any) => void;
}

const AIModelCategoryCard = (props: AIModelCategoryCardProps) => {
  const { data, onClick } = props;

  const { id, name = "", icon, image, isActive, isDelete } = data;

  return (
    <div
      className="w-[220px] h-[120px] flex flex-col items-center gap-[16px] px-[12px] py-[8px] rounded-[15px] bg-white cursor-pointer ai-model-category-card"
      style={{
        border: "1px solid rgba(0, 0, 0, 0.05)",
        boxShadow: "0px 2px 4px 0px rgba(28, 39, 76, 0.08)",
      }}
      onClick={() => onClick?.(id)}
    >
      <div className="flex">
        <IconEmbeddings />
      </div>
      <div className="flex text-[#334466] text-[14px] font-medium leading-[21px]">
        {name}
      </div>
    </div>
  );
};

export default AIModelCategoryCard;
