import { useCallback, useEffect, useMemo, useState } from "react";
import { AI_MODEL_VIEW_MODE, regUUID } from "./util/const";
import { setBreadcrumbs } from "@/services/breadrumbs";
import AIModelList from "./AIModelList";
import AIModelDetail from "./AIModelDetail";
import { useHistory, useLocation } from "react-router";
import { PermissionMessage } from "@/components/PermissionMessage/PermissionMessage";
import { useAPI } from "@/providers/ApiProvider";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { AccessDenied } from "../../common/AccessDenied/AccessDenied";

const IconVector = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="8"
    height="20"
    viewBox="0 0 8 20"
    fill="none"
  >
    <path d="M7.56117 0L2.17781 20H0L5.38336 0H7.56117Z" fill="#ADB8CC" />
  </svg>
);

export const VIEW_MODE = {
  LIST: "list",
  DETAIL: "detail",
};

const AIModel = () => {
  const location = useLocation();
  const history = useHistory();
  const api = useAPI();

  const { hasPermission } = useCheckPermission();
  const canSystemConfiguration = hasPermission(
    ABILITY_NEW.can_system_configuration
  );

  const [selectedModelCategoryId, setSelectedModelCategoryId] = useState<any>();
  const [selectedModelCategory, setSelectedModelCategory] = useState<any>();

  const [loadingModelCategory, setLoadingModelCategory] = useState(false);
  const [modelCategoryList, setModelCategoryList] = useState<any>([]);

  const [viewMode, setViewMode] = useState(VIEW_MODE.LIST);
  const [selectedModel, setSelectedModel] = useState<any>();

  const fetchModelCategory = useCallback(async () => {
    setLoadingModelCategory(true);

    const res: any = await api.callApi("allModelCategories");

    setLoadingModelCategory(false);

    if (res?.length) {
      setModelCategoryList(res);
    }
  }, []);

  const redirectToModelCategory = useCallback(() => {
    history.push("system-management-v2?tab=ai-model");
    fetchModelCategory();
  }, []);

  const renderTitle = useMemo(() => {
    if (selectedModelCategoryId) {
      if (viewMode === VIEW_MODE.DETAIL) {
        return (
          <div className="flex items-center px-[2px]">
            <div
              className="flex text-[#ADB8CC] text-[20px] font-medium leading-[36px] cursor-pointer"
              onClick={redirectToModelCategory}
            >
              AI Model
            </div>
            <div className="flex px-[5px]">
              <IconVector />
            </div>
            <div
              className="flex text-[#ADB8CC] text-[20px] font-medium leading-[36px] cursor-pointer"
              onClick={() => setViewMode(VIEW_MODE.LIST)}
            >
              {selectedModelCategory?.name ?? ""}
            </div>
            <div className="flex px-[5px]">
              <IconVector />
            </div>
            <div className="flex text-[#334466] text-[20px] font-medium leading-[36px]">
              {selectedModel?.modelName ?? "New model"}
            </div>
          </div>
        );
      }

      return selectedModelCategoryId?.match(regUUID) ? (
        <div className="flex items-center px-[2px]">
          <div
            className="flex text-[#ADB8CC] text-[20px] font-medium leading-[36px] cursor-pointer"
            onClick={redirectToModelCategory}
          >
            AI Model
          </div>
          <div className="flex px-[5px]">
            <IconVector />
          </div>
          <div className="flex text-[#334466] text-[20px] font-medium leading-[36px]">
            {selectedModelCategory?.name ?? ""}
          </div>
        </div>
      ) : (
        ""
      );
    }

    return (
      <div className="flex items-center px-[2px]">
        <div className="flex text-black text-[20px] font-medium leading-[36px]">
          AI Model
        </div>
      </div>
    );
  }, [viewMode, selectedModel, selectedModelCategoryId, selectedModelCategory]);

  const renderContent = useMemo(() => {
    if (selectedModelCategoryId) {
      return selectedModelCategoryId?.match(regUUID) ? (
        <AIModelDetail
          selectedModelCategory={selectedModelCategory}
          viewMode={viewMode}
          setViewMode={setViewMode}
          selectedModel={selectedModel}
          setSelectedModel={setSelectedModel}
          backToModelCategory={redirectToModelCategory}
        />
      ) : (
        <PermissionMessage />
      );
    }
    return (
      <AIModelList
        loading={loadingModelCategory}
        data={modelCategoryList}
        onSelect={(value) =>
          history.push(`system-management-v2?tab=ai-model&categoryId=${value}`)
        }
      />
    );
  }, [
    viewMode,
    selectedModel,
    selectedModelCategoryId,
    selectedModelCategory,
    modelCategoryList,
    loadingModelCategory,
  ]);

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const activeModelCategoryID = urlParams.get("categoryId");

    if (activeModelCategoryID) {
      setSelectedModelCategoryId(activeModelCategoryID);

      if (activeModelCategoryID?.match(regUUID)) {
        const modelCategory = modelCategoryList?.find(
          (model: any) => model.id === activeModelCategoryID
        );

        setSelectedModelCategory(modelCategory);
      }
    } else {
      setSelectedModelCategoryId(undefined);
      setSelectedModelCategory(undefined);
    }
  }, [JSON.stringify(location.search), modelCategoryList]);

  useEffect(() => {
    setTimeout(() => {
      setBreadcrumbs([
        {
          path: "/home",
          title: "Home",
          key: "/system-management-v2",
        },
        {
          path: "/system-management-v2",
          title: "System Management",
          key: "/system-management-v2",
        },
        {
          path: "/system-management-v2",
          title: "AI Model",
          key: "/system-management-v2",
        },
      ]);
    }, 500);
  }, []);

  useEffect(async () => {
    if (canSystemConfiguration) {
      await fetchModelCategory?.();
    }
  }, [canSystemConfiguration]);

  return (
    <>
      {canSystemConfiguration ? (
        <div className="w-full h-full flex flex-col items-start px-[16px] py-[20px]">
          {renderTitle}
          {renderContent}
        </div>
      ) : (
        <AccessDenied />
      )}
    </>
  );
};

export default AIModel;
