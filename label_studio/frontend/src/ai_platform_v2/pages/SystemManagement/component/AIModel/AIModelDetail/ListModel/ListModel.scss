.model-category-search {
  border-color: rgba(0, 0, 0, 0.1) !important;
}

.model-category-search-focus {
  border-color: #3361ff !important;
}

.model-category-search {
  input::placeholder {
    color: var(--Gray-Blue-Grey-Blue-85, #c3cad9);
    font-family: "Be Vietnam Pro";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }
}

.model-category-card-add-new {
  cursor: pointer;
  display: flex;
  width: 220px;
  height: 120px;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;

  border-radius: 15px;
  border: 1px dashed rgba(0, 0, 0, 0.2);
  background: #fff;
  box-shadow: 0px 2px 4px 0px rgba(28, 39, 76, 0.08);

  color: #d9d9d9;
  text-align: center;

  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 21px;

  transition:
    color 0.3s,
    background 0.3s,
    border 0.3s;

  svg path {
    transition: fill 0.3s;
  }

  &:hover {
    color: #3361ff;
    border: 1px dashed #3361ff;
    background: linear-gradient(
        0deg,
        rgba(51, 97, 255, 0.05) 0%,
        rgba(51, 97, 255, 0.05) 100%
      ),
      #fff;

    svg path {
      fill: #3361ff;
    }
  }
}
