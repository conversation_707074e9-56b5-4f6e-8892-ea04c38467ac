import React from "react";
import AIModelCategoryCard from "../component/AIModelCategoryCard";
import AIModelCategoryLoading from "../component/AIModelCategoryLoading";
import ModelDetailEmpty from "../component/ModelDetailEmpty";

interface AIModelListProps {
  loading?: boolean;
  data?: any;
  onSelect: (value: any) => void;
}

const AIModelList = (props: AIModelListProps) => {
  const { loading, data, onSelect } = props;

  return (
    <div className="w-full h-full flex flex-col justify-between items-end p-[12px] relative">
      {data?.length || loading ? (
        <>
          {loading ? (
            <div className="grid grid-cols-[repeat(auto-fill,_220px)] place-content-evenly p-[8px] items-start gap-x-[20px] gap-y-[16px] w-full">
              <AIModelCategoryLoading />
            </div>
          ) : (
            <div
              className="min-h-[100px] grid grid-cols-[repeat(auto-fill,_220px)] p-[8px] items-start gap-x-[20px] gap-y-[16px] w-full overflow-auto"
              style={{ placeContent: "start space-evenly" }}
            >
              {data.map((model: any) => (
                <AIModelCategoryCard
                  key={model.id}
                  data={model}
                  onClick={(modelId: any) => onSelect?.(modelId)}
                />
              ))}
            </div>
          )}
        </>
      ) : (
        <div className="w-full h-full flex justify-center overflow-hidden">
          <ModelDetailEmpty showCreateModel={false} />
        </div>
      )}
    </div>
  );
};

export default AIModelList;
