import { useCallback, useEffect, useState } from "react";
import { Button, Input, ModalConfirmBig } from "@taureau/ui";
import classNames from "classnames";
import { regUrl, validDataTypes } from "../../util/const";
import IconError from "../../assets/IconError";
import { Upload } from "@/ai_platform_v2/pages/ImportData/Upload/Upload";
import { DropzoneDes } from "../../component/DropzoneDes/DropzoneDes";
import IconBackSpace from "../../assets/IconBackSpace";
import { useAPI } from "@/providers/ApiProvider";
import IconCopy from "../../assets/IconCopy";
import Message from "@/ai_platform_v2/component/Message/Message";
import { Tooltip } from "antd";
import "./ModelDetail.scss";

const convertBase64 = (file: any) => {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader();

    fileReader.readAsDataURL(file);

    fileReader.onload = () => {
      resolve(fileReader.result);
    };

    fileReader.onerror = (error) => {
      reject(error);
    };
  });
};

interface ModelDetailProps {
  [key: string]: any;
}

const ModelDetail = (props: ModelDetailProps) => {
  const { modelCategoryID, backToList, selectedModel } = props;
  const api = useAPI();

  const [loading, setLoading] = useState(false);
  const [isChanged, setChanged] = useState(false);
  const [modelName, setModelName] = useState("");

  const [apiEndpoint, setApiEndpoint] = useState("");
  const [apiEndpointErrMessage, setApiEndpointErrMessage] = useState("");

  const [imgPreview, setImagePreview] = useState("");
  const [aiModelOutput, setAIModelOutput] = useState<any>();

  const [isCopied, setIsCopied] = useState(false);

  const handleChangeModelName = useCallback((e: any) => {
    const { value } = e.target;

    setModelName(value);
    setChanged(true);
  }, []);

  const validateModelName = useCallback(() => {
    const newModelName = modelName?.trim();

    if (!newModelName) {
      return false;
    }

    setModelName(newModelName);
    return true;
  }, [modelName]);

  const handleChangeApiEndpoint = useCallback((e: any) => {
    const { value } = e.target;

    setApiEndpoint(value);
    setChanged(true);
    setImagePreview("");
    setAIModelOutput(undefined);
  }, []);

  const validateApiEndpoint = useCallback(() => {
    const newApiEndpoint = apiEndpoint?.trim();

    if (newApiEndpoint && !regUrl.test(newApiEndpoint)) {
      setApiEndpointErrMessage("Invalid url format");
      return false;
    }

    setApiEndpoint(newApiEndpoint);
    return true;
  }, [apiEndpoint]);

  const handleCopyToClipboard = useCallback(() => {
    setIsCopied(true);
    navigator.clipboard.writeText(aiModelOutput?.embedding);
  }, [aiModelOutput]);

  const getImagePreviewUrl = useCallback(
    async (file) => {
      try {
        if (!apiEndpoint) {
          setApiEndpointErrMessage("Type in api to see preview.");
          return;
        }
        if (apiEndpointErrMessage) {
          return;
        }

        const imgBase64: any = await convertBase64(file.file);

        setImagePreview(imgBase64);

        const res = await api.callApi("fetchPreviewEmbeddings", {
          headers: { "Content-Type": "multipart/form-data" },
          body: {
            aiApiUrl: apiEndpoint,
            image: file?.file,
          },
        });

        if (res?.success) {
          setAIModelOutput(res?.data);
        }
      } catch (error: any) {
        console.log("error: ", error?.message);
      }
    },
    [apiEndpointErrMessage, aiModelOutput, apiEndpoint]
  );

  const handleSaveData = useCallback(async () => {
    setLoading(true);

    if (selectedModel?.id) {
      const res: any = await api.callApi("updateCategoryModels", {
        params: { pk: modelCategoryID, modelId: selectedModel?.id },
        body: {
          modelName: modelName?.trim(),
          apiEndpoint: apiEndpoint?.trim(),
          status:
            selectedModel.modelName !== modelName?.trim() ||
            selectedModel.apiEndpoint !== apiEndpoint?.trim()
              ? "Cancel"
              : selectedModel.status,
        },
      });

      if (res?.success) {
        Message.success({
          content: `Model updated.`,
        });
        backToList();
      } else {
        res?.message &&
          Message.error({
            content: res.message,
          });
      }
    } else {
      const res: any = await api.callApi("createCategoryModels", {
        params: { pk: modelCategoryID },
        body: {
          modelName: modelName?.trim(),
          apiEndpoint: apiEndpoint?.trim(),
          status: "New",
        },
      });

      if (res?.success) {
        Message.success({
          content: `Model created.`,
        });
        backToList();
      } else {
        res?.message &&
          Message.error({
            content: res.message,
          });
      }
    }

    setLoading(false);
  }, [modelCategoryID, selectedModel, modelName, apiEndpoint]);

  const handleBackToList = useCallback(() => {
    if (isChanged) {
      ModalConfirmBig.warning({
        title: "Wanna leave?",
        content: "If you continue, changes you made may not be saved",
        okText: "Continue",
        cancelText: "Cancel",
        onOk: () => {
          backToList();
        },
      });
      return;
    }
    backToList();
  }, [isChanged]);

  useEffect(() => {
    setModelName(selectedModel?.modelName);
    setApiEndpoint(selectedModel?.apiEndpoint);
  }, [selectedModel]);

  return (
    <>
      <div className="w-full h-full flex flex-col gap-[12px] px-[8px]">
        <div className="w-full flex flex-col items-end gap-[12px] px-[8px] py-[12px]">
          <div className="w-full flex items-start gap-[20px]">
            <div className="w-[50%] gap-[12px] model-category-detail-side">
              <div className="flex text-[#346] text-[14px] font-medium leading-[21px]">
                API Configuration
              </div>
              <div className="w-full flex flex-col items-start gap-[8px] px-[8px] pb-[20px]">
                <div className="w-full flex flex-col items-start gap-[4px]">
                  <div className="flex items-start pl-[4px] text-[#346] text-[12px] font-medium leading-[18px]">
                    Model name
                  </div>
                  <Input
                    value={modelName}
                    placeholder="Enter name here"
                    className="w-full"
                    inputClassName="text-[12px] w-full leading-[18px]"
                    onChange={(e) => handleChangeModelName(e)}
                    onBlur={() => validateModelName()}
                    maxLength={50}
                  />
                </div>
                <div className="w-full flex flex-col items-start gap-[4px]">
                  <div className="flex items-start pl-[4px] text-[#346] text-[12px] font-medium leading-[18px]">
                    API Endpoint
                  </div>
                  <Input
                    value={apiEndpoint}
                    placeholder="Enter an url here"
                    inputClassName="w-full text-[12px] w-full leading-[18px]"
                    onChange={(e) => handleChangeApiEndpoint(e)}
                    onBlur={() => validateApiEndpoint()}
                    onFocus={() => setApiEndpointErrMessage("")}
                    className={classNames("w-full border-2", {
                      "border-[#cc1414]": apiEndpointErrMessage,
                    })}
                    maxLength={1000}
                  />
                  {apiEndpointErrMessage && (
                    <div className="flex items-center gap-[2px] px-[4px]">
                      <div className="flex">
                        <IconError />
                      </div>
                      <div className="flex text-[#CC1414] text-[10px] leading-[13px]">
                        {apiEndpointErrMessage}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="w-[50%] gap-[8px] model-category-detail-side">
              <div className="w-full flex flex-col">
                <div className="flex text-[#346] text-[14px] font-medium leading-[21px]">
                  Preview
                </div>
                <div className="flex text-[#6B7A99] text-[12px] leading-[18px]">
                  View embedding result
                </div>
              </div>
              {imgPreview ? (
                <div className="flex justify-center w-full h-full bg-[#F5F6F7] rounded-[10px] overflow-hidden">
                  <img
                    src={imgPreview}
                    alt="preview-model-img"
                    className="w-full h-full min-h-[200px] object-contain"
                    style={{ maxHeight: "calc(100vh - 366px)" }}
                  />
                </div>
              ) : (
                <Upload
                  multiple={false}
                  customClassName="!h-full !bg-[#F5F6F7] !border-none !rounded-[10px]"
                  dataTypes={validDataTypes}
                  sendFiles={getImagePreviewUrl}
                >
                  <DropzoneDes
                    dataTypes={validDataTypes?.map((dataType: string) =>
                      dataType.toLowerCase()
                    )}
                  />
                </Upload>
              )}
              {aiModelOutput && (
                <div className="w-full flex flex-col items-start">
                  <div className="w-full flex justify-between items-center pr-[12px]">
                    <div className="flex text-[#334466] text-[14px] font-medium leading-[21px]">
                      Embedding result:{" "}
                      {`${aiModelOutput?.embedding?.[0]},...,${aiModelOutput
                        ?.embedding?.[aiModelOutput?.dims - 1]}`}
                    </div>
                    <Tooltip
                      open={isCopied}
                      title="Copied!"
                      onOpenChange={(newOpen) => {
                        if (!newOpen) {
                          setIsCopied(false);
                        }
                      }}
                    >
                      <div
                        className="flex cursor-pointer embedding-result-icon-copy"
                        onClick={handleCopyToClipboard}
                      >
                        <IconCopy />
                      </div>
                    </Tooltip>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex text-[#334466] text-[14px] font-medium leading-[21px]">
                      Embedding dimensions: {aiModelOutput?.dims}
                    </div>
                  </div>
                </div>
              )}
              <div className="w-full flex justify-end">
                <div
                  className={classNames(
                    "w-fit flex px-[4px] py-[2px] text-[12px] font-medium leading-[18px] rounded-[5px] cursor-pointer",
                    {
                      "text-[#CC1414] btn-delete-enable": imgPreview,
                      "text-[#ADB8CC]": !imgPreview,
                    }
                  )}
                  onClick={() => {
                    setImagePreview("");
                    setAIModelOutput(undefined);
                  }}
                >
                  Remove
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="w-full flex justify-between px-[22px] py-[10px]">
        <Button
          className="w-fit h-[30px] text-[14px] font-medium leading-[21px]"
          corner="Rounded"
          theme="Primary"
          size="xs"
          iconLeft={<IconBackSpace />}
          onClick={() => handleBackToList()}
        >
          Back to list
        </Button>
        <Button
          className={classNames("text-[14px] font-medium leading-[21px]", {
            "border-none bg-[#0000001A] text-[#ADB8CC]":
              !modelName?.trim() ||
              !apiEndpoint?.trim() ||
              !!apiEndpointErrMessage,
          })}
          corner="Rounded"
          theme="Primary"
          size="xs"
          onClick={handleSaveData}
          disabled={
            !modelName?.trim() ||
            !apiEndpoint?.trim() ||
            !!apiEndpointErrMessage
          }
          loading={loading}
        >
          Save change
        </Button>
      </div>
    </>
  );
};

export default ModelDetail;
