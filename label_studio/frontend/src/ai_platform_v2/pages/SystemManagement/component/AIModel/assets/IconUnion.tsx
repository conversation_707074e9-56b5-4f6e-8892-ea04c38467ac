const IconUnion = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.99992 0C4.47716 0 0 4.47708 0 9.99984C0 15.5229 4.47716 20 9.99992 20C15.5229 20 20 15.5229 20 9.99984C20 4.47708 15.5229 0 9.99992 0ZM17.6562 13.9062C17.6562 14.5967 17.0966 15.1562 16.4062 15.1562C16.0841 15.1562 15.7915 15.0334 15.5698 14.8334C15.4834 14.8395 15.3947 14.8438 15.3018 14.8438C14.5189 14.8438 13.5175 14.6294 12.402 14.2284C11.9081 16.5651 11.0173 18.125 10 18.125C8.98276 18.125 8.09189 16.5649 7.59803 14.2284C6.48247 14.6295 5.48119 14.8438 4.69819 14.8438C4.60517 14.8438 4.51667 14.8395 4.43016 14.8334C4.20853 15.0334 3.91586 15.1562 3.59375 15.1562C2.90344 15.1562 2.34375 14.5967 2.34375 13.9062C2.34375 13.3601 2.69455 12.897 3.18261 12.7269C3.53355 11.9028 4.30251 10.921 5.3775 9.92156C3.51608 8.19077 2.57011 6.51291 3.12813 5.61584C3.38775 5.19822 3.9415 5 4.69833 5C5.48881 5 6.50162 5.21805 7.63016 5.62667C7.90794 4.37378 8.30131 3.35541 8.76717 2.69806C8.75686 2.63336 8.75 2.56759 8.75 2.5C8.75 1.80955 9.30969 1.25 10 1.25C10.6903 1.25 11.25 1.80955 11.25 2.5C11.25 2.56759 11.2431 2.63336 11.2329 2.69806C11.6988 3.35541 12.0921 4.37378 12.3698 5.62667C13.4984 5.2182 14.5111 5 15.3017 5H15.3039L15.3037 5.625C14.5425 5.625 13.5694 5.84641 12.4961 6.24389C12.6172 6.9133 12.7071 7.63703 12.7599 8.39997C13.2618 8.76281 13.7296 9.13314 14.1579 9.50362C14.6415 9.057 15.0678 8.60841 15.4221 8.1688C16.3819 6.97816 16.5182 6.22727 16.3433 5.94589C16.2165 5.74188 15.8376 5.625 15.304 5.625L15.3039 5C16.0594 5.00038 16.6125 5.19862 16.872 5.61584C17.43 6.51291 16.4841 8.19077 14.6227 9.92156C15.6976 10.921 16.4665 11.9028 16.8175 12.7269C17.3055 12.897 17.6562 13.3601 17.6562 13.9062ZM10.625 2.5C10.625 2.84518 10.3452 3.125 10 3.125C9.65482 3.125 9.375 2.84518 9.375 2.5C9.375 2.15482 9.65482 1.875 10 1.875C10.3452 1.875 10.625 2.15482 10.625 2.5ZM12.0915 7.93728C11.8916 7.80331 11.6877 7.67056 11.4793 7.53994C11.2042 7.36752 10.9297 7.20502 10.6562 7.04877C11.0783 6.8362 11.4941 6.64547 11.8995 6.47656C11.9803 6.94256 12.0445 7.4313 12.0915 7.93728ZM11.7802 5.85125C11.6724 5.35138 11.545 4.88308 11.3978 4.45767C11.2391 3.99914 11.0738 3.63553 10.9115 3.35156C10.6834 3.59539 10.3597 3.74859 9.99945 3.74859C9.63919 3.74859 9.31555 3.59539 9.08744 3.35156C8.92516 3.63553 8.75991 3.99914 8.60122 4.45767C8.45397 4.88308 8.32649 5.35138 8.21875 5.85125C8.79264 6.08456 9.39017 6.36319 9.99953 6.68469C10.6087 6.36319 11.2063 6.08456 11.7802 5.85125ZM8.0982 6.47656C8.50362 6.64533 8.91966 6.8362 9.34164 7.04877C9.0682 7.20502 8.7937 7.36752 8.51866 7.53994C8.31006 7.67072 8.10614 7.80331 7.90625 7.93744C7.95317 7.43145 8.01748 6.94256 8.0982 6.47656ZM4.21875 13.9062C4.21875 14.2514 3.93893 14.5312 3.59375 14.5312C3.24857 14.5312 2.96875 14.2514 2.96875 13.9062C2.96875 13.5611 3.24857 13.2812 3.59375 13.2812C3.93893 13.2812 4.21875 13.5611 4.21875 13.9062ZM8.51838 12.3026C8.30437 12.1684 8.09548 12.0322 7.89062 11.8945C7.93397 12.4071 7.99492 12.9031 8.07267 13.3766C8.48603 13.2053 8.91061 13.0106 9.34128 12.7936C9.06792 12.6375 8.79342 12.4748 8.51838 12.3026ZM4.58001 8.1688C3.62015 6.97816 3.4839 6.22727 3.65892 5.94573C3.78571 5.74188 4.16451 5.625 4.69834 5.625C5.45953 5.625 6.43259 5.84625 7.50596 6.24389C7.38489 6.9133 7.29493 7.63717 7.24221 8.40011C6.74036 8.76297 6.27251 9.13314 5.84428 9.50362C5.36073 9.057 4.93432 8.60825 4.58001 8.1688ZM6.31641 9.9252C6.59556 10.1643 6.88991 10.4019 7.19814 10.636C7.19256 10.4269 7.18883 10.2165 7.18883 10.0033C7.18883 9.73584 7.19386 9.47186 7.20256 9.21094C6.89274 9.44623 6.59686 9.68487 6.31641 9.9252ZM4.58139 11.6789C4.93578 11.2393 5.36227 10.7904 5.84597 10.3438C6.27145 10.7118 6.73547 11.0794 7.23359 11.4401C7.28098 12.2072 7.36528 12.9368 7.48155 13.6131C6.46624 13.9871 5.54392 14.2004 4.80516 14.2189C4.83034 14.12 4.84514 14.017 4.84514 13.9102C4.84514 13.3218 4.43803 12.8297 3.89062 12.6968C4.04611 12.4043 4.27027 12.0648 4.58139 11.6789ZM16.4062 14.5312C16.7514 14.5312 17.0312 14.2514 17.0312 13.9062C17.0312 13.5611 16.7514 13.2812 16.4062 13.2812C16.0611 13.2812 15.7812 13.5611 15.7812 13.9062C15.7812 14.2514 16.0611 14.5312 16.4062 14.5312ZM14.1553 10.3398C14.639 10.7866 15.0655 11.2355 15.4198 11.6751C15.7307 12.061 15.9549 12.4005 16.1105 12.6931C15.563 12.826 15.1559 13.3181 15.1559 13.9064C15.1559 14.0133 15.1707 14.1163 15.1959 14.2151C14.4572 14.1967 13.5348 13.9833 12.5195 13.6093C12.6358 12.9331 12.7202 12.2034 12.7675 11.4363C13.2657 11.0758 13.7297 10.708 14.1553 10.3398ZM13.6831 9.9252C13.404 10.1643 13.1095 10.4019 12.8013 10.636C12.807 10.4269 12.8106 10.2165 12.8106 10.0033C12.8106 9.73584 12.8056 9.47172 12.7969 9.21094C13.1068 9.44608 13.4027 9.68487 13.6831 9.9252ZM10.6602 12.7936C11.0909 13.0106 11.5154 13.2053 11.9288 13.3766C12.0066 12.9033 12.0676 12.4072 12.111 11.8945C11.9062 12.0322 11.6973 12.1684 11.4834 12.3024C11.2082 12.4748 10.9338 12.6375 10.6602 12.7936ZM10.0001 7.39453C10.3824 7.60405 10.7666 7.82758 11.1494 8.06745C11.4945 8.28367 11.8292 8.50614 12.1524 8.73319C12.1753 9.14716 12.1875 9.56938 12.1875 9.99769C12.1875 10.37 12.1783 10.7379 12.161 11.0998C11.835 11.3289 11.4975 11.5535 11.1494 11.7715C10.7666 12.0114 10.3822 12.2351 10 12.4446C9.61776 12.2351 9.23355 12.0115 8.85078 11.7717C8.50266 11.5535 8.16498 11.3289 7.83905 11.0998C7.82173 10.7379 7.8125 10.37 7.8125 9.99769C7.8125 9.56938 7.8247 9.14731 7.84751 8.73334C8.17086 8.5063 8.5057 8.28367 8.85078 8.06745C9.23362 7.82758 9.61784 7.60405 10.0001 7.39453ZM8.75 9.99769C8.75 10.6881 9.30969 11.2477 10 11.2477C10.6903 11.2477 11.25 10.6881 11.25 9.99769C11.25 9.30723 10.6903 8.74769 10 8.74769C9.30969 8.74769 8.75 9.30723 8.75 9.99769ZM8.1875 14.0027C8.30127 14.5572 8.43942 15.0735 8.6008 15.5395C9.10128 16.9856 9.66677 17.4987 9.99895 17.4987C10.3311 17.4987 10.8966 16.9856 11.3971 15.5397C11.5584 15.0735 11.6966 14.5572 11.8105 14.0027C11.2273 13.7669 10.6191 13.4835 9.99895 13.1562C9.37884 13.4835 8.7707 13.7669 8.1875 14.0027ZM10.625 10C10.625 10.3452 10.3452 10.625 10 10.625C9.65482 10.625 9.375 10.3452 9.375 10C9.375 9.65482 9.65482 9.375 10 9.375C10.3452 9.375 10.625 9.65482 10.625 10Z"
      fill="url(#paint0_linear_16692_274107)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_16692_274107"
        x1="10.4167"
        y1="-2.5"
        x2="11.084"
        y2="28.2851"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0.05497" stopColor="#5FF9FF" />
        <stop offset="0.26971" stopColor="#4FF1E2" />
        <stop offset="0.340131" stopColor="#4AEED8" />
        <stop offset="0.452434" stopColor="#40E9C5" />
        <stop offset="0.577428" stopColor="#34E3AF" />
        <stop offset="0.64938" stopColor="#29DEA8" />
        <stop offset="0.727114" stopColor="#1BD9A0" />
        <stop offset="0.912465" stopColor="#00CE90" />
      </linearGradient>
    </defs>
  </svg>
);

export default IconUnion;
