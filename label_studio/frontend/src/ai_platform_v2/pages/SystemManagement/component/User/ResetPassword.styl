:global(.modal-reset-password)
  transition: width .3s

  :global(.ant-modal-content)
    padding-bottom: 20px

    :global(.ant-modal-header)
      border-bottom: none
      padding: 10px

    :global(.ant-modal-body)
      padding: 0px

.reset-password-header
  display: flex
  justify-content: space-between
  align-items: center
  width: 100%
  
  span
    color: #000
    font-family: "Be Vietnam Pro", sans-serif
    font-size: 15px
    font-weight: 400
    line-height: 20px
  
  .reset-password-close
    cursor: pointer
    display: flex
    align-items: center
    justify-content: center
    width: 20px
    height: 20px

.reset-password
  display: flex
  width: 100%
  flex-direction: column
  align-items: center
  gap: 20px
  padding-bottom: 16px

  &__user-info
    display: flex
    flex-direction: row
    justify-content: center
    align-items: center
    gap: 4px
    padding: 0 20px
    width: 100%
    
    .user-name
      color: #334466
      font-family: "Be Vietnam Pro", sans-serif
      font-size: 14px
      font-weight: 500
      line-height: 21px
      text-align: center
    
    .user-avatar
      width: 20px
      height: 20px
      border-radius: 999px
      background-color: #ADB8CC
    
    .user-username
      color: #6B7A99
      font-family: "Be Vietnam Pro", sans-serif
      font-size: 14px
      font-weight: 400
      line-height: 21px
      text-align: center

  &__content
    display: flex
    flex-direction: column
    gap: 4px
    
    .password-input-container
      display: flex
      flex-direction: row
      align-items: center
      gap: 12px
      
      .password-field
        display: flex
        flex-direction: column
        gap: 5px
        padding-left: 32px
        
        .password-label
          color: #334466
          font-family: "Be Vietnam Pro", sans-serif
          font-size: 12px
          font-weight: 500
          line-height: 18px
        
        .password-input
          width: 340px
          height: 40px
          padding: 5px 10px
          border-radius: 5px
          border: 2px solid #F5F6F7
          background: #FFF
          box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.03)
          
          input
            color: #334466
            font-family: "Be Vietnam Pro", sans-serif
            font-size: 13px
            font-weight: 400
            line-height: 20px
        
        .password-input-error
          border: 2px solid #CC1414
      
      .generate-button-container
        display: flex
        padding: 20px 20px 0 0
        
        .generate-button
          display: flex
          padding: 3px 15px
          justify-content: center
          align-items: center
          border-radius: 15px
          background: #EBF0FF
          color: #3361FF
          font-family: "Be Vietnam Pro", sans-serif
          font-size: 12px
          font-weight: 500
          line-height: 18px
          border: none
          height: 26px
    
    .password-requirements
      display: flex
      justify-content: center
      align-items: center
      padding-left: 32px
      
      .requirements-text
        color: #CC1414
        font-family: Roboto, sans-serif
        font-size: 11px
        font-weight: 400
        line-height: 15px
      
      .requirements-error
        color: #CC1414

  &__footer
    display: flex
    justify-content: flex-end
    align-self: stretch
    gap: 10px
    padding: 0 12px
    
    .save-button
      display: flex
      padding: 3px 15px
      justify-content: center
      align-items: center
      gap: 30px
      border-radius: 15px
      background: rgba(0, 0, 0, 0.1)
      color: #ADB8CC
      font-family: "Be Vietnam Pro", sans-serif
      font-size: 12px
      font-weight: 500
      line-height: 18px
      border: none
      height: 26px
    
    .save-button-active
      background: #3361FF
      color: #FFF
