import IconDotsHorizontal from "@/ai_platform_v2/assets/Icons/IconDotsHorizontal";
import { Popover } from "antd";
import classNames from "classnames";
import { memo, useState } from "react";
import "./styles.scss";

const UserActions = ({ renderMenu }: any) => {
  const [isOpenPopover, togglePopover] = useState(false);

  return (
    <Popover
      open={isOpenPopover}
      onOpenChange={(open) => togglePopover(open)}
      content={() => renderMenu()}
      showArrow={false}
      trigger="click"
      placement="bottomRight"
      overlayClassName="popover-user-actions overflow-y-auto"
      zIndex={1}
      getPopupContainer={(triggerNode) => triggerNode.parentNode}
    >
      {/* <Tooltip title="More actions"> */}
      <div
        className={classNames(
          "cursor-pointer rounded-[6px] w-6 h-6 flex items-center justify-center",
          {
            "bg-[#F5F6F7]": isOpenPopover,
            "hover:bg-[#F5F6F7]": !isOpenPopover,
          }
        )}
      >
        <IconDotsHorizontal
          color={isOpenPopover ? "#6B7A99" : "#C3CAD9 "}
          className="rotate-90"
          size={16}
        />
      </div>
      {/* </Tooltip> */}
    </Popover>
  );
};

export default memo(UserActions);
