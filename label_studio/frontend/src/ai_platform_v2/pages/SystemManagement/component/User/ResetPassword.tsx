import Message from "@/ai_platform_v2/component/Message/Message";
import { CopyableTooltip } from "@/components/CopyableTooltip/CopyableTooltip";
import { useAPI } from "@/providers/ApiProvider";
import { Button } from "@taureau/ui";
import { Modal, Typography } from "antd";
import classNames from "classnames";
import { createRef, memo, useCallback, useState } from "react";

import IconEye from "./IconEye";
import IconEyeSlash from "./IconEyeSlash";
import "./ResetPassword.scss";

interface ResetPasswordProps {
  open?: boolean;
  onClose?: () => void;
  user?: any;
}

const { Text } = Typography;

const ModalResetPassword = (props: ResetPasswordProps) => {
  const { open, onClose, user } = props;

  const api = useAPI();

  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [isShowPassword, toggleShowPassword] = useState(false);

  const inpRef = createRef<HTMLInputElement>();

  const handleClose = useCallback(() => {
    onClose?.();
  }, [onClose]);

  const handleResetPassword = useCallback(async () => {
    if (loading) return;
    setLoading(true);

    try {
      const res = await api.callApi("overridePassword", {
        params: {
          userId: user.id,
        },
        body: { password },
      });

      if (res?.success) {
        Message.success({ content: "New password already set" });
        handleClose();
      }
    } catch (error) {
      console.log("error: ", error);
    } finally {
      setLoading(false);
    }
  }, [password, handleClose, loading, user?.id]);

  const handleGeneratePassword = useCallback(() => {
    const length = 12;
    const uppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const lowercaseChars = "abcdefghijklmnopqrstuvwxyz";
    const numberChars = "0123456789";
    const specialChars = '!@#$%^&*()_+=[]{};:"\\|,.<>/?';

    let generatedPassword = "";

    generatedPassword += uppercaseChars.charAt(
      Math.floor(Math.random() * uppercaseChars.length)
    );
    generatedPassword += lowercaseChars.charAt(
      Math.floor(Math.random() * lowercaseChars.length)
    );
    generatedPassword += numberChars.charAt(
      Math.floor(Math.random() * numberChars.length)
    );
    generatedPassword += specialChars.charAt(
      Math.floor(Math.random() * specialChars.length)
    );

    const allChars =
      uppercaseChars + lowercaseChars + numberChars + specialChars;

    for (let i = 4; i < length; i++) {
      generatedPassword += allChars.charAt(
        Math.floor(Math.random() * allChars.length)
      );
    }

    generatedPassword = generatedPassword
      .split("")
      .sort(() => 0.5 - Math.random())
      .join("");

    setPassword(generatedPassword);
  }, []);

  const validatePassword = (password: string) => {
    if (!password) return true;
    const regex =
      /^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[!@#$%^&*()_+=[\]{};':"\\|,.<>/?]).{8,}$/;

    return regex.test(password);
  };

  const isPasswordValid = validatePassword(password);

  return (
    <Modal
      className="modal-reset-password"
      title={
        <span className=" text-[#000] text-[15px] leading-[20px]">
          Reset password
        </span>
      }
      centered
      open={open}
      onCancel={(e) => {
        e.stopPropagation();
        handleClose();
      }}
      footer={null}
      width={490}
    >
      <div className="mt-[8px] w-full flex justify-center items-center gap-[4px] px-[20px]">
        <Text
          style={{
            fontSize: 14,
            fontWeight: 500,
            lineHeight: "21px",
            color: "#346",
            maxWidth: "48%",
          }}
          ellipsis={{ tooltip: { color: "#000" } }}
        >
          {user.fullName}
        </Text>
        <div className="w-[3px] h-[3px] rounded-full bg-[#ADB8CC]" />
        <Text
          ellipsis={{ tooltip: { color: "#000" } }}
          style={{
            fontSize: 14,
            fontWeight: 500,
            lineHeight: "21px",
            color: "#6B7A99",
            maxWidth: "48%",
          }}
        >
          {`@${user.userName}`}
        </Text>
      </div>
      <div className="mt-[12px] pl-[32px] pr-[20px]">
        <div className="flex flex-col">
          <span className="font-medium text-[#346] text-[12px] leading-[18px] mb-[5px]">
            New password
          </span>
          <div className="flex gap-[12px] items-center">
            <div
              className={classNames(
                "w-[340px] h-[40px] shadow-Shadows/Gray-Blue-30/3%/5b px-[10px] py-[5px] rounded-[5px] border-[2px] flex items-center gap-[10px] cursor-text",
                {
                  "border-[#CC1414]": !isPasswordValid,
                  "border-[#F5F6F7]": isPasswordValid,
                }
              )}
              onClick={() => inpRef?.current?.focus()}
            >
              <input
                className="outline-0 border-none text-[13px] leading-[20px] text-[#346] w-[274px]"
                ref={inpRef}
                autoComplete="new-password"
                type={isShowPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e?.target?.value?.trim())}
              />
              <div className="flex items-center justify-center gap-[4px]">
                {password ? (
                  <CopyableTooltip
                    textForCopy={password}
                    children={
                      <div className="cursor-pointer">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 16 16"
                          fill="none"
                        >
                          <g clipPath="url(#clip0_19594_59773)">
                            <path
                              fillRule="evenodd"
                              clipRule="evenodd"
                              d="M7.44605 0.833008H12.5533C12.9094 0.833001 13.2066 0.832995 13.4495 0.852834C13.7027 0.873526 13.9405 0.918267 14.1653 1.03283C14.5103 1.2086 14.7908 1.48906 14.9665 1.83403C15.0811 2.05887 15.1258 2.29664 15.1465 2.54989C15.1664 2.79271 15.1663 3.08994 15.1663 3.44604V8.55331C15.1663 8.90941 15.1664 9.20664 15.1465 9.44945C15.1258 9.70271 15.0811 9.94048 14.9665 10.1653C14.7908 10.5103 14.5103 10.7908 14.1653 10.9665C13.9405 11.0811 13.7027 11.1258 13.4495 11.1465C13.2066 11.1664 12.9094 11.1663 12.5533 11.1663H11.1663V12.5533C11.1663 12.9094 11.1664 13.2066 11.1465 13.4495C11.1258 13.7027 11.0811 13.9405 10.9665 14.1653C10.7908 14.5103 10.5103 14.7908 10.1653 14.9665C9.94048 15.0811 9.70271 15.1258 9.44945 15.1465C9.20664 15.1664 8.90941 15.1663 8.55331 15.1663H3.44604C3.08994 15.1663 2.79271 15.1664 2.54989 15.1465C2.29664 15.1258 2.05887 15.0811 1.83403 14.9665C1.48906 14.7908 1.2086 14.5103 1.03283 14.1653C0.918267 13.9405 0.873526 13.7027 0.852834 13.4495C0.832995 13.2066 0.833001 12.9094 0.833008 12.5533V7.44605C0.833001 7.08995 0.832995 6.79272 0.852834 6.54989C0.873526 6.29664 0.918267 6.05887 1.03283 5.83403C1.2086 5.48906 1.48906 5.2086 1.83403 5.03283C2.05887 4.91827 2.29664 4.87353 2.54989 4.85283C2.79272 4.83299 3.08995 4.833 3.44606 4.83301H4.83301V3.44606C4.833 3.08995 4.83299 2.79272 4.85283 2.54989C4.87353 2.29664 4.91827 2.05887 5.03283 1.83403C5.2086 1.48906 5.48906 1.2086 5.83403 1.03283C6.05887 0.918267 6.29664 0.873526 6.54989 0.852834C6.79272 0.832995 7.08995 0.833001 7.44605 0.833008ZM3.46634 5.83301C3.08472 5.83301 2.82858 5.8334 2.63133 5.84951C2.43991 5.86515 2.34839 5.89307 2.28802 5.92384C2.13121 6.00373 2.00373 6.13121 1.92384 6.28802C1.89307 6.34839 1.86515 6.43991 1.84951 6.63133C1.8334 6.82858 1.83301 7.08472 1.83301 7.46634V12.533C1.83301 12.9146 1.8334 13.1708 1.84951 13.368C1.86515 13.5594 1.89307 13.651 1.92384 13.7113C2.00373 13.8681 2.13121 13.9956 2.28802 14.0755C2.34839 14.1063 2.43991 14.1342 2.63133 14.1498C2.82858 14.166 3.08472 14.1663 3.46634 14.1663H8.53301C8.91463 14.1663 9.17077 14.166 9.36802 14.1498C9.55944 14.1342 9.65096 14.1063 9.71133 14.0755C9.86814 13.9956 9.99562 13.8681 10.0755 13.7113C10.1063 13.651 10.1342 13.5594 10.1498 13.368C10.166 13.1708 10.1663 12.9146 10.1663 12.533V7.46634C10.1663 7.08472 10.166 6.82858 10.1498 6.63133C10.1342 6.43991 10.1063 6.34839 10.0755 6.28802C9.99562 6.13121 9.86813 6.00373 9.71133 5.92384C9.65096 5.89307 9.55944 5.86515 9.36802 5.84951C9.17077 5.8334 8.91463 5.83301 8.53301 5.83301H3.46634ZM11.1663 10.1663V7.44604C11.1663 7.08994 11.1664 6.79271 11.1465 6.54989C11.1258 6.29664 11.0811 6.05887 10.9665 5.83403C10.7908 5.48906 10.5103 5.2086 10.1653 5.03283C9.94048 4.91827 9.70271 4.87353 9.44945 4.85283C9.20663 4.83299 8.9094 4.833 8.55329 4.83301H5.83301V3.46634C5.83301 3.08472 5.8334 2.82858 5.84951 2.63133C5.86515 2.43991 5.89307 2.34839 5.92384 2.28802C6.00373 2.13121 6.13121 2.00373 6.28802 1.92384C6.34839 1.89307 6.43991 1.86515 6.63133 1.84951C6.82858 1.8334 7.08472 1.83301 7.46634 1.83301H12.533C12.9146 1.83301 13.1708 1.8334 13.368 1.84951C13.5594 1.86515 13.651 1.89307 13.7113 1.92384C13.8681 2.00373 13.9956 2.13121 14.0755 2.28802C14.1063 2.34839 14.1342 2.43991 14.1498 2.63133C14.166 2.82858 14.1663 3.08472 14.1663 3.46634V8.53301C14.1663 8.91463 14.166 9.17077 14.1498 9.36802C14.1342 9.55944 14.1063 9.65096 14.0755 9.71133C13.9956 9.86814 13.8681 9.99562 13.7113 10.0755C13.651 10.1063 13.5594 10.1342 13.368 10.1498C13.1708 10.166 12.9146 10.1663 12.533 10.1663H11.1663Z"
                              fill="#334466"
                            />
                          </g>
                          <defs>
                            <clipPath id="clip0_19594_59773">
                              <rect width="16" height="16" fill="white" />
                            </clipPath>
                          </defs>
                        </svg>
                      </div>
                    }
                  />
                ) : (
                  <div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 16 16"
                      fill="none"
                    >
                      <g clipPath="url(#clip0_19594_59850)">
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M7.44605 0.833008H12.5533C12.9094 0.833001 13.2066 0.832995 13.4495 0.852834C13.7027 0.873526 13.9405 0.918267 14.1653 1.03283C14.5103 1.2086 14.7908 1.48906 14.9665 1.83403C15.0811 2.05887 15.1258 2.29664 15.1465 2.54989C15.1664 2.79271 15.1663 3.08994 15.1663 3.44604V8.55331C15.1663 8.90941 15.1664 9.20664 15.1465 9.44945C15.1258 9.70271 15.0811 9.94048 14.9665 10.1653C14.7908 10.5103 14.5103 10.7908 14.1653 10.9665C13.9405 11.0811 13.7027 11.1258 13.4495 11.1465C13.2066 11.1664 12.9094 11.1663 12.5533 11.1663H11.1663V12.5533C11.1663 12.9094 11.1664 13.2066 11.1465 13.4495C11.1258 13.7027 11.0811 13.9405 10.9665 14.1653C10.7908 14.5103 10.5103 14.7908 10.1653 14.9665C9.94048 15.0811 9.70271 15.1258 9.44945 15.1465C9.20664 15.1664 8.90941 15.1663 8.55331 15.1663H3.44604C3.08994 15.1663 2.79271 15.1664 2.54989 15.1465C2.29664 15.1258 2.05887 15.0811 1.83403 14.9665C1.48906 14.7908 1.2086 14.5103 1.03283 14.1653C0.918267 13.9405 0.873526 13.7027 0.852834 13.4495C0.832995 13.2066 0.833001 12.9094 0.833008 12.5533V7.44605C0.833001 7.08995 0.832995 6.79272 0.852834 6.54989C0.873526 6.29664 0.918267 6.05887 1.03283 5.83403C1.2086 5.48906 1.48906 5.2086 1.83403 5.03283C2.05887 4.91827 2.29664 4.87353 2.54989 4.85283C2.79272 4.83299 3.08995 4.833 3.44606 4.83301H4.83301V3.44606C4.833 3.08995 4.83299 2.79272 4.85283 2.54989C4.87353 2.29664 4.91827 2.05887 5.03283 1.83403C5.2086 1.48906 5.48906 1.2086 5.83403 1.03283C6.05887 0.918267 6.29664 0.873526 6.54989 0.852834C6.79272 0.832995 7.08995 0.833001 7.44605 0.833008ZM3.46634 5.83301C3.08472 5.83301 2.82858 5.8334 2.63133 5.84951C2.43991 5.86515 2.34839 5.89307 2.28802 5.92384C2.13121 6.00373 2.00373 6.13121 1.92384 6.28802C1.89307 6.34839 1.86515 6.43991 1.84951 6.63133C1.8334 6.82858 1.83301 7.08472 1.83301 7.46634V12.533C1.83301 12.9146 1.8334 13.1708 1.84951 13.368C1.86515 13.5594 1.89307 13.651 1.92384 13.7113C2.00373 13.8681 2.13121 13.9956 2.28802 14.0755C2.34839 14.1063 2.43991 14.1342 2.63133 14.1498C2.82858 14.166 3.08472 14.1663 3.46634 14.1663H8.53301C8.91463 14.1663 9.17077 14.166 9.36802 14.1498C9.55944 14.1342 9.65096 14.1063 9.71133 14.0755C9.86814 13.9956 9.99562 13.8681 10.0755 13.7113C10.1063 13.651 10.1342 13.5594 10.1498 13.368C10.166 13.1708 10.1663 12.9146 10.1663 12.533V7.46634C10.1663 7.08472 10.166 6.82858 10.1498 6.63133C10.1342 6.43991 10.1063 6.34839 10.0755 6.28802C9.99562 6.13121 9.86813 6.00373 9.71133 5.92384C9.65096 5.89307 9.55944 5.86515 9.36802 5.84951C9.17077 5.8334 8.91463 5.83301 8.53301 5.83301H3.46634ZM11.1663 10.1663V7.44604C11.1663 7.08994 11.1664 6.79271 11.1465 6.54989C11.1258 6.29664 11.0811 6.05887 10.9665 5.83403C10.7908 5.48906 10.5103 5.2086 10.1653 5.03283C9.94048 4.91827 9.70271 4.87353 9.44945 4.85283C9.20663 4.83299 8.9094 4.833 8.55329 4.83301H5.83301V3.46634C5.83301 3.08472 5.8334 2.82858 5.84951 2.63133C5.86515 2.43991 5.89307 2.34839 5.92384 2.28802C6.00373 2.13121 6.13121 2.00373 6.28802 1.92384C6.34839 1.89307 6.43991 1.86515 6.63133 1.84951C6.82858 1.8334 7.08472 1.83301 7.46634 1.83301H12.533C12.9146 1.83301 13.1708 1.8334 13.368 1.84951C13.5594 1.86515 13.651 1.89307 13.7113 1.92384C13.8681 2.00373 13.9956 2.13121 14.0755 2.28802C14.1063 2.34839 14.1342 2.43991 14.1498 2.63133C14.166 2.82858 14.1663 3.08472 14.1663 3.46634V8.53301C14.1663 8.91463 14.166 9.17077 14.1498 9.36802C14.1342 9.55944 14.1063 9.65096 14.0755 9.71133C13.9956 9.86814 13.8681 9.99562 13.7113 10.0755C13.651 10.1063 13.5594 10.1342 13.368 10.1498C13.1708 10.166 12.9146 10.1663 12.533 10.1663H11.1663Z"
                          fill="#ADB8CC"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_19594_59850">
                          <rect width="16" height="16" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                )}

                <div
                  className="cursor-pointer"
                  onClick={() => toggleShowPassword(!isShowPassword)}
                >
                  {isShowPassword ? <IconEyeSlash /> : <IconEye />}
                </div>
              </div>
            </div>
            <div
              className="text-[12px] leading-[18px] font-medium rounded-[15px] flex items-center justify-center py-[3px] px-[15px] cursor-pointer text-[#3361FF] bg-[#EBF0FF] hover:bg-[#D6DFFF]"
              onClick={handleGeneratePassword}
            >
              Generate
            </div>
          </div>

          {!isPasswordValid && (
            <div className="mt-[4px] w-[340px] text-[11px] leading-[15px] text-[#CC1414]">
              At least 8 characters long, 1 uppercase, 1 lowercase, 1 number and
              1 special character
            </div>
          )}

          <div className="flex my-[16px] justify-end ">
            <Button
              size="xs"
              corner="Rounded"
              theme="Primary"
              disabled={!password || !isPasswordValid}
              className={classNames("", {
                "border-none": !password || !isPasswordValid,
              })}
              loading={loading}
              onClick={handleResetPassword}
            >
              Save
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default memo(ModalResetPassword);
