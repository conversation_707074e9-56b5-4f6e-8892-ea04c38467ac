import { Fragment, useCallback, useEffect, useMemo, useState } from "react";
import { Redirect, useHistory, useParams } from "react-router-dom";
import { ProjectMenu } from "../../../components/ProjectMenu/ProjectMenu";
import { ImportProgress } from "../../../components_lse/ImportProgress/ImportProgress";
import { ABILITY_NEW } from "../../../config/PermissionsConfig";
import { useAPI } from "../../../providers/ApiProvider";
import {
  ROLES,
  useCheckPermission,
  useRole,
} from "../../../providers/PermissionProvider";
import { useProject } from "../../../providers/ProjectProvider";
import { Progress } from "./Progress";
import { PermissionMessage } from "../../../components/PermissionMessage/PermissionMessage";

import { useCurrentUser } from "../../../providers/CurrentUser";
import size from "../../../utils/min/size";
import { ProjectState } from "./ProjectState";
import ProjectLabels from "./ProjectLabels";
import { Project } from "../Project/Project";
import { ProjectMembers } from "./ProjectMembers";
import ProjectWorkflow from "./ProjectWorkflow";
import ProjectDashboardLoading from "./ProjectDashboardLoading";
import useFetchAssignmentConfig from "@/ai_platform_v2/hooks/useFetchAssignmentConfig";
import useFetchAssignedMemberWorkflow from "@/ai_platform_v2/hooks/useFetchAssignedMemberWorkflow";
import { ProjectType } from "../Home/utils/const";

const tabList = ["Project", "Labeling", "Inreview"];

const getType = () => {
  const typeFromURL = new URLSearchParams(location.search).get("type");

  // return typeFromURL ?? 'Project';
  return typeFromURL;
};

const getCurrentModule = () => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const params = useParams();

  return params?.moduleId;
};

const getCurrentProject = () => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const params = useParams();

  return params?.projectId;
};

const useCheckMemberWorkFlow = (assignedMembersWorkflow) => {
  const { user } = useCurrentUser();

  const hasWorkFlowStep = useCallback(
    (assignmentType) => {
      const userId = user?.uuid;

      if (!userId) {
        return false;
      }

      if (!assignedMembersWorkflow?.[assignmentType]) {
        return undefined;
      }

      const stepWorkFlow = assignedMembersWorkflow[
        assignmentType
      ]?.find((step) =>
        step.members_info?.find((member) => member.userId === userId)
      );

      return stepWorkFlow ? true : false;
    },
    [user, assignedMembersWorkflow]
  );

  return { hasWorkFlowStep };
};

export const InnerProjectDashboard = ({ disabled = false }) => {
  const [progressData, setProgressData] = useState<{
    new: number;
    labeling: number;
    inreview: number;
    completed: number;
    total: number;
    remaining: number;
    aiPrediction: number;
  }>();
  const [workflowProgressData, setWorkflowProgressData] = useState();

  const api = useAPI();

  const { project } = useProject();

  const { data: taskAssignmentConfig } = useFetchAssignmentConfig();

  const { data: assignedMembersWorkflow } = useFetchAssignedMemberWorkflow();

  const isTaxonomy = useMemo(() => project?.labelConfig?.includes("Taxonomy"), [
    project?.labelConfig,
  ]);

  const [loading, setLoading] = useState(false);
  const [tabSelect, setTabSelect] = useState(getType());

  // const [assignedMembersWorkflow, setAssignedMembersWorkflow] = useState();
  const [projectMembers, setProjectMembers] = useState();

  const [labelData, setLabelData] = useState([]);

  const currentModule = getCurrentModule();
  const currentProject = getCurrentProject();
  const { hasPermissionAllScope } = useCheckPermission();
  const canViewDataset = hasPermissionAllScope(
    ABILITY_NEW.can_view_dataset,
    currentModule,
    currentProject
  );
  const canAssignTask = hasPermissionAllScope(
    ABILITY_NEW.can_assign_task,
    currentModule,
    currentProject
  );

  const { hasWorkFlowStep } = useCheckMemberWorkFlow(assignedMembersWorkflow);
  const canLabeling = hasWorkFlowStep("Labeling");
  const canReviewing = hasWorkFlowStep("Inreview");

  const workflow = project?.annotationWorkFlow;

  const [stepWorkFlow, setStepWorkFlow] = useState(null);

  const tabsProps = useMemo(() => {
    const result = [];

    if (!tabList.find((tab) => tab === tabSelect)) {
      if (canAssignTask) {
        setTabSelect("Project");
      } else if (canLabeling) {
        setTabSelect("Labeling");
      } else if (canReviewing) {
        setTabSelect("Inreview");
      }
    }

    if (canAssignTask) {
      result.push({ key: "Project", label: `Project` });
    }

    if (canLabeling) {
      result.push({ key: "Labeling", label: `Labeling Workspace` });
    }
    if (canReviewing) {
      result.push({ key: "Inreview", label: `Reviewing Workspace ` });
    }

    return result;
  }, [canAssignTask, canLabeling, canReviewing, tabSelect]);

  const fetchProjectDashboard = useCallback(async () => {
    const res = await api.callApi("projectStatistic", {
      params: {
        project: currentProject,
        static_filter: "Status",
      },
    });

    if (res?.data) {
      setProgressData({
        new: res.data.New,
        labeling: res.data.Labeling,
        inreview: res.data.Inreview,
        completed: res.data.Completed,
        aiPrediction: res.data.AIPrediction || 0,
        total:
          res.data.New +
          res.data.Labeling +
          res.data.Inreview +
          res.data.Completed +
          (res.data.AIPrediction || 0),
        remaining:
          res.data.New +
          res.data.Labeling +
          res.data.Inreview +
          (res.data.AIPrediction || 0),
      });
    }
  }, [api, currentProject]);

  useEffect(async () => {
    const res = await api.callApi("projectMembers", {
      params: {
        pk: currentProject,
        page: 1,
        pageSize: 1000,
      },
    });

    setProjectMembers(res?.items);
  }, []);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(async () => {
    // && !isDefined(data)
    if (currentProject && assignedMembersWorkflow) {
      fetchProjectDashboard();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentProject, tabSelect, assignedMembersWorkflow]);

  useEffect(() => {
    if (getType() !== tabSelect) {
      setTabSelect(getType());
    }
  }, [getType()]);

  useEffect(async () => {
    const res = await api.callApi("stepsWorkflow", {
      params: {
        pk: currentProject,
      },
    });

    if (res?.success) {
      setStepWorkFlow(res.data);
    }
  }, [currentProject]);

  useEffect(async () => {
    if (isTaxonomy) {
      setLoading(true);

      const res = await api.callApi("projectStatisticClassTree", {
        params: {
          pk: currentProject,
        },
        errorFilter: () => true,
      });

      setLoading(false);

      setLabelData(res);
    }
  }, [currentProject, isTaxonomy]);

  return (
    <Project router="/dashboard-v2">
      {/* {tabsProps.length ? ( */}
      <div className="p-[20px]">
        <div className="flex flex-col ">
          {loading ? (
            <ProjectDashboardLoading />
          ) : (
            <>
              {canViewDataset ? (
                <div className="flex flex-wrap gap-[16px] max-w-[1235px]">
                  <Progress data={progressData} />
                  <ProjectState data={progressData} />
                  {isTaxonomy && <ProjectLabels data={labelData} />}
                  <ProjectMembers
                    projectID={currentProject}
                    membersInfo={projectMembers}
                    workflowStep={stepWorkFlow}
                    canAssignTask={
                      canAssignTask &&
                      (taskAssignmentConfig?.enableAutoAssign ||
                        taskAssignmentConfig?.enableManualAssign)
                    }
                  />
                  <ProjectWorkflow
                    projectMembers={projectMembers}
                    workflow={workflow}
                    assignedMemberWorkflow={assignedMembersWorkflow}
                    preAnnotationWorkflow={project?.preProccessConfig}
                  />
                </div>
              ) : (
                canViewDataset === false && <PermissionMessage />
              )}
            </>
          )}
        </div>
      </div>
      {/* ) : (
        <PermissionMessage />
      )} */}
    </Project>
  );
};

export const ProjectStatus = {
  Inactive: "Inactive",
  Active: "Active",
};

export const ProjectDashboard = () => {
  const { project } = useProject();
  const moduleID = getCurrentModule();
  const projectID = getCurrentProject();

  const {
    hasAnyPermissionAllScope,
    hasPermissionAllScopeInProject,
  } = useCheckPermission();
  const canViewDataset = hasPermissionAllScopeInProject(
    ABILITY_NEW.can_view_dataset,
    moduleID,
    projectID
  );

  if (
    project.projectType === ProjectType.NonAnnotation ||
    project?.projectType === ProjectType.ML
  )
    return <PermissionMessage />;

  if (canViewDataset === false) {
    return <PermissionMessage />;
  }

  const isHaveProjectData = size(project) > 0;

  if (!isHaveProjectData) {
    return <Fragment />;
  }

  const canUpdateOrUpdateStatusProject = hasAnyPermissionAllScope(
    [ABILITY_NEW.can_update_status_projects, ABILITY_NEW.can_update_projects],
    moduleID,
    projectID
  );

  const projectStatus = project.status;

  if (
    projectStatus === ProjectStatus.Inactive &&
    !canUpdateOrUpdateStatusProject
  ) {
    return <PermissionMessage />;
  }
  if (
    projectStatus === ProjectStatus.Inactive &&
    canUpdateOrUpdateStatusProject
  ) {
    return <InnerProjectDashboard disabled={true} />;
  }

  return <InnerProjectDashboard />;
};

// ProjectDashboard.title = "Dashboard";
ProjectDashboard.path = "/dashboard-v2";
ProjectDashboard.alias = "project-dashboard-v2";
ProjectDashboard.context = () => (
  <>
    <ImportProgress />
    <ProjectMenu />
  </>
);
ProjectDashboard.access = ABILITY_NEW.can_view_dataset;
ProjectDashboard.fallback = () => {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { project } = useProject();
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const role = useRole();

  if (project?.id === undefined) return null;

  localStorage.setItem(`project_entry_${project.id}`, "dashboard");
  const historyAccess = role.is(ROLES.ANNOTATOR)
    ? project?.show_annotation_history
    : true;
  const dmLink = `/projects/${project?.id}/data${
    !historyAccess ? "?labeling=1" : ""
  }`;

  return <Redirect to={dmLink} />;
};
