import IconWorkflowAIPrediction from "@/ai_platform_v2/assets/Icons/IconWorkflowAIPrediction";
import IconPlayArrow from "@v2/assets/Icons/IconPlayArrow";
import IconWorkflowCompleted from "@v2/assets/Icons/IconWorkflowCompleted";
import IconWorkflowLabeling from "@v2/assets/Icons/IconWorkflowLabeling";
import IconWorkflowNew from "@v2/assets/Icons/IconWorkflowNew";
import IconWorkflowReviewing from "@v2/assets/Icons/IconWorkflowReviewing";
import Avatar from "@v2/component/Avatar/Avatar";
import { Popover, Tooltip } from "antd";
import React, { useContext, useMemo, useRef, useState } from "react";
import "../Project/Project.css";

import IconPreProcessingCompleted from "@v2/assets/Icons/IconPreProcessingCompleted";
import IconPreProcessingHuman from "@v2/assets/Icons/IconPreProcessingHuman";
import IconPreProcessingNew from "@v2/assets/Icons/IconPreProcessingNew";

import {
  orderPreProcessingWorkflow,
  orderWorkflow,
} from "@v2/pages/TaskManagers/helper";
import { concat } from "lodash";

const WorkflowExtraMemberPopover = ({
  name,
  type,
}: {
  name: string;
  type: string;
}) => {
  const assignedMemberWorkflow_flatened = useContext(PopoverContext);
  // Get members from assignedMemberWorkflow_flatened with given name and type
  const members = assignedMemberWorkflow_flatened?.filter(
    (member) => member.name === name && member.type === type
  );

  return (
    <div className="scrollbar-v-sm">
      <div className="h-fit max-h-[250px] overflow-y-scroll overflow-x-hidden p-[10px]">
        <div className="flex flex-col w-[160px] p-[11px] justify-center items-start gap-[15px]">
          {/* Take the fift member onward */}

          {members?.slice(4).map((member) => {
            // Clean first name and last name from null and undefined
            let name = member.firstName ?? "";

            name += " ";
            name += member.lastName ?? "";

            return (
              <div className="flex items-center gap-[10px]" key={member.userId}>
                <Avatar
                  className={"rounded-full"}
                  url={member.avatar}
                  size={"40px"}
                  fontSize={"12px"}
                  name={name}
                />
                <Tooltip
                  placement="top"
                  title={name}
                  showArrow={true}
                  overlayClassName="styled-tooltip pb-[6.5px] font-normal text-[13px]"
                >
                  <div className=" cursor-pointer w-fit max-w-[90px] h-[30px] text-[14px] leading-[30px] font-normal text-gray-blue-60 text-ellipsis overflow-hidden whitespace-nowrap">
                    {name}
                  </div>
                </Tooltip>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

const WorkflowMemberPopover = ({
  name,
  type,
}: {
  name: string;
  type: string;
}) => {
  const [open, setOpen] = useState(false);
  const assignedMemberWorkflow_flatened = useContext(PopoverContext);
  // Get members from assignedMemberWorkflow_flatened with given name and type
  const members = assignedMemberWorkflow_flatened?.filter(
    (member) => member.name === name && member.type === type
  );

  return (
    <div className="flex w-fit px-[20px] py-[15px] justify-center items-center gap-[15px]">
      {/* Take 4 first member only */}
      {members?.slice(0, 4).map((member) => {
        let name = member.firstName ?? "";

        name += " ";
        name += member.lastName ?? "";
        return (
          <Tooltip
            placement="top"
            title={name}
            showArrow={true}
            overlayClassName="styled-tooltip font-normal text-[13px]"
            key={member.userId}
          >
            <Avatar
              className={"rounded-full cursor-pointer"}
              url={member.avatar}
              size={"40px"}
              fontSize={"12px"}
              name={name}
            />
            <></>
          </Tooltip>
        );
      })}
      {members.length > 4 && (
        <Popover
          overlayClassName="popover-p-0 popover-near"
          placement="right"
          content={<WorkflowExtraMemberPopover name={name} type={type} />}
          trigger="hover"
          open={open}
          onOpenChange={(newOpen) => setOpen(newOpen)}
          overlayInnerStyle={{
            borderRadius: "5px",
          }}
          showArrow={false}
        >
          <div
            className={`flex justify-center items-center bg-blue-100 border-[0px] w-[40px] h-[40px] font-normal text-[14px] text-center text-white leading-[30px] rounded-full ${
              open && "shadow-Shadows/Blue/50%/5b"
            } cursor-pointer`}
          >
            +{Math.min(members.length - 4, 99)}
          </div>
        </Popover>
      )}
    </div>
  );
};

const WorkflowNode = ({
  name,
  type,
  nodeSettings,
}: {
  name: string;
  type: string;
  nodeSettings: any;
}) => {
  const [open, setOpen] = useState(false);

  if (type === "New") {
    return (
      <div>
        <div className="bg-[#2EE6CA] mt-[9px] border-2 border-solid border-white w-fit h-fit rounded-full p-[10px] shadow-Shadows/Gray-Blue-30/10%/10b">
          <IconWorkflowNew color="white" size={30} />
        </div>
      </div>
    );
  }
  if (type === "Labeling") {
    return (
      <div>
        <Popover
          overlayClassName="popover-p-0 popover-near"
          placement="top"
          content={<WorkflowMemberPopover name={name} type={type} />}
          trigger="hover"
          open={open}
          onOpenChange={(newOpen) => setOpen(newOpen)}
          overlayInnerStyle={{
            borderRadius: "5px",
          }}
        >
          <div
            className={`bg-[#3361FF] mt-[9px] border-2 border-solid border-white w-fit h-fit rounded-full p-[10px] ${
              open
                ? "shadow-Shadows/Gray-Blue-30/25%/10b"
                : "shadow-Shadows/Gray-Blue-30/10%/10b"
            } cursor-pointer`}
          >
            <IconWorkflowLabeling color="white" size={30} />
          </div>
        </Popover>
      </div>
    );
  }
  if (type === "Inreview") {
    return (
      <div>
        <Popover
          overlayClassName="popover-p-0 popover-near"
          placement="top"
          content={<WorkflowMemberPopover name={name} type={type} />}
          trigger="hover"
          open={open}
          onOpenChange={(newOpen) => setOpen(newOpen)}
          overlayInnerStyle={{
            borderRadius: "5px",
          }}
        >
          <div
            className={`bg-[#FFCB33] mt-[9px] border-2 border-solid border-white w-fit h-fit rounded-full p-[10px] ${
              open
                ? "shadow-Shadows/Gray-Blue-30/25%/10b"
                : "shadow-Shadows/Gray-Blue-30/10%/10b"
            } cursor-pointer`}
          >
            <IconWorkflowReviewing color="white" size={30} />
          </div>
        </Popover>
      </div>
    );
  }
  if (type === "Completed") {
    return (
      <div>
        <div className="bg-[#29CC39] mt-[9px] border-2 border-solid border-white w-fit h-fit rounded-full p-[10px] shadow-Shadows/Gray-Blue-30/10%/10b">
          <IconWorkflowCompleted color="white" size={30} />
        </div>
      </div>
    );
  }
  if (type === "AIPrediction") {
    const isManual = nodeSettings.manualPrediction;

    if (isManual) {
      return (
        <div>
          <Popover
            overlayClassName="popover-p-0 popover-near"
            placement="top"
            content={<WorkflowMemberPopover name={name} type={type} />}
            trigger="hover"
            open={open}
            onOpenChange={(newOpen) => setOpen(newOpen)}
            overlayInnerStyle={{
              borderRadius: "5px",
            }}
          >
            <div
              className={`bg-white mt-[9px] border-2 border-solid border-white w-fit h-fit rounded-full${
                open
                  ? "shadow-Shadows/Gray-Blue-30/25%/10b"
                  : "shadow-Shadows/Gray-Blue-30/10%/10b"
              } cursor-pointer`}
            >
              <IconWorkflowAIPrediction size={50} />
            </div>
          </Popover>
        </div>
      );
    }
    return (
      <div
        className={`bg-white mt-[9px] border-2 border-solid border-white w-fit h-fit rounded-full shadow-Shadows/Gray-Blue-30/10%/10b`}
      >
        <IconWorkflowAIPrediction size={50} />
      </div>
    );
  }
  if (type === "PreProcessingNew") {
    return (
      <div
        className={`bg-white mt-[9px] border-2 border-solid border-white w-fit h-fit rounded-full shadow-Shadows/Gray-Blue-30/10%/10b`}
      >
        <IconPreProcessingNew size={50} />
      </div>
    );
  }
  if (type === "HumanApproval") {
    return (
      <div>
        <Popover
          overlayClassName="popover-p-0 popover-near"
          placement="top"
          content={<WorkflowMemberPopover name={name} type={type} />}
          trigger="hover"
          open={open}
          onOpenChange={(newOpen) => setOpen(newOpen)}
          overlayInnerStyle={{
            borderRadius: "5px",
          }}
        >
          <div
            className={`bg-white mt-[9px] border-2 border-solid border-white w-fit h-fit rounded-full${
              open
                ? "shadow-Shadows/Gray-Blue-30/25%/10b"
                : "shadow-Shadows/Gray-Blue-30/10%/10b"
            } cursor-pointer`}
          >
            <IconPreProcessingHuman size={50} />
          </div>
        </Popover>
      </div>
    );
  }
  if (type === "PreProcessingCompleted") {
    const isManualTransfer = nodeSettings?.isManualConfirmation;

    if (isManualTransfer) {
      return (
        <div>
          <Popover
            overlayClassName="popover-p-0 popover-near"
            placement="top"
            content={<WorkflowMemberPopover name={name} type={type} />}
            trigger="hover"
            open={open}
            onOpenChange={(newOpen) => setOpen(newOpen)}
            overlayInnerStyle={{
              borderRadius: "5px",
            }}
          >
            <div
              className={`bg-white mt-[9px] border-2 border-solid border-white w-fit h-fit rounded-full${
                open
                  ? "shadow-Shadows/Gray-Blue-30/25%/10b"
                  : "shadow-Shadows/Gray-Blue-30/10%/10b"
              } cursor-pointer`}
            >
              <IconPreProcessingCompleted size={50} />
            </div>
          </Popover>
        </div>
      );
    }
    return (
      <div
        className={`bg-white mt-[9px] border-2 border-solid border-white w-fit h-fit rounded-full shadow-Shadows/Gray-Blue-30/10%/10b`}
      >
        <IconPreProcessingCompleted size={50} />
      </div>
    );
  }
  return null;
};

const PopoverContext = React.createContext([]);

export const ProjectWorkflow = ({
  workflow,
  assignedMemberWorkflow,
  projectMembers,
  preAnnotationWorkflow,
}: {
  workflow: string;
  assignedMemberWorkflow: any;
  projectMembers: any;
  preAnnotationWorkflow: any;
}) => {
  // If the assignedMemberWorkflow is empty, return null
  if (!assignedMemberWorkflow) return null;

  // If the Inreview and Labeling array is empty, return null
  if (
    assignedMemberWorkflow.Inreview.length === 0 &&
    assignedMemberWorkflow.Labeling.length === 0 &&
    assignedMemberWorkflow.AIPrediction.length === 0
  )
    return null;

  // Parse workflow from string to object
  const parsedWorkflow = useMemo(() => {
    if (!workflow) {
      return null;
    }

    try {
      return JSON.parse(workflow);
    } catch (e) {
      return null;
    }
  }, [workflow]);

  const parsedPreWorkflow = useMemo(() => {
    if (!preAnnotationWorkflow) {
      return null;
    }

    try {
      return JSON.parse(preAnnotationWorkflow);
    } catch (e) {
      return null;
    }
  }, [preAnnotationWorkflow]);

  const orderedWorkflow = useMemo(() => {
    return orderWorkflow(parsedWorkflow);
  }, [parsedWorkflow]);

  const orderedPreWorkflow = useMemo(() => {
    return orderPreProcessingWorkflow(parsedPreWorkflow);
  }, [parsedPreWorkflow]);

  // Add Play button inbetween nodes to orderedWorkflow
  const paddedWorkflow = useMemo(() => {
    if (!orderedWorkflow) {
      return null;
    }

    const result = concat(orderedPreWorkflow, orderedWorkflow).reduce(
      (acc, node, index) => {
        if (index === 0) {
          return [node];
        }

        return [...acc, { type: "_padding_" }, node];
      },
      []
    );

    return result;
  }, [orderedWorkflow]);

  let assignedMemberWorkflow_flatened: any = [];

  // Flatten the assignedMemberWorkflow into 1 array containing members info and their node type
  if (
    assignedMemberWorkflow &&
    assignedMemberWorkflow?.Inreview &&
    assignedMemberWorkflow?.Labeling &&
    assignedMemberWorkflow?.AIPrediction &&
    assignedMemberWorkflow?.HumanApproval &&
    assignedMemberWorkflow?.PreProcessingCompleted
  ) {
    // Flatten the assignedMemberWorkflow into 1 array containing members info and their node type
    for (let i = 0; i < assignedMemberWorkflow?.Labeling.length; i++) {
      assignedMemberWorkflow_flatened = [
        ...assignedMemberWorkflow_flatened,
        ...assignedMemberWorkflow?.Labeling[i]?.members_info.map((obj) => ({
          ...obj,
          name: assignedMemberWorkflow?.Labeling[i]?.name,
          type: assignedMemberWorkflow?.Labeling[i]?.type,
        })),
      ];
    }

    for (let i = 0; i < assignedMemberWorkflow?.Inreview.length; i++) {
      assignedMemberWorkflow_flatened = [
        ...assignedMemberWorkflow_flatened,
        ...assignedMemberWorkflow?.Inreview[i]?.members_info?.map((obj) => ({
          ...obj,
          name: assignedMemberWorkflow?.Inreview[i]?.name,
          type: assignedMemberWorkflow?.Inreview[i]?.type,
        })),
      ];
    }

    for (let i = 0; i < assignedMemberWorkflow?.AIPrediction.length; i++) {
      assignedMemberWorkflow_flatened = [
        ...assignedMemberWorkflow_flatened,
        ...assignedMemberWorkflow?.AIPrediction[i]?.members_info?.map(
          (obj) => ({
            ...obj,
            name: assignedMemberWorkflow?.AIPrediction[i]?.name,
            type: assignedMemberWorkflow?.AIPrediction[i]?.type,
          })
        ),
      ];
    }

    for (let i = 0; i < assignedMemberWorkflow?.HumanApproval.length; i++) {
      assignedMemberWorkflow_flatened = [
        ...assignedMemberWorkflow_flatened,
        ...assignedMemberWorkflow?.HumanApproval[i]?.members_info?.map(
          (obj) => ({
            ...obj,
            name: assignedMemberWorkflow?.HumanApproval[i]?.name,
            type: assignedMemberWorkflow?.HumanApproval[i]?.type,
          })
        ),
      ];
    }

    for (
      let i = 0;
      i < assignedMemberWorkflow?.PreProcessingCompleted.length;
      i++
    ) {
      assignedMemberWorkflow_flatened = [
        ...assignedMemberWorkflow_flatened,
        ...assignedMemberWorkflow?.PreProcessingCompleted[i]?.members_info?.map(
          (obj) => ({
            ...obj,
            name: assignedMemberWorkflow?.PreProcessingCompleted[i]?.name,
            type: assignedMemberWorkflow?.PreProcessingCompleted[i]?.type,
          })
        ),
      ];
    }
  }

  // Change avatar in assignedMemberWorkflow_flatened to respective avatar in projectMembers
  if (assignedMemberWorkflow_flatened && projectMembers) {
    assignedMemberWorkflow_flatened = assignedMemberWorkflow_flatened.map(
      (obj) => {
        const member = projectMembers.find(
          (member) => member.userId === obj.userId
        );

        return {
          ...obj,
          avatar: member?.avatar,
        };
      }
    );
  }

  const containerRef = useRef(null);
  const [scrollInterval, setScrollInterval] = useState(null);
  const stopScroll = () => {
    clearInterval(scrollInterval);
  };

  const handleMouseOver = (e) => {
    if (!containerRef.current) {
      return;
    }
    const container = containerRef.current;
    const containerWidth = container.offsetWidth;
    const isRightEnd = e.pageX > container.offsetLeft + containerWidth - 20;
    const isLeftEnd = e.pageX < container.offsetLeft + 20;

    let scrollSpeed = 0;

    if (isRightEnd) {
      scrollSpeed = 5;
    } else if (isLeftEnd) {
      scrollSpeed = -5;
    }

    clearInterval(scrollInterval);

    setScrollInterval(
      setInterval(() => {
        container.scrollLeft += scrollSpeed;
      }, 10)
    );

    container.addEventListener("mouseleave", stopScroll);
    container.addEventListener("mouseup", stopScroll);
  };

  return (
    <div
      className={
        "w-[609px] h-[217px] pt-[20px] pr-[20px] pb-[10px] pl-[20px] bg-white shadow-Shadows/Gray-Blue-30/3%/5b rounded-lg scrollbar-none"
      }
    >
      <div className=" font-semibold text-[15px] text-gray-blue-40">
        PROJECT WORKFLOW
      </div>
      <div
        className="flex justify-between mx-auto mt-[44px] h-[70px] gap-[20px] w-fit max-w-[430px] overflow-y-hidden"
        ref={containerRef}
        onMouseOver={handleMouseOver}
      >
        {paddedWorkflow?.map((node, idx) => {
          if (node.type === "_padding_") {
            return (
              <div className="w-[30px] mt-[19px]" key={idx}>
                <IconPlayArrow color="#C3CAD9" size={30} />
              </div>
            );
          }
          return (
            <PopoverContext.Provider
              value={assignedMemberWorkflow_flatened}
              key={idx}
            >
              <WorkflowNode
                name={node.name}
                type={node.type}
                nodeSettings={node?.settings}
              />
            </PopoverContext.Provider>
          );
        })}
      </div>
    </div>
  );
};
export default ProjectWorkflow;
