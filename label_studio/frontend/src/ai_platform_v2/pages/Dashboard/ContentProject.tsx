import { MessageWorkflowUpdate } from "@/ai_platform_v2/component/MessageWorkflowUpdate/MessageWorkflowUpdate";
import { useProject } from "@/providers/ProjectProvider";
import { useEffect, useMemo } from "react";
import { useParams } from "react-router";

export const ContentProject = ({ children }: any) => {
  const { project, fetchProject } = useProject();
  const params = useParams();

  useEffect(() => {
    fetchProject(params.projectId, true);
  }, [fetchProject]);

  const isProcessingWorkflow = useMemo(
    () =>
      project?.workflowStatus === "Process" ||
      project?.advancedAnnotationStatus === "Processing",
    [project?.workflowStatus, project?.advancedAnnotationStatus]
  );

  return (
    <div className="h-full w-full grow wrapper-content-project overflow-y-auto">
      {isProcessingWorkflow ? <MessageWorkflowUpdate /> : children}
    </div>
  );
};
