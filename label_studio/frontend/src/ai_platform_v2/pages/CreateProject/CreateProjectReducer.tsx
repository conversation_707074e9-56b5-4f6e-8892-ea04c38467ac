// import { unique } from "../../../utils/helpers";

export const DEFAULT_COLOR = "#3361FF";

export const PO_ROLE_ID = "ae71f29d-f0cc-4299-a5fc-f84b42ec4e12";

export const autoModeSelect = [
  { value: "Evenly", label: "Assign Evenly" },
  { value: "InTurn", label: "Assign In Turn" },
];

export const sortFieldOptions = [
  { value: "Priority", label: "Priority" },
  { value: "Uploaded Date", label: "Uploaded Date" },
];

export const steps = [
  {
    key: 1,
    title: "Information",
    disabled: false,
  },
  {
    key: 2,
    title: "Member",
    disabled: false,
  },
  {
    key: 3,
    title: "Tool Configuration",
    disabled: false,
  },
  {
    key: 4,
    title: "Workflow",
    disabled: false,
  },
  {
    key: 5,
    title: "Assignment Configuration",
    disabled: false,
  },
];

export const ACTIONS = {
  SET_MODULE_ID: "set_module_id",
  LOAD_MODULES_LIST: "load_modules_list",
  SET_CURRENT_STEP: "set_current_step",
  SET_FINAL_STEP: "set_final_step",
  UPDATE_INFORMATION_STEP: "update_information_step",
  SET_NAME: "set_user",
  SET_DESCRIPTION: "set_description",
  SET_MEMBER_ROLES: "set_member_roles",
  SET_LABEL_CONFIG: "set_label_config",
  SET_WORKFLOW: "set_workflow",
  SET_ASSIGNMENT_CONFIG_STEP: "set_assignment_config_step",
  RESET: "reset",

  LOAD_ROLE_LIST: "load_role_list",
};

export const initialState = {
  roleList: [],
  modulesList: [],

  moduleId: "",
  currentStep: steps[0].key,
  isFinalStep: false,
  name: "",
  description: "",
  color: DEFAULT_COLOR,
  status: "Active",
  memberRoles: [],
  labelConfig: "",
  annotationWorkFlow: "",
  preProccessConfig: "",
  classConfig: "<TORUS_TEMPLATE>\n</TORUS_TEMPLATE>",

  enableManualAssign: false,
  enableAutoAssign: false,
  autoMode: autoModeSelect[1].value,
  batchSize: 1,
  sortField: sortFieldOptions[0].value,
  orderASC: false,
  selftAssigning: false,
  taskReminder: false,
  taskRemindHours: 0,
};

export const CreateProjectReducer = (state = initialState, action: any) => {
  if (action.type === ACTIONS.LOAD_ROLE_LIST) {
    return {
      ...state,
      roleList: action.payload,
    };
  } else if (action.type === ACTIONS.LOAD_MODULES_LIST) {
    return {
      ...state,
      modulesList: action.payload,
    };
  } else if (action.type === ACTIONS.SET_MODULE_ID) {
    return {
      ...state,
      moduleId: action.payload,
    };
  } else if (action.type === ACTIONS.SET_CURRENT_STEP) {
    const stepObject = steps.find((step) => step.key === action.payload);

    if (!stepObject) {
      return { ...state };
    }

    return {
      ...state,
      currentStep: stepObject.key,
    };
  } else if (action.type === ACTIONS.SET_FINAL_STEP) {
    return {
      ...state,
      isFinalStep: action.payload,
    };
  } else if (action.type === ACTIONS.UPDATE_INFORMATION_STEP) {
    return {
      ...state,
      ...action.payload,
    };
  } else if (action.type === ACTIONS.SET_NAME) {
    return {
      ...state,
      name: action.payload,
    };
  } else if (action.type === ACTIONS.SET_DESCRIPTION) {
    return {
      ...state,
      description: action.payload,
    };
  } else if (action.type === ACTIONS.SET_MEMBER_ROLES) {
    return {
      ...state,
      memberRoles: action.payload,
    };
  } else if (action.type === ACTIONS.SET_LABEL_CONFIG) {
    return {
      ...state,
      labelConfig: action.payload,
    };
  } else if (action.type === ACTIONS.SET_WORKFLOW) {
    return {
      ...state,
      annotationWorkFlow: action.payload?.annotationWorkFlow,
      preProccessConfig: action.payload?.preProccessConfig,
    };
  } else if (action.type === ACTIONS.SET_ASSIGNMENT_CONFIG_STEP) {
    return {
      ...state,
      ...action.payload,
    };
  } else if (action.type === ACTIONS.RESET) {
    return initialState;
  }

  return state;
};
