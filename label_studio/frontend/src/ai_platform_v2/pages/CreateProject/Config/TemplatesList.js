import "codemirror/lib/codemirror.css";
import "codemirror/mode/xml/xml";
import React, { useCallback } from "react";
import { useAPI } from "../../../../providers/ApiProvider";
import { cn } from "../../../../utils/bem";
import "./Config.styl";
import { Spin } from "antd";
import { ToggleItems } from "../../../../components";
import { Button, Checkbox } from "@taureau/ui";
import classNames from "classnames";

const listClass = cn("create-project-templates-list");

const TemplatesInGroup = ({
  templates,
  group,
  onSelectRecipe,
  configMix,
  setConfigMix,
}) => {
  const picked = templates
    .filter((recipe) => recipe.group === group)
    .sort(
      (a, b) => (a.order_in_tas ?? Infinity) - (b.order_in_tas ?? Infinity)
    );

  const addMixTool = (recipe) => {
    const cloneConfig = [...configMix];

    if (configMix.includes(recipe)) {
      setConfigMix(cloneConfig.filter((n) => n !== recipe));
    } else {
      cloneConfig.push(recipe);

      setConfigMix(cloneConfig);
    }
  };

  const renderButtonOpenConfig = useCallback(
    (recipe) => {
      return (
        <div className="absolute top-[-10px] bottom-0 right-0 left-0 bg-black-10 rounded-[5px] flex justify-center items-center opacity-0 group-hover:opacity-100">
          <Button
            theme="Primary"
            size={"sm"}
            corner="Rounded"
            onClick={(e) => {
              e.stopPropagation();
              onSelectRecipe(recipe);
            }}
          >
            Open
          </Button>
        </div>
      );
    },
    [onSelectRecipe]
  );

  return (
    <ul
      style={{
        justifyContent: "space-evenly",
        justifyItems: "center",
        alignContent: "space-evenly",
        alignItems: "center",
      }}
    >
      {picked.map((recipe) => (
        <li
          key={recipe.title}
          onClick={() => {
            if (
              recipe.title === "Ellipse" ||
              recipe.title === "Keypoint" ||
              recipe.title === "Skeleton" ||
              recipe.title === "Taxonomy"
            )
              return;
            addMixTool(recipe.title);
          }}
          className={listClass.elem("template")}
        >
          <li className="w-full flex px-[10px] pt-[10px]">
            <Checkbox
              checked={configMix.includes(recipe.title)}
              disabled={
                recipe.title === "Ellipse" ||
                recipe.title === "Keypoint" ||
                recipe.title === "Skeleton" ||
                recipe.title === "Taxonomy"
              }
            />
          </li>
          <div className="w-full h-full flex justify-center items-center">
            <div className="group relative w-[250px] h-[200px]">
              <img src={recipe.image} />
              {renderButtonOpenConfig(recipe)}
            </div>
          </div>
          <h3>{recipe.title}</h3>
        </li>
      ))}
    </ul>
  );
};

export const TemplatesList = ({
  selectedGroup,
  onCustomTemplate,
  onSelectGroup,
  onSelectRecipe,
  configMix,
  setConfigMix,
  genMixTool,
  setGenMixTool,
}) => {
  const [groups, setGroups] = React.useState([]);
  const [templates, setTemplates] = React.useState();
  const api = useAPI();

  React.useEffect(async () => {
    const res = await api.callApi("configTemplates");

    if (!res) return;
    const { templates, groups } = res;

    setTemplates(templates);
    setGroups(groups);
  }, []);

  const selected = selectedGroup || groups[0];

  const onSelect = (e) => {
    onSelectGroup(e);
  };

  return (
    <div className={listClass}>
      <div className="w-full flex justify-between pb-[12px]">
        <div className="flex gap-2 items-center">
          <button
            type="button"
            onClick={onCustomTemplate}
            style={{
              backgroundColor: "#3361FF",
              border: "1px solid #3361FF",
              color: "white",
              fontSize: "12px",
              fontWeight: "500",
              display: "flex",
              gap: "5px",
              padding: "5px 10px",
              height: 30,
              alignItems: "center",
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="17"
              height="16"
              viewBox="0 0 17 16"
              fill="none"
            >
              <path
                d="M4.1552 5.91233C4.1552 4.66586 5.16586 3.6552 6.41233 3.6552C7.3893 3.6552 8.23151 4.29528 8.53471 5.17119L11.2803 2.42557L9.96647 1.11172C9.81487 0.960122 9.56221 0.960122 9.41061 1.11172L8.77053 1.7518C8.43364 1.56652 8.07991 1.41492 7.6925 1.31385V0.404262C7.6925 0.168443 7.50721 0 7.28823 0H5.53643C5.30061 0 5.13217 0.168443 5.13217 0.404262V1.31385C4.74475 1.41492 4.39102 1.56652 4.05414 1.7518L3.41406 1.11172C3.26246 0.960122 3.00979 0.960122 2.8582 1.11172L1.62856 2.34135C1.47697 2.49295 1.47697 2.74561 1.62856 2.89721L2.26865 3.53729C2.08336 3.87418 1.93176 4.22791 1.8307 4.61532H0.904262C0.668443 4.61532 0.5 4.80061 0.5 5.01959V6.75454C0.5 6.99036 0.668443 7.15881 0.904262 7.15881H1.81385C1.91492 7.54622 2.06652 7.89995 2.2518 8.23684L1.61172 8.87692C1.44328 9.02852 1.46012 9.28118 1.61172 9.43278L2.92557 10.7466L5.67119 8.00102C4.77844 7.73151 4.1552 6.8893 4.1552 5.91233Z"
                fill="white"
              />
              <path
                d="M11.6025 4.39551L5.59961 10.3984L8.1842 12.983L14.1871 6.9801L11.6025 4.39551Z"
                fill="white"
              />
              <path
                d="M16.3334 4.07716L14.5311 2.25798C14.3121 2.03901 13.9752 2.03901 13.7562 2.25798L12.5771 3.43708L15.1712 6.03109L16.3503 4.852C16.5524 4.63302 16.5524 4.27929 16.3334 4.07716Z"
                fill="white"
              />
              <path
                d="M3.96975 12.0429C3.90237 12.1103 3.85184 12.2114 3.81815 12.3124L3.27913 14.6538C3.19491 15.0412 3.54864 15.3949 3.93606 15.3107L6.27741 14.7717C6.37848 14.7548 6.4627 14.7043 6.54692 14.6201L7.22069 13.9463L4.64352 11.3691L3.96975 12.0429Z"
                fill="white"
              />
            </svg>
            Custom template
          </button>
          <Button
            disabled={configMix.length < 2}
            loading={genMixTool}
            className={classNames(
              `h-[30px] text-[12px] font-medium px-[10px] border-none`,
              configMix.length > 1
                ? "text-blue-blue bg-blue-blue-20 hover:bg-blue-blue-20"
                : "bg-black-5 hover:bg-black-10"
            )}
            theme="Primary"
            size="sm"
            onClick={() => {
              setGenMixTool(true);
            }}
          >
            Generate mixed tool
          </Button>
        </div>
        <div className="w-fit">
          <ToggleItems
            items={{
              "Computer Vision": "Computer Vision",
              Document: "Document",
              Others: "Others",
            }}
            active={selected}
            onSelect={onSelect}
          />
        </div>
      </div>

      <main className="scrollbar">
        {!templates && <Spin style={{ width: "100%", height: 200 }} />}
        <TemplatesInGroup
          templates={templates || []}
          group={selected}
          onSelectRecipe={onSelectRecipe}
          configMix={configMix}
          setConfigMix={setConfigMix}
        />
      </main>
    </div>
  );
};
