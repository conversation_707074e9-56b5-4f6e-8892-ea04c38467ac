import "codemirror/lib/codemirror.css";
import "codemirror/mode/xml/xml";
import React, { useMemo, useState } from "react";
import { Button as ButtonT } from "@taureau/ui";
import { UnControlled as <PERSON><PERSON>ir<PERSON><PERSON> } from "react-codemirror2";
import { Button } from "../../../../components";
import { ToggleItems } from "../../../component/ToggleItems/ToggleItems";
import { Form } from "../../../../components/Form/Form";
import { Oneof } from "../../../../components/Oneof/Oneof";
import { cn } from "../../../utils/bem";
import { Palette } from "../../../utils/colors";
import { colorNames, rgbaToString } from "./colors";
import "./Config.styl";
import { Preview } from "./Preview";
import {
  DEFAULT_COLUMN,
  EMPTY_CONFIG,
  isEmptyConfig,
  Template,
} from "./Template";
import { TemplatesList } from "./TemplatesList";
import { absoluteURL } from "../../../utils/helpers";
import { Tabs, Tooltip } from "antd";
import IconPhotoFilter from "@/ai_platform_v2/assets/Icons/IconPhotoFilter";
import IconSettingsV2 from "@/ai_platform_v2/assets/Icons/IconSettingsV2";
import IconInvertColors from "@/ai_platform_v2/assets/Icons/IconInvertColors";
import { ChromePicker } from "react-color";
import tags from "./schema.json";
import CM from "codemirror";
import "./config-hint";
import "./codemirror.css";
import "codemirror/addon/hint/show-hint";
import "codemirror/addon/hint/show-hint.css";
import { startCase } from "lodash";

// don't do this, kids
const formatXML = (xml) => {
  // don't use formatting if the config has new lines
  if (xml.indexOf("\n") >= 0) {
    return xml;
  }

  let depth = 0;

  try {
    return xml.replace(/<(\/)?.*?(\/)?>[\s\n]*/g, (tag, close1, close2) => {
      if (!close1) {
        const res = "  ".repeat(depth) + tag.trim() + "\n";

        if (!close2) depth++;
        return res;
      } else {
        depth--;
        return "  ".repeat(depth) + tag.trim() + "\n";
      }
    });
  } catch (e) {
    return xml;
  }
};

const wizardClass = cn("create-project-wizard");
const configClass = cn("create-project-configure");

const EmptyConfigPlaceholder = () => (
  <div className={configClass.elem("empty-config")}>
    <p>
      Your labeling configuration is empty. It is required to label your data.
    </p>
    <p>
      Start from one of our predefined templates or create your own config on
      the Code panel. The labeling config is XML-based and you can{" "}
      <a href="https://labelstud.io/tags/" target="_blank">
        read about the available tags in our documentation
      </a>
      .
    </p>
  </div>
);

const Label = ({ label, template, color }) => {
  const value = label.getAttribute("value");

  return (
    <li
      className={configClass
        .elem("label")
        .mod({ choice: label.tagName === "Choice" })}
    >
      <label style={{ borderLeft: `solid 3px ${color}`, background: color }}>
        {/* <input
          type="color"
          className={configClass.elem("label-color")}
          value={colorNames[color] || color}
          onChange={e => template.changeLabel(label, { background: e.target.value })}
        /> */}
      </label>
      <span className="text-[#346] text-[12px] font-medium leading-[18px]">
        {value}
      </span>
      <div style={{ display: "flex", alignItems: "center" }}>
        {/* <ColorPicker 
        rootClassName='taureau-color-picker'
        placement='bottom'
        value={colorNames[color] || color} 
        onChange={value => template.changeLabel(label, { background: value.toHexString() })}
      >
        <button type="button" className={configClass.elem("color-label")}>
          <IconInvertColors />
        </button>
      </ColorPicker> */}
        <Tooltip
          trigger="click"
          color="white"
          placement="bottom"
          // usePopupContainer={false}
          // maxHeight={300}
          overlayClassName="taureau-color-picker"
          title={
            <ChromePicker
              color={colorNames[color] || color}
              onChange={(value) => {
                template.changeLabel(label, {
                  background: rgbaToString(value.rgb),
                });
              }}
            />
          }
          // onOpenChange={newOpen => setOpenSort(newOpen)}
        >
          <button type="button" className={configClass.elem("color-label")}>
            <IconInvertColors />
          </button>
        </Tooltip>
        <button
          type="button"
          className={configClass.elem("delete-label")}
          onClick={() => template.removeLabel(label)}
        >
          <svg
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            stroke="red"
            strokeWidth="2"
            strokeLinecap="square"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M2 12L12 2" />
            <path d="M12 12L2 2" />
          </svg>
        </button>
      </div>
    </li>
  );
};

const ConfigureControl = ({ control, template }) => {
  const refLabels = React.useRef();
  const tagname = control.tagName;

  const toolName = useMemo(() => {
    if (tagname === "Choices") return "Choices";
    if (control.hasAttribute("drawCircle")) return "Circle Labels";
    return startCase(tagname); // Convert camelCase text to tile case text ex: PolygonLabels ===> Polygon Labels
  }, [control, tagname]);

  if (tagname !== "Choices" && !tagname.endsWith("Labels")) return null;
  const palette = Palette();

  const onAddLabels = () => {
    if (!refLabels.current) return;
    template.addLabels(control, refLabels.current.value);
    refLabels.current.value = "";
  };
  const onKeyPress = (e) => {
    if (e.key === "Enter" && e.ctrlKey) {
      e.preventDefault();
      onAddLabels();
    }
  };

  return (
    <div className={configClass.elem("labels")}>
      <form className={configClass.elem("add-labels")} action="">
        <h3>
          {toolName} ({control.children.length})
        </h3>
        <textarea
          name="labels"
          id=""
          cols="30"
          rows="5"
          ref={refLabels}
          onKeyPress={onKeyPress}
          placeholder="Enter label name to add label values, use a new line to add multiple labels simultaneously."
        ></textarea>
        <input type="button" value="Add" onClick={onAddLabels} />
      </form>
      <div
        className={configClass.elem("current-labels")}
        style={{
          overflowY:
            Array.from(control.children).length > 5 ? "overlay" : "visible",
        }}
      >
        {/* <h3>
          {toolName} ({control.children.length})
        </h3> */}
        <ul>
          {Array.from(control.children).map((label) => (
            <Label
              label={label}
              template={template}
              key={label.getAttribute("value")}
              color={label.getAttribute("background") || palette.next().value}
            />
          ))}
        </ul>
      </div>
    </div>
  );
};

const ConfigureSettings = ({ template }) => {
  const { settings } = template;

  if (!settings) return null;
  const keys = Object.keys(settings);

  const items = keys.map((key) => {
    const options = settings[key];
    const type = Array.isArray(options.type) ? Array : options.type;
    const $object = options.object;
    const $tag = options.control ? options.control : $object;

    if (!$tag) return null;
    if (options.when && !options.when($tag)) return;
    let value = false;

    if (options.value) value = options.value($tag);
    else if (typeof options.param === "string")
      value = $tag.getAttribute(options.param);
    if (value === "true") value = true;
    if (value === "false") value = false;
    let onChange;
    let size;

    switch (type) {
      case Array:
        onChange = (e) => {
          if (typeof options.param === "function") {
            options.param($tag, e.target.value);
          } else {
            $object.setAttribute(options.param, e.target.value);
          }
          template.render();
        };
        return (
          <li key={key}>
            <label>
              {options.title}{" "}
              <select value={value} onChange={onChange}>
                {options.type.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            </label>
          </li>
        );
      case Boolean:
        onChange = (e) => {
          if (typeof options.param === "function") {
            options.param($tag, e.target.checked);
          } else {
            $object.setAttribute(
              options.param,
              e.target.checked ? "true" : "false"
            );
          }
          template.render();
        };
        return (
          <li key={key}>
            <label>
              <input type="checkbox" checked={value} onChange={onChange} />{" "}
              {options.title}
            </label>
          </li>
        );
      case String:
      case Number:
        size = options.type === Number ? 5 : undefined;
        onChange = (e) => {
          if (typeof options.param === "function") {
            options.param($tag, e.target.value);
          } else {
            $object.setAttribute(options.param, e.target.value);
          }
          template.render();
        };
        return (
          <li key={key}>
            <label>
              {options.title}{" "}
              <input type="text" onInput={onChange} value={value} size={size} />
            </label>
          </li>
        );
    }
  });

  // check for active settings
  if (!items.filter(Boolean).length) return null;

  return (
    <ul className={configClass.elem("settings")}>
      <li>
        {/* <h4>Configure settings</h4> */}
        <h4>Settings</h4>
        <ul className={configClass.elem("object-settings")}>{items}</ul>
      </li>
    </ul>
  );
};

const ConfigureColumns = ({ columns, template }) => {
  const updateValue = (obj) => (e) => {
    const attrName = e.target.value.replace(/^\$/, "");

    obj.setAttribute("value", "$" + attrName);
    template.render();
  };

  if (!template.objects.length) return null;

  return (
    <div className={configClass.elem("object")}>
      <h4>Configure data</h4>
      {template.objects.length > 1 &&
        columns?.length > 0 &&
        columns.length < template.objects.length && (
          <p className={configClass.elem("object-error")}>
            This template requires more data then you have for now
          </p>
        )}
      {columns?.length === 0 && (
        <p className={configClass.elem("object-error")}>
          To select which field(s) to label you need to upload the data.
          Alternatively, you can provide it using Code mode.
        </p>
      )}
      {template.objects.map((obj) => (
        <p key={obj.getAttribute("name")}>
          Use {obj.tagName.toLowerCase()}
          {template.objects > 1 && ` for ${obj.getAttribute("name")}`}
          {" from "}
          {columns?.length > 0 && columns[0] !== DEFAULT_COLUMN && "field "}
          <select
            onChange={updateValue(obj)}
            value={obj.getAttribute("value")?.replace(/^\$/, "")}
          >
            {columns?.map((column) => (
              <option key={column} value={column}>
                {column === DEFAULT_COLUMN ? "<imported file>" : `$${column}`}
              </option>
            ))}
            {!columns?.length && (
              <option value={obj.getAttribute("value")?.replace(/^\$/, "")}>
                {"<imported file>"}
              </option>
            )}
          </select>
        </p>
      ))}
    </div>
  );
};

const Configurator = ({
  config,
  template,
  setTemplate,
  onBrowse,
  onSaveClick,
  disableSaveButton,
}) => {
  const [configure, setConfigure] = React.useState(
    isEmptyConfig(config) ? "code" : "visual"
  );
  const [visualLoaded, loadVisual] = React.useState(configure === "visual");
  const [waiting, setWaiting] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState();
  const [configToCheck, setConfigToCheck] = React.useState();
  const [data, setData] = React.useState();
  const debounceTimer = React.useRef();

  const [saved, setSaved] = React.useState(false);

  React.useEffect(() => {
    // config may change during init, so wait for that, but for a very short time only
    debounceTimer.current = window.setTimeout(
      () => setConfigToCheck(config),
      configToCheck ? 500 : 30
    );
    return () => window.clearTimeout(debounceTimer.current);
  }, [config]);

  function completeAfter(cm, pred) {
    if (!pred || pred()) {
      setTimeout(function () {
        if (!cm.state.completionActive) cm.showHint({ completeSingle: false });
      }, 100);
    }
    return CM.Pass;
  }

  function completeIfInTag(cm) {
    return completeAfter(cm, function () {
      const token = cm.getTokenAt(cm.getCursor());

      if (
        token.type === "string" &&
        (!/['"]$/.test(token.string) || token.string.length === 1)
      )
        return false;

      const inner = CM.innerMode(cm.getMode(), token.state).state;

      return inner.tagName;
    });
  }
  React.useEffect(async () => {
    let infoData = {};
    const parser = new DOMParser();
    let a = parser.parseFromString(config, "application/xml");
    // console.log(a.querySelectorAll("*")[0].childNodes[0].nodeName);

    for (let i = 0; i < a.querySelectorAll("*")[0].childNodes.length; i++) {
      if (a.querySelectorAll("*")[0].childNodes[i].nodeName === "#comment") {
        let valueComment = JSON.parse(
          a.querySelectorAll("*")[0].childNodes[i].textContent
        );

        if (valueComment.data) {
          infoData = valueComment.data;
        }
      }
    }
    infoData.state = "Labeling";
    if (!infoData.image) {
      infoData.image = absoluteURL("/static/templates/raise-hand.jpg");
    }
    setData(infoData);
  }, [configToCheck]);

  React.useEffect(() => {
    setError(null);
  }, [template, config]);

  // code should be reloaded on every render because of uncontrolled codemirror
  // visuals should be always rendered after first render
  // so load it on the first access, then just show/hide
  const onSelect = (value) => {
    setConfigure(value);
    if (value === "visual") loadVisual(true);
  };

  const onSave = async () => {
    setError(null);
    setWaiting(true);
    const res = await onSaveClick();

    setWaiting(false);
    if (res !== true) {
      setError(res);
    } else {
      setSaved(true);
      setTimeout(() => setSaved(false), 3000);
    }
  };

  const extra = <p className={configClass.elem("tags-link")}></p>;

  const tabItems = [
    {
      key: "config-control",
      label: <IconPhotoFilter color={"rgba(0, 0, 0, 0.4)"} />,
      children: (
        <div
          className={configClass.elem("editor")}
          style={{
            display: configure === "visual" ? undefined : "none",
            height: "100%",
          }}
        >
          <div className={configClass.elem("visual")}>
            {isEmptyConfig(config) && <EmptyConfigPlaceholder />}

            <div className={configClass.elem("object")}>
              <h4>Label value</h4>
            </div>
            {template.controls.map((control) => (
              <ConfigureControl
                control={control}
                template={template}
                key={control.getAttribute("name")}
              />
            ))}
          </div>
        </div>
      ),
    },
    {
      key: "config-settings",
      label: <IconSettingsV2 color={"rgba(0, 0, 0, 0.4)"} />,
      children: (
        <div
          className={configClass.elem("editor")}
          style={{
            display: configure === "visual" ? undefined : "none",
            height: 1008,
          }}
        >
          <div className={configClass.elem("visual")}>
            {isEmptyConfig(config) && <EmptyConfigPlaceholder />}
            <ConfigureSettings template={template} />
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className={configClass}>
      <div className={configClass.elem("container")}>
        <header>
          {/* <button onClick={onBrowse}>Browse Templates</button> */}
          <ButtonT
            className="h-[30px] px-[10px] py-[5px] text-[12px]"
            theme="Primary"
            size="sm"
            onClick={onBrowse}
          >
            Browse Templates
          </ButtonT>
          <ToggleItems
            items={{ code: "Code", visual: "Visual" }}
            active={configure}
            onSelect={onSelect}
          />
        </header>
        {configure === "code" && (
          <div className={configClass.elem("editor")}>
            <div
              className={configClass.elem("code")}
              style={{ display: configure === "code" ? undefined : "none" }}
            >
              <CodeMirror
                name="code"
                id="edit_code"
                value={config}
                autoCloseTags={true}
                smartIndent={true}
                detach
                extensions={["hint", "xml-hint"]}
                options={{
                  mode: "xml",
                  theme: "default",
                  lineNumbers: true,
                  extraKeys: {
                    "'<'": completeAfter,
                    // "'/'": completeIfAfterLt,
                    "' '": completeIfInTag,
                    "'='": completeIfInTag,
                    "Ctrl-Space": "autocomplete",
                  },
                  hintOptions: { schemaInfo: tags },
                }}
                onChange={(editor, data, value) => setTemplate(value)}
              />
            </div>
          </div>
        )}
        {configure === "visual" && visualLoaded && (
          // <div className={configClass.elem("visual")} style={{ display: configure === "visual" ? undefined : "none" }}>
          <Tabs
            className="taureau-tabs"
            style={{ flexDirection: "row", height: "100%" }}
            size="small"
            tabPosition="left"
            type="card"
            items={tabItems}
          />
          // <div className={configClass.elem("visual")} style={{ display: configure === "visual" ? undefined : "none" }}>
          //   {isEmptyConfig(config) && <EmptyConfigPlaceholder />}
          //   {/* <ConfigureColumns columns={columns} project={project} template={template} /> */}
          //   {template.controls.map(control => <ConfigureControl control={control} template={template} key={control.getAttribute("name")} />)}
          //   <ConfigureSettings template={template} />
          // </div>
        )}
        {!disableSaveButton && onSaveClick && (
          <Form.Actions
            size="small"
            extra={configure === "code" && extra}
            valid
          >
            {saved && (
              <span case="success" style={{ color: "#00B74A" }}>
                Saved!
              </span>
            )}
            <Button
              look="primary"
              size="compact"
              style={{ width: 120 }}
              onClick={onSave}
              waiting={waiting}
            >
              Save
            </Button>
          </Form.Actions>
        )}
      </div>
      <Preview config={config} data={data} error={error} loading={loading} />
    </div>
  );
};

export const ConfigPage = ({
  config: initialConfig = "",
  columns: externalColumns,
  project,
  onUpdate,
  onSaveClick,
  onValidate,
  disableSaveButton,
  classConfig,
  setClassConfig,
  show = true,
  style,
}) => {
  const [config, _setConfig] = useState("");
  const [mode, setMode] = useState("list"); // view | list
  const [selectedGroup, setSelectedGroup] = useState(null);
  const [selectedRecipe, setSelectedRecipe] = useState(null);
  const [template, setCurrentTemplate] = useState(null);

  const [configMix, setConfigMix] = useState([]);
  const [genMixTool, setGenMixTool] = useState(false);

  const setConfig = React.useCallback(
    (config) => {
      _setConfig(config);
      onUpdate(config);
    },
    [_setConfig, onUpdate]
  );

  const setTemplate = React.useCallback(
    (config) => {
      const tpl = new Template({ config });

      tpl.onConfigUpdate = setConfig;
      setConfig(config);
      setCurrentTemplate(tpl);

      // Set class config from template
      tpl.controls.map((control) => {
        // console.log('control.tagName', control.tagName)
        classConfig?.class_type.push(control?.tagName);
        setClassConfig?.(classConfig);
      });
    },
    [setConfig, setCurrentTemplate, classConfig]
  );

  const [columns, setColumns] = React.useState();

  React.useEffect(() => {
    if (columns?.length && template) {
      template.fixColumns(columns);
    }
  }, [columns, template]);

  const onSelectRecipe = React.useCallback((recipe) => {
    if (!recipe) {
      setSelectedRecipe(null);
      setMode("list");
      return;
    }

    setTemplate(recipe.config);
    setSelectedRecipe(recipe);
    setMode("view");
  });

  const onCustomTemplate = React.useCallback(() => {
    setTemplate(EMPTY_CONFIG);
    setMode("view");
  }, []);

  const genConfigTool = (tool) => {
    switch (tool) {
      case "Polygon":
        return '  <PolygonLabels name="polygonlabels" toName="image">\n    <Label value="Airplane" background="red"/>\n    <Label value="Car" background="blue"/>\n  </PolygonLabels>';
      case "Circle":
        return '  <EllipseLabels name="circlelabels" toName="image" drawCircle="true">\n    <Label value="Person" />\n    <Label value="Animal" />\n  </EllipseLabels>';
      case "Brush":
        return '  <BrushLabels name="brushlabels" toName="image">\n    <Label value="Airplane" background="rgba(255, 0, 0, 0.7)"/>\n    <Label value="Car" background="#15ecae"/>\n  </BrushLabels>';
      case "Bounding Box":
        return '  <RectangleLabels name="rectanglelabels" toName="image">\n    <Label value="Airplane" background="green"/>\n    <Label value="Car" background="blue"/>\n  </RectangleLabels>';
      case "Scoring":
        return '  <ScoringLabels name="scoringlabels" toName="image" value=\'[{"name":"A","min":0,"max":1,"step":0.1,"background":"#F55050"}]\' />\n\n';
      case "Ellipse":
        return '  <EllipseLabels name="ellipselabels" toName="image">\n    <Label value="Person" />\n    <Label value="Animal" />\n  </EllipseLabels>\n';
      case "Skeleton":
        return '  <SkeletonLabels name="labels" toName="image" position_point=\'{"circle":[[2,10],[2,4],[2,2],[3,4]],"relation":[[0,1],[1,2],[1,3]]}\'>\n    <Label value="Person" />\n    <Label value="Animal" />\n  </SkeletonLabels>\n ';
      case "Keypoint":
        return '  <KeyPointLabels name="kp-1" toName="image">\n    <Label value="Face" background="red" />\n    <Label value="Nose" background="green" />\n  </KeyPointLabels>\n  ';
      case "Taxonomy":
        return '  <Taxonomy name="taxonomy" toName="image">\n    <Choice value="Archaea" />\n    <Choice value="Bacteria" />\n    <Choice value="Eukarya">\n      <Choice value="Human" />\n      <Choice value="Oppossum" />\n      <Choice value="Extraterrestial" />\n    </Choice>\n  </Taxonomy>';
      default:
        break;
    }
  };

  const genConfigMix = () => {
    let mixConfig =
      '<View>\n  <Image name="image" value="$image" zoom="true"/>\n';

    // console.log("configMix", configMix);
    configMix.map((item) => {
      mixConfig = mixConfig + genConfigTool(item) + "\n";
    });

    mixConfig = mixConfig + "\n</View>\n";

    setTemplate(mixConfig);
  };

  React.useEffect(() => {
    if (initialConfig) {
      setTemplate(initialConfig);
      setMode("view");
    }
  }, []);

  React.useEffect(() => {
    if (mode === "list") {
      setConfigMix([]);
    }
  }, [mode]);

  React.useEffect(() => {
    if (genMixTool) {
      const timer = setTimeout(() => {
        genConfigMix();
        setGenMixTool(false);
        setConfigMix([]);
        setMode("view");
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [genMixTool]);

  if (!show) return null;

  return (
    <div
      className={wizardClass}
      data-mode="list"
      id="config-wizard"
      style={{ ...style }}
    >
      <Oneof value={mode}>
        <TemplatesList
          case="list"
          selectedGroup={selectedGroup}
          selectedRecipe={selectedRecipe}
          onSelectGroup={setSelectedGroup}
          onSelectRecipe={onSelectRecipe}
          onCustomTemplate={onCustomTemplate}
          configMix={configMix}
          setConfigMix={setConfigMix}
          genMixTool={genMixTool}
          setGenMixTool={setGenMixTool}
        />
        <Configurator
          case="view"
          columns={columns}
          config={config}
          project={project}
          selectedRecipe={selectedRecipe}
          template={template}
          setTemplate={setTemplate}
          onBrowse={setMode.bind(null, "list")}
          onValidate={onValidate}
          disableSaveButton={disableSaveButton}
          onSaveClick={onSaveClick}
        />
      </Oneof>
    </div>
  );
};
