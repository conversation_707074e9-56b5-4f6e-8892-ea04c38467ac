$scroll_width = 5px

styled_scroll()
  &::-webkit-scrollbar
    width: 6px
    height: 6px // for horizontal scroll
    background-color: $grey_3
  &::-webkit-scrollbar-thumb
    background-color: $grey_6

.ls-panel {
  display: none;
}
.create-project-wizard
  flex: 1
  min-height: 94%
  height: 100%
  display: flex
  flex-direction: column

// hack for settings embedding
.sidebar-menu__content > .create-project-wizard
  height: calc(100% + 64px)
  margin: -32px -40px

.create-project-wizard > .create-project-configure
  min-width: 770px
  flex: 1;
  min-height: 0;
  display: flex;
  align-items: stretch;
  > *
    flex: 50%

.create-project-wizard .create-project-configure__container
  display: flex;
  flex-direction: column;
  // 20px from header's breadcrumbs
  padding: 0px (16px - $scroll_width) 0px 0px;
  overflow-y: initial;
  background-color #fff

  &::-webkit-scrollbar
    width: $scroll_width
  &::-webkit-scrollbar-track
    background: none;

.create-project-templates-list
  min-width: 770px
  display: block !important
  height: calc(100% - 18px)
  grid-template: 1fr auto / 224px auto
  padding: 0px

  &__sidebar
    padding: 16px 8px 16px 32px

    h3:not(:first-child)
      margin-top: 2em
    .create-project-templates-list__custom-template
      margin-top: 20px
      padding-left: 8px
      font-weight: 500
      border: none
      color: #1890FF
      background: none
      width: 100%
      text-align: left
    ul
      padding: 0
      list-style: none
      display: flex
      flex-direction: column
  &__group
    margin: 0 0 4px
    padding: 4px 8px
    border-radius: 4px
    cursor: pointer
    color: #595959
    font-weight: 500
    line-height: 24px
    display: flex
    align-items: center
    &:hover
      background-color: #F4F4F4
    &_active
      color: #000
      background-color: #F0F0F0
    svg // arrow
      margin-left: auto
      padding-left: 16px
      width: 24px
      flex-shrink 0

  main
    position: relative
    flex-grow: 1
    overflow-y: auto
    height: 95%
    ul
      display grid
      flex-wrap wrap
      padding 0
      margin 0px 8px 16px 8px
      grid-template-rows fit-content
      grid-template-columns repeat(auto-fill, 276px)
      grid-auto-columns 276px
      grid-gap 40px

  &__header
    display block
    margin 20px

  &__custom-btn
    display block
  
  &__group-toggle
    display flex
    width 100%
    justify-content right 

  &__template
    display: flex
    flex-direction: column
    border-radius: 4px
    cursor: pointer
    position: relative
    width: 276px
    box-sizing: content-box
    border-radius: 10px
    background: #FFF
    box-shadow: 0px 2px 10px 0px rgba(38, 51, 77, 0.15)
    border: none 
    &_active
    &:hover
      background-color: #F5F6F7;
    &:hover
      opacity: 0.9

    h3
      margin: 0
      color: var(--gray-blue-grey-blue-40, #346)
      font-feature-settings: 'clig' off, 'liga' off
      font-size: 14px
      font-style: normal
      font-weight: 500
      line-height: 30px
      border: none
      text-align: center
    div
      display: flex
      align-items: center
      justify-content: center
      margin-top: 10px
      // padding: 5px
      img
        width: 250px
        height: 200px
        object-fit: cover
        border: none
        // border-radius: 4px 4px 0 0

  &__template-name
    display: flex
    margin-top: 6px
    height: 20px
    justify-content: center
    align-items: center
    flex-shrink: 0
    align-self: stretch

    color: #346
    font-feature-settings: 'clig' off, 'liga' off

    /* Medium/Medium 14 */
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 30px; /* 214.286% */

  &__template-item
    display: flex
    height: fit-content
    padding: 10px
    flex-direction: column
    border-radius: 4px
    cursor: pointer
    position: relative
    width: 180px
    // border: 1px solid $grey_5
    border: none !important
    box-sizing: content-box

    border-radius: 10px
    background: #FFF

    /* Shadows/Gray Blue 30/15%/10b */
    box-shadow: 0px 2px 10px 0px rgba(38, 51, 77, 0.15)
    &_active
    &:hover
      box-shadow: 0px 0px 0 2px #1890ff
    &:hover
      opacity: 0.9

    h3
      margin: 0
      padding: 12px
      font-size: 14px
      font-weight: 500
      border-top: 1px solid $grey_5
    img
      width: 276px
      height: 204px
      object-fit: cover
      border-radius: 6px !important
  footer
  .modal__footer
    grid-column: 1 / span 2
    padding: 16px 40px
    font-size: 14px
    background: rgba(0, 0, 0, 0.03)
    box-shadow: inset 0px 1px 0px rgba(0, 0, 0, 0.05)
    text-align: center
  &__info-icon
    margin-right: 8px
    vertical-align: -5px
    height: 20px

.create-project-wizard a.back { // @todo ?
  align-self: center;
  width: 220px;
  height: 30px;
}
.create-project-wizard a.back::before {
  content: '\2190';
  margin-right: 8px;
}
.create-project-wizard .project__create { // @todo ?
  display: none;
}
.create-project-wizard .project__create p {
  margin: 0;
}
.create-project-wizard h1 {
  font-size: 1.4rem;
  width: 100%;
  display: flex;
}
.create-project-wizard h1 a[name=config] {
  margin-left: auto;
  font-size: 1rem;
  font-weight: 400;
  background: #ccc;
}
.create-project-wizard h1 a[name=config]:hover {
  background: #2593fc;
}
.create-project-wizard h1 a[name=config]:hover::before {
  content: "Advanced! ";
}
.create-project-wizard a.button,
.create-project-wizard button,
.create-project-wizard input[type=button] {
  line-height: 1;
}


.create-project-configure__container > header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 12px;
}

:global(.taureau-tabs)
  :global(.ant-tabs-nav)
    min-width 30px !important
    width 30px !important
  :global(.ant-tabs-tab)
    border: none !important
    padding: 3px !important
    border-radius: 3px 0px 0px 3px !important
    box-shadow: 0px 2px 5px 0px rgba(13,17,26,0.15);

  :global(.ant-tabs-nav-wrap)
    left: 1px
    margin-top: 5px
    // :global(.ant-tabs-nav-list)
    //   margin-top: 5px
    :global(.ant-tabs-tab-active)
      svg > path
        fill #334466
    :global(.ant-tabs-tab)
      margin-top 5px !important
      background: var(--black-black-20, rgba(0, 0, 0, 0.20)) !important
    :global(.ant-tabs-tab):not(:first-child)
      margin-top 5px !important
    :global(.ant-tabs-tab-active)
      background: #fff !important
  :global(.ant-tabs-tabpane)
    padding-left: 0 !important
  :global(.ant-tabs-content-holder)
    border-left: none !important

.create-project-configure__editor
  // box-shadow: 0px 2px 10px 0px rgba(13, 17, 26, 0.15)
  // border-radius: 10px
  display flex
  flex 1
  overflow-y: auto
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(13, 17, 26, 0.15);
  padding: 8px
  styled_scroll()

.create-project-configure__tags-link
  margin: 0
  // to keep this block at 36px height as the Save button
  // to prevent blinking on mode switching
  line-height: 21px
  margin-top: -6px

.create-project-configure__container > header .toggle-items-new {
  margin-left: auto;
}

.create-project-wizard .create-project-configure__code
  overflow-x: hidden
  textarea
  :global(.react-codemirror2)
    flex: 1
    font-family: monospace

    :global(.CodeMirror)
      height: 100%
      border: none
      border-radius: 10px

      :global(.CodeMirror-sizer)
        :global(.CodeMirror-lines)
          :global(.CodeMirror-code)
            :global(.CodeMirror-gutter-wrapper)
              :global(.CodeMirror-linenumber)
                color: #ADB8CC;
                text-align: center;
                font-size: 12px;
                font-style: normal;
                font-weight: 900;
                line-height: 20px;

      :global(.CodeMirror-scroll)
        :global(.CodeMirror-gutters)
          background #F5F6F7
          border-right none


.create-project-wizard .create-project-configure__code
.create-project-wizard .create-project-configure__visual
  flex: 1;
  display: flex;
  flex-direction: column;
.create-project-wizard .create-project-configure__visual
  padding 0 12px
.create-project-wizard .create-project-configure__visual
  > *
    // border-top: 1px solid $black_10
    border-top: none
    padding-bottom: 12px
.create-project-wizard .create-project-configure__object
  h4
    margin-bottom: 8px
  > p
    line-height: 32px
  > p.create-project-configure__object-error
    line-height: 22px
    margin-top: 8px
    color: #771b04
  input
    margin-left: 8px
    padding: 4px 8px
    line-height: 1.4em
  select
    padding: 4px 8px
    font: inherit
    border-color: #dededf
.create-project-wizard .create-project-configure__enable_manual_columns
  border-bottom: 1px dashed #888
  width: fit-content
  cursor: pointer
  &:hover
    opacity: 0.8

.create-project-wizard .create-project-configure__labels
  display: flex
  height: fit-content
  max-height: fit-content
  flex-direction: column
.create-project-wizard .create-project-configure__add-labels
  display: flex
  flex-direction: column
  align-items: flex-start
  flex: 1
  margin-bottom: 12px
  p
    margin-bottom: 12px
    color: #346;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 21px; 

  textarea
    flex-grow: 1
    margin: 8px 0
    font: inherit

    width: 100%
    padding: 8px 12px;
    border-radius: 5px;
    border: 2px solid var(--Gray-Blue-Grey-Blue-97, #F5F6F7);
    background: var(--White-White, #FFF);
    box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.03);
    height: 80px 
    resize: none 

  textarea::placeholder
    color: var(--Gray-Blue-Grey-Blue-85, #C3CAD9);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px; 

  textarea:focus
    outline none

  input
    border: none
    margin-left: auto
    margin-right: 10px
    color: var(--black-black-30, rgba(0, 0, 0, 0.30));
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
.create-project-wizard .create-project-configure__visual h4
  margin: 0
  color: #346;
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 21px; /* 150% */

.create-project-wizard .create-project-configure__visual h3
  margin: 0
  color: #346;
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 18px; 

/*** Configure Labels ***/
.create-project-wizard .create-project-configure__current-labels
  h3
    color: #346;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 150% */
.create-project-wizard .create-project-configure__labels ul
  padding: 0 4em 0 0
  margin: 0
  list-style: none
  display: flex
  flex-direction: column
  align-items: stretch
  flex: 1
  max-height: 150px
  styled_scroll()
  .create-project-configure__label
    display: flex
    align-items: stretch
    position: relative
    &:not(:first-child)
      margin-top: 8px
    &_choice
      label
        display: none
      span
        background: rgba(0, 0, 0, 0.05)
    label
      width: 0px
      cursor: default
      &::before
        content: ""
        position: absolute
        width: 100%
        height: 100%
        background: inherit
        opacity: 0.2
        left: 0
        right: 0
        // pointer-events: none // disable if you want label to react, like to edit text
        border-radius: 0 4px 4px 0
      &::after
        position: absolute
        left: -4px
        width: 4px
        content: ""
        height: 100%
        background: inherit
        border-radius: 3px 0 0 3px
        transition: 0.1s all
        will-change: width, left
    .create-project-configure__label-color
      width: 1px
      padding: 0
      border: 0
      position: absolute
      opacity: 0
    span
      padding: 0 8px
      overflow-x: hidden
      word-break: break-word
    .create-project-configure__delete-label
      min-width: 0
      border: 0
      border-radius: 0 4px 4px 0
      background: none
      cursor: pointer
      padding: 2px 6px
      font-size: 12px
      position: absolute
      top: 0
      left: calc(100% + 25px)
      height: 100%
      opacity: 0.3
      &:hover
        background: rgba(255,0,0,0.133)
        opacity: 1
    .create-project-configure__color-label
      min-width: 0
      border: 0
      border-radius: 0 4px 4px 0
      background: none
      cursor: pointer
      padding: 2px 6px
      font-size: 12px
      position: absolute
      top: 0
      left: 100%
      height: 100%
      opacity: 0.3
      &:hover
        background: rgba(51, 191, 255, 0.133)
        opacity: 1
    &:hover
      background: rgba(0,0,0,0.05);
      label::before
        border-radius: 0
      label::after
        width: 12px
        left: -12px

.create-project-wizard ul.create-project-configure__settings
  padding: 0
  h4
    margin-bottom: 15px
    color: var(--gray-blue-grey-blue-50, #4D5E80);
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px; /* 125% */
  ul
    padding: 0
  li
    list-style: none
    margin: 12px 0
  label
    cursor: pointer
  // input[type=checkbox]
  //   margin-right: 8px
  input[type=text]
  select
    font: inherit
    line-height: 1.2em
    padding: 4px 8px
    border-radius: 4px
    border: 1px solid $black_10

.create-project-wizard ul.create-project-configure__object-settings
  label
    display: flex;
    gap: 5px;
    align-items: center;
    color: var(--gray-blue-grey-blue-40, #346);
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px; /* 142.857% */
    padding-left: 20px;

  input[type=checkbox]
    transform: scale(1.2)
    border-radius: 5px;
    border: 2px solid var(--gray-blue-grey-blue-96, #F2F3F5);
    background: var(--white-white, #FFF);
    box-shadow: 0px 2px 10px 0px rgba(38, 51, 77, 0.03);

.create-project-wizard .create-project-configure__preview
  display: flex
  flex-direction: column
  gap: 10px
  flex-grow: 10
  min-width: 500px
  padding: 10px 0
  overflow-y: auto
  // border-left: 1px solid $black_10
  // background-color: $grey_2
  background-color: #fff
  box-shadow: 0px 2px 10px 0px rgba(13, 17, 26, 0.15)
  border-radius: 10px
  styled_scroll()
  h3
    // margin: 8px 0 16px
    // font-size: 16px
    margin: 0 10px
    color: #000;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 30px; 
  iframe
    display: block
    width: 100%
    height: 100%
  &-error
    color: #771b04
    border: 1px solid #e68a6e
    background-color: #ffc1ae
    padding: 10px 15px
    border-radius: 3px
    margin-bottom: 16px
    white-space: pre-line
    h2
      font-size: 16px
  .editor > .common
    flex: 1
    max-width: 100%

  :global(.lsf-main-view)
    background #F5F6F7;
    background-image none

// super hack for LSF regions width on sideBottom layout
div[class^=App_menu]>div
  width: 100%


:global(.taureau-color-picker)
  padding 0 !important
  :global(.ant-tooltip-arrow)
    display: none

  :global(.ant-tooltip-inner)
    border-radius 12px !important
    width: fit-content
    :global(.chrome-picker)
      // padding 10px
      box-shadow: none !important
    
    :global(.chrome-picker) > div:first-child
      border-radius 12px !important

    :global(.chrome-picker) > div:last-child
      padding 10px 0 5px 0 !important

      :global(.flexbox-fix) > div:last-child > div:first-child
        :global(.hue-horizontal)
          border-radius: 6px
        
      :global(.flexbox-fix) > div:last-child > div:last-child 
        div > div:nth-child(2)
          border-radius: 6px

      :global(.flexbox-fix)
        label, input
          color: var(--gray-blue-grey-blue-60, #6B7A99) !important;
          font-size: 10px;
          font-style: normal;
          font-weight: 500;

        input
          border-radius: 4px !important
          border: 1px solid var(--gray-blue-grey-blue-95, #EDEFF2);

        input:focus
          outline: none;

        svg 
          fill: #C3CAD9 !important

:global(.scrollbar)
  &::-webkit-scrollbar
    width: 7.5px;
    height: 7.5px;

  &::-webkit-scrollbar-track 
    box-shadow: 0;

  &::-webkit-scrollbar-thumb 
    background: #62708c;
    border-radius: 10px;