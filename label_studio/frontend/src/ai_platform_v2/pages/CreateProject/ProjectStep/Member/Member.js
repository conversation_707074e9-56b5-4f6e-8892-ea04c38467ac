import { Block, Elem } from "../../../../utils/bem";
import { Button, Input } from "@taureau/ui";
import "./Member.styl";
import { UserAssigner } from "../../../../component/UserAssigner/UserAssigner";
import { useCurrentUser } from "../../../../../providers/CurrentUser";
import { ADMIN_ID, PO_ID } from "../../../../../config/Config";
import { useAPI } from "../../../../../providers/ApiProvider";
import { useCallback } from "react";

export const Member = ({
  onSetRoleList,
  projectName,
  members,
  onChange,
  style,
}) => {
  const api = useAPI();
  const { user } = useCurrentUser();
  const pageSize = 999999;

  const membersInput = members;

  const fetchUsers = useCallback(async () => {
    const rolesList = (await api.callApi("defaultRolesProject"))?.data?.roles;
    const firstRole = await rolesList?.find(
      (role) => role?.name === "Project Owner"
    );
    const midRole = await rolesList?.filter(
      (role) => role?.name !== "Project Owner" && role?.name !== "Member"
    );
    const lastRole = await rolesList?.find((role) => role?.name === "Member");
    const finalRole = [firstRole, ...midRole, lastRole];

    await onSetRoleList(finalRole);

    const users = await api.callApi("membersModule", {
      params: {
        // pk: project.moduleId,
        page: 1,
        pageSize,
      },
    });

    const usersList = users?.items;

    let members = membersInput;

    if (user?.role?.[0] !== "Admin" && user?.role?.[0] !== "Super Admin") {
      if (members?.length < 1 || !members?.length) {
        const poRole = rolesList.find((role) => role.id === PO_ID);

        members = [
          ...members,
          {
            id: user?.uuid,
            avatar: user?.avatar,
            firstName: user.firstName,
            lastName: user.lastName,
            fullName: user.fullName,
            email: user.email,
            userName: user.userName,
            status: user.status,
            role: poRole.id,
            roleId: poRole.id,
            roleName: poRole.name,
            permissions: poRole.permissions,
          },
        ];
      }
    }

    return {
      rolesList: finalRole,
      usersList,
      totalUsersList: users.total,
      selectedUsersList: members,
      totalSelectedUsersList: 0,
    };
  }, [api, user, membersInput, onSetRoleList]);

  const fetchUsersScroll = async (page, keyword) => {
    const params = { page, pageSize };

    if (keyword) params.keyword = keyword;

    return await api.callApi("membersModule", { params });
  };

  const fetchUsersSearch = async (keyword) => {
    const params = { page: 1, pageSize };

    if (keyword) params.keyword = keyword;

    return await api.callApi("membersModule", { params });
  };

  return (
    <UserAssigner
      useRolePermissions={true}
      usersListTitle="Member"
      usersListSubtitle="Members"
      selectedUsersListTitle={projectName}
      selectedUsersListSubtitle="Members"
      usersLoader={fetchUsers}
      usersAssignerCreate={onChange}
      fetchUsersScroll={fetchUsersScroll}
      fetchUsersSearch={fetchUsersSearch}
      isSearchFetchUsers={false}
      pageSize={pageSize}
      enableScopeUser={false}
      hideSaveAction={true}
      style={style}
    />
  );
};
