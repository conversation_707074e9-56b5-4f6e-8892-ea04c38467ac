import { Select } from "@/ai_platform_v2/component/Select/Select";
import { Input } from "@taureau/ui";
import { useCallback, useEffect, useState } from "react";
import { Block, Elem } from "../../../../utils/bem";
import { SelectColor } from "../../component/SelectColor/SelectColor";
import { colors } from "../../utils/colors";
import "./Information.styl";

export const Information = ({
  currentModule,
  moduleId,
  modulesList,
  projectName,
  projectDescription,
  projectColor,
  onChange,
  onNextStep,
  style,
}) => {
  const [modulesListItems, setModulesListItems] = useState(modulesList);
  const [moduleIdSelect, setModuleIdSelect] = useState(moduleId);
  const [name, setName] = useState(projectName);
  const [description, setDescription] = useState(projectDescription);
  const [color, setColor] = useState(projectColor);

  const handleNextStep = useCallback(() => {
    onNextStep({ name, description, color });
  }, [name, description, color, onNextStep]);

  useEffect(() => {
    onChange?.({ moduleId: moduleIdSelect, name, description, color });
  }, [moduleIdSelect, name, description, color]);

  useEffect(() => {
    setModulesListItems(modulesList);
    setModuleIdSelect(moduleId);
    setName(projectName);
    setDescription(projectDescription);
    setColor(projectColor);
  }, [modulesList, moduleId, projectName, projectDescription, projectColor]);

  return (
    <Block name="information-step" style={style}>
      <Elem
        name="gradient"
        style={{
          "--gradient-background": colors.find(
            (colorItem) => colorItem.label === color
          ).value,
        }}
      >
        <SelectColor
          colors={colors}
          value={color}
          onChange={(value) => setColor(value)}
        />
      </Elem>

      <Elem name="information">
        <Elem name="information-input">
          <Elem name="project-name">
            <Elem name="information-label">Project name</Elem>
            <Input
              className="input-project-name"
              size="md"
              placeholder="Enter name here..."
              maxLength={50}
              value={name}
              onChange={(e) => setName(e.target.value)}
            />
          </Elem>

          <Elem name="project-description">
            <Elem name="information-label">Description</Elem>
            <textarea
              style={{ height: 170, padding: "9px 16px" }}
              maxLength={255}
              rows={5}
              className="min-h-[50px] max-h-[170px] w-full shadow-Shadows/Gray-Blue-10/15%/5b border-[0.5px] border-solid border-[#F5F6F7] rounded-[5px] focus:border-blue-100"
              placeholder="Enter description here..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </Elem>

          {!currentModule && (
            <Elem name="select-module">
              <Elem name="information-label">Module</Elem>
              <Select
                placeholder="Choose module"
                value={moduleIdSelect}
                items={modulesListItems}
                onSelect={(value) => setModuleIdSelect(value)}
              />
            </Elem>
          )}
        </Elem>
        {/* <Elem name="continue-button">
          <Button
            className={`w-[120px] ${
              !(name && moduleIdSelect) ? "button-disabled" : ""
            }`}
            size="sm"
            corner="Rounded"
            theme="Primary"
            onClick={handleNextStep}
            disabled={!(name && moduleIdSelect)}
          >
            Continue
          </Button>
        </Elem> */}
      </Elem>
    </Block>
  );
};
