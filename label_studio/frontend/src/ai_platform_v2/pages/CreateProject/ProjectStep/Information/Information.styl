.information-step
    display flex
    width 100%
    justify-content space-between

    &__gradient
        display: flex
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;

        background: var(--gradient-background)
        // background-size: 300% 300%;
        
        padding-bottom: 10px

        min-width 476px 
        width: 40%
        min-height 302px
        height 100%
        border-radius 10px

        transition: background .6s ease-out;
        // animation: gradient 3s ease infinite;

        .taureau-select-color
            padding 0px 50px

    &__information
        height: 100%
        display flex
        padding-left 40px
        flex-direction column
        align-items flex-end
        justify-content center
        gap 30px
        flex 1 0 0
        min-height 302px

    &__information-input
        overflow: auto
        display flex
        flex-direction column
        align-items flex-start
        gap 24px
        align-self stretch

        // input
        //     display: flex;
        //     width: 100%;
        //     padding: 6px 21px 6px 15px;
        //     align-items: flex-start;
        //     gap: 10px;
        //     align-self: stretch;
        //     border-radius: 5px;
        //     border: 2px solid #F5F6F7
        //     background: #FFF;
        //     box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.15);
        
        input, textarea
            color: var(--gray-blue-grey-blue-40, #346);
            
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 30px;

        input::placeholder, textarea::placeholder
            color: var(--gray-blue-grey-blue-85, #C3CAD9);
            
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 30px;

        // textarea
        //     outline-width: none

        textarea:focus
            outline-width: 0px

        &::-webkit-scrollbar {
            width: 7.5px;
            height: 7.5px;
        }

        &::-webkit-scrollbar-track {
            box-shadow: 0;
            /* border-radius: 10px; */
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgb(98 112 140)
            border-radius: 10px;
        }

    &__information-label
        color: var(--gray-blue-grey-blue-40, #346);
        
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 21px;


    &__project-name
        width 100%

        :global(.input-project-name)
            // display: flex;
            // width: 100%;
            // padding: 6px 21px 6px 15px;
            // align-items: flex-start;
            // gap: 10px;
            // align-self: stretch;
            // border-radius: 5px;
            // border: 2px solid #F5F6F7
            // background: #FFF;
            box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.15);

            input
                width: 100%;

    &__project-description
        width 100%


    &__select-module
        width 100%

        .taureau-select
            height: 55px;
            padding: 10px 15px 10px 20px;

            &__placeholder, &__content
                font-size: 14px;
                line-height: 20px;

            &__placeholder
                font-weight: 400;

            &__content
                font-weight: 500;

            &__icon > svg
                width 30px
                height 30px


    //     :global(.item-list-tooltip-filter)
    //         min-width: 300px !important

    //     :global(.ls-item-list-filter__body)
    //         max-height: 180px


    &__project-name, &__project-description, &__select-module
        display: flex
        flex-direction: column
        gap: 12px


@keyframes gradient {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}
