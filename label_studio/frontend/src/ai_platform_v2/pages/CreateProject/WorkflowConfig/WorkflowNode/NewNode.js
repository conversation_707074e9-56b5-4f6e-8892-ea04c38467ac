import React, { Fragment, useContext, useEffect } from "react";
import { Handle, Position, useNodes, useUpdateNodeInternals } from "reactflow";
import { Space, Typography } from "antd";
import { UserContext } from "../WorkflowSpace";
import cn from "classnames";
import IconNewFile from "@/ai_platform_v2/assets/Icons/IconNewFile";
import IconNewCircle from "@/ai_platform_v2/assets/Icons/NodeIcons/new-circle.svg?url";
import { isEmpty } from "lodash";
const { Text } = Typography;
const color = "#2EE6CA";

export function NewNode({ data, selected }) {
  const { sum, sumCompleted, sumProcess } = useContext(UserContext);

  const updateNodeInternals = useUpdateNodeInternals();

  const nodes = useNodes();

  const isHasPreProcessingCompletedNode = !isEmpty(
    nodes.find((n) => n.type === "PreProcessingCompleted")
  );

  useEffect(() => {
    updateNodeInternals(data?.id);
  }, [isHasPreProcessingCompletedNode]);

  return (
    <div className={`dataset-node ${selected ? "active" : ""}`}>
      {/* {isHasPreProcessingCompletedNode && ( */}
      <Handle
        type="target"
        position={Position.Left}
        id="new-handle-target"
        className={"handle-node"}
        style={{
          top: 23,
          backgroundImage: `url(${IconNewCircle})`,
          visibility: isHasPreProcessingCompletedNode ? "visible" : "hidden",
        }}
      />
      {/* )} */}
      <Space direction={"vertical"}>
        <div
          className={cn("flow-node", {
            "flow-node__no-bottom": false,
          })}
        >
          <Space size={"small"}>
            <IconNewFile active />
            <Text style={{ color }}>{data.name}</Text>
          </Space>
          {/* <div className="amount">
            {data?.amount || 0}
          </div> */}
        </div>
        <Fragment>
          <div className=" font-medium text-gray-blue-40 text-[16px]">
            Dataset
          </div>
          <div className="container-dataset-info">
            <div className="info-item">
              <div className=" font-medium text-gray-blue-40 text-[15px]">
                {sum}
              </div>
              <div className=" font-normal text-gray-blue-40 text-[12px]">
                Files
              </div>
            </div>
            <div className="info-item">
              <div className=" font-medium text-gray-blue-40 text-[15px]">
                {sumCompleted}
              </div>
              <div className=" font-normal text-gray-blue-40 text-[12px]">
                Completed
              </div>
            </div>
            <div className="info-item">
              <div className=" font-medium text-gray-blue-40 text-[15px]">
                {sumProcess}
              </div>
              <div className=" font-normal text-gray-blue-40 text-[12px]">
                In process
              </div>
            </div>
          </div>
        </Fragment>
        {/* <Progress percent={30} /> */}
      </Space>
      <Handle
        type="source"
        position={Position.Right}
        id="new"
        className={"handle-node"}
        style={{
          top: 30,
          backgroundImage: `url(${IconNewCircle})`,
        }}
      />
    </div>
  );
}
