import React, { useContext, useEffect, useMemo } from "react";
import { <PERSON><PERSON>, Position, useNodes, useUpdateNodeInternals } from "reactflow";
import { Avatar, Space, Tooltip, Typography } from "antd";
import CardUser from "../../CardUser";
import { UserContext } from "../../WorkflowSpace";
import Anyone from "../../Icon/Anyone";
import { isEmpty } from "lodash";

import { PreProcessingCompletedIcon } from "@/ai_platform_v2/assets/Icons/WorkflowIcons";

import IconCompleteCircle from "@/ai_platform_v2/assets/Icons/NodeIcons/pre-processing-completed-circle.svg?url";
import classNames from "classnames";

const { Text } = Typography;
const color = "#15803D";

export function PreProcessingCompletedNode({ data, selected }) {
  const { memberPreProcessing } = useContext(UserContext);
  const [users, setUsers] = React.useState([]);

  const nodes = useNodes();
  const updateNodeInternals = useUpdateNodeInternals();

  const isHasNewNode = !isEmpty(nodes.find((n) => n.type === "New"));

  const settings = useMemo(() => {
    return data?.settings;
  }, [data]);

  useEffect(() => {
    updateNodeInternals(data?.id);
  }, [isHasNewNode]);

  React.useEffect(() => {
    setUsers(
      memberPreProcessing.filter((user) => {
        return data?.members?.find((mem) => mem === user.userId);
      })
    );
  }, [data?.members, memberPreProcessing]);
  return (
    <Space
      className={`workflow-node ${
        selected ? "pre-processing-completed-active" : ""
      }`}
      direction={"vertical"}
    >
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id="preProcessingCompleted-handle-target"
          className={"handle-node"}
          style={{
            backgroundImage: `url(${IconCompleteCircle})`,
          }}
        />
        <div
          className={classNames("flow-node", {
            "!border-0": !settings?.isManualConfirmation,
          })}
        >
          <Space size={"small"}>
            <PreProcessingCompletedIcon />
            <Text style={{ color }}>{data?.name}</Text>
          </Space>
        </div>
        {/* {isHasNewNode && ( */}
        <Handle
          type="source"
          position={Position.Right}
          id="preProcessingCompleted"
          className={"handle-node"}
          style={{
            backgroundImage: `url(${IconCompleteCircle})`,
            visibility: isHasNewNode ? "visible" : "hidden",
          }}
        />
        {/* )} */}
      </div>
      {settings?.isManualConfirmation && (
        <Space direction={"vertical"} className="list-user" size={"small"}>
          {!users || users.length === 0 ? (
            <CardUser
              icon={
                <div className="icon-anyone">
                  <Anyone />
                </div>
              }
              user={{
                id: "all",
                lastName: "Anyone",
                firstName: "",
              }}
              selected={[]}
              // onSelect={(id) => {}}
            />
          ) : (
            <Avatar.Group
              maxCount={8}
              maxStyle={{ color: "#f56a00", backgroundColor: "#fde3cf" }}
            >
              {users.map((user, i) => (
                <Tooltip
                  key={user.userId}
                  title={user?.lastName + " " + user?.firstName}
                  placement="top"
                >
                  {user?.avatar &&
                  user?.avatar_url !== "https://minio.taureau.ai/dms/None" ? (
                    <Avatar src={<img src={user?.avatar_url} alt="avatar" />} />
                  ) : (
                    <Avatar>{user.firstName[0]}</Avatar>
                  )}
                </Tooltip>
              ))}
            </Avatar.Group>
          )}
        </Space>
      )}
    </Space>
  );
}
