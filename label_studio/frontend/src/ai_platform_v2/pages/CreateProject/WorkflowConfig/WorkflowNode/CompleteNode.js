import React, { useContext } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Avatar, Space, Tooltip, Typography } from "antd";
import CardUser from "../CardUser";
import { UserContext } from "../WorkflowSpace";
import IconCompletedFile from "@/ai_platform_v2/assets/Icons/IconCompletedFile";
import IconCompleteCircle from "@/ai_platform_v2/assets/Icons/NodeIcons/complete-circle.svg?url";
import Anyone from "../Icon/Anyone";

const { Text } = Typography;
const color = "#29CC39";

export function CompleteNode({ data, isConnectable, selected }) {
  const { memberAnnotator } = useContext(UserContext);
  const [users, setUsers] = React.useState([]);

  React.useEffect(() => {
    setUsers(
      memberAnnotator.filter((user) => {
        return data?.members?.find((mem) => mem === user.userId);
      })
    );
  }, [data?.members, memberAnnotator]);
  return (
    <Space
      className={`workflow-node ${selected ? "complete-active" : ""}`}
      direction={"vertical"}
    >
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id="complete-handle-target"
          className={"handle-node"}
          style={{
            top: 30,
            backgroundImage: `url(${IconCompleteCircle})`,
          }}
        />
        <div className="flow-node">
          <Space size={"small"}>
            <IconCompletedFile active />
            <Text style={{ color }}>{data?.name}</Text>
          </Space>
        </div>
      </div>
      <Space direction={"vertical"} className="list-user" size={"small"}>
        {!users || users.length === 0 ? (
          <CardUser
            icon={
              <div className="icon-anyone">
                <Anyone />
              </div>
            }
            user={{
              id: "all",
              lastName: "Anyone",
              firstName: "",
            }}
            selected={[]}
            // onSelect={(id) => {}}
          />
        ) : (
          <Avatar.Group
            maxCount={8}
            maxStyle={{ color: "#f56a00", backgroundColor: "#fde3cf" }}
          >
            {users.map((user, i) => (
              <Tooltip
                key={user.userId}
                title={user?.lastName + " " + user?.firstName}
                placement="top"
              >
                {user?.avatar &&
                user?.avatar_url !== "https://minio.taureau.ai/dms/None" ? (
                  <Avatar src={<img src={user?.avatar_url} alt="avatar" />} />
                ) : (
                  <Avatar>{user.firstName[0]}</Avatar>
                )}
                {/* <Avatar style={{ backgroundColor: '#87d068' }} icon={<UserOutlined />} /> */}
              </Tooltip>
            ))}
          </Avatar.Group>
        )}
      </Space>
    </Space>
  );
}
