import React, { useContext } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Avatar, Space, Tooltip, Typography } from "antd";
import CardUser from "../CardUser";
import { UserContext } from "../WorkflowSpace";
import IconLabelingFile from "@/ai_platform_v2/assets/Icons/IconLabelingFile";
import LabelingCircle from "@/ai_platform_v2/assets/Icons/NodeIcons/labeling-circle.svg?url";
import Anyone from "../Icon/Anyone";

const { Text } = Typography;
const color = "#3361FF";

export function LabelingNode({ data, isConnectable, selected }) {
  const { memberAnnotator } = useContext(UserContext);
  const [users, setUsers] = React.useState([]);

  // let user = [];

  // listUser.map((u) =>{
  //   data.members.map((mem) => {
  //     if (mem === u.id){
  //       user.push(u);
  //     }
  //   });
  // });
  React.useEffect(() => {
    setUsers(
      memberAnnotator.filter((user) => {
        return data?.members.find((mem) => mem === user.userId);
      })
    );
  }, [data?.members, memberAnnotator]);

  // console.log(data?.members);
  return (
    <Space
      className={`workflow-node ${selected ? "labeling-active" : ""}`}
      direction={"vertical"}
    >
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id="labeling-handle-target"
          className={"handle-node"}
          style={{
            top: 30,
            backgroundImage: `url(${LabelingCircle})`,
          }}
        />
        <div className="flow-node">
          <Space size={"small"}>
            <IconLabelingFile active />
            <Text style={{ color }}>{data?.name}</Text>
          </Space>
        </div>
        <Handle
          type="source"
          position={Position.Right}
          id="labeling"
          className={"handle-node"}
          style={{
            top: 30,
            backgroundImage: `url(${LabelingCircle})`,
          }}
        />
      </div>
      <Space direction={"vertical"} className="list-user" size={"small"}>
        {!users || users.length === 0 ? (
          <CardUser
            icon={
              <div className="icon-anyone">
                <Anyone />
              </div>
            }
            user={{
              id: "all",
              lastName: "Anyone",
              firstName: "",
            }}
            selected={[]}
          />
        ) : (
          <Avatar.Group
            maxCount={8}
            maxStyle={{ color: "#f56a00", backgroundColor: "#fde3cf" }}
          >
            {users.map((user, i) => (
              <Tooltip
                key={user.userId}
                title={user?.lastName + " " + user?.firstName}
                placement="top"
              >
                {user?.avatar &&
                user?.avatar_url !== "https://minio.taureau.ai/dms/None" ? (
                  <Avatar src={<img src={user?.avatar_url} alt="avatar" />} />
                ) : (
                  <Avatar>{user.firstName[0]}</Avatar>
                )}
                {/* <Avatar style={{ backgroundColor: '#87d068' }} icon={<UserOutlined />} /> */}
              </Tooltip>
            ))}
          </Avatar.Group>
        )}
      </Space>
    </Space>
  );
}
