import React, { useContext } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Avatar, Space, Tooltip, Typography } from "antd";
import CardUser from "../CardUser";
import { CheckOutlined, CloseOutlined } from "@ant-design/icons";
import { UserContext } from "../WorkflowSpace";
import IconReviewFile from "@/ai_platform_v2/assets/Icons/IconReviewFile";
import IconReviewCircle from "@/ai_platform_v2/assets/Icons/NodeIcons/reviewing-circle.svg?url";
import Anyone from "../Icon/Anyone";

const { Text } = Typography;
const color = "#FFCB33";

export function ReviewNode({ data, isConnectable, selected }) {
  const { memberReviewer } = useContext(UserContext);
  const [users, setUsers] = React.useState([]);

  React.useEffect(() => {
    setUsers(
      memberReviewer.filter((user) => {
        return !!data?.members.find((mem) => mem === user.userId);
      })
    );
  }, [data.members, memberReviewer]);

  return (
    <Space
      className={`workflow-node ${selected ? "review-active" : ""}`}
      direction={"vertical"}
    >
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id="review-handle-target"
          className={"handle-node"}
          style={{
            top: 30,
            backgroundImage: `url(${IconReviewCircle})`,
          }}
        />
        <div className="flow-node">
          <Space size={"small"}>
            <IconReviewFile active />
            <Text style={{ color }}>{data.name}</Text>
          </Space>
          {/* <div className="amount">
            {data?.amount || 0}
          </div> */}
        </div>
        <div className="flow-node">
          <Space size={"small"}>
            <CheckOutlined style={{ color: "rgb(59, 186, 59)" }} />
            <div className=" font-normal text-green-100 text-[15px]">
              Accept
            </div>
          </Space>
          <Handle
            type="source"
            position="right"
            id="accept"
            className={"handle-node"}
            style={{
              top: 75,
              backgroundImage: `url(${IconReviewCircle})`,
            }}
          />
        </div>
        <div className="flow-node">
          <Space size={"small"}>
            <CloseOutlined style={{ color: "rgb(220, 24, 24)" }} />
            <div className=" font-normal text-red-100 text-[15px]">Reject</div>
          </Space>
          <Handle
            type="source"
            position="right"
            id="reject"
            className={"handle-node"}
            style={{
              top: 115,
              backgroundImage: `url(${IconReviewCircle})`,
            }}
          />
        </div>
      </div>
      <Space direction={"vertical"} className="list-user" size={"small"}>
        {!users || users.length === 0 ? (
          <CardUser
            icon={
              <div className="icon-anyone">
                <Anyone />
              </div>
            }
            user={{
              id: "all",
              lastName: "Anyone",
              firstName: "",
            }}
            selected={[]}
            // onSelect={(id) => {}}
          />
        ) : (
          <Avatar.Group
            maxCount={8}
            maxStyle={{ color: "#f56a00", backgroundColor: "#fde3cf" }}
          >
            {users.map((user, i) => (
              <Tooltip
                key={user.userId}
                title={user?.lastName + " " + user?.firstName}
                placement="top"
              >
                {user?.avatar &&
                user?.avatar_url !== "https://minio.taureau.ai/dms/None" ? (
                  <Avatar src={<img src={user?.avatar_url} alt="avatar" />} />
                ) : (
                  <Avatar>{user.firstName[0]}</Avatar>
                )}
                {/* <Avatar style={{ backgroundColor: '#87d068' }} icon={<UserOutlined />} /> */}
              </Tooltip>
            ))}
          </Avatar.Group>
        )}
      </Space>
    </Space>
  );
}
