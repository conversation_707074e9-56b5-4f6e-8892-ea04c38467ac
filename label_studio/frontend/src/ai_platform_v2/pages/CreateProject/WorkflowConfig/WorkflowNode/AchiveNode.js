import { Handle, Position } from "reactflow";
import { Space, Typography } from "antd";
import { circleImgHandle } from "../../../../../utils/colors";
import { DeleteOutlined } from "@ant-design/icons";

const handleStyle = { left: 10 };
const { Text } = Typography;
const color = "rgb(220, 24, 24)";

export function ArchiveNode({ data, isConnectable, selected }) {
  return (
    <div className={`workflow-node ${selected ? "archive-active" : ""}`}>
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id="archive-handle-target"
          className={"handle-node"}
          style={{
            backgroundImage: `url(${circleImgHandle["rgb(220, 24, 24)"]})`,
          }}
        />
        <div className="flow-node-archive">
          <Space size={"small"}>
            <DeleteOutlined style={{ color }} />
            <Text style={{ color }}>{data.name}</Text>
          </Space>
        </div>
      </div>
    </div>
  );
}
