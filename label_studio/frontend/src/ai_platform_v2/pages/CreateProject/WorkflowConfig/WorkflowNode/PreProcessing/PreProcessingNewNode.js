import React, { useContext, useMemo } from "react";
import { <PERSON>le, Position } from "reactflow";
import { Space, Typography } from "antd";

import { PreProcessingNewIcon } from "@/ai_platform_v2/assets/Icons/WorkflowIcons";

import IconNewCircle from "@/ai_platform_v2/assets/Icons/NodeIcons/pre-processing-new-circle.svg?url";
import Icon3DCubeScan from "../../../../SettingsProject/component/ModelConfiguration/icons/Icon3DCubeScan";
import classNames from "classnames";

const { Text } = Typography;
const color = "#818CF8";

export function PreProcessingNewNode({ data, selected }) {
  const nodeSettings = data?.settings;

  const canShowCurrentModel = useMemo(
    () =>
      nodeSettings?.isCopilot &&
      nodeSettings?.isSimilarityCheck &&
      nodeSettings?.systemModelName,
    [nodeSettings]
  );

  return (
    <Space
      className={`workflow-node ${selected ? "pre-processing-new-active" : ""}`}
      direction={"vertical"}
    >
      <div>
        <Handle
          type="source"
          position={Position.Right}
          id="preProcessingNew"
          className={"handle-node"}
          style={{
            backgroundImage: `url(${IconNewCircle})`,
          }}
        />
        <div
          className={classNames("flow-node", {
            "!border-none": !canShowCurrentModel,
          })}
        >
          <Space size={"small"}>
            <PreProcessingNewIcon />
            <Text style={{ color }}>{data?.name}</Text>
          </Space>
        </div>
      </div>
      {canShowCurrentModel && (
        <Space direction={"vertical"} className="list-user" size={"small"}>
          <div
            className={classNames(
              "flex items-center gap-[8px] px-[8px] text-[#334466] text-[14px] font-medium leading-[15px]"
            )}
          >
            <div className="flex">
              <Icon3DCubeScan />
            </div>
            {nodeSettings.systemModelName}
          </div>
        </Space>
      )}
    </Space>
  );
}
