// import { <PERSON><PERSON> } from "@taureau/ui";
import React, { useEffect } from "react";
import { WorkflowSpace } from "./WorkflowSpace";
// import { WorkflowSpace } from "../../SettingsProject/component/WorkflowProject/WorkflowSpace";
import { DEFAULT_NODE_SETTINGS } from "./types";
// import { notification, success } from "../../../../../components_lse/Popup/Popup";
// import { useParams as useParamsContext } from "../../../../providers/RoutesProvider";
// import { useAPI } from "@/providers/ApiProvider";

import { ReactFlowProvider } from "reactflow";
import { isEmpty } from "lodash";
import {
  ANNOTATE_EDGES,
  ANNOTATE_NODES,
  PRE_PROCESSING_EDGES,
  PRE_PROCESSING_NODES,
} from "../../SettingsProject/component/WorkflowProject/WorkflowProject";
import Message from "@/ai_platform_v2/component/Message/Message";

interface Props {
  projectState: any;
  onChange: (value: any) => void;
}

export const WorkflowProject = ({ projectState, onChange }: Props) => {
  // const api = useAPI();
  // const params = useParamsContext();
  const [nodes, setNodes] = React.useState([]);
  const [edges, setEdges] = React.useState([]);

  const onSave = async () => {
    try {
      const newAnnotationWorkFlow: any = {
        node: [],
        edge: [],
      };

      const newPreProcessingWorkFlow: any = {
        node: [],
        edge: [],
      };

      let isFullConnnect = true;

      let existNew = false;
      let existCompleted = false;

      let existPreProcessingNew = false;
      let existPreProcessingCompleted = false;

      const preProcessingNodes = nodes.filter((node: any) =>
        PRE_PROCESSING_NODES.includes(node?.type)
      );

      const preProcessingEdges = edges.filter((edge: any) =>
        PRE_PROCESSING_EDGES.includes(edge?.type)
      );

      const annotationNodes = nodes.filter((node: any) =>
        ANNOTATE_NODES.includes(node?.type)
      );

      const annotationEdges = edges.filter((edge: any) =>
        ANNOTATE_EDGES.includes(edge?.type)
      );

      const annotationBlockNode = nodes.find(
        (node: any) => node?.id === "Annotation"
      );

      const preProcessingBlockNode = nodes.find(
        (node: any) => node?.id === "PreProcessing"
      );

      // if (isEmpty(annotationBlockNode)) {
      //   Message.error({
      //     content: "Cannot save workflow without Annotation block",
      //   });
      //   return;
      // }

      // if (
      //   !annotationNodes?.length ||
      //   (!isEmpty(preProcessingBlockNode) && !preProcessingNodes?.length)
      // ) {
      //   Message.error({
      //     content: "Cannot save workflow with blank block",
      //   });
      //   return;
      // }

      annotationNodes.map((node, i) => {
        newAnnotationWorkFlow.node.push({
          id: node?.id,
          name: node?.name,
          type: node?.type,
          position: node?.position,
          width: node?.width,
          height: node?.height,
          settings: node?.settings || DEFAULT_NODE_SETTINGS,
        });

        if (node?.members) {
          newAnnotationWorkFlow.node[i].members = node?.members;
        }

        const totalEdges = annotationEdges.filter(
          (n) => n.source === node.id || n.target === node.id
        );

        switch (node.type) {
          case "New":
            existNew = true;
            if (totalEdges.length < 1) {
              isFullConnnect = false;
            }
            break;
          case "Labeling":
            if (totalEdges.length < 2) {
              isFullConnnect = false;
            }
            break;
          case "Inreview":
            if (totalEdges.length < 3) {
              isFullConnnect = false;
            }
            break;
          case "Completed":
            existCompleted = true;
            if (totalEdges.length < 1) {
              isFullConnnect = false;
            }
            break;
          default:
            break;
        }
      });
      newAnnotationWorkFlow.edge.push(...annotationEdges);

      if (preProcessingNodes?.length) {
        preProcessingNodes.map((node, i) => {
          newPreProcessingWorkFlow.node.push({
            id: node?.id,
            name: node?.name,
            type: node?.type,
            position: node?.position,
            width: node?.width,
            height: node?.height,
            settings: node?.settings || DEFAULT_NODE_SETTINGS,
          });

          if (node?.members) {
            newPreProcessingWorkFlow.node[i].members = node?.members;
          }

          const totalEdges = preProcessingEdges.filter(
            (n) => n.source === node.id || n.target === node.id
          );

          switch (node.type) {
            case "PreProcessingNew":
              existPreProcessingNew = true;
              if (totalEdges.length < 1) {
                isFullConnnect = false;
              }
              break;
            case "HumanApproval":
              if (totalEdges.length < 2) {
                isFullConnnect = false;
              }
              break;
            case "PreProcessingCompleted":
              existPreProcessingCompleted = true;
              if (totalEdges.length < 1) {
                isFullConnnect = false;
              }
              break;
            default:
              break;
          }
        });

        newPreProcessingWorkFlow.edge.push(...preProcessingEdges);
      }

      // if (!existNew) {
      //   Message.error({
      //     content: "Missing New step in Annotation workflow block",
      //   });
      //   return;
      // }

      // if (!existCompleted) {
      //   Message.error({
      //     content: "Missing Completed step in Annotation workflow block",
      //   });
      //   return;
      // }

      // if (!isEmpty(preProcessingBlockNode) && !existPreProcessingNew) {
      //   Message.error({
      //     content: "Missing New step in Pre-processing workflow block",
      //   });
      //   return;
      // }

      // if (!isEmpty(preProcessingBlockNode) && !existPreProcessingCompleted) {
      //   Message.error({
      //     content: "Missing Completed step in Pre-processing workflow block",
      //   });
      //   return;
      // }

      // if (!isFullConnnect) {
      //   Message.error({
      //     content: "Missing connection between 2 steps",
      //   });
      //   return;
      // }

      newAnnotationWorkFlow.annotationBlockNode = {
        id: annotationBlockNode?.id,
        type: annotationBlockNode?.type,
        data: annotationBlockNode?.data,
        position: annotationBlockNode?.position,
        width: annotationBlockNode?.width,
        height: annotationBlockNode?.height,
        style: annotationBlockNode?.style,
      };

      if (!isEmpty(preProcessingBlockNode)) {
        newPreProcessingWorkFlow.preProcessingBlockNode = {
          id: preProcessingBlockNode?.id,
          type: preProcessingBlockNode?.type,
          data: preProcessingBlockNode?.data,
          position: preProcessingBlockNode?.position,
          width: preProcessingBlockNode?.width,
          height: preProcessingBlockNode?.height,
          style: preProcessingBlockNode?.style,
        };
      }

      const result = {
        annotationWorkFlow: JSON.stringify(newAnnotationWorkFlow),
        preProccessConfig: JSON.stringify(newPreProcessingWorkFlow),
      };

      onChange(result);
    } catch (error: any) {
      console.log(error?.message);
    }
  };

  useEffect(() => {
    onSave();
  }, [nodes, edges]);

  return (
    <div className="flex flex-col items-center w-full h-full">
      <div className="w-full h-full flex flex-col items-center overflow-auto">
        <div className="annotation-content w-full h-full rounded-[10px] shadow-Shadows/Gray-Blue-10/15%/10b">
          <ReactFlowProvider>
            <WorkflowSpace
              projectState={projectState}
              nodes={nodes}
              edges={edges}
              setNodes={setNodes}
              setEdges={setEdges}
            />
          </ReactFlowProvider>
        </div>
      </div>
    </div>
  );
};
