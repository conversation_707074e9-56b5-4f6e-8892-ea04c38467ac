import IconSearch from "@/ai_platform_v2/assets/Icons/IconSearch";
import SearchOutlined from "@ant-design/icons/SearchOutlined";
import { Input } from "@taureau/ui";
import { Space, Typography } from "antd";
import { useContext, useEffect, useState } from "react";
import IconMembersWorkflow from "../../../assets/Icons/IconMembersWorkflow";
import IconSettingsWorkflow from "../../../assets/Icons/IconSettingsWorkflow";
import { Tabs } from "../../../component/Tabs/Tabs";
import AnyoneActive from "../../SettingsProject/component/WorkflowProject/Icon/AnyoneActive";
import CardSetting from "./CardSetting/CardSetting";
import CardUser from "./CardUser";
import Anyone from "./Icon/Anyone";
import Completed from "./Icon/Completed";
import Labeling from "./Icon/Labeling";
import New from "./Icon/New";
import Reviewing from "./Icon/Reviewing";
import { WorkflowNodeType } from "./types";
import { UserContext } from "./WorkflowSpace";

const renderIcon = (type) => {
  switch (type) {
    case "New":
      return <New />;
    case "Labeling":
      return <Labeling />;
    case "Inreview":
      return <Reviewing />;
    case "Completed":
      return <Completed />;
    default:
      return null;
  }
};

const DrawerDetail = ({
  visible,
  onSelectUser,
  onChangeNameNode,
  onClose,
  selectedNode,
  type,
  handleChangeSettingsNode,
  categoryModelDetailList,
  hasPreProcessingBlock,
}) => {
  const {
    memberAnnotator,
    memberReviewer,
    memberCompleted,
    memberPreProcessing,
  } = useContext(UserContext);
  let [names, setNames] = useState(
    selectedNode?.type === "Labeling"
      ? memberAnnotator
      : selectedNode?.type === "Inreview"
        ? memberReviewer
        : memberCompleted
  );

  const [activeTab, setActiveTab] = useState("settings");

  useEffect(() => {
    if (
      memberAnnotator &&
      memberReviewer &&
      memberCompleted &&
      selectedNode?.type
    ) {
      // console.log(memberAnnotator);
      setNames(
        selectedNode?.type === "Labeling"
          ? memberAnnotator
          : selectedNode?.type === "Inreview"
            ? memberReviewer
            : memberCompleted
      );
    }
  }, [
    selectedNode?.type,
    memberAnnotator,
    memberReviewer,
    memberCompleted,
    memberPreProcessing,
  ]);
  const onChangeSearch = (value) => {
    let listUser =
      selectedNode?.type === "Labeling"
        ? memberAnnotator
        : selectedNode?.type === "Inreview"
          ? memberReviewer
          : memberCompleted;

    let searchValue = value;

    if (searchValue.endsWith(" ")) {
      searchValue = searchValue.trim(); // Loại bỏ ký tự cách ở cuối và đầu chuỗi
    }

    setNames(
      listUser.filter((name) => {
        return name?.firstName
          ?.concat(" ", name?.lastName)
          .toLowerCase()
          .includes(searchValue.toLowerCase());
      })
    );
  };

  const tabItems = [
    {
      key: "settings",
      label: "Settings",
      icon: <IconSettingsWorkflow size={20} />,
      children: (
        <CardSetting
          isValidation={false}
          handleChangeSettingsNode={handleChangeSettingsNode}
          defaultSettings={selectedNode?.settings}
          selectedNodeType={selectedNode?.type}
          categoryModelDetailList={categoryModelDetailList}
          hasPreProcessingBlock={hasPreProcessingBlock}
        />
      ),
    },
    {
      key: "member",
      label: "Member",
      icon: <IconMembersWorkflow size={20} />,
      children: (
        <>
          {type !== "completed" &&
            selectedNode?.type !== WorkflowNodeType.New && (
              <div className="w-full h-full">
                <Input
                  iconLeft={<IconSearch />}
                  placeholder={"Search Users"}
                  inputClassName="text-[14px]"
                  onChange={(e) => onChangeSearch(e.target.value)}
                />
                <Space
                  direction={"vertical"}
                  className="list-user"
                  size={"small"}
                >
                  <CardUser
                    icon={
                      selectedNode?.members?.includes("all") ? (
                        <AnyoneActive />
                      ) : (
                        <Anyone />
                      )
                    }
                    user={{
                      id: "all",
                      lastName: "Anyone",
                      firstName: "",
                    }}
                    selected={selectedNode?.members}
                    onSelect={() => {
                      onSelectUser(["all"]);
                    }}
                  />
                  <div className="flex flex-col gap-[5px]">
                    {names?.map((user) => (
                      <CardUser
                        key={user.userId}
                        user={user}
                        selected={selectedNode?.members}
                        onSelect={(id) => {
                          let ids = selectedNode?.members;

                          if (ids.includes(id)) {
                            let a = ids.filter((u) => u !== id);

                            if (a.length > 0) {
                              onSelectUser(ids.filter((u) => u !== id));
                            } else {
                              onSelectUser(["all"]);
                            }
                            return;
                          }

                          ids = ids.filter((id) => id !== "all");
                          ids = [...ids, id];
                          onSelectUser(ids);
                        }}
                      />
                    ))}
                  </div>
                </Space>
              </div>
            )}
        </>
      ),
    },
  ].filter(
    (item) =>
      !(
        item.key === "member" &&
        (selectedNode?.type === WorkflowNodeType.New ||
          selectedNode?.type === WorkflowNodeType.PreProcessingNew ||
          (selectedNode?.type === WorkflowNodeType.PreProcessingCompleted &&
            !selectedNode?.settings?.isManualConfirmation))
      )
  );

  return (
    <div
      className={`drawer-detail scrollbar-v-sm ${
        visible ? "active" : "inactive"
      }`}
      style={{ overflow: "overlay", height: "95%", top: "10px" }}
    >
      <div className="drawer-detail-header">
        <div className="flex gap-[10px] items-center">
          {renderIcon(selectedNode?.type)}
          <div className=" font-medium text-[14px] text-gray-blue-40">
            Step name
          </div>
        </div>
        <div className="icon-stage" onClick={onClose}>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M13.927 5.53369C13.8025 5.40885 13.6334 5.3387 13.457 5.3387C13.2807 5.3387 13.1116 5.40885 12.987 5.53369L9.72703 8.78702L6.46703 5.52702C6.34248 5.40219 6.17338 5.33203 5.99703 5.33203C5.82069 5.33203 5.65159 5.40219 5.52703 5.52702C5.26703 5.78702 5.26703 6.20702 5.52703 6.46702L8.78703 9.72702L5.52703 12.987C5.26703 13.247 5.26703 13.667 5.52703 13.927C5.78703 14.187 6.20703 14.187 6.46703 13.927L9.72703 10.667L12.987 13.927C13.247 14.187 13.667 14.187 13.927 13.927C14.187 13.667 14.187 13.247 13.927 12.987L10.667 9.72702L13.927 6.46702C14.1804 6.21369 14.1804 5.78702 13.927 5.53369Z"
              fill="#C3CAD9"
            />
          </svg>
        </div>
        <Input
          value={selectedNode?.name}
          inputClassName="text-[14px]"
          onChange={(e) => {
            if (selectedNode?.type !== "New") {
              onChangeNameNode(e.target.value);
            }
          }}
        />
      </div>

      {type === "dataset" && (
        <div>
          <Typography.Title
            level={5}
            style={{
              marginTop: 15,
            }}
          >
            Connected Dataset
          </Typography.Title>
          <Input
            prefix={<SearchOutlined />}
            value={"Dataset"}
            disabled={true}
          />
        </div>
      )}
      <Tabs
        activeKey={
          activeTab === "member" &&
          (selectedNode?.type === WorkflowNodeType.New ||
            selectedNode?.type === WorkflowNodeType.PreProcessingNew ||
            (selectedNode?.type === WorkflowNodeType.PreProcessingCompleted &&
              !selectedNode?.settings?.isManualConfirmation))
            ? "settings"
            : activeTab
        }
        items={tabItems}
        onChange={(activeKey) => setActiveTab(activeKey)}
      />
      {/* {selectedNode && (
        <CardSetting
          handleChangeSettingsNode={handleChangeSettingsNode}
          defaultSettings={selectedNode?.settings}
          selectedNodeType={selectedNode?.type}
        />
      )}

      {type !== "completed" && selectedNode?.type !== WorkflowNodeType.New && (
        <div>
          <div className="flex gap-[10px] items-center mt-[15px] mb-[10px] pt-[10px] border-black-10 border-t-[1px] w-full">
            <Member />
            <div className=" font-medium text-[15px]">Member</div>
          </div>

          <Input
            iconLeft={<IconSearch />}
            placeholder={"Search Users"}
            inputClassName="text-[14px]"
            onChange={(e) => onChangeSearch(e.target.value)}
          />
          <Space direction={"vertical"} className="list-user" size={"small"}>
            <CardUser
              icon={<Anyone />}
              user={{
                id: "all",
                lastName: "Anyone",
                firstName: "",
              }}
              selected={selectedNode?.members}
              onSelect={() => {
                onSelectUser(["all"]);
              }}
            />
            <div>
              {names?.map((user) => (
                <CardUser
                  key={user.userId}
                  user={user}
                  selected={selectedNode?.members}
                  onSelect={(id) => {
                    let ids = selectedNode?.members;

                    if (ids.includes(id)) {
                      let a = ids.filter((u) => u !== id);

                      if (a.length > 0) {
                        onSelectUser(ids.filter((u) => u !== id));
                      } else {
                        onSelectUser(["all"]);
                      }
                      return;
                    }

                    ids = ids.filter((id) => id !== "all");
                    ids = [...ids, id];
                    onSelectUser(ids);
                  }}
                />
              ))}
            </div>
          </Space>
        </div>
      )} */}
    </div>
  );
};

export default DrawerDetail;
