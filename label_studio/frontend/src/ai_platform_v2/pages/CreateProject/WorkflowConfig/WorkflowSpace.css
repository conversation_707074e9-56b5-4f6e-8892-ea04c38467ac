.create-project-setting-workflow {
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.create-workflow-button {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}

.create-project-setting-workflow .react-workflow-wrapper {
  height: 100%;
}

.create-project-setting-workflow .sidebar-workflow {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translate(-50%, 0);
  background: #fff;
  border-radius: 8px;
  padding: 6px 8px;
  z-index: 1;
  border: 1px solid #dfe1e2;
  box-shadow: 0px 2px 20px 0px rgba(0, 0, 0, 0.1);
  gap: 8px;
}

.create-project-setting-workflow .sidebar-workflow--active {
  border-radius: 0 0 8px 8px;
}

.create-project-setting-workflow .sidebar-options {
  display: flex;
  z-index: 1;
  box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
  gap: 4px;
  background: #f1f1f1;
  border-radius: 12px;
  padding: 4px;
  position: absolute;
  bottom: 48px;
  left: 50%;
  transform: translate(-50%, 0);
}

/* .create-project-setting-workflow .sidebar-workflow .sidebar-submit {
  margin-right: 8px;
} */

.create-project-setting-workflow .drawer-detail {
  position: absolute;
  right: 5px;
  top: 75px;
  height: calc(100% - 100px);
  width: 300px;
  background: #fff;
  border-radius: 8px;
  padding: 0;
  z-index: 1;
  border: 1px solid #dfe1e2;
  box-shadow: 0 1px 2px rgb(16 24 40 / 5%);
  transition: right 0.5s cubic-bezier(0.82, 0.085, 0.395, 0.895);
}

.create-project-setting-workflow .drawer-detail.inactive {
  right: -320px;
}

.create-project-setting-workflow .drawer-detail.active {
  right: 5px;
}

.create-project-setting-workflow .drawer-detail .drawer-detail-header {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  padding: 10px;
  gap: 5px;
}

.create-project-setting-workflow
  .drawer-detail
  .drawer-detail-header
  .icon-stage {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: fit-content;
  width: fit-content;
  cursor: pointer;
  position: absolute;
  top: 4px;
  right: 4px;
}

.create-project-setting-workflow .drawer-detail .ant-input,
.create-project-setting-workflow .drawer-detail .ant-input-affix-wrapper {
  border-radius: 6px;
}

.create-project-setting-workflow .drawer-detail .list-user {
  margin-top: 10px;
  width: 100%;
}

.create-project-setting-workflow .drawer-detail .list-user .icon-anyone {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 99px;
  background-color: #e9ebed;
}

.create-project-setting-workflow
  .drawer-detail
  .drawer-detail-header
  .icon-node {
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.04);
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
}

.create-project-setting-workflow .flow-node {
  padding: 6px 12px 10px 12px;
  width: 100%;
  font-weight: 600;
  /*min-height: 80px;*/
  border-bottom: 1px solid #dfe1e2;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.create-project-setting-workflow .flow-node__no-bottom {
  border-bottom: 0px;
}

.create-project-setting-workflow .handle-node {
  height: 16px;
  width: 16px;
  border-radius: 99px;
  background-color: #fff;
  /*background: url(./circle.svg) no-repeat center center fixed;*/
}

.create-project-setting-workflow .react-flow__handle-left {
  left: -9px;
}

.create-project-setting-workflow .react-flow__handle-right {
  right: -7px;
}

.create-project-setting-workflow .workflow-node {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 10px;
  padding: 6px;
  width: 240px;
  border: 2px solid white;
  /*min-height: 80px;*/
  box-shadow:
    0 1px 2px rgb(0 0 0 / 8%),
    0 0 1px rgb(0 0 0 / 4%),
    0 2px 4px -1px rgb(49 51 53 / 20%);
}

.create-project-setting-workflow .labeling-active {
  border: 2px solid #3361ff;
}

.create-project-setting-workflow .complete-active {
  border: 2px solid #29cc39;
}
.create-project-setting-workflow .dataset-active {
  border: 2px solid #2ee6ca;
}
.create-project-setting-workflow .review-active {
  border: 2px solid #ffcb33;
}
.create-project-setting-workflow .archive-active {
  border: 2px solid rgb(220, 24, 24);
}

.create-project-setting-workflow .pre-processing-new-active {
  border: 2px solid #818cf8;
}

.create-project-setting-workflow .pre-processing-completed-active {
  border: 2px solid #15803d;
}

.create-project-setting-workflow .human-approval-active {
  border: 2px solid #edb7ed;
}

.create-project-setting-workflow .flow-node .amount {
  width: 24px;
  height: 24px;
  border-radius: 99px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #e9ebed;
  font-size: smaller;
}

.create-project-setting-workflow .dataset-node {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 10px;
  padding: 6px;
  width: 270px;
  border: 2px solid white;
  /*min-height: 80px;*/
  box-shadow:
    0 1px 2px rgb(0 0 0 / 8%),
    0 0 1px rgb(0 0 0 / 4%),
    0 2px 4px -1px rgb(49 51 53 / 20%);
}

.create-project-setting-workflow .dataset-node.active {
  border: 2px solid #2ee6ca;
}

.create-project-setting-workflow .dataset-node .container-dataset-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-project-setting-workflow
  .dataset-node
  .container-dataset-info
  .info-item {
  /*flex: 1;*/
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 6px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  width: 31%;
}

.react-flow__panel > a {
  visibility: hidden;
}

.has-current-model {
  color: #cc1414 !important;
}

.has-current-model svg path {
  fill: #cc1414;
}
