import { But<PERSON> } from "@taureau/ui";
import { Block, Elem } from "../../../utils/bem";
import { WorkflowProject } from "./WorkflowProject";
// import { WorkflowProject } from "../../SettingsProject/component/WorkflowProject/WorkflowProject";

export const PreviewWorkflow = ({ projectState, onChange, onSelect }) => {
  return (
    <Block name="preview-workflow">
      <Elem name="header">
        <Button
          className="h-[30px] px-[10px] py-[5px] text-[12px]"
          theme="Primary"
          size="sm"
          onClick={() => onSelect?.()}
        >
          Browse Templates
        </Button>
      </Elem>
      <Elem name="body">
        <WorkflowProject projectState={projectState} onChange={onChange} />
      </Elem>
    </Block>
  );
};
