import Message from "@/ai_platform_v2/component/Message/Message";
import React, { createContext, useCallback, useEffect, useState } from "react";
import {
  addEdge,
  applyEdgeChanges,
  applyNodeChanges,
  Background,
  Controls,
  getNodesBounds,
  MiniMap,
  ReactFlow,
  ReactFlowProvider,
  useKeyPress,
} from "reactflow";
import { v4 as uuidv4 } from "uuid";
import { ABILITY_NEW } from "../../../../config/PermissionsConfig";
import { useAPI } from "../../../../providers/ApiProvider";

import {
  BlockNode,
  CompleteNode,
  HumanApprovalNode,
  LabelingNode,
  NewNode,
  PreProcessingCompletedNode,
  PreProcessingNewNode,
  ReviewNode,
} from "./WorkflowNode";

import {
  AcceptEdge,
  CompleteEdge,
  HumanApprovalEdge,
  LabelingEdge,
  NewEdge,
  PreProcessingCompletedEdge,
  PreProcessingNewEdge,
  RejectEdge,
  ReviewEdge,
} from "@v2/pages/SettingsProject/component/WorkflowProject/WorkflowEdge";

import Sidebar from "@v2/pages/SettingsProject/component/WorkflowProject/Sidebar";
import ConnectionLine from "@v2/pages/SettingsProject/component/WorkflowProject/WorkflowEdge/ConnectionLine";
import DrawerDetail from "./DrawerDetail";

import { isEmpty, isNil } from "lodash";
import {
  ANNOTATE_CONNECTION,
  ANNOTATE_NODES,
  PRE_PROCESSING_CONNECTION,
  PRE_PROCESSING_NODES,
} from "../../SettingsProject/component/WorkflowProject/WorkflowProject";
import {
  DEFAULT_HUMAN_APPROVAL_SETTINGS,
  DEFAULT_NODE_SETTINGS,
  DEFAULT_PRE_PROCESSING_COMPLETED_SETTINGS,
  DEFAULT_PRE_PROCESSING_NEW_SETTINGS,
} from "./types";
import "./WorkflowSpace.css";

const MEMBER_ROLE_ID = "002926bf-f341-4f36-b417-91eab47d9cde";

export const UserContext = createContext();

export const UserProvider = ({ members, children }) => {
  const [listUser, setListUser] = React.useState([]);
  const [memberAnnotator, setMemberAnnotator] = React.useState([]);
  const [memberReviewer, setMemberReviewer] = React.useState([]);
  const [memberCompleted, setMemberCompleted] = React.useState([]);

  const [memberPreProcessing, setMemberPreProcessing] = React.useState([]);

  const [sum, setSum] = React.useState(0);
  const [sumCompleted, setSumCompleted] = React.useState(0);
  const [sumProcess, setSumProcess] = React.useState(0);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  React.useEffect(async () => {
    if (members?.length) {
      const finalMembers = members.map((member) => ({
        ...member,
        userId: member.id,
        avatar_url: member.avatar,
      }));

      setListUser(finalMembers);
      setMemberReviewer(finalMembers ?? []);
      setMemberAnnotator(finalMembers ?? []);
      setMemberCompleted(finalMembers ?? []);
      setMemberPreProcessing(
        finalMembers?.filter(
          (member) =>
            member?.permissions?.search(ABILITY_NEW.can_data_proceed) !== -1
        ) ?? []
      );
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <UserContext.Provider
      value={{
        listUser,
        memberAnnotator,
        memberReviewer,
        memberCompleted,
        memberPreProcessing,
        sum,
        sumCompleted,
        sumProcess,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const WorkflowSpace = ({
  projectState,
  nodes,
  edges,
  setNodes,
  setEdges,
}) => {
  const api = useAPI();
  const reactFlowWrapper = React.useRef(null);
  const delPressed = useKeyPress("Delete");
  const [reactFlowInstance, setReactFlowInstance] = React.useState(null);

  const [visibleDetail, setVisibleDetail] = React.useState(false);
  const [selectedNode, setSelectedNode] = React.useState(null);

  const [tempData, setTempData] = useState({
    preProccessConfig: projectState?.preProccessConfig,
    annotationWorkFlow: projectState?.annotationWorkFlow,
  });

  const [categoryModelDetailList, setCategoryModelDetailList] = React.useState(
    []
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(async () => {
    try {
      const resCategoryModels = await api.callApi("allModelCategories");

      if (resCategoryModels?.length) {
        const embeddingModelId = resCategoryModels?.[0]?.id;

        if (embeddingModelId) {
          const resModelDetail = await api.callApi("categoryModels", {
            params: {
              pk: embeddingModelId,
              status: "Publish",
              page: 1,
              pageSize: 99999,
            },
          });

          if (resModelDetail?.success) {
            setCategoryModelDetailList(resModelDetail.items);
          }
        }
      }
    } catch (error) {
      console.log("error: ", error?.message);
    } finally {
      //
    }
  }, []); // eslint-disable-next-line react-hooks/exhaustive-deps

  React.useEffect(() => {
    let nodes = [];
    let edges = [];

    if (tempData?.preProccessConfig) {
      const preProcessingWorkflow = JSON.parse(tempData.preProccessConfig);

      if (preProcessingWorkflow?.node?.length) {
        const newPreProcessingWorkflow = preProcessingWorkflow.node.map(
          (node) => ({
            ...node,
            data: {
              name: node.name,
              members: node.members,
              id: node.id,
              settings: node.settings,
            },
            parentNode: "PreProcessing",
            expandParent: true,
            extent: "parent",
            style: {
              zIndex: 2,
            },
          })
        );

        if (preProcessingWorkflow?.preProcessingBlockNode) {
          const preProcessingBlock = {
            ...preProcessingWorkflow?.preProcessingBlockNode,
          };

          nodes = [preProcessingBlock, ...newPreProcessingWorkflow];
        } else {
          const annotationBlockBounds = getNodesBounds(
            newPreProcessingWorkflow
          );

          const { x, y, width, height } = annotationBlockBounds;

          const preProcessingBlock = {
            id: "Annotation",
            type: "Block",
            data: {
              label: "Annotation",
              id: "Annotation",
            },
            position: {
              x,
              y,
            },
            style: {
              width,
              height,
            },
          };

          nodes = [preProcessingBlock, ...newPreProcessingWorkflow];
        }

        edges = [...preProcessingWorkflow?.edge];
      }
    }

    if (tempData?.annotationWorkFlow) {
      const annotationWorkflow = JSON.parse(tempData.annotationWorkFlow);

      if (annotationWorkflow?.node?.length) {
        const _annotationWorkflow = annotationWorkflow.node.map((node) => ({
          ...node,
          data: {
            name: node.name,
            members: node.members,
            id: node.id,
            settings: node.settings,
          },
          parentNode: "Annotation",
          expandParent: true,
          extent: "parent",
          style: {
            zIndex: 2,
          },
        }));

        if (annotationWorkflow?.annotationBlockNode) {
          const annotationBlockNode = {
            ...annotationWorkflow?.annotationBlockNode,
          };

          nodes = [...nodes, annotationBlockNode, ..._annotationWorkflow];
        } else {
          const annotationBlockBounds = getNodesBounds(_annotationWorkflow);

          const { x, y, width, height } = annotationBlockBounds;

          const annotationBlockNode = {
            id: "Annotation",
            type: "Block",
            data: {
              label: "Annotation",
              id: "Annotation",
            },
            position: {
              x,
              y,
            },
            style: {
              width,
              height,
            },
          };
          const newAnnotationWorkflow = _annotationWorkflow?.map((node) => ({
            ...node,
            position: { x: node?.position?.x - x, y: node?.position?.y - y },
          }));

          nodes = [...nodes, annotationBlockNode, ...newAnnotationWorkflow];
        }

        edges = [...edges, ...annotationWorkflow?.edge];
      }
    }

    setNodes(nodes);
    setEdges(edges);
  }, [tempData.preProccessConfig, tempData.annotationWorkFlow]);

  useEffect(() => {
    const newNode = nodes.find((item) => item.type === "New");

    const preProcessingCompletedNode = nodes.find(
      (item) => item.type === "PreProcessingCompleted"
    );

    if (
      !isEmpty(preProcessingCompletedNode) &&
      !isEmpty(newNode) &&
      isEmpty(edges.find((edge) => edge?.type === "PreProcessingCompleted"))
    ) {
      const preProcessingCompletedConnection = {
        id: preProcessingCompletedNode.id.concat("_", newNode.id),
        type: "PreProcessingCompleted",
        source: preProcessingCompletedNode.id,
        sourceHandle: "preProcessingCompleted",
        target: newNode.id,
        targetHandle: "new-handle-target",
      };

      edges.push(preProcessingCompletedConnection);

      setEdges(edges);
    }
  }, [nodes, edges]);

  const onNodesChange = React.useCallback(
    (changes) => setNodes((nds) => applyNodeChanges(changes, nds)),
    [setNodes]
  );

  const onEdgesChange = React.useCallback(
    (changes) => setEdges((eds) => applyEdgeChanges(changes, eds)),
    [setEdges]
  );

  const onConnect = React.useCallback(
    (changes) => {
      const connection = changes;

      if (connection.source !== connection.target) {
        const edge_available = edges.filter(
          (edge) =>
            edge.source === connection.source &&
            edge.target === connection.target
        );

        if (edge_available.length === 0) {
          if (changes.targetHandle.endsWith("-bg")) {
            connection.targetHandle = changes.targetHandle.replace("-bg", "");
          }
          const getClassOfEdge = () => {
            const source = connection?.source;
            const type = nodes.find((obj) => obj.id === source)?.type;

            if (type === "Inreview") {
              if (connection.sourceHandle === "reject") {
                return "Reject";
              } else if (connection.sourceHandle === "accept") {
                return "Accept";
              }
            }
            return type;
          };

          const node_isconnected = edges.find(
            (edge) =>
              edge.source === connection.source &&
              edge.sourceHandle === connection.sourceHandle
          );

          if (!node_isconnected) {
            setEdges((eds) =>
              addEdge(
                {
                  ...connection,
                  type: getClassOfEdge(),
                  id: connection.source.concat("_", connection.target),
                },
                eds
              )
            );
          } else {
            const edgesClone = [...edges];

            const result = [];

            edgesClone.map((edge) => {
              if (node_isconnected.id !== edge.id) {
                result.push(edge);
              }
            });

            result.push({
              ...connection,
              type: getClassOfEdge(),
              id: connection.source.concat("_", connection.target),
            });

            setEdges(result);
          }
        }
      }
    },
    [setEdges, edges, nodes]
  );

  const nodeTypes = React.useMemo(
    () => ({
      New: NewNode,
      Completed: CompleteNode,
      Labeling: LabelingNode,
      Inreview: ReviewNode,
      Block: BlockNode,
      PreProcessingNew: PreProcessingNewNode,
      HumanApproval: HumanApprovalNode,
      PreProcessingCompleted: PreProcessingCompletedNode,
    }),
    []
  );

  const edgeTypes = React.useMemo(
    () => ({
      New: NewEdge,
      Completed: CompleteEdge,
      Labeling: LabelingEdge,
      Inreview: ReviewEdge,
      Accept: AcceptEdge,
      Reject: RejectEdge,
      PreProcessingNew: PreProcessingNewEdge,
      HumanApproval: HumanApprovalEdge,
      PreProcessingCompleted: PreProcessingCompletedEdge,
    }),
    []
  );

  const onDragOver = React.useCallback((event) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
  }, []);

  const onDrop = React.useCallback(
    (event) => {
      event.preventDefault();

      const type = event.dataTransfer.getData("application/reactflow");

      if (isNil(type)) {
        return;
      }

      const position = reactFlowInstance.screenToFlowPosition({
        x: event.clientX,
        y: event.clientY,
      });

      let name;

      const node = reactFlowInstance.toObject().nodes;

      const annotationBlock = node?.find((item) => item.id === "Annotation");
      const preProcessingBlock = node?.find(
        (item) => item.id === "PreProcessing"
      );

      const preProcessingNewNode = node?.find(
        (item) => item.type === "PreProcessingNew"
      );

      const preProcessingCompletedNode = node?.find(
        (item) => item.type === "PreProcessingCompleted"
      );

      const nodeNew = node?.find((item) => item.type === "New");

      const nodeCompleted = node?.find((item) => item.type === "Completed");

      if (type === "AnnotationBlock") {
        if (!isEmpty(annotationBlock)) {
          Message.error({
            content: "Cannot add more than 1 block of each type",
          });

          return;
        }
        const newNode = {
          id: "Annotation",
          type: "Block",
          position,
          data: {
            label: "Annotation",
            id: "Annotation",
          },
        };

        const id_new = uuidv4();
        const id_labeling = uuidv4();
        const id_inreview = uuidv4();
        const id_completed = uuidv4();

        const annotationChildNode = [
          {
            id: id_new,
            name: "New",
            type: "New",
            position: {
              x: 43.12066988376034,
              y: 63.665180725053915,
            },
            width: 270,
            height: 152,
            members: ["all"],
            data: { name: "New", members: ["all"] },
            parentNode: "Annotation",
            expandParent: true,
            extent: "parent",
          },
          {
            id: id_labeling,
            name: "Labeling",
            type: "Labeling",
            position: {
              x: 368.3955312296895,
              y: 63.2822507241159,
            },
            width: 240,
            height: 102,
            members: ["all"],
            data: { name: "Labeling", members: ["all"] },
            parentNode: "Annotation",
            expandParent: true,
            extent: "parent",
          },
          {
            id: id_inreview,
            name: "Reviewing",
            type: "Inreview",
            position: {
              x: 675.549591693198,
              y: 64.81340631093155,
            },
            width: 240,
            height: 183,
            members: ["all"],
            data: { name: "Reviewing", members: ["all"] },
            parentNode: "Annotation",
            expandParent: true,
            extent: "parent",
          },
          {
            id: id_completed,
            name: "Completed",
            type: "Completed",
            position: {
              x: 978.4178750812418,
              y: 69.62122665897493,
            },
            width: 240,
            height: 102,
            members: ["all"],
            data: { name: "Completed", members: ["all"] },
            parentNode: "Annotation",
            expandParent: true,
            extent: "parent",
          },
        ];

        const annotationChildEdges = [
          {
            source: id_new,
            sourceHandle: "new",
            target: id_labeling,
            targetHandle: "labeling-handle-target",
            type: "New",
            id: id_new.concat("_", id_labeling),
          },
          {
            source: id_labeling,
            sourceHandle: "labeling",
            target: id_inreview,
            targetHandle: "review-handle-target",
            type: "Labeling",
            id: id_labeling.concat("_", id_inreview),
          },
          {
            source: id_inreview,
            sourceHandle: "accept",
            target: id_completed,
            targetHandle: "complete-handle-target",
            type: "Accept",
            id: id_inreview.concat("_", id_completed),
          },
          {
            source: id_inreview,
            sourceHandle: "reject",
            target: id_labeling,
            targetHandle: "labeling-handle-target",
            type: "Reject",
            id: id_inreview.concat("_", id_labeling),
          },
        ];

        setNodes((nds) => nds.concat(newNode, annotationChildNode));
        setEdges((eds) => eds.concat(annotationChildEdges));
      } else if (type === "PreProcessingBlock") {
        if (!isEmpty(preProcessingBlock)) {
          Message.error({
            content: "Cannot add more than 1 block of each type",
          });

          return;
        }
        const newNode = {
          id: "PreProcessing",
          type: "Block",
          position,
          data: {
            label: "Pre-processing",
            id: "PreProcessing",
          },
        };

        const id_new = uuidv4();
        const id_human = uuidv4();
        const id_completed = uuidv4();

        const preProcessingChildNode = [
          {
            id: id_new,
            name: "New",
            type: "PreProcessingNew",
            position: {
              x: 43.12066988376034,
              y: 63.665180725053915,
            },
            width: 270,
            height: 152,
            members: ["all"],
            data: {
              name: "New",
              members: ["all"],
              settings: DEFAULT_PRE_PROCESSING_NEW_SETTINGS,
            },
            parentNode: "PreProcessing",
            expandParent: true,
            extent: "parent",
            settings: DEFAULT_PRE_PROCESSING_NEW_SETTINGS,
          },
          {
            id: id_human,
            name: "Human approval",
            type: "HumanApproval",
            position: {
              x: 368.3955312296895,
              y: 63.2822507241159,
            },
            width: 240,
            height: 102,
            members: ["all"],
            data: {
              name: "Human approval",
              members: ["all"],
              settings: DEFAULT_HUMAN_APPROVAL_SETTINGS,
            },
            parentNode: "PreProcessing",
            expandParent: true,
            extent: "parent",
            settings: DEFAULT_HUMAN_APPROVAL_SETTINGS,
          },
          {
            id: id_completed,
            name: "Completed",
            type: "PreProcessingCompleted",
            position: {
              x: 800,
              y: 69.62122665897493,
            },
            width: 240,
            height: 102,
            members: ["all"],
            data: {
              name: "Completed",
              members: ["all"],
              settings: DEFAULT_PRE_PROCESSING_COMPLETED_SETTINGS,
            },
            parentNode: "PreProcessing",
            expandParent: true,
            extent: "parent",
            settings: DEFAULT_PRE_PROCESSING_COMPLETED_SETTINGS,
          },
        ];

        const preProcessingChildEdges = [
          {
            source: id_new,
            sourceHandle: "preProcessingNew",
            target: id_human,
            targetHandle: "humanApproval-handle-target",
            type: "PreProcessingNew",
            id: id_new.concat("_", id_human),
          },
          {
            source: id_human,
            sourceHandle: "humanApproval",
            target: id_completed,
            targetHandle: "preProcessingCompleted-handle-target",
            type: "HumanApproval",
            id: id_human.concat("_", id_completed),
          },
        ];

        const nodeNewSettings = nodeNew?.settings;

        if (nodeNewSettings?.isReadyToWork) {
          const newNodes = nodes.map((node) => {
            if (node.type === "New") {
              return {
                ...node,
                settings: { ...node.settings, isReadyToWork: false },
              };
            }

            return node;
          });

          setNodes(newNodes.concat(newNode, preProcessingChildNode));

          // If SelectedNode === NodeNew
          if (selectedNode?.type === "New") {
            setSelectedNode({
              ...selectedNode,
              settings: { ...selectedNode.settings, isReadyToWork: false },
            });
          }
        } else {
          setNodes((nds) => nds.concat(newNode, preProcessingChildNode));
        }
        setEdges((eds) => eds.concat(preProcessingChildEdges));
      } else {
        const members = ["all"];
        let settings = DEFAULT_NODE_SETTINGS;

        switch (type) {
          case "New":
            // New node is existed
            if (!isEmpty(nodeNew)) {
              Message.error({
                content: "Cannot add more than 1 New step to Annotation block.",
              });
              return;
            }
            name = "New";
            break;
          case "Labeling":
            name = "Labeling";
            break;
          case "Inreview":
            name = "Reviewing";
            break;
          case "Completed":
            // Completed node is existed
            if (!isEmpty(nodeCompleted)) {
              Message.error({
                content:
                  "Cannot add more than 1 Completed step to Annotation block.",
              });
              return;
            }
            name = "Completed";
            break;
          case "PreProcessingNew":
            // preProcessingNew node is existed
            if (!isEmpty(preProcessingNewNode)) {
              Message.error({
                content:
                  "Cannot add more than 1 New step to Pre-processing block.",
              });
              return;
            }
            name = "New";
            settings = DEFAULT_PRE_PROCESSING_NEW_SETTINGS;
            break;
          case "PreProcessingCompleted":
            // preProcessingCompleted node is existed
            if (!isEmpty(preProcessingCompletedNode)) {
              Message.error({
                content:
                  "Cannot add more than 1 Completed step to Pre-processing block.",
              });
              return;
            }
            name = "Completed";
            settings = DEFAULT_PRE_PROCESSING_COMPLETED_SETTINGS;

            break;
          case "HumanApproval":
            name = "Human approval";
            settings = DEFAULT_HUMAN_APPROVAL_SETTINGS;

            break;
          default:
            name = "";
        }

        node.map((item) => {
          if (item.type === type) {
            if (name === item.name) {
              const a = name.split(" ");

              if (type === "HumanApproval") {
                if (a[2]) {
                  name = a[0].concat(
                    " ",
                    a[1],
                    " ",
                    (parseInt(a[2]) + 1).toString()
                  );
                } else {
                  name = name.concat(" 1");
                }
              } else {
                if (a[1]) {
                  name = a[0].concat(" ", (parseInt(a[1]) + 1).toString());
                } else {
                  name = name.concat(" 1");
                }
              }
            }
          }
        });

        if (ANNOTATE_NODES?.includes(type)) {
          // Annotate Block nodes is existed
          if (!isEmpty(annotationBlock)) {
            const newNode = {
              id: uuidv4(),
              name,
              members,
              type,
              position: {
                x: position.x - annotationBlock.position.x,
                y: position.y - annotationBlock.position.y,
              },

              data: { name, members },
              parentNode: "Annotation",
              expandParent: true,
              extent: "parent",
            };

            setNodes((nds) => nds.concat(newNode));
          } else {
            const annotationBlockNode = {
              id: "Annotation",
              type: "Block",
              position,
              data: {
                label: "Annotation",
                id: "Annotation",
              },
            };
            const newNode = {
              id: uuidv4(),
              name,
              members,
              type,
              position: {
                x: 10,
                y: 50,
              },
              data: { name, members },
              parentNode: "Annotation",
              expandParent: true,
              extent: "parent",
            };

            setNodes((nds) => nds.concat([annotationBlockNode, newNode]));
          }
        }

        if (PRE_PROCESSING_NODES?.includes(type)) {
          // Annotate Block nodes is existed
          if (!isEmpty(preProcessingBlock)) {
            const newNode = {
              id: uuidv4(),
              name,
              members,
              type,
              position: {
                x: position.x - preProcessingBlock.position.x,
                y: position.y - preProcessingBlock.position.y,
              },

              data: { name, members },
              parentNode: "PreProcessing",
              expandParent: true,
              extent: "parent",
              settings,
            };

            const nodeNewSettings = nodeNew?.settings;

            if (nodeNewSettings?.isReadyToWork) {
              const newNodes = nodes.map((node) => {
                if (node.type === "New") {
                  return {
                    ...node,
                    settings: { ...node.settings, isReadyToWork: false },
                  };
                }
                return node;
              });

              setNodes(newNodes.concat(newNode));

              // If SelectedNode === NodeNew
              if (selectedNode?.type === "New") {
                setSelectedNode({
                  ...selectedNode,
                  settings: { ...selectedNode.settings, isReadyToWork: false },
                });
              }
            } else {
              setNodes((nds) => nds.concat(newNode));
            }
          } else {
            const preProcessingBlockNode = {
              id: "PreProcessing",
              type: "Block",
              position,
              data: {
                label: "Pre-processing",
                id: "PreProcessing",
              },
            };
            const newNode = {
              id: uuidv4(),
              name,
              members,
              type,
              position: {
                x: 10,
                y: 50,
              },
              data: { name, members },
              parentNode: "PreProcessing",
              expandParent: true,
              extent: "parent",
              settings,
            };

            const nodeNewSettings = nodeNew?.settings;

            if (nodeNewSettings?.isReadyToWork) {
              const newNodes = nodes.map((node) => {
                if (node.type === "New") {
                  return {
                    ...node,
                    settings: { ...node.settings, isReadyToWork: false },
                  };
                }
                return node;
              });

              setNodes(newNodes.concat([preProcessingBlockNode, newNode]));

              // If SelectedNode === NodeNew
              if (selectedNode?.type === "New") {
                setSelectedNode({
                  ...selectedNode,
                  settings: { ...selectedNode.settings, isReadyToWork: false },
                });
              }
            } else {
              setNodes((nds) => nds.concat([preProcessingBlockNode, newNode]));
            }
          }
        }
      }
    },
    [nodes, reactFlowInstance, selectedNode]
  );

  const onNodeClick = (event, node) => {
    if (
      [...PRE_PROCESSING_NODES, ...ANNOTATE_NODES].includes(node?.type)
      // && node?.type !== "PreProcessingNew" // => Ignore click on PreProcessingNew Node
    ) {
      event.stopPropagation();
      //TODO check node type for detail
      setSelectedNode(node);
      setVisibleDetail(true);
    } else {
      setSelectedNode(null);
      setVisibleDetail(false);
    }
  };

  const onEdgeClick = (event, edge) => {
    event.stopPropagation();
    if (edge?.type !== "PreProcessingCompleted") {
      const a = edges.filter(
        (e) => e.id === edge.id && e.sourceHandle === edge.sourceHandle
      );

      setEdges((eds) => eds.filter((e) => e.id !== a[0].id));
    }
  };

  const onSelectUser = (ids) => {
    const node = {
      ...selectedNode,
      members: ids,
      data: { ...selectedNode.data, members: ids },
    };

    const list_node = nodes;

    list_node.map((n, i) => {
      if (n.id === node.id) {
        list_node[i] = node;
      }
    });

    setNodes([...list_node]);
    setSelectedNode(node);
  };

  const onChangeNameNode = (value) => {
    const node = {
      ...selectedNode,
      name: value,
      data: { ...selectedNode.data, name: value },
    };

    const list_node = nodes;

    list_node.map((n, i) => {
      if (n.id === node.id) {
        list_node[i] = node;
      }
    });

    setNodes([...list_node]);
    setSelectedNode(node);
  };

  const handleChangeSettingsNode = useCallback(
    (settings) => {
      const copyNode = {
        ...selectedNode,
        settings,
        data: { ...selectedNode.data, settings },
      };
      const targetNodeIndex = nodes.findIndex((n) => n.id === copyNode.id);

      setNodes((nodes) => {
        nodes[targetNodeIndex] = copyNode;
        return [...nodes];
      });
      setSelectedNode(copyNode);
    },
    [nodes, selectedNode]
  );

  const validateConnection = useCallback((connection) => {
    const { sourceHandle, targetHandle } = connection;

    const _targetHandle = targetHandle.split("-")?.[0];

    // Check target node and source node are the same block
    if (
      (ANNOTATE_CONNECTION.includes(sourceHandle) &&
        ANNOTATE_CONNECTION.includes(_targetHandle)) ||
      (PRE_PROCESSING_CONNECTION.includes(sourceHandle) &&
        PRE_PROCESSING_CONNECTION.includes(_targetHandle))
    ) {
      return true;
    }

    return false;
  }, []);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  React.useEffect(async () => {
    if (delPressed && selectedNode) {
      setVisibleDetail(false);
      setNodes((nds) => nds.filter((n) => n.id !== selectedNode.id));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [delPressed]);

  return (
    <div className="create-project-setting-workflow">
      <UserProvider
        members={projectState.memberRoles.filter((member) => {
          const memberRoleId = member.role;

          const roleDetail = projectState?.roleList?.find(
            (role) => role.id === memberRoleId
          );

          if (roleDetail) {
            return (
              roleDetail.permissions.search(ABILITY_NEW.can_update_dataset) > -1
            );
          }

          return false;
        })}
      >
        <ReactFlowProvider>
          <Sidebar hasAIPredictionNode={false} />
          <DrawerDetail
            visible={visibleDetail}
            onSelectUser={onSelectUser}
            onChangeNameNode={onChangeNameNode}
            handleChangeSettingsNode={handleChangeSettingsNode}
            onClose={() => {
              setVisibleDetail(false);
              setSelectedNode(null);
            }}
            selectedNode={selectedNode}
            categoryModelDetailList={categoryModelDetailList}
            hasPreProcessingBlock={
              !isEmpty(nodes.find((node) => node.id === "PreProcessing"))
            }
          />
          <div
            className="react-workflow-wrapper"
            ref={reactFlowWrapper}
            onClick={() => {
              setVisibleDetail(false);
              setSelectedNode(null);
            }}
          >
            <ReactFlow
              nodes={nodes.map((node) => ({ ...node, key: node.id }))}
              edges={edges.map((edge) => ({ ...edge, key: edge.id }))}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              connectionLineComponent={ConnectionLine}
              onConnect={onConnect}
              nodeTypes={nodeTypes}
              edgeTypes={edgeTypes}
              // for dnd
              onInit={setReactFlowInstance}
              onDrop={onDrop}
              onDragOver={onDragOver}
              //event
              onNodeClick={onNodeClick}
              onEdgeClick={onEdgeClick}
              defaultEdgeOptions={{ zIndex: 1 }}
              isValidConnection={(connection) => validateConnection(connection)}
              fitView
            >
              <Background style={{ backgroundColor: "#caccce2f" }} />
              <Controls position={"top-left"} />
              <MiniMap
                nodeStrokeWidth={3}
                zoomable
                pannable
                position={"bottom-left"}
              />
            </ReactFlow>
          </div>
        </ReactFlowProvider>
      </UserProvider>
    </div>
  );
};
