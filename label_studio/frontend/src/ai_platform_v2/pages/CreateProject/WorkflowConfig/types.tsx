export {
  WorkflowNodeType,
  DatasetRowStatusInEditor,
} from "../../../../consts/workflow";

export enum EnumWorkflowNodeSetting {
  AutoNextStep,
  CancelAction,
  Archive,
  RequireRejectReason,
  Redo,
  RequireRedoReason,
  CorrectResult,
  NoLabel,

  //review settings
  CancelReview,

  ManualConfirmation,

  ReadyToWork,
  Copilot,
  SimilarityCheck,
  ModelDetail,
  Threshold,
}

export interface INodeSettings {
  isAutoNextStep?: boolean;
  isCancel?: boolean;
  isArchive?: boolean;
  isRequireRejectReason?: boolean;
  isRedo?: boolean;
  isRequireRedoReason?: boolean;
  isReviewCancel?: boolean;
  [key: string]: any;
}

export const DEFAULT_NODE_SETTINGS: INodeSettings = {
  isAutoNextStep: false,
  isReadyToWork: false,
  isCancel: false,
  isArchive: false,
  isRequireRejectReason: false,
  isRedo: false,
  isRequireRedoReason: false,
  isNoLabel: false,
  isCorrectResult: false,
};

export const DEFAULT_PRE_PROCESSING_NEW_SETTINGS: any = {
  isReadyToWork: false,
  isCopilot: false,
  isSimilarityCheck: true,
  systemModelName: "",
  systemModelDetailId: "",
  apiEndpoint: "",
  threshold: 0.95,
};

export const DEFAULT_HUMAN_APPROVAL_SETTINGS: any = {
  isCancel: true,
  isRequireRejectReason: true,
};

export const DEFAULT_PRE_PROCESSING_COMPLETED_SETTINGS: any = {
  isManualConfirmation: false,
  isRedo: true,
  isRequireRedoReason: true,
};
