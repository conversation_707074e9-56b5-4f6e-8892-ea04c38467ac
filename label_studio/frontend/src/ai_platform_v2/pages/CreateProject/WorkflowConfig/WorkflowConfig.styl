.workflow-config
    height 100%

.workflow-template-list
    display: flex
    flex-direction: column
    gap: 12px

    &__body
        padding 0px 20px 0px 20px

        display: grid;
        flex-wrap: wrap;
        grid-template-columns: repeat(6, 203px);
        grid-auto-columns: 203px;
        column-gap: 50px;
        row-gap: 30px;

    &__item
        display: inline-flex;
        height: 201px;
        padding: 10px;
        flex-direction: column;
        justify-content: space-between;
        align-items: flex-start;
        flex-shrink: 0;
        border-radius: 10px;
        background: #FFF;
        cursor pointer

        /* Shadows/Gray Blue 30/15%/10b */
        box-shadow: 0px 2px 10px 0px rgba(38, 51, 77, 0.15);

    &__item:hover
        border: 1px solid var(--blue-blue, #3361FF);

    &__item-name
        display: flex;
        height: 20px;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        align-self: stretch;

        color: var(--gray-blue-grey-blue-40, #346);
        font-feature-settings: 'clig' off, 'liga' off;

        /* Medium/Medium 14 */
        
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 30px; /* 214.286% */


.preview-workflow
    display: flex
    flex-direction: column
    gap: 12px
    width 100%
    height 100%

    &__body
        width 100%
        height 96%