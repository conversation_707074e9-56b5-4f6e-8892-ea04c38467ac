import { But<PERSON> } from "@taureau/ui";
import { v4 as uuidv4 } from "uuid";
import { IconCustomize, ImgBasicWorkflow } from "../../../assets";
import { Block, Elem } from "../../../utils/bem";

export const TemplatesList = ({ onSelect }) => {
  const id_new = uuidv4();
  const id_labeling = uuidv4();
  const id_inreview = uuidv4();
  const id_completed = uuidv4();
  const basicWorkflow = {
    node: [
      {
        id: id_new,
        name: "New",
        type: "New",
        position: {
          x: 43.12066988376034,
          y: 63.665180725053915,
        },
        width: 270,
        height: 152,
      },
      {
        id: id_labeling,
        name: "Labeling",
        type: "Labeling",
        position: {
          x: 368.3955312296895,
          y: 63.2822507241159,
        },
        width: 240,
        height: 102,
        members: ["all"],
      },
      {
        id: id_inreview,
        name: "Reviewing",
        type: "Inreview",
        position: {
          x: 675.************,
          y: 64.81340631093155,
        },
        width: 240,
        height: 183,
        members: ["all"],
      },
      {
        id: id_completed,
        name: "Completed",
        type: "Completed",
        position: {
          x: 978.4178750812418,
          y: 69.62122665897493,
        },
        width: 240,
        height: 102,
        members: ["all"],
      },
    ],
    edge: [
      {
        zIndex: 1,
        source: id_new,
        sourceHandle: "new",
        target: id_labeling,
        targetHandle: "labeling-handle-target",
        type: "New",
        id: id_new.concat("_", id_labeling),
      },
      {
        zIndex: 1,
        source: id_labeling,
        sourceHandle: "labeling",
        target: id_inreview,
        targetHandle: "review-handle-target",
        type: "Labeling",
        id: id_labeling.concat("_", id_inreview),
      },
      {
        zIndex: 1,
        source: id_inreview,
        sourceHandle: "accept",
        target: id_completed,
        targetHandle: "complete-handle-target",
        type: "Accept",
        id: id_inreview.concat("_", id_completed),
      },
      {
        zIndex: 1,
        source: id_inreview,
        sourceHandle: "reject",
        target: id_labeling,
        targetHandle: "labeling-handle-target",
        type: "Reject",
        id: id_inreview.concat("_", id_labeling),
      },
    ],
    annotationBlockNode: {
      id: "Annotation",
      type: "Block",
      data: {
        label: "Annotation",
        id: "Annotation",
      },
      position: {
        x: 100,
        y: 100,
      },
      width: 1272,
      height: 298,
      style: {
        width: 1272,
        height: 298,
      },
    },
  };

  const templates = [
    {
      key: "basic-workflow",
      label: "Basic Workflow",
      value: basicWorkflow,
    },
  ];

  return (
    <Block name="workflow-template-list">
      <Elem name="header">
        <Button
          className="h-[30px] px-[10px] py-[5px] text-[12px]"
          theme="Primary"
          size="sm"
          iconLeft={<IconCustomize />}
          onClick={() => onSelect?.(templates[0].value)}
        >
          Custom
        </Button>
      </Elem>
      <Elem name="body">
        {templates.map((template) => (
          <Elem
            key={template.key}
            name="item"
            onClick={() => onSelect?.(template.value)}
          >
            <Elem name="item-preview">
              <ImgBasicWorkflow />
            </Elem>
            <Elem name="item-name">{template.label}</Elem>
          </Elem>
        ))}
      </Elem>
    </Block>
  );
};
