import { TemplatesList } from "./TemplatesList";
import "./WorkflowConfig.styl";
import { Block, Elem } from "../../../utils/bem";
import { useState } from "react";
import { PreviewWorkflow } from "./PreviewWorkflow";

export const WorkflowConfig = ({ projectState, onChange }) => {
  const [mode, setMode] = useState(
    projectState.annotationWorkFlow ? "view" : "list"
  );
  // const [template, setTemplate] = useState();

  const handleSelectTemplates = (value) => {
    // setTemplate(value);
    setMode("view");
    onChange({ annotationWorkFlow: JSON.stringify(value) });
  };

  const handleSetMode = () => {
    // setTemplate();
    setMode("list");
  };

  return (
    <Block name="workflow-config">
      {mode === "list" ? (
        <TemplatesList case="list" onSelect={handleSelectTemplates} />
      ) : (
        <PreviewWorkflow
          case="view"
          onSelect={handleSetMode}
          projectState={projectState}
          onChange={onChange}
        />
      )}
    </Block>
  );
};
