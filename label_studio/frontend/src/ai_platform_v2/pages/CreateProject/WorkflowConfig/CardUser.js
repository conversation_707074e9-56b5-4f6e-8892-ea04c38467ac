import React from "react";
import { randomColor } from "../../../../utils/helpers";
import { Avatar, Space, Typography } from "antd";

const { Paragraph } = Typography;

export default function CardUser({
  user,
  icon = null,
  onSelect = (id) => {},
  selected = [],
}) {
  const isSelected = selected.includes(user?.userId || "all");
  const bgColor = React.useMemo(() => randomColor(), [user?.userId]);
  const [error, setError] = React.useState(false);

  const name = (user?.lastName || "") + " " + (user?.firstName || "");

  const avatarChar = React.useMemo(() => {
    if (user?.firstName && user?.lastName) {
      return user.firstName.charAt(0) + user.lastName.charAt(0);
    } else if (user?.firstName) {
      //return first two char
      return user.firstName.slice(0, 2).toUpperCase();
    } else if (user?.lastName) {
      return user.lastName.slice(0, 2).toUpperCase();
    }
  }, [user?.firstName, user?.lastName]);
  const handleImageError = () => {
    // event.currentTarget.src = DEFAULT_SOURCE;
    setError(true);
  };

  return (
    <Space
      style={{
        backgroundColor: isSelected ? "#e0ebff" : "transparent",
        cursor: "pointer",
        width: "100%",
        borderRadius: 5,
        padding: "4px 8px",
      }}
      align={"center"}
      onClick={() => onSelect(user?.userId)}
    >
      <div className="flex">
        {icon ? (
          icon
        ) : !error ? (
          <Avatar
            style={{
              width: "30px",
              height: "30px",
              minWidth: "30px",
              minHeight: "30px",
            }}
            src={
              <img
                src={user?.avatar_url}
                alt="avatar"
                onError={handleImageError}
              />
            }
          />
        ) : (
          <Avatar style={{ width: "30px", height: "30px" }}>
            {avatarChar}
          </Avatar>
        )}
      </div>
      <div className="font-medium text-[12px]">
        <Paragraph
          className="col-content-text"
          style={{
            width: "100%",
            color: isSelected ? "#3361FF" : "#334466",
            fontWeight: 500,
            margin: 0,
          }}
          ellipsis={{ rows: 2, tooltip: name }}
        >
          {name}
        </Paragraph>
      </div>
    </Space>
  );
}
