import React from "react";
import { Input } from "antd";

interface NumericInputProps {
  className?: string;
  style?: React.CSSProperties;
  bordered: boolean;
  value: string;
  onChange: (value: string) => void;
  onBlur: () => void;
}

const NumericInput = (props: NumericInputProps) => {
  const { value, onChange, onBlur } = props;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value: inputValue } = e.target;
    const reg = /^-?\d*(\.\d*)?$/;

    if (reg.test(inputValue) || inputValue === "" || inputValue === "-") {
      const inputValueFixed = parseFloat(inputValue);

      if (0 <= inputValueFixed && inputValueFixed <= 1) {
        onChange(inputValue);
      } else if (inputValue === "") {
        onChange(inputValue);
      }
    }
  };

  // '.' at the end or only '-' in the input box.
  const handleBlur = () => {
    let valueTemp = value;

    if (value.charAt(value.length - 1) === "." || value === "-") {
      valueTemp = value.slice(0, -1);
    }
    onChange(valueTemp.replace(/0*(\d+)/, "$1"));
    onBlur?.();
  };

  return (
    <Input
      {...props}
      onChange={handleChange}
      onBlur={handleBlur}
      maxLength={4}
    />
  );
};

export default NumericInput;
