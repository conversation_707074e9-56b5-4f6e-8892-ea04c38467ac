import { memo, useCallback, useContext } from "react";

import Icon3dCubeScan from "@/ai_platform_v2/assets/Icons/Icon3dCubeScan";
import { InputNumberSetting } from "@/ai_platform_v2/component/InputNumberSetting/InputNumberSetting";
import { WorkflowContext } from "@/ai_platform_v2/pages/SettingsProject/SettingsProject";
import { ModalConfirmBig, Toggle } from "@taureau/ui";
import { Typography } from "antd";
import type { CheckboxChangeEvent } from "antd/lib/checkbox";
import classNames from "classnames";
import IconEmpty from "../Icon/IconEmpty";
import {
  EnumWorkflowNodeSetting,
  INodeSettings,
  WorkflowNodeType,
} from "../types";
import NumericInput from "./component/NumericInput";
import "./styles.scss";

const { Text } = Typography;

interface INodeOptions {
  label: string;
  value: EnumWorkflowNodeSetting;
  tooltip?: string;
  render?: (label: string, value: EnumWorkflowNodeSetting) => JSX.Element;
}
interface Props {
  handleChangeSettingsNode: (
    settings: INodeSettings,
    isChangeModelModeDeduplication?: boolean
  ) => void;
  defaultSettings?: INodeSettings;
  selectedNodeType: string;
  categoryModelDetailList?: any;
  isValidation?: boolean;
  hasPreProcessingBlock: boolean;
}
const CardSetting = ({
  handleChangeSettingsNode,
  defaultSettings,
  selectedNodeType,
  categoryModelDetailList,
  isValidation = true,
  hasPreProcessingBlock = false,
}: Props) => {
  const {
    isNewPreNew,
    isChangedPreNew,
    setIsChangedPreNew,
    isSelectedModel,
    setIsSelectedModel,
    imageDeduplication,
  } = useContext(WorkflowContext);

  const onChangeReadyToWorkNew = useCallback(
    (checked: boolean) => {
      // If setting preProcessing block, disable ready to work
      if (hasPreProcessingBlock) return;
      const settingsCopy: INodeSettings = {
        ...defaultSettings,
        isReadyToWork: checked,
      };

      if (checked) {
        settingsCopy.isAutoNextStep = false;
      }

      handleChangeSettingsNode(settingsCopy);
    },
    [defaultSettings, handleChangeSettingsNode, hasPreProcessingBlock]
  );

  const onChangeAutoNextStep = useCallback(
    (checked: boolean) => {
      const settingsCopy: INodeSettings = {
        ...defaultSettings,
        isAutoNextStep: checked,
      };

      if (checked) {
        settingsCopy.isReadyToWork = false;
      }

      handleChangeSettingsNode(settingsCopy);
    },
    [defaultSettings, handleChangeSettingsNode]
  );

  const onChangeReadyToWorkPreNew = useCallback(
    (checked: boolean) => {
      const settingsCopy: INodeSettings = {
        ...defaultSettings,
        isReadyToWork: checked,
      };

      if (checked) {
        settingsCopy.isCopilot = !checked;
      }

      handleChangeSettingsNode(settingsCopy);
    },
    [defaultSettings, handleChangeSettingsNode]
  );

  const onChangeCopilotPreNew = useCallback(
    (checked: boolean) => {
      const settingsCopy: INodeSettings = {
        ...defaultSettings,
        isCopilot: checked,
      };

      if (checked) {
        settingsCopy.isReadyToWork = !checked;
      }

      handleChangeSettingsNode(settingsCopy);
    },
    [defaultSettings, handleChangeSettingsNode]
  );

  const onChangeSwitchRequireRedoReason = useCallback(
    (checked: boolean) => {
      const settingsCopy: INodeSettings = {
        ...defaultSettings,
        isRequireRedoReason: checked,
      };

      handleChangeSettingsNode(settingsCopy);
    },
    [defaultSettings, handleChangeSettingsNode]
  );

  const onChangeSwitchSimilarityCheck = useCallback(
    (checked: boolean) => {
      const handleUpdateSimilarity = () => {
        const settingsCopy: INodeSettings = {
          ...defaultSettings,
          isSimilarityCheck: checked,
        };

        handleChangeSettingsNode(settingsCopy);
      };

      if (isNewPreNew) {
        ModalConfirmBig.warning({
          title: `${checked ? "Enable" : "Disable"} image deduplication?`,
          content: checked
            ? "If continue, all files haven't been transferred will be reset to NEW step to be deduplicated."
            : "If continue, any on-going deduplication process will be cancelled. All clusters created will be deleted, but rest assured, embedding results will be preserved.",
          okText: "Continue",
          cancelText: "Cancel",
          onOk: () => {
            handleUpdateSimilarity();
          },
        });
      } else {
        handleUpdateSimilarity();
      }
    },
    [isNewPreNew, defaultSettings, handleChangeSettingsNode]
  );

  const onChangeModelDetail = useCallback(
    (value: any) => {
      const handleUpdateModel = () => {
        const settingsCopy: INodeSettings = {
          ...defaultSettings,
          systemModelName: value.modelName,
          systemModelDetailId: value.id,
          apiEndpoint: value.apiEndpoint,
        };

        handleChangeSettingsNode(settingsCopy, true);
        setIsSelectedModel(true);
      };

      if (isNewPreNew && !isChangedPreNew) {
        setIsChangedPreNew(true);
        ModalConfirmBig.warning({
          title: "Change image deduplication model?",
          content:
            "If continue, any on-going deduplication process will be cancelled. All files in Data Manager will be generated new embedding result using new model",
          okText: "Continue",
          cancelText: "Cancel",
          onOk: () => {
            handleUpdateModel();
          },
        });
      } else {
        handleUpdateModel();
      }
    },
    [isNewPreNew, isChangedPreNew, defaultSettings, handleChangeSettingsNode]
  );

  const onChangeThreshold = useCallback(
    (value: any) => {
      const settingsCopy: INodeSettings = {
        ...defaultSettings,
        threshold: value,
      };

      handleChangeSettingsNode(settingsCopy);
    },
    [defaultSettings, handleChangeSettingsNode]
  );

  const onChangeCheck = useCallback(
    (e: CheckboxChangeEvent, type: EnumWorkflowNodeSetting) => {
      const value = e.target.checked;
      const settingsCopy = { ...defaultSettings };

      switch (type) {
        // case EnumWorkflowNodeSetting.AutoNextStep:
        //   settingsCopy.isAutoNextStep = value;
        //   break;
        case EnumWorkflowNodeSetting.CancelAction:
          settingsCopy.isCancel = value;
          break;
        case EnumWorkflowNodeSetting.Archive:
          settingsCopy.isArchive = value;
          break;
        case EnumWorkflowNodeSetting.RequireRejectReason:
          settingsCopy.isRequireRejectReason = value;
          break;
        case EnumWorkflowNodeSetting.Redo:
          settingsCopy.isRedo = value;
          break;
        case EnumWorkflowNodeSetting.CancelReview:
          settingsCopy.isReviewCancel = value;
          break;
        case EnumWorkflowNodeSetting.NoLabel:
          settingsCopy.isNoLabel = value;
          break;
        case EnumWorkflowNodeSetting.CorrectResult:
          settingsCopy.isCorrectResult = value;
          break;
        case EnumWorkflowNodeSetting.ManualConfirmation:
          settingsCopy.isManualConfirmation = value;
          break;
        case EnumWorkflowNodeSetting.ReadyToWork:
          settingsCopy.isReadyToWork = value;
          break;
        case EnumWorkflowNodeSetting.Copilot:
          settingsCopy.isCopilot = value;
          break;
        case EnumWorkflowNodeSetting.ModelDetail:
          settingsCopy.modelDetail = value;
          break;
        case EnumWorkflowNodeSetting.Threshold:
          settingsCopy.threshold = value;
          break;
        default:
          break;
      }

      handleChangeSettingsNode(settingsCopy);
    },
    [defaultSettings, handleChangeSettingsNode]
  );

  const getCheckedByType = (
    type: EnumWorkflowNodeSetting,
    settings: INodeSettings | undefined
  ) => {
    if (!settings) return false;

    switch (type) {
      case EnumWorkflowNodeSetting.AutoNextStep:
        return settings.isAutoNextStep;
      case EnumWorkflowNodeSetting.CancelAction:
        return settings.isCancel;
      case EnumWorkflowNodeSetting.Archive:
        return settings.isArchive;
      case EnumWorkflowNodeSetting.RequireRejectReason:
        return settings.isRequireRejectReason;
      case EnumWorkflowNodeSetting.Redo:
        return settings.isRedo;
      case EnumWorkflowNodeSetting.RequireRedoReason:
        return settings.isRequireRedoReason;
      case EnumWorkflowNodeSetting.CancelReview:
        return settings.isReviewCancel;
      case EnumWorkflowNodeSetting.NoLabel:
        return settings.isNoLabel;
      case EnumWorkflowNodeSetting.CorrectResult:
        return settings.isCorrectResult;
      case EnumWorkflowNodeSetting.ManualConfirmation:
        return settings.isManualConfirmation;
      case EnumWorkflowNodeSetting.ReadyToWork:
        return settings.isReadyToWork;
      case EnumWorkflowNodeSetting.Copilot:
        return settings.isCopilot;
      case EnumWorkflowNodeSetting.ModelDetail:
        return settings.modelDetail;
      case EnumWorkflowNodeSetting.Threshold:
        return settings.threshold;
      default:
        return false;
    }
  };

  const getOptionsByNodeType = (nodeType: string) => {
    const options: Array<INodeOptions> = [
      {
        label: "Archive data",
        value: EnumWorkflowNodeSetting.Archive,
        tooltip: "Allows member to archive data in this step.",
      },
    ];

    if (nodeType === WorkflowNodeType.New) {
      options.push({
        label: "Auto-next step",
        value: EnumWorkflowNodeSetting.AutoNextStep,
        tooltip:
          "Data is automatically transferred to the next step when member view it in Editor.",
        render: (
          label: string,
          value: EnumWorkflowNodeSetting.AutoNextStep,
          tooltip: string
        ) => {
          const isAutoNextStep = defaultSettings?.isAutoNextStep;

          return (
            <div className="settings-redo flex flex-col justify-between w-full py-[5px] border-[1px] border-solid border-black-3 rounded-[5px]">
              <div className="flex flex-row justify-between w-full pb-[10px] px-[10px] gap-[10px]">
                <div className="flex flex-col gap-[3px]">
                  <div className=" font-medium text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {label}
                  </div>
                  <div className=" font-normal text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {tooltip}
                  </div>
                </div>

                <Toggle
                  size="sm"
                  checked={isAutoNextStep}
                  onChange={(e) => onChangeAutoNextStep(e.target.checked)}
                />
              </div>
            </div>
          );
        },
      });

      options.push({
        label: "Ready to work",
        value: EnumWorkflowNodeSetting.ReadyToWork,
        tooltip:
          "Once imported, data will be transferred immediately to the step right after New",
        render: (
          label: string,
          value: EnumWorkflowNodeSetting.ReadyToWork,
          tooltip: string
        ) => {
          const isReadyToWork = defaultSettings?.isReadyToWork;

          return (
            <div className="settings-redo flex flex-col justify-between w-full py-[5px] border-[1px] border-solid border-black-3 rounded-[5px]">
              <div
                className={classNames(
                  "flex flex-row justify-between w-full pb-[10px] px-[10px] gap-[10px]",
                  {
                    "opacity-60": hasPreProcessingBlock,
                  }
                )}
              >
                <div className="flex flex-col gap-[3px]">
                  <div className=" font-medium text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {label}
                  </div>
                  <div className=" font-normal text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {tooltip}
                  </div>
                </div>

                <Toggle
                  size="sm"
                  checked={isReadyToWork}
                  onChange={(e) => {
                    onChangeReadyToWorkNew(e.target.checked);
                  }}
                  disabled={hasPreProcessingBlock}
                />
              </div>
            </div>
          );
        },
      });
    }

    if (nodeType === WorkflowNodeType.Labeling) {
      options.push({
        label: "Cancel action",
        value: EnumWorkflowNodeSetting.CancelAction,
        tooltip: "Allows member to discard submission in this step.",
      });

      options.push({
        label: "No label",
        value: EnumWorkflowNodeSetting.NoLabel,
        tooltip: "Allows member to submit image without label in this step.",
      });
      options.push({
        label: "Multi-version",
        value: EnumWorkflowNodeSetting.Redo,
        tooltip: "Creating and working on multiple versions independently.",
        render: (
          label: string,
          value: EnumWorkflowNodeSetting,
          tooltip: string
        ) => {
          const isCheckedRedo = getCheckedByType(value, defaultSettings);
          const isCheckedRequireReasonRedo = getCheckedByType(
            EnumWorkflowNodeSetting.RequireRedoReason,
            defaultSettings
          );

          return (
            <div className="settings-redo flex flex-col justify-between w-full py-[5px] border-[1px] border-solid border-black-3 rounded-[5px]">
              <div className="flex flex-row justify-between w-full pb-[10px] px-[10px] gap-[10px]">
                <div className="flex flex-col gap-[3px]">
                  <div className=" font-medium text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {label}
                  </div>
                  <div className=" font-normal text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {tooltip}
                  </div>
                </div>
              </div>

              <div className="flex flex-row justify-between w-full py-[10px] pl-[10px] gap-[10px] border-t-[1px] border-solid border-black-3">
                <div className="flex flex-col">
                  <div className=" font-medium text-[12px] text-gray-blue-40">
                    Required version
                  </div>
                  <div className=" font-normal text-[12px] text-gray-blue-40">
                    Number of versions need to be created and transferred to the
                    next step in flow.
                  </div>
                </div>

                <InputNumberSetting value={1} />
              </div>
            </div>
          );
        },
      });
    }

    if (nodeType === WorkflowNodeType.Inreview) {
      options.push({
        label: "Cancel action",
        value: EnumWorkflowNodeSetting.CancelReview,
        tooltip: "Allows member to discard submission in this step.",
      });
      options.push({
        label: "Reject reason",
        value: EnumWorkflowNodeSetting.RequireRejectReason,
        tooltip:
          "Require member to add in reasons when reject annotation results.",
      });
      options.push({
        label: "Correct result",
        value: EnumWorkflowNodeSetting.CorrectResult,
        tooltip: "Allows member to adjust annotation results when reviewing.",
      });
    }

    if (nodeType === WorkflowNodeType.Completed) {
      options.push({
        label: "Redo",
        value: EnumWorkflowNodeSetting.Redo,
        tooltip: "Allows member to send back to any previous steps.",
        render: (
          label: string,
          value: EnumWorkflowNodeSetting,
          tooltip: string
        ) => {
          const isCheckedRedo = getCheckedByType(value, defaultSettings);
          const isCheckedRequireReasonRedo = getCheckedByType(
            EnumWorkflowNodeSetting.RequireRedoReason,
            defaultSettings
          );

          return (
            <div className="settings-redo flex flex-col justify-between w-full py-[5px] border-[1px] border-solid border-black-3 rounded-[5px]">
              {/* <Checkbox
                checked={isCheckedRedo}
                onChange={(e) => onChangeCheck(e, value)}
              >
                {label}
              </Checkbox> */}
              <div className="flex flex-row justify-between w-full pb-[10px] px-[10px] gap-[10px]">
                <div className="flex flex-col gap-[3px]">
                  <div className=" font-medium text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {label}
                  </div>
                  <div className=" font-normal text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {tooltip}
                  </div>
                </div>

                <Toggle
                  size="sm"
                  checked={isCheckedRedo}
                  onChange={(e) => onChangeCheck(e, value)}
                />
              </div>

              {defaultSettings?.isRedo && (
                // <>
                //   <div className="flex gap-[3px]  font-normal text-[14px] text-gray-blue-40">
                //     Require reason
                //     <Tooltip
                //       overlayClassName="label-setting-workflow"
                //       title="Require member to add in reasons when send back annotation results."
                //       color="linear-gradient(0deg, rgba(51, 97, 255, 0.10) 0%, rgba(51, 97, 255, 0.10) 100%), #FFF"
                //     >
                //       <InfoCircleOutlined
                //         className="info-icon-custom"
                //         style={{ height: "10px", fontSize: "12px" }}
                //       />
                //     </Tooltip>
                //   </div>
                //   {/* <Checkbox
                //     size="sm"
                //     checked={isCheckedRequireReasonRedo}
                //     onChange={(e) =>
                //       onChangeSwitchRequireRedoReason(e.target.checked)
                //     }
                //   /> */}
                //   <Toggle
                //     size="sm"
                //     checked={isCheckedRequireReasonRedo}
                //     onChange={(e) =>
                //       onChangeSwitchRequireRedoReason(e.target.checked)
                //     }
                //   />
                // </>
                <div className="flex flex-row justify-between w-full py-[10px] px-[20px] gap-[10px] border-t-[1px] border-solid border-black-3">
                  <div className="flex flex-col">
                    <div className=" font-medium text-[12px] text-gray-blue-40">
                      Require reason
                    </div>
                    <div className=" font-normal text-[12px] text-gray-blue-40">
                      Require member to add in reasons when reject annotation
                      results.
                    </div>
                  </div>

                  <Toggle
                    size="sm"
                    checked={isCheckedRequireReasonRedo}
                    onChange={(e) =>
                      onChangeSwitchRequireRedoReason(e.target.checked)
                    }
                  />
                </div>
              )}
            </div>
          );
        },
      });
    }

    if (nodeType === WorkflowNodeType.PreProcessingNew) {
      options.shift();

      options.push({
        label: "Ready to work",
        value: EnumWorkflowNodeSetting.ReadyToWork,
        tooltip:
          "Once imported, data will be transferred immediately to the step right after New",
        render: (
          label: string,
          value: EnumWorkflowNodeSetting.ReadyToWork,
          tooltip: string
        ) => {
          const isReadyToWork = defaultSettings?.isReadyToWork;

          return (
            <div className="settings-redo flex flex-col justify-between w-full py-[5px] border-[1px] border-solid border-black-3 rounded-[5px]">
              <div className="flex flex-row justify-between w-full pb-[10px] px-[10px] gap-[10px]">
                <div className="flex flex-col gap-[3px]">
                  <div className=" font-medium text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {label}
                  </div>
                  <div className=" font-normal text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {tooltip}
                  </div>
                </div>

                <Toggle
                  size="sm"
                  checked={isReadyToWork}
                  onChange={(e) => onChangeReadyToWorkPreNew(e.target.checked)}
                />
              </div>
            </div>
          );
        },
      });

      options.push({
        label: "Copilot",
        value: EnumWorkflowNodeSetting.Copilot,
        tooltip: "Execute pre-processing operation with AI Assistance",
        render: (
          label: string,
          value: EnumWorkflowNodeSetting.Copilot,
          tooltip: string
        ) => {
          const isReadyToWork = defaultSettings?.isReadyToWork;
          const isCheckedCopilot = getCheckedByType(value, defaultSettings);
          const isCopilot = defaultSettings?.isCopilot;
          const isCheckedSimilarityCheck = defaultSettings?.isSimilarityCheck;
          const systemModelDetailId = isValidation
            ? isSelectedModel
              ? defaultSettings?.systemModelDetailId
              : defaultSettings?.systemModelDetailId && imageDeduplication?.id
            : defaultSettings?.systemModelDetailId;
          const threshold = defaultSettings?.threshold;

          return (
            <div className="settings-redo flex flex-col justify-between w-full py-[5px] border-[1px] border-solid border-black-3 rounded-[5px]">
              <div className="flex flex-row justify-between w-full pb-[10px] px-[10px] gap-[10px]">
                <div className="flex flex-col gap-[3px]">
                  <div className=" font-medium text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {label}
                  </div>
                  <div className=" font-normal text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {tooltip}
                  </div>
                </div>

                <Toggle
                  size="sm"
                  checked={isCheckedCopilot}
                  onChange={(e) => onChangeCopilotPreNew(e.target.checked)}
                />
              </div>

              {isCopilot && (
                <div className="flex flex-row justify-between w-full py-[10px] px-[20px] gap-[10px] border-t-[1px] border-solid border-black-3">
                  <div className="flex flex-col">
                    <div className=" font-medium text-[12px] text-gray-blue-40">
                      Image Deduplication
                    </div>
                    <div className=" font-normal text-[12px] text-gray-blue-40">
                      Using AI Model to find duplicate
                    </div>
                  </div>

                  <Toggle
                    size="sm"
                    checked={isCheckedSimilarityCheck}
                    onChange={(e) =>
                      onChangeSwitchSimilarityCheck(e.target.checked)
                    }
                  />
                </div>
              )}

              {isCopilot && isCheckedSimilarityCheck && (
                <div className="w-full flex flex-col gap-[4px] px-[4px]">
                  <div className="w-full flex flex-col gap-[4px] px-[12px] py-[4px]">
                    <div className="w-full flex justify-center text-[#334466] text-[12px] font-medium leading-[18px]">
                      Select model
                    </div>
                    <div className="w-full flex flex-col gap-[4px] pb-[4px]">
                      {categoryModelDetailList?.length ? (
                        <>
                          {categoryModelDetailList?.map((model: any) => (
                            <div
                              key={model.id}
                              className={`similarity-check-select-item ${
                                systemModelDetailId === model.id
                                  ? "similarity-check-select-item-checked"
                                  : ""
                              }`}
                              onClick={() => onChangeModelDetail(model)}
                            >
                              <Icon3dCubeScan />
                              <Text
                                style={{
                                  width: 200,
                                  color:
                                    systemModelDetailId === model.id
                                      ? "#3361ff"
                                      : "#346",
                                  lineHeight: "18px",
                                }}
                                ellipsis={{ tooltip: model.modelName }}
                              >
                                {model.modelName}
                              </Text>
                            </div>
                          ))}
                        </>
                      ) : (
                        <div className="w-full flex flex-col justify-center items-center">
                          <IconEmpty />
                          <div className="flex text-[#334466] text-[14px] font-medium leading-[21px]">
                            Empty!
                          </div>
                          <div className="flex text-[#62708C] text-[12px] leading-[18px]">
                            No published model now
                          </div>
                        </div>
                      )}
                    </div>
                    <div className="flex justify-between">
                      <div className="w-[196px] flex flex-col">
                        <div className="flex text-[#334466] text-[12px] font-medium leading-[18px]">
                          Threshold
                        </div>
                        <div className="flex text-[#334466] text-[11px] leading-[16px]">
                          Represents a cutoff point beyond which we consider two
                          images as similar.
                        </div>
                      </div>
                      <NumericInput
                        className="workflow-pre-new-input-threshold"
                        bordered={false}
                        value={threshold}
                        onChange={(value) => {
                          onChangeThreshold(value);
                        }}
                        onBlur={() => {
                          if (!threshold) {
                            onChangeThreshold("0.95");
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          );
        },
      });
    }

    if (nodeType === WorkflowNodeType.HumanApproval) {
      options.shift();

      options.push({
        label: "Cancel action",
        value: EnumWorkflowNodeSetting.CancelAction,
        tooltip: "Allows member to discard submission in this step.",
      });

      options.push({
        label: "Reject reason",
        value: EnumWorkflowNodeSetting.RequireRejectReason,
        tooltip:
          "Require member to add in reasons when reject annotation results.",
      });
    }

    if (nodeType === WorkflowNodeType.PreProcessingCompleted) {
      options.shift();
      options.push({
        label: "Manual confirmation",
        value: EnumWorkflowNodeSetting.ManualConfirmation,
        tooltip:
          "Requires data processor to approve AI results then transfer to main storage",
      });
    }

    return options;
  };

  return (
    <div className="flex flex-col w-full h-full gap-[10px]">
      {getOptionsByNodeType(selectedNodeType).map((option) => {
        return (
          <div key={option.value}>
            {typeof option.render === "function" ? (
              option.render(option.label, option.value, option.tooltip)
            ) : (
              <div className="flex flex-row justify-between w-full py-[5px] px-[10px] gap-[10px] border-[1px] border-solid border-black-3 rounded-[5px]">
                <div className="flex flex-col gap-[3px]">
                  <div className=" font-medium text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {option.label}
                  </div>
                  <div className=" font-normal text-[12px] text-gray-blue-40 flex gap-[3px]">
                    {option.tooltip}
                  </div>
                </div>

                {/* <div className=" font-normal text-[14px] text-gray-blue-40 flex gap-[3px]">
                  {option.label}
                  <Tooltip
                    overlayClassName="label-setting-workflow"
                    title={option.tooltip}
                    color="linear-gradient(0deg, rgba(51, 97, 255, 0.10) 0%, rgba(51, 97, 255, 0.10) 100%), #FFF"
                  >
                    <InfoCircleOutlined
                      className="info-icon-custom"
                      style={{ height: "10px", fontSize: "12px" }}
                    />
                  </Tooltip>
                </div> */}
                <Toggle
                  size="sm"
                  checked={getCheckedByType(option.value, defaultSettings)}
                  onChange={(e) => onChangeCheck(e, option.value)}
                />
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default memo(CardSetting);
