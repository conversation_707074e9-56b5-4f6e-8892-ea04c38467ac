.settings-redo {
  .content {
    margin-top: 5px;

    span {
      margin-right: 5px;
    }
  }
}
.info-icon-custom:hover {
  color: #3361ff;
}
.label-setting-workflow {
  .ant-tooltip-arrow {
    display: none;
  }

  .ant-tooltip-inner {
    border-radius: 5px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    color: #3361ff;
    bottom: 0px;
  }
}

.similarity-check-select-item {
  cursor: pointer;
  color: #334466;
  font-size: 12px;
  font-weight: 400;
  line-height: 18px;

  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px 8px 12px;
  border-radius: 20px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  background: #ffffff;

  transition: all 0.3s;

  svg path {
    transition: all 0.3s;
  }
}

.similarity-check-select-item-checked {
  color: #3361ff !important;
  background: rgba(51, 97, 255, 0.05) !important;

  svg path {
    fill: #3361ff !important;
  }
}

.workflow-pre-new-input-threshold {
  padding: 0px 5px !important;
  width: 40px !important;
  height: 18px !important;

  border-radius: 5px !important;
  background: var(--Gray-<PERSON>-<PERSON>-Blue-94, #e4e7f0) !important;

  color: var(--Gray-Blue-Grey-Blue-40, #346) !important;
  font-family: "Be Vietnam Pro" !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 18px !important;

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
  }

  &[type="number"] {
    -moz-appearance: textfield; /* Firefox */
  }
}
