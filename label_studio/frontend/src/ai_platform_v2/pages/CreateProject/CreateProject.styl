.create-project
    width: 100%;
    display: flex;
    padding: 6px 24px 24px 24px;
    flex-direction: column;
    align-items: center;
    gap: 16px;

    border-radius: 10px;
    background: #F7F8FA;

    /* Shadows/Gray Blue 10/10%/15b */
    box-shadow: 0px 2px 15px 0px rgba(13, 17, 26, 0.10);

    &__steps
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;                  
        gap: 10px;
        align-self: stretch;

        .taureau-steps
            max-width: 1048px;
            width: 100%;
            overflow: auto;

            &::-webkit-scrollbar
                width: 5.5px;
                height: 5.5px;

            &::-webkit-scrollbar-track 
                box-shadow: 0;

            &::-webkit-scrollbar-thumb 
                background: #62708c;
                border-radius: 10px;

    &__step-content
        display: flex;
        // margin: 0px 20px 20px 20px;
        flex-direction: column;
        align-items: center;
        align-self: stretch;
        
        border-radius 10px
        background #fff
        box-shadow 0px 2px 15px 0px rgba(13, 17, 26, 0.15)
        align-self stretch

    &__step-body
        display flex
        // padding 34px 50px
        width 100%
        min-height 330px
        height 60vh
        max-height 750px
        overflow auto

        &::-webkit-scrollbar {
            width: 7.5px;
            height: 7.5px;
        }

        &::-webkit-scrollbar-track {
            box-shadow: 0;
            /* border-radius: 10px; */
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgb(98 112 140)
            border-radius: 10px;
        }

    &__step-footer
        display: flex;
        width: 100%;
        padding: 10px 22px;
        align-items: center;
        gap: 10px;

        border: 1px solid #F7F8FA;
        background: #FAFBFC;
        border-bottom-left-radius: 10px
        border-bottom-right-radius: 10px

:global(.modal-create-project)
    :global(.ant-modal-header)
        margin-bottom: 0px

        :global(.ant-modal-title)
            color: var(--gray-blue-grey-blue-40, #346) !important;
            font-size: 18px !important;
            font-style: normal !important;
            font-weight: 500 !important;
            line-height: 30px !important;