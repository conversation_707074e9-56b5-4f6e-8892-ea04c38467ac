import { LimitWrapper } from "@/ai_platform_v2/component/Limit/Limit";
import Message from "@/ai_platform_v2/component/Message/Message";
import useFetchModule from "@/ai_platform_v2/pages/CreateProject/hooks/useFetchModule";
import useModulesStore from "@/ai_platform_v2/stores/modulesStore";
import { useAPI } from "@/providers/ApiProvider";
import { useCurrentUser } from "@/providers/CurrentUser";
import {
  useCheckPermission,
  usePermissions,
} from "@/providers/PermissionProvider";
import { Button, ModalConfirmBig } from "@taureau/ui";
import { Modal as ModalAnt } from "antd";
import { isEmpty } from "lodash";
import { useCallback, useEffect, useMemo, useReducer, useState } from "react";
import { Modal } from "../../component/Modal/Modal";
import { Steps } from "../../component/Steps/Steps";
import { Block, Elem } from "../../utils/bem";
import "./CreateProject.styl";
import {
  ACTIONS,
  CreateProjectReducer,
  DEFAULT_COLOR,
  initialState,
  PO_ROLE_ID,
  steps,
} from "./CreateProjectReducer";
import { AssignmentConfigProject } from "./ProjectStep/AssignmentConfigProject/AssignmentConfigProject";
import { Information } from "./ProjectStep/Information/Information";
import { Member } from "./ProjectStep/Member/Member";
import { ToolConfiguration } from "./ProjectStep/ToolConfiguration/ToolConfiguration";
import { Workflow } from "./ProjectStep/Workflow/Workflow";

interface Probs {
  open: boolean;
  currentModule?: any;
  onClose: () => void;
}

export const CreateProject = (props: Probs) => {
  const { open, currentModule, onClose } = props;
  const { user } = useCurrentUser();

  const { filterModulesHaveCreateProject } = useCheckPermission();

  const api = useAPI();
  const { fetchPermissions } = usePermissions();
  const { checkCanCreateProject } = useCurrentUser();
  const [watting, setWaitingStatus] = useState(false);

  const [projectState, dispatch] = useReducer(
    CreateProjectReducer,
    initialState
  );
  const {
    currentStep,
    moduleId,
    modulesList,
    name,
    description,
    color,
    memberRoles,
    labelConfig,
    annotationWorkFlow,
    preProccessConfig,
  } = projectState;

  const { fetchModule } = useFetchModule();
  const { updateModuleProjectById } = useModulesStore((state) => ({
    updateModuleProjectById: state.updateModuleProjectById,
  }));
  const handleCreateProject = useCallback(async () => {
    try {
      setWaitingStatus(true);

      const annotationNodes = JSON.parse(annotationWorkFlow);

      const preProcessingNodes = JSON.parse(preProccessConfig);

      const preProcessingNew = preProcessingNodes?.node?.find(
        (node: any) => node.type === "PreProcessingNew"
      );

      const annotationNew = annotationNodes?.node?.find(
        (node: any) => node.type === "New"
      );

      const preProcessingNewSetting = preProcessingNew?.settings;

      const res: any = await api.callApi("createProject", {
        params: { module: projectState.moduleId },
        body: {
          name: projectState.name?.trim(),
          description: projectState.description,
          color: projectState.color,
          status: projectState.status,
          memberRoles: projectState.memberRoles.map((member: any) => ({
            userId: member.id,
            roleId: member.role,
          })),
          labelConfig: projectState.labelConfig,
          annotationWorkFlow,
          preProccessConfig,
          classConfig: projectState.classConfig,

          assignmentConfig: {
            enableManualAssign: projectState.enableManualAssign,
            enableAutoAssign: projectState.enableAutoAssign,
            autoMode: projectState.autoMode,
            batchSize: projectState.batchSize,
            sortField: projectState.sortField,
            orderASC: projectState.orderASC,
            selftAssigning: projectState.selftAssigning,
            taskReminder: projectState.taskReminder,
            taskRemindHours: projectState.taskRemindHours,
          },
          preReadyToWork: !!preProcessingNewSetting?.isReadyToWork,
          readyToWork: !!annotationNew?.settings?.isReadyToWork,
        },
      });

      if (res?.success) {
        if (
          preProcessingNewSetting?.isReadyToWork === false &&
          preProcessingNewSetting?.isCopilot &&
          preProcessingNewSetting?.isSimilarityCheck &&
          preProcessingNewSetting?.systemModelDetailId
        ) {
          const body: any = {
            systemModelDetailId: preProcessingNewSetting.systemModelDetailId,
            systemModelName: preProcessingNewSetting.systemModelName,
            apiEndpoint: preProcessingNewSetting.apiEndpoint,
            threshold: preProcessingNewSetting.threshold,
          };

          await api.callApi("createApplyToWorkflow", {
            params: {
              pk: res.data.id,
              stepId: preProcessingNew.id,
            },
            body,
          });
        }

        onClose?.();

        Message.success({
          title: "Success",
          content: `${projectState.name} created successfully!`,
        });

        dispatch({ type: ACTIONS.RESET });

        fetchPermissions();
        checkCanCreateProject();

        const module = await fetchModule(projectState.moduleId);

        if (module) {
          updateModuleProjectById(module.id, module.project);
        }
      } else if (res?.errorCode === "Create.ResourceLimit") {
        ModalAnt.info({
          title: "",
          content: (
            <LimitWrapper content="Sorry about that, but you’ve exceeded the 5 free projects for your plan" />
          ),
          width: 700,
          className: "reached-resource-limit",
          icon: <></>,
          maskClosable: true,
          centered: true,
          closable: true,
          okText: null,
          okButtonProps: { style: { display: "none" } },
        });
      } else if (res?.errorCode || res?.message) {
        if (res?.errorCode === "Create.NameExists") {
          Message.error({
            title: "Error",
            content: "Project name already exists!",
          });
        } else {
          Message.error({
            title: res?.errorCode,
            content: res?.message,
          });
        }
      }
    } catch (error: any) {
      console.log("error", error?.message);
    } finally {
      setWaitingStatus(false);
    }
  }, [api, fetchPermissions, onClose, projectState, checkCanCreateProject]);

  const verifyProjectName = useCallback(
    async (targetStep = null) => {
      const res = await api.callApi("validateProjectName", {
        params: {
          moduleId: projectState.moduleId,
        },
        body: {
          projectName: projectState.name?.trim(),
        },
      });

      if (res?.success) {
        dispatch({
          type: ACTIONS.SET_CURRENT_STEP,
          payload: targetStep ?? currentStep + 1,
        });
      } else {
        Message.error({
          title: "Error",
          content: "Project name already exists!",
        });
      }
    },
    [projectState.name, projectState.moduleId, currentStep]
  );

  const verifyMemberRole = useCallback(async () => {
    const isHasPO = projectState?.memberRoles?.find(
      (member: any) => member.role === PO_ROLE_ID
    );

    if (isHasPO) {
      dispatch({
        type: ACTIONS.SET_CURRENT_STEP,
        payload: currentStep + 1,
      });
    } else {
      Message.error({
        title: "Error",
        content:
          "The project has to have at least one Project Owner. Please check.",
      });
    }
  }, [projectState?.memberRoles, currentStep]);

  const verifyWorkflow = useCallback(
    (hasReturn = false) => {
      if (!projectState?.annotationWorkFlow) {
        return false;
      }

      const annotationWorkFlow = JSON.parse(projectState?.annotationWorkFlow);

      let preProcessingWorkFlow = {
        node: [],
        edge: [],
        preProcessingBlockNode: {},
      };

      if (projectState?.preProccessConfig) {
        preProcessingWorkFlow = JSON.parse(projectState?.preProccessConfig);
      }

      let isFullConnnect = true;

      let existNew = false;
      let existCompleted = false;

      let existPreProcessingNew = false;
      let existPreProcessingCompleted = false;

      const { annotationBlockNode } = annotationWorkFlow;

      const { preProcessingBlockNode } = preProcessingWorkFlow;

      annotationWorkFlow?.node?.map((node, i) => {
        const totalEdges = annotationWorkFlow?.edge?.filter(
          (n) => n.source === node.id || n.target === node.id
        );

        switch (node.type) {
          case "New":
            existNew = true;
            if (totalEdges.length < 1) {
              isFullConnnect = false;
            }
            break;
          case "Labeling":
            if (totalEdges.length < 2) {
              isFullConnnect = false;
            }
            break;
          case "Inreview":
            if (totalEdges.length < 3) {
              isFullConnnect = false;
            }
            break;
          case "Completed":
            existCompleted = true;
            if (totalEdges.length < 1) {
              isFullConnnect = false;
            }
            break;
          default:
            break;
        }
      });

      if (preProcessingWorkFlow?.node?.length) {
        preProcessingWorkFlow?.node?.map((node, i) => {
          const totalEdges = preProcessingWorkFlow?.edge?.filter(
            (n) => n.source === node.id || n.target === node.id
          );

          switch (node.type) {
            case "PreProcessingNew":
              existPreProcessingNew = true;
              if (totalEdges.length < 1) {
                isFullConnnect = false;
              }
              break;
            case "HumanApproval":
              if (totalEdges.length < 2) {
                isFullConnnect = false;
              }
              break;
            case "PreProcessingCompleted":
              existPreProcessingCompleted = true;
              if (totalEdges.length < 1) {
                isFullConnnect = false;
              }
              break;
            default:
              break;
          }
        });
      }

      if (hasReturn) {
        return (
          !isEmpty(annotationBlockNode) &&
          !(
            !annotationWorkFlow?.node?.length ||
            (!isEmpty(preProcessingBlockNode) &&
              !preProcessingWorkFlow?.node?.length)
          ) &&
          existNew &&
          existCompleted &&
          !(
            !isEmpty(preProcessingBlockNode) &&
            (!existPreProcessingNew || !existPreProcessingCompleted)
          ) &&
          isFullConnnect
        );
      }

      if (isEmpty(annotationBlockNode)) {
        Message.error({
          content: "Cannot save workflow without Annotation block",
        });
        return false;
      }

      if (
        !annotationWorkFlow?.node?.length ||
        (!isEmpty(preProcessingBlockNode) &&
          !preProcessingWorkFlow?.node?.length)
      ) {
        Message.error({
          content: "Cannot save workflow with blank block",
        });
        return false;
      }

      if (!existNew) {
        Message.error({
          content: "Missing New step in Annotation workflow block",
        });
        return false;
      }

      if (!existCompleted) {
        Message.error({
          content: "Missing Completed step in Annotation workflow block",
        });
        return false;
      }

      if (!isEmpty(preProcessingBlockNode) && !existPreProcessingNew) {
        Message.error({
          content: "Missing New step in Pre-processing workflow block",
        });
        return false;
      }

      if (!isEmpty(preProcessingBlockNode) && !existPreProcessingCompleted) {
        Message.error({
          content: "Missing Completed step in Pre-processing workflow block",
        });
        return false;
      }

      if (!isFullConnnect) {
        Message.error({
          content: "Missing connection between 2 steps",
        });
        return false;
      }

      const preProcessingNew: any = preProcessingWorkFlow?.node?.find(
        (node: any) => node.type === "PreProcessingNew"
      );

      if (
        preProcessingNew !== undefined &&
        preProcessingNew?.settings?.isCopilot &&
        preProcessingNew?.settings?.isSimilarityCheck &&
        !preProcessingNew?.settings?.systemModelDetailId
      ) {
        Message.error({
          content: "Cannot save Pre-processing New step without model",
        });
        return false;
      }

      dispatch({
        type: ACTIONS.SET_CURRENT_STEP,
        payload: currentStep + 1,
      });
    },
    [
      currentStep,
      projectState?.annotationWorkFlow,
      projectState?.preProccessConfig,
    ]
  );

  const next = async (valueObject: any) => {
    if (currentStep === 1) {
      verifyProjectName();
    } else if (currentStep === 2) {
      verifyMemberRole();
    } else if (currentStep === 4) {
      verifyWorkflow();
    } else if (currentStep === 5) {
      await handleCreateProject();
    } else {
      dispatch({
        type: ACTIONS.SET_CURRENT_STEP,
        payload: currentStep + 1,
      });
    }
  };

  const handleChangeInformation = (valueObject: any) => {
    dispatch({
      type: ACTIONS.UPDATE_INFORMATION_STEP,
      payload: valueObject,
    });
  };

  const handleChangeMemberRoles = (value: any) => {
    value?.forEach((member: any) => {
      if (
        user?.role?.[0] !== "Admin" &&
        user?.role?.[0] !== "Super Admin" &&
        member.id === user?.uuid &&
        member.role !== PO_ROLE_ID
      ) {
        member.role = PO_ROLE_ID;
        Message.error({
          content: "Creator need to be granted Project Owner role",
        });
      }
    });

    dispatch({
      type: ACTIONS.SET_MEMBER_ROLES,
      payload: value,
    });
  };

  const handleChangeLabelConfig = (value: any) => {
    dispatch({
      type: ACTIONS.SET_LABEL_CONFIG,
      payload: value,
    });
  };

  const handleChangeWorkflow = (value: any) => {
    dispatch({
      type: ACTIONS.SET_WORKFLOW,
      payload: value,
    });
  };

  const handleChangeAssignmentConfig = (value: any) => {
    dispatch({
      type: ACTIONS.SET_ASSIGNMENT_CONFIG_STEP,
      payload: value,
    });
  };

  const onChangeCurrentStep = (valueObject: any) => {
    if (currentStep === 1) {
      verifyProjectName(valueObject);
    } else {
      dispatch({
        type: ACTIONS.SET_CURRENT_STEP,
        payload: valueObject,
      });
    }
  };

  const handleLoadRoleList = (value: any) => {
    dispatch({
      type: ACTIONS.LOAD_ROLE_LIST,
      payload: value,
    });
  };

  const stepBody = useMemo(() => {
    switch (currentStep) {
      case 1:
        return (
          <Information
            currentModule={currentModule}
            moduleId={moduleId}
            modulesList={modulesList}
            projectName={name}
            projectDescription={description}
            projectColor={color}
            onChange={handleChangeInformation}
            onNextStep={next}
            style={{ margin: "16px 20px" }}
          />
        );
      case 2:
        return (
          <Member
            onSetRoleList={handleLoadRoleList}
            projectName={name}
            members={memberRoles}
            onChange={handleChangeMemberRoles}
            style={{ width: "100%", margin: 16, maxWidth: 944 }}
          />
        );
      case 3:
        return (
          <ToolConfiguration
            labelConfig={labelConfig}
            onChange={handleChangeLabelConfig}
            style={{ padding: 12 }}
          />
        );
      case 4:
        return (
          <Workflow
            projectState={projectState}
            onChange={handleChangeWorkflow}
          />
        );
      case 5:
        return (
          <AssignmentConfigProject
            projectState={projectState}
            onChange={handleChangeAssignmentConfig}
          />
        );
    }
  }, [currentStep, projectState]);

  const isDisabledContinue = useMemo(() => {
    const currentStep = projectState.currentStep;

    if (currentStep === 1) {
      return !(projectState.name && projectState.moduleId);
    } else if (currentStep === 2) {
      return !(projectState.memberRoles.length > 0);
    } else if (currentStep === 3) {
      return !projectState.labelConfig;
    } else if (currentStep === 4) {
      // return !verifyWorkflow(true);
      return !projectState.annotationWorkFlow;
    } else if (currentStep === 5) {
      return !(
        projectState.name &&
        projectState.memberRoles.length > 0 &&
        projectState.labelConfig &&
        projectState.annotationWorkFlow
      );
    }

    return false;
  }, [projectState]);

  const stepsFinal = useMemo(() => {
    const disabledSteps = {
      step1: !(projectState.name && projectState.moduleId),
      step2: !(
        projectState.memberRoles.length > 0 &&
        !!projectState?.memberRoles?.find(
          (member: any) => member.role === PO_ROLE_ID
        )
      ),
      step3: !projectState.labelConfig,
      step4: !verifyWorkflow(true),
      step5: !(
        projectState.name &&
        projectState.memberRoles.length > 0 &&
        projectState.labelConfig &&
        verifyWorkflow(true)
      ),
    };

    return steps.map((step) => {
      switch (step.key) {
        // case 1:
        //   step.disabled = disabledSteps.step1;
        //   break;
        case 2:
          step.disabled = disabledSteps.step1;
          break;
        case 3:
          step.disabled = disabledSteps.step1 || disabledSteps.step2;
          break;
        case 4:
          step.disabled =
            disabledSteps.step1 || disabledSteps.step2 || disabledSteps.step3;
          break;
        case 5:
          step.disabled = disabledSteps.step5;
          break;
        // default:
        //   console.log(`Sorry, we are out of ${expr}.`);
      }

      return step;
    });
  }, [projectState]);

  const fetchModulesList = useCallback(async () => {
    const res = await api.callApi("modules", {
      params: { status: "Active", isDelete: false, page: 1, pageSize: 99999 },
    });

    if (res?.success) {
      dispatch({
        type: ACTIONS.LOAD_MODULES_LIST,
        payload: filterModulesHaveCreateProject(res.items).map(
          (module: any) => ({
            value: module.id,
            label: module.name,
          })
        ),
      });
    }
  }, [filterModulesHaveCreateProject]);

  useEffect(() => {
    if (open) {
      if (currentModule) {
        dispatch({
          type: ACTIONS.SET_MODULE_ID,
          payload: currentModule,
        });
      } else {
        fetchModulesList();
      }
    }
  }, [open]);

  const isChanged = useMemo(
    () =>
      (currentModule
        ? projectState.moduleId !== currentModule
        : projectState.moduleId) ||
      projectState.name?.trim() ||
      projectState.description?.trim() ||
      projectState.color !== DEFAULT_COLOR ||
      projectState.memberRoles?.length ||
      projectState.labelConfig ||
      projectState.annotationWorkFlow ||
      projectState.classConfig !== "<TORUS_TEMPLATE>\n</TORUS_TEMPLATE>" ||
      projectState.enableManualAssign ||
      projectState.enableAutoAssign ||
      projectState.autoMode !== "InTurn" ||
      projectState.batchSize !== 1 ||
      projectState.sortField !== "Priority" ||
      projectState.orderASC ||
      projectState.selftAssigning ||
      projectState.taskReminder ||
      projectState.taskRemindHours,
    [currentModule, projectState]
  );

  const handleCancelModal = useCallback(() => {
    if (isChanged) {
      ModalConfirmBig.warning({
        title: "Wanna leave?",
        content: "If you continue, changes you made may not be saved",
        okText: "Leave",
        cancelText: "Cancel",
        onOk: () => {
          dispatch({ type: ACTIONS.RESET });
          onClose?.();
        },
      });
    } else {
      dispatch({ type: ACTIONS.RESET });
      onClose?.();
    }
  }, [isChanged, onClose]);

  return (
    <div className="font-sans">
      <Modal
        className="modal-create-project"
        title="Create New Project"
        centered
        open={open}
        // onOk={() => setOpen(false)}
        onCancel={handleCancelModal}
        footer={null}
        width="84%"
      >
        <Block name="create-project">
          <Elem name="steps">
            <Steps
              current={currentStep}
              items={stepsFinal}
              onChange={onChangeCurrentStep}
            />
          </Elem>
          <Elem name="step-content">
            <Elem name="step-body">{stepBody}</Elem>
            <Elem
              name="step-footer"
              style={{
                justifyContent:
                  currentStep === 3 ? "space-between" : "flex-end",
              }}
            >
              {currentStep === 3 && (
                <div className="flex items-center gap-[5px]">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12ZM13 7C13 7.55228 12.5523 8 12 8C11.4477 8 11 7.55228 11 7C11 6.44772 11.4477 6 12 6C12.5523 6 13 6.44772 13 7ZM11 9.25C10.5858 9.25 10.25 9.58579 10.25 10C10.25 10.4142 10.5858 10.75 11 10.75H11.25V17C11.25 17.4142 11.5858 17.75 12 17.75C12.4142 17.75 12.75 17.4142 12.75 17V10C12.75 9.58579 12.4142 9.25 12 9.25H11Z"
                      fill="#334466"
                    />
                  </svg>
                  <div className="text-gray-blue-40 text-[12px]  font-medium leading-[20px]">
                    Select more than one tool to generate mixed template.
                  </div>
                </div>
              )}
              <Button
                className={`w-[120px] h-[30px] px-[15px] py-[10px] ${
                  isDisabledContinue ? "button-disabled" : ""
                }`}
                size="sm"
                corner="Rounded"
                theme="Primary"
                onClick={next}
                disabled={isDisabledContinue}
                loading={watting}
              >
                {currentStep < 5 ? "Continue" : "Save"}
              </Button>
            </Elem>
          </Elem>
        </Block>
      </Modal>
    </div>
  );
};
