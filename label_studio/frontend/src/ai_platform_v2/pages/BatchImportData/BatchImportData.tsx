import IconMinus from "@/ai_platform_v2/assets/Icons/IconMinus";
import { InputLabel } from "@/ai_platform_v2/component/InputLabel/InputLabel";
import Message from "@/ai_platform_v2/component/Message/Message";
import RouteLeavingProject from "@/ai_platform_v2/component/RouteLeavingProject";
import { useBatchImportData } from "@/ai_platform_v2/providers/BatchImportDataProvider";
import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import { useProject } from "@/providers/ProjectProvider";
import { Button, ModalConfirmBig } from "@taureau/ui";
import { useCallback, useEffect, useMemo, useState } from "react";
import { Modal } from "../../component/Modal/Modal";
import "./BatchImportData.styl";
import { ACTIONS, fileStatusOptions } from "./BatchImportDataReducer";
import { checkFileTypeOtherZip } from "./const/validate";
import { DropzoneDes } from "./DropzoneDes/DropzoneDes";
import { ExportFailedList, FileList } from "./FileList/FileList";
import { Upload } from "./Upload/Upload";

interface Props {
  open?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  fetchDataSets?: () => void;
}

export const BatchImportData = (props: Props) => {
  const { open, onOpen, onClose, fetchDataSets } = props;
  const [hovered, setHovered] = useState<boolean>(false);
  const { project } = useProject();
  const batchImportData = useBatchImportData();

  const {
    isZipProgress,
    fileTree,
    batchImportState,
    filesSelected,
    filesCompletedLength,
    filesLength,
    filesLengthMess,
    filesSelectedLength,
    filesSelectedLengthMess,
    dispatch,
    handleSaveFiles,
    handleRemoveFiles,
    handleSelectFiles,
    handleAddInvalidFiles,
    handleMinusImportModal,
    handleChangeBatchName,
    handleUpdateBatchData,
    fetchProjectDataTypes,
    onUploadFiles,
    pause,
    handlePause,
    handleClear: setClear,
    handleCancelUploading,
    handleCloseImport,

    isContinue,
    setIsContinue,

    setFuncCloseModal,
    isBatchNameAlready,
    rejectUploadingProgress,
  } = batchImportData;
  const {
    batchData,
    batchName,
    currentProject,
    dataTypes,
    waiting,
    progressing,
    invalidFiles,
    files,
    uploadZipTasks,
    filesInProgress,
  } = batchImportState;

  const [isHaveFiles, setIsHaveFiles] = useState(false);
  const [isRefetch, setIsRefetch] = useState(false);

  const handleUpload = useCallback(() => {
    if (pause) {
      return;
    }

    onUploadFiles();
  }, [pause, onUploadFiles]);

  const handleRetry = useCallback(() => {
    onUploadFiles(true);
  }, [onUploadFiles]);

  const dataTypesFinal = useMemo(() => dataTypes, [dataTypes]);

  const uploadButtonContent = useMemo(() => {
    let countProgress = 0;
    let sumProgress = 0;
    const totalFiles = files?.length ?? 0;
    const findFilesUploadingLength = files?.filter((file: any) => {
      countProgress += file?.progress ? 1 : 0;
      sumProgress += file?.progress ?? 0;
      return (
        file.status === fileStatusOptions.save ||
        file.status === fileStatusOptions.uploading ||
        file.status === fileStatusOptions.paused
      );
    })?.length;

    const findFilesUploadingMess =
      findFilesUploadingLength > 1
        ? `${findFilesUploadingLength} files`
        : `${findFilesUploadingLength} file`;

    const percentZip =
      findFilesUploadingLength === 0
        ? 0
        : sumProgress / (countProgress === 0 ? 1 : countProgress);

    const percent =
      totalFiles === 0
        ? 0
        : Math.floor(
            ((totalFiles - findFilesUploadingLength) * 100 + percentZip) /
              totalFiles
          );

    return pause
      ? "Paused"
      : findFilesUploadingLength
        ? progressing
          ? `Uploading ${percent}%`
          : `Upload ${findFilesUploadingLength ? findFilesUploadingMess : ""}`
        : "Upload";
  }, [progressing, pause, files]);

  const removeButtonContent = useMemo(() => {
    return `Delete ${
      filesSelectedLength > 1
        ? `${filesSelectedLength} files`
        : `${filesSelectedLength} file`
    }`;
  }, [filesSelectedLength]);

  const hideMinusModalButton = useMemo(() => {
    const uploadedFileLength = files?.filter(
      (file: any) =>
        file.status === fileStatusOptions.uploading ||
        file.status === fileStatusOptions.paused
    )?.length;

    return !uploadedFileLength;
  }, [files]);

  const isLeaveProject = useMemo(() => {
    const uploadedFileLength = files?.filter(
      (file: any) => file.status !== fileStatusOptions.save
    )?.length;

    return !uploadedFileLength;
  }, [files]);

  const handleClickDelete = useCallback(() => {
    handleRemoveFiles(filesSelected.map((file: any) => file.file));
  }, [filesSelected]);

  const checkIsRefetch = useMemo(() => {
    const fileCompleted = files?.find(
      (file: any) =>
        file.status === fileStatusOptions.completed ||
        (file.status === fileStatusOptions.failed &&
          file.file.name.includes("zip"))
    );

    if (fileCompleted && !isRefetch) {
      setIsRefetch(true);
    }
  }, [isRefetch, files]);

  const checkIsHaveFiles = useMemo(() => {
    if (files?.length && !isHaveFiles) {
      setIsHaveFiles(true);
    }
  }, [isHaveFiles, files]);

  const handleCloseModal = useCallback(
    (isShowConfirm = true) => {
      // if (
      //   files.filter((file: any) => {
      //     const fileNameSplit = file.file.name.split(".");
      //     const fileType =
      //       fileNameSplit?.[fileNameSplit?.length - 1].toLowerCase();

      //     return (
      //       (file.status === fileStatusOptions.uploading ||
      //         file.status === fileStatusOptions.paused) &&
      //       fileType === "zip"
      //     );
      //   }).length !== uploadZipTasks.length
      // ) {
      //   return;
      // }

      const filesLengthAll = files?.length;
      const filesCompletedLengthAll = files?.filter(
        (file: any) =>
          file.status === fileStatusOptions.completed ||
          file.status === fileStatusOptions.failed
      ).length;

      if (isHaveFiles) {
        if (filesLengthAll !== filesCompletedLengthAll) {
          if (isShowConfirm) {
            ModalConfirmBig.warning({
              title: "Stop import progress?",
              content: `If you continue, the current import progress will be closed and stopped.`,
              onOk: () => {
                handleCancelUploading(true);

                isRefetch && fetchDataSets?.();
                onClose?.();
                handleCloseImport();
              },
            });
          } else {
            handleCancelUploading(true);

            isRefetch && fetchDataSets?.();
            onClose?.();
            handleCloseImport();
          }
        } else {
          isRefetch && fetchDataSets?.();
          onClose?.();
          handleCloseImport();
        }
      } else {
        onClose?.();
        handleCloseImport();
      }
    },
    [
      files,
      filesLength,
      filesCompletedLength,
      handleCloseImport,
      uploadZipTasks?.toString(),

      isRefetch,
      isHaveFiles,
    ]
  );

  useEffect(() => {
    setFuncCloseModal(() => handleCloseModal);
  }, [
    files,
    filesLength,
    filesCompletedLength,
    handleCloseImport,
    uploadZipTasks?.toString(),

    isRefetch,
    isHaveFiles,
  ]);

  const failedFiles = useMemo(() => {
    const failedList = files
      .filter((file: any) => file.status === fileStatusOptions.failed)
      .map((file: any) => ({
        fileName: file.file.name,
        imagePath: file.fullPath,
        status: "Failed",
      }));

    const invalidList = invalidFiles.map((file: any) => ({
      ...file,
      status: "Invalid",
    }));

    return [...invalidList, ...failedList];
  }, [files, invalidFiles]);

  const labelFailedFiles = useMemo(() => {
    const failedList = files
      .filter((file: any) => file.status === fileStatusOptions.failed)
      .map((file: any) => ({
        fileName: file.file.name,
        imagePath: file.fullPath,
        status: "Failed",
      }));

    return failedList.length ? "Export failed list" : "Export invalid list";
  }, [files, invalidFiles]);

  const disabledUploadButton = useMemo(
    () =>
      !(batchData?.id || batchName?.trim()) ||
      (filesCompletedLength === filesLength && !pause),
    [batchData, batchName, filesCompletedLength, filesLength, pause]
  );

  const showClearButton = useMemo(
    () => (files?.length ? !progressing || pause : false),
    [files, progressing, pause]
  );

  const handleClear = useCallback(() => {
    dispatch({
      type: ACTIONS.REMOVE_ALL,
    });
    setClear(true);
    handlePause?.(false);
  }, [handlePause]);

  useEffect(() => {
    if (isContinue) {
      onUploadFiles();
      setIsContinue(false);
    }
  }, [isContinue]);

  useEffect(async () => {
    if (currentProject?.id) {
      await fetchProjectDataTypes();
    }
  }, [currentProject?.id, fetchProjectDataTypes]);

  useEffect(async () => {
    if (open && project && !currentProject?.id) {
      await dispatch({
        type: ACTIONS.LOAD_CURRENT_PROJECT,
        payload: project,
      });

      setIsHaveFiles(false);
      setIsRefetch(false);
    }
  }, [open, project, currentProject]);

  const uploadRaw = (
    <Upload
      id="batch-file-input"
      className={`${files?.length ? "is-files" : ""}`}
      multiple={true}
      loading={waiting}
      dataTypes={dataTypesFinal}
      onInvalid={(invalidFiles: any) =>
        handleAddInvalidFiles(
          invalidFiles.map((file: any) => ({
            fileName: file.file.name,
            imagePath: file.fullPath,
          }))
        )
      }
      onHover={(value) => setHovered(value)}
      sendFiles={async (value) => {
        handleSaveFiles(value);
      }}
    >
      <DropzoneDes dataTypes={dataTypesFinal} hovered={hovered} />
    </Upload>
  );

  return (
    <>
      <RouteLeavingProject
        isShowConfirm={!hideMinusModalButton}
        when={!isLeaveProject}
        handleAfterUnload={() => handleCloseModal(false)}
        handleAfterSuccess={
          hideMinusModalButton ? () => handleCloseModal(false) : undefined
        }
      />
      <Modal
        className="modal-import-data"
        title={
          <div>
            {batchData?.id ? (
              batchData?.batchName ?? batchName
            ) : (
              <InputLabel
                classNameInput="ml-[10px]"
                placeholder="Enter batch name here"
                errorText="Batch name already exists!"
                isError={isBatchNameAlready}
                value={batchName}
                onChange={(e) => handleChangeBatchName(e.target.value)}
              />
            )}
            {!hideMinusModalButton && (
              <button
                type="button"
                aria-label="Close"
                className="ant-modal-close taureau-minus-button"
                style={{ top: 2, right: 33 }}
                onClick={handleMinusImportModal}
              >
                <span className="ant-modal-close-x">
                  <span
                    role="img"
                    aria-label="close"
                    className="anticon anticon-close ant-modal-close-icon"
                  >
                    <IconMinus />
                  </span>
                </span>
              </button>
            )}
          </div>
        }
        centered
        open={open}
        // onOk={() => setOpen(false)}
        onCancel={() => handleCloseModal()}
        footer={null}
        // width={
        //   !currentPreProcessed && currentTab === "preProcessed"
        //     ? "997px"
        //     : "70%"
        // }
        width="997px"
      >
        <Block name="import-data">
          <Elem name="content">
            <Elem name="header"></Elem>

            <Elem name="body-out">
              <Elem
                name="body"
                // style={{
                //   gap:
                //     filesByTab?.length || currentTab === "preProcessed"
                //       ? 10
                //       : 0,
                // }}
              >
                {uploadRaw}
                <Elem
                  name="body-detail"
                  className={`${filesLength ? "show-body-detail" : ""}`}
                >
                  <FileList
                    importState={batchImportState}
                    files={fileTree}
                    filesLength={filesLength}
                    filesLengthMess={filesLengthMess}
                    filesSelectedLength={filesSelectedLength}
                    filesSelectedLengthMess={filesSelectedLengthMess}
                    onRemove={handleRemoveFiles}
                    onSelect={handleSelectFiles}
                  />
                </Elem>
              </Elem>
              {!!failedFiles.length && (
                <ExportFailedList
                  label={labelFailedFiles}
                  style={{ marginTop: files?.length ? 0 : 10 }}
                  files={failedFiles}
                  currentProject={currentProject}
                />
              )}
            </Elem>
          </Elem>

          <Elem name="footer">
            {!!filesSelectedLength && (
              <Button
                className="text-[#E62E2E]"
                size="sm"
                corner="Rounded"
                theme="Light"
                onClick={handleClickDelete}
                // disabled={!importState.files.length}
                // loading={watting}
              >
                {removeButtonContent}
              </Button>
            )}
            {!!files.filter(
              (file: any) => file.status === fileStatusOptions.failed
            ).length && (
              <Button
                className="text-[#6B7A99]"
                size="sm"
                corner="Rounded"
                theme="Light"
                onClick={handleRetry}
                // disabled={!importState.files.length}
                // loading={watting}
              >
                Retry
              </Button>
            )}
            {/* {(progressing[currentTab] || pause[currentTab]) && ( */}
            {(progressing || pause) &&
              files?.find(
                (file: any) =>
                  checkFileTypeOtherZip(file) &&
                  file.status !== fileStatusOptions.save
              ) && (
                <Button
                  className="text-[#6B7A99]"
                  size="sm"
                  corner="Rounded"
                  theme="Light"
                  onClick={() => {
                    if (isZipProgress && !pause) {
                      Message.error({
                        content: "Cannot pause zip file upload process.",
                      });
                      return;
                    }

                    if (pause) {
                      setIsContinue(true);
                      dispatch({ type: ACTIONS.SAVE_PAUSED });
                    } else {
                      dispatch({ type: ACTIONS.PAUSED_ALL });
                      rejectUploadingProgress.current(
                        new Error("Upload Process was Cancelled form Outside")
                      );
                    }
                    handlePause?.(!pause);
                  }}
                  // disabled={isDisabledContinueBtn}
                  // loading={watting}
                >
                  {pause ? "Continue" : "Pause"}
                </Button>
              )}

            {showClearButton && (
              <Button
                className="text-[#6B7A99]"
                size="sm"
                corner="Rounded"
                theme="Light"
                onClick={handleClear}
              >
                Clear
              </Button>
            )}
            <Button
              className={`${disabledUploadButton ? "button-disabled" : ""} ${
                progressing ? "button-import-loading" : ""
              } min-w-[114px] min-h-[40px] px-[15px] py-[5px]`}
              size="sm"
              corner="Rounded"
              theme="Primary"
              onClick={handleUpload}
              disabled={disabledUploadButton}
              // loading={progressing[currentTab]}
            >
              {uploadButtonContent}
            </Button>
          </Elem>
        </Block>
      </Modal>
    </>
  );
};
