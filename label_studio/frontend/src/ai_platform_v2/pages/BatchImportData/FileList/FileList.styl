.file-list-length
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    background: #FFF;
    height: 0
    opacity: 0
    
    &__content
        display: flex;
        width: 550px;
        align-items: center;
        gap: 10px;

    &__file-length
        color: var(--blue-blue, #3361FF);
        text-align: center;
        
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 25px;
        cursor pointer

    &__file-selected-length
        color: var(--gray-blue-grey-blue-40, #346);
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;

        /* Regular/Regular 13 */
        
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 25px; /* 192.308% */

    &__folder-action
        display: flex;
        align-items: center;
        gap: 6px;
        

.file-list
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    height: 0
    opacity: 0
    transition: all .6s ease-out 

    &__content
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        align-self: stretch;

    &__footer
        display: flex;
        justify-content: flex-end;
        align-items: center;
        align-self: stretch;

.export-failed-list
    display: flex
    width: 100%
    justify-content: flex-end

    &__content
        cursor pointer

    &__failed-list
        color: var(--blue-blue, #3361FF);
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;

        /* Regular/Regular 13 */
        
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 25px; /* 192.308% */
        
.show-file-list
    min-height: 82px
    height: 100%
    opacity: 1
    max-height: fit-content
    overflow: auto
    transition: all .6s ease-out

    &::-webkit-scrollbar {
        width: 7.5px;
        height: 7.5px;
    }

    &::-webkit-scrollbar-track {
        box-shadow: 0;
        /* border-radius: 10px; */
    }

    &::-webkit-scrollbar-thumb {
        background-color: rgb(98 112 140)
        border-radius: 10px;
    }

.show-file-list-length
    height: fit-content
    opacity: 1
    transition: all .6s ease-out