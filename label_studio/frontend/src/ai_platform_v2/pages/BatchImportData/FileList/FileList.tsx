import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import { FileItem } from "../FileItem/FileItem";
import "./FileList.styl";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { CSVLink } from "react-csv";
import { ACTIONS, fileStatusOptions } from "../BatchImportDataReducer";
import { useImportData } from "@/ai_platform_v2/providers/ImportDataProvider";
import { getYYYYMMDDNow } from "@/ai_platform_v2/utils/helpers";
import { csvHeaders } from "@/ai_platform_v2/utils/const";
import { Tooltip } from "antd";
import { useBatchImportData } from "@/ai_platform_v2/providers/BatchImportDataProvider";
// import _, { slice } from "lodash";

interface FileListLengthProps {
  className?: any;
  filesLength?: number;
  filesLengthMess?: string;
  filesSelectedLength?: number;
  filesSelectedLengthMess?: string;
  disabledSelectAll?: boolean;
  isSelectAll?: boolean;
  disabledFolder?: boolean;
  onRemainFolder?: (checked: boolean) => void;
  onSelectFolderPath?: (pathName: any) => void;
  onSelectAllFiles?: () => void;
}

export const FileListLength = (props: FileListLengthProps) => {
  const {
    className,
    filesLength = 0,
    filesLengthMess,
    filesSelectedLength = 0,
    filesSelectedLengthMess,
    disabledSelectAll = false,
    isSelectAll = false,

    onSelectAllFiles,
  } = props;

  const showFilesLength = useMemo(() => {
    return filesLength === filesSelectedLength
      ? filesSelectedLengthMess
      : filesLengthMess;
  }, [
    filesLength,
    filesSelectedLength,
    filesLengthMess,
    filesSelectedLengthMess,
  ]);

  const showSelectedFilesLength = useMemo(() => {
    return filesLength === filesSelectedLength
      ? ""
      : filesSelectedLength > 0
        ? filesSelectedLengthMess
        : "";
  }, [filesLength, filesSelectedLength, filesSelectedLengthMess]);

  const tooltipContent = useMemo(
    () => (isSelectAll ? "Unselect all" : "Select all"),
    [isSelectAll]
  );

  return (
    <Block name="file-list-length" className={className}>
      <Elem name="content">
        {disabledSelectAll ? (
          <Elem name="file-length">{showFilesLength}</Elem>
        ) : (
          <Tooltip overlayClassName="import-tooltip" title={tooltipContent}>
            <Elem name="file-length" onClick={() => onSelectAllFiles?.()}>
              {showFilesLength}
            </Elem>
          </Tooltip>
        )}
        <Elem name="file-selected-length">{showSelectedFilesLength}</Elem>
      </Elem>
    </Block>
  );
};

interface ExportFailedListProps {
  label?: any;
  style?: any;
  files?: any;
  currentProject?: any;
}

export const ExportFailedList = (props: ExportFailedListProps) => {
  const { label, style, files, currentProject } = props;

  return (
    <Block name="export-failed-list" style={style}>
      <Elem name="content">
        <CSVLink
          data={files}
          headers={csvHeaders}
          filename={`${getYYYYMMDDNow()}_Failed list_${currentProject?.id}.csv`}
        >
          <Elem name="failed-list">{label}</Elem>
        </CSVLink>
      </Elem>
    </Block>
  );
};

interface HandleTreeFileProps {
  level?: number;
  file?: any;
  progressing?: boolean;
  onLoop?: (files: any, level: number) => void;
  onRemove?: (files: any) => void;
  onSelect?: (files: any) => void;
  onUpload?: (files: any) => void;
}

const HandleTreeFile = (props: HandleTreeFileProps) => {
  const {
    level = 0,
    file,
    progressing,
    onLoop,
    onRemove,
    onSelect,
    onUpload,
  } = props;
  const [expand, setExpand] = useState(false);

  return (
    <>
      <FileItem
        expand={expand}
        level={level}
        data={file}
        progressing={progressing}
        onExpand={() => setExpand(!expand)}
        onRemove={onRemove}
        onSelect={onSelect}
        onUpload={onUpload}
      />
      {expand && onLoop?.(file?.children, level + 1)}
    </>
  );
};

interface Props {
  folderTree?: any;
  importState?: any;
  files?: any;
  filesLength?: number;
  filesLengthMess?: string;
  filesSelectedLength?: number;
  filesSelectedLengthMess?: string;
  onRemainFolder?: (checked: boolean) => void;
  onSelectFolderPath?: (pathName: any) => void;
  onRemove?: (files: any) => void;
  onSelect?: (files: any) => void;
}

// const PAGE_SIZE = 10;

export const FileList = (props: Props) => {
  const {
    importState,
    files = [],
    filesLength,
    filesLengthMess,
    filesSelectedLength,
    filesSelectedLengthMess,
    onRemainFolder,
    onSelectFolderPath,
    onRemove,
    onSelect,
  } = props;

  const importData = useBatchImportData();
  const { onUploadFiles, dispatch } = importData;
  const { progressing, currentTab } = importState;

  // const [page, setPage] = useState(0);

  // const [_files, setFiles] = useState([]);

  // const [isLoading, setLoading] = useState(false);

  // const maxPage = useMemo(
  //   () => Math.ceil((filesLength || 0) / PAGE_SIZE),
  //   [filesLength]
  // );

  // useEffect(() => {
  //   // Show 10 items init
  //   setFiles(slice(files, 0, 10));
  //   setPage(0);
  //   document
  //     .getElementById("listFileImport")
  //     ?.scrollTo({ top: 0, behavior: "smooth" });
  // }, [files]);

  const handleRemove = useCallback((file) => {
    onRemove?.(file);
  }, []);

  const handleSelect = useCallback((file) => {
    onSelect?.(file);
  }, []);

  const loop = (files: any, level: number) =>
    files?.map((file: any) => {
      return (
        <HandleTreeFile
          key={file.fullPath}
          progressing={progressing}
          file={file}
          level={level}
          onLoop={loop}
          onRemove={handleRemove}
          onSelect={handleSelect}
          onUpload={onUploadFiles}
        />
      );
    });

  const disabledSelectAll = useMemo(() => {
    const disabledSelect = !files.find(
      (file: any) =>
        file.status === fileStatusOptions.save ||
        file.status === fileStatusOptions.paused ||
        file.status === fileStatusOptions.failed
    );

    return disabledSelect || progressing[currentTab];
  }, [progressing, files, currentTab]);

  const isSelectAll = useMemo(
    () =>
      !files.find(
        (file: any) =>
          !file.checked &&
          (file.status === fileStatusOptions.save ||
            file.status === fileStatusOptions.paused ||
            file.status === fileStatusOptions.failed)
      ),
    [files]
  );

  const handleSelectAllFiles = useCallback(() => {
    if (progressing[currentTab]) {
      return;
    }

    dispatch({ type: isSelectAll ? ACTIONS.UNSELECT_ALL : ACTIONS.SELECT_ALL });
  }, [isSelectAll, progressing, currentTab]);

  // const observerTarget = useRef(null);

  // const nextPage = useCallback(() => {
  //   if (isLoading) return;
  //   console.log("page: ", page);
  //   console.log("maxPage: ", maxPage);
  //   console.log("files: ", files);

  //   if (page < maxPage) {
  //     setLoading(true);
  //     const nextItems = _.slice(
  //       files,
  //       (page + 1) * PAGE_SIZE,
  //       (page + 2) * PAGE_SIZE
  //     );

  //     setFiles([..._files, ...nextItems]);
  //     setPage(page + 1);
  //     setTimeout(() => {
  //       setLoading(false);
  //     }, 500);
  //   }
  // }, [_files, files, isLoading, maxPage, page]);

  // const observer = useMemo(() => {
  //   return new IntersectionObserver(
  //     (entries) => {
  //       console.log("entries: ", entries);

  //       if (entries[0].isIntersecting) {
  //         nextPage();
  //       }
  //     },
  //     {
  //       threshold: 1,
  //       root: document.querySelector("#listFileImport"),
  //     }
  //   );
  // }, [nextPage]);

  // useEffect(() => {
  //   if (observerTarget.current) {
  //     observer.observe(observerTarget.current);
  //   }

  //   return () => {
  //     if (observerTarget.current) {
  //       observer.unobserve(observerTarget.current);
  //     }
  //   };
  // }, [observerTarget, observer]);

  return (
    <>
      <FileListLength
        className={`${filesLength ? "show-file-list-length" : ""}`}
        filesLength={filesLength}
        filesLengthMess={filesLengthMess}
        filesSelectedLength={filesSelectedLength}
        filesSelectedLengthMess={filesSelectedLengthMess}
        disabledSelectAll={disabledSelectAll}
        isSelectAll={isSelectAll}
        disabledFolder={currentTab === "raw"}
        onRemainFolder={onRemainFolder}
        onSelectFolderPath={onSelectFolderPath}
        onSelectAllFiles={handleSelectAllFiles}
      />
      <Block
        name="file-list"
        className={`${filesLength ? "show-file-list" : ""}`}
        id="listFileImport"
      >
        <Elem name="content">
          {loop(files, 0)}

          {/* {loop(_files, 0)}
          <div
            ref={observerTarget}
            className="flex items-center justify-center"
          ></div> */}
        </Elem>
      </Block>
    </>
  );
};
