.dropzone
    // position relative
    cursor pointer
    display: flex;
    min-height: 200px;
    // height: 100%
    height: 40vh
    flex-direction: column;
    justify-content: center;
    align-items: center;
    align-self: stretch;
    border-radius: 20px;
    border: 2px dashed var(--gray-blue-grey-blue-80, #ADB8CC);
    background: rgba(255, 255, 255, 0) 
    transition: all 0.3s, height .6s ease-out

.dropzone:hover
    border-color: #3361FF

    .dropzone-description
        &__icon-upload
            background: var(--blue-blue, #3361FF);

            svg > path
                fill #FFF

.hovered
    border: 2px dashed var(--blue-blue, #3361FF);
    transition: all .6s ease-out

.is-files
    height: 20vh
