import * as d3 from "d3";

const regUUID =
  /^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

export const csvParserHeader = async (file: any) => {
  return await new Promise((resolve) => {
    const reader = new FileReader();

    reader.onload = (e: any) => {
      const text = e.target.result;
      const csvData = d3.csvParse(text);

      resolve(csvData.columns);
    };

    if (file) {
      reader.readAsText(file);
    }
  }).then((csvHeader: any) => Promise.all(csvHeader));
};

export const csvParser = async (file: any) => {
  return await new Promise((resolve) => {
    const reader = new FileReader();

    reader.onload = (e: any) => {
      const text = e.target.result;
      const csvData = d3.csvParse(text);

      resolve(csvData);
    };

    if (file) {
      reader.readAsText(file);
    }
  }).then((csvData: any) => Promise.all(csvData));
};

export const requiredColumnAttached = [
  "image_path_local",
  "image_width",
  "image_height",
];

export const requiredColumnDetached = [
  "file_id",
  "file_name",
  "image_path_local",
  "image_width",
  "image_height",
];

export const PREFIX_TOOL = "view_annotation_result_";
export const PREFIX_META = "meta_";

export const validateRequiredColumnAttached = (header: any, data: any) => {
  const checkDuplicateColumn = header.filter(
    (item: any, index: number) => header.indexOf(item) !== index
  );

  // if duplicate column
  if (checkDuplicateColumn.length) {
    return false;
  }

  return (
    !requiredColumnAttached.find((column: any) => !header?.includes(column)) &&
    !data?.find((record: any) => {
      let image_width = false;
      let image_height = false;

      if (record?.image_width) {
        image_width = isNaN(Number(record?.image_width));
      }

      if (record?.image_height) {
        image_height = isNaN(Number(record?.image_height));
      }

      return image_width || image_height || !record?.image_path_local;
    })
  );
};

export const validateRequiredColumnDetached = (header: any, data: any) => {
  const checkDuplicateColumn = header.filter(
    (item: any, index: number) => header.indexOf(item) !== index
  );

  // if duplicate column
  if (checkDuplicateColumn.length) {
    return false;
  }

  return (
    !requiredColumnDetached.find((column: any) => !header?.includes(column)) &&
    !data?.find((record: any) => {
      let image_width = false;
      let image_height = false;

      if (record?.image_width) {
        image_width = isNaN(Number(record?.image_width));
      } else {
        image_width = true;
      }

      if (record?.image_height) {
        image_height = isNaN(Number(record?.image_height));
      } else {
        image_height = true;
      }

      return (
        !record?.file_id?.match(regUUID) ||
        image_width ||
        image_height ||
        !record?.image_path_local
      );
    })
  );
};

export const validateAttribute = (header: any, toolList: any) => {
  const headerViewAnnotation = header?.filter((column: any) =>
    column.includes(PREFIX_TOOL)
  );

  // if do not have any annotation result attr
  if (!headerViewAnnotation?.length) {
    return true;
  }

  // if toolList > 0 and do not have any annotation result attr
  // if (!headerViewAnnotation?.length && toolList?.length > 0) {
  //   return false;
  // }

  const checkDuplicateColumn = headerViewAnnotation.filter(
    (item: any, index: number) => headerViewAnnotation.indexOf(item) !== index
  );

  // if duplicate column
  if (checkDuplicateColumn.length) {
    return false;
  }

  const isInvalidViewAnnotationHeader = headerViewAnnotation.find(
    (column: any) =>
      !toolList?.find((tool: any) => column === `${PREFIX_TOOL}${tool}`)
  );

  return !isInvalidViewAnnotationHeader;
};

export const checkFileTypeOtherZip = (file: any) => {
  const fileNameSplit = file.file.name.split(".");
  const fileType = fileNameSplit?.[fileNameSplit?.length - 1];

  return ".zip" !== `.${fileType}`.toLowerCase();
};
