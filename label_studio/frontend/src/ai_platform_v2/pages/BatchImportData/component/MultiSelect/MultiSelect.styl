.tag-import-multi-select
    display: flex;
    min-width: 86px;
    width: fit-content;
    padding: 5px 0px 5px 10px;
    align-items: center;
    gap: 10px;
    flex-shrink: 0;

    border-radius: 5px;
    background: var(--blue-blue-5, rgba(51, 97, 255, 0.05));

    &__tag-name
        color: var(--Gray-Blue-Grey-Blue-40, #346);
        
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;

    &__tag-close
        cursor pointer
        display: flex


.taureau-import-multi-select-list-option
    display: flex
    flex-direction: column
    padding: 4px 0px;
    transition: all .2s
    max-height: 300px
    gap: 4px;
    overflow: auto

    &__option
        cursor pointer
        display: flex;
        padding: 4px 12px;
        flex-direction: column;
        align-items: flex-start;
        align-self: stretch;
        transition: background .2s

    &__option:hover
        background: var(--black-black-5, rgba(0, 0, 0, 0.05));

    &__option-content
        display: flex;
        width 100%
        padding: 10px 20px 10px 0px;
        justify-content: space-between;
        align-items: center;
        flex: 1 0 0;

    &__option-content-label
        color: var(--Gray-Blue-Grey-Blue-40, #346);
        font-feature-settings: 'clig' off, 'liga' off;
        
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 21px; /* 142.857% */

        transition: color .2s

    &__option-content-checked
        height: 0
        position relative
        top: -12px
        // animation fadein .2s
    
    .option-checked
        background: var(--blue-blue-5, rgba(51, 97, 255, 0.05)) !important;

        .taureau-import-multi-select-list-option__option-content-label
            color: var(--blue-blue, #3361FF) !important;

    &::-webkit-scrollbar {
        width: 7.5px;
        height: 7.5px;
    }
    
    &::-webkit-scrollbar-track {
        box-shadow: 0;
        /* border-radius: 10px; */
    }
    
    &::-webkit-scrollbar-thumb {
        background-color: rgb(98 112 140)
        border-radius: 10px;
    }

.taureau-import-multi-select
    display: flex;
    width: 100%;
    min-height: 50px;
    height: fit-content;
    padding: 5px 10px 5px 15px;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;

    border-radius: 10px;
    border: 2px solid var(--gray-blue-grey-blue-97, #F5F6F7);
    background: var(--white-white, #FFF);
    box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.03);

    transition: all .3s

    &__content
        width: 100%
        display: flex
        align-items: center
        gap: 10px
        flex: auto
        flex-wrap: wrap

    &__placeholder
        color: var(--gray-blue-grey-blue-85, #C3CAD9);
        
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;

    &__icon-arrow
        svg path
            transition: all .3s

:global(.tooltip-hide-arrow)
    padding 0px !important

    :global(.ant-tooltip-arrow)
        display none

    :global(.ant-tooltip-inner)
        border-radius 10px

:global(.tooltip-full-width)
    max-width: 100% !important

:global(.tooltip-custom-style)
    border-radius: 10px;
    border: 2px solid var(--Gray-Blue-Grey-Blue-97, #F5F6F7);
    background: var(--White-White, #FFF);
    box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.03);