import IconRemove from "@/ai_platform_v2/assets/Icons/IconRemove";
import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import {
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import "./MultiSelect.styl";
import IconArrowDownNew from "@/ai_platform_v2/assets/Icons/IconArrowDownNew";
import { Tooltip } from "antd";
import { HomeNoData } from "@/ai_platform_v2/assets/Images/NoData";
import { Button } from "@taureau/ui";
import { useHistory } from "react-router";
import { getCurrentModule, getCurrentProject } from "@/pages/DataSet/Const";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { ABILITY_NEW } from "@/config/PermissionsConfig";

interface PropsTag {
  option?: any;
  onClose?: (value: any) => void;
}
const Tag = (props: PropsTag) => {
  const { option, onClose } = props;

  const value = useMemo(() => option?.value, [option]);
  const tag = useMemo(() => option?.tag, [option]);

  const handleClose = useCallback(
    (event: any) => {
      event.stopPropagation();

      onClose?.(value);
    },
    [value, onClose]
  );

  return (
    <Block name="tag-import-multi-select">
      <Elem name="tag-name">{tag}</Elem>
      <Elem name="tag-close" onClick={handleClose}>
        <IconRemove />
      </Elem>
    </Block>
  );
};

interface PropsMultiSelectListOption {
  value?: any;
  options?: any;
  onChange?: (value: any) => void;
}
export const MultiSelectListOption = (props: PropsMultiSelectListOption) => {
  const { value, options, onChange } = props;

  const history = useHistory();

  const handleChange = useCallback(
    (option: any) => {
      const findOption = value?.includes(option?.value);

      if (findOption) {
        onChange?.(value?.filter((v: any) => v !== option?.value));
      } else {
        onChange?.([...value, option?.value]);
      }
    },
    [value, onChange]
  );

  // const checkedList = useMemo(
  //   () => options?.filter((option: any) => value?.includes(option.value)),
  //   [options, value]
  // );

  // const unCheckedList = useMemo(
  //   () => options?.filter((option: any) => !value?.includes(option.value)),
  //   [options, value]
  // );

  // const finalList = useMemo(
  //   () => [...checkedList, ...unCheckedList],
  //   [checkedList, unCheckedList]
  // );

  const moduleId = getCurrentModule();
  const projectId = getCurrentProject();
  const { hasPermissionAllScope } = useCheckPermission();

  const canUpdateProject = hasPermissionAllScope(
    ABILITY_NEW.can_update_projects,
    moduleId,
    projectId
  );

  return (
    <Block name="taureau-import-multi-select-list-option">
      {options?.length ? (
        options?.map((option: any) => (
          <Elem
            key={option?.value}
            name="option"
            className={`${
              value?.includes(option?.value) ? "option-checked" : ""
            }`}
            onClick={() => handleChange(option)}
          >
            {/* <Elem name="option-content"> */}
            <Elem name="option-content-label">{option?.label}</Elem>
            {/* {value?.includes(option?.value) && (
                <Elem name="option-content-checked">
                  <IconCheck />
                </Elem>
              )} */}
            {/* </Elem> */}
          </Elem>
        ))
      ) : (
        <div className="w-full h-full flex flex-col justify-center items-center bg-white p-5">
          <img
            src={HomeNoData}
            alt="search-no-data"
            width={150}
            className="mb-[20px]"
          />
          <span className=" text-[17px] font-medium text-gray-blue-40 leading-[30px]">
            Empty!
          </span>
          <span className=" text-[13px] font-light text-[#62708C] text-center leading-5 mb-[10px]">
            There’re no available meta data now.
          </span>
          {canUpdateProject && (
            <Button
              theme="Primary"
              corner="Rounded"
              size="sm"
              onClick={() =>
                history.push(
                  `/modules/${moduleId}/projects/${projectId}/settings-project?tab=metadata`
                )
              }
            >
              Create attribute
            </Button>
          )}
        </div>
      )}
    </Block>
  );
};

interface PropsMultiSelect {
  open?: boolean;
  overlayClassName?: string;
  placement?: any;
  placeholder?: string;
  value?: any;
  options?: any;
  onChange?: (value: any) => void;
}
export const MultiSelect = (props: PropsMultiSelect) => {
  const {
    open = false,
    placeholder,
    overlayClassName,
    placement = "bottom",
    value,
    options,
    onChange,
  } = props;
  const refSelect = useRef(null);
  const [width, setWidth] = useState(0);

  const handleClose = useCallback(
    (valueRemove) => {
      onChange?.(value?.filter((v: any) => v !== valueRemove));
    },
    [value, onChange]
  );

  const handleChange = useCallback(
    (value) => {
      onChange?.(value);
    },
    [onChange]
  );

  useLayoutEffect(() => {
    setWidth(refSelect.current.offsetWidth);
  }, [refSelect.current?.offsetWidth]);

  useEffect(() => {
    function handleWindowResize() {
      setWidth(refSelect.current.clientWidth);
    }

    window.addEventListener("resize", handleWindowResize);

    return () => {
      window.removeEventListener("resize", handleWindowResize);
    };
  }, []);

  return (
    <Tooltip
      //   open={open}
      trigger="click"
      color="#fff"
      placement={placement}
      title={
        <MultiSelectListOption
          value={value}
          options={options}
          onChange={handleChange}
        />
      }
      overlayClassName={`tooltip-hide-arrow tooltip-full-width ${
        overlayClassName ? overlayClassName : ""
      }`}
      overlayStyle={{
        width,
      }}
      overlayInnerStyle={{
        overflow: "auto",
        padding: 0,
      }}
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      //   onOpenChange={(newOpen: any) => {
      //     setOpenFilter(newOpen);
      //   }}
    >
      <Block
        name="taureau-import-multi-select"
        className={open ? "taureau-import-multi-select-expand" : ""}
        ref={refSelect}
      >
        <Elem name="content">
          {value?.map((value: any) => {
            const option = options?.find(
              (option: any) => option?.value === value
            );

            return (
              <Tag key={option?.value} option={option} onClose={handleClose} />
            );
          })}

          {!value?.length && <Elem name="placeholder">{placeholder}</Elem>}
        </Elem>
        <Elem name="icon-arrow">
          <IconArrowDownNew />
        </Elem>
      </Block>
    </Tooltip>
  );
};
