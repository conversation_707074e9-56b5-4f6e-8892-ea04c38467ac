import Message from "@/ai_platform_v2/component/Message/Message";
import { checkFileTypeOtherZip } from "./const/validate";

export const fileStatusOptions = {
  save: "save",
  uploading: "uploading",
  paused: "paused",
  failed: "failed",
  completed: "completed",
};

export const ACTIONS = {
  LOAD_CURRENT_PROJECT: "load_current_project",
  LOAD_DATA_TYPES: "load_data_types",

  CHANGE_BATCH_NAME: "change_batch_name",
  UPDATE_BATCH_DATA: "update_batch_data",

  SET_PROGRESSING: "set_progressing",

  ADD_UPLOAD_ZIP_TASKS: "add_upload_zip_task",
  REMOVE_UPLOAD_ZIP_TASKS: "remove_upload_zip_task",

  WAITING_STATUS: "waiting_status",

  ADD_INVALID_FILES: "add_invalid_files",

  SAVE: "save",
  SAVE_PAUSED: "save_paused",
  REMOVE: "remove",
  REMOVE_ALL: "remove_all",
  UPLOADING: "uploading",
  UPLOADING_ALL: "uploading_all",
  PAUSED: "paused",
  PAUSED_ALL: "paused_all",
  FAILED: "failed",
  INPROGRESS_TO_FAILED: "inprogress_to_failed",
  COMPLETED: "completed",

  RETRY_ALL: "retry_all",

  UPDATE_PROGRESS_FILE: "update_progress_file",

  SELECT: "select",
  SELECT_ALL: "select_all",
  UNSELECT_ALL: "unselect_all",
  RESET: "reset",

  ADD_FILE_INPROGRESS: "add_file_inprogress",
  REMOVE_FILE_INPROGRESS: "remove_file_inprogress",

  CLOSE: "close",
};

export const initialState = {
  currentProject: {},
  dataTypes: [],

  batchName: "",
  batchData: {},

  progressing: false,

  uploadZipTasks: [],

  waiting: false,

  invalidFiles: [],

  files: [],

  filesInProgress: [],

  close: false,
};

export const BatchImportDataReducer = (state = initialState, action: any) => {
  switch (action.type) {
    case ACTIONS.LOAD_CURRENT_PROJECT:
      return {
        ...state,
        currentProject: action.payload,
      };

    case ACTIONS.LOAD_DATA_TYPES:
      return {
        ...state,
        dataTypes: action.payload,
      };

    case ACTIONS.CHANGE_BATCH_NAME: {
      return {
        ...state,
        batchName: action.payload,
      };
    }

    case ACTIONS.UPDATE_BATCH_DATA: {
      return {
        ...state,
        batchData: action.payload,
      };
    }

    case ACTIONS.SET_PROGRESSING: {
      state.progressing = action.payload;

      return {
        ...state,
        // progressing: action.payload,
      };
    }

    case ACTIONS.ADD_UPLOAD_ZIP_TASKS: {
      const isHasZipTask = state.uploadZipTasks.includes(action.payload);

      if (isHasZipTask) {
        return state;
      }

      return {
        ...state,
        uploadZipTasks: [...state.uploadZipTasks, action.payload],
      };
    }

    case ACTIONS.REMOVE_UPLOAD_ZIP_TASKS:
      return {
        ...state,
        uploadZipTasks: state.uploadZipTasks.filter(
          (task: any) => task !== action.payload
        ),
      };

    case ACTIONS.WAITING_STATUS:
      return {
        ...state,
        waiting: action.payload,
      };

    case ACTIONS.ADD_INVALID_FILES: {
      const finalInvalidFiles = action.payload.filter((file: any) => {
        const duplicateInvalidFile = state.invalidFiles.find(
          (fileCheck: any) => fileCheck.imagePath === file.imagePath
        );

        return !duplicateInvalidFile;
      });

      return {
        ...state,
        invalidFiles: [...state.invalidFiles, ...finalInvalidFiles],
      };
    }

    case ACTIONS.SAVE: {
      const filesSave = action.payload.filter((file: any) => {
        const pathName = file?.fullPath ?? file.file.name;

        return !state.files.find((fileState: any) => {
          const pathNameState = fileState?.fullPath ?? fileState.file.name;

          return pathName === pathNameState;
        });
      });

      if (filesSave.length !== action.payload.length) {
        Message.warning({
          // title: "Invalid or unknown formats",
          content: "You have uploaded duplicated file. Please recheck!",
        });
      }
      return {
        ...state,
        files: [...filesSave, ...state.files],
      };
    }

    case ACTIONS.SAVE_PAUSED:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            file.status === fileStatusOptions.paused &&
            checkFileTypeOtherZip(file)
          ) {
            file.status = fileStatusOptions.save;
          }
          return file;
        }),
      };

    case ACTIONS.REMOVE:
      return {
        ...state,
        files: state.files.filter((file: any) => {
          const findFile = !action.payload.includes(file.file);

          if (!findFile) {
            const fileType = file.file.type;

            if (fileType[fileType.search("image")]) {
              URL.revokeObjectURL(file.file);
            }
          }

          return findFile;
        }),
      };

    case ACTIONS.REMOVE_ALL:
      return {
        ...state,
        files: [],
      };

    case ACTIONS.UPLOADING: {
      const payload = action.payload.map((file: any) => file.file);

      return {
        ...state,
        files: state.files.map((file: any) => {
          if (payload.includes(file.file)) {
            file.status = fileStatusOptions.uploading;
          }
          return file;
        }),
      };
    }

    case ACTIONS.UPLOADING_ALL:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (file.status === fileStatusOptions.save) {
            file.status = fileStatusOptions.uploading;
          }
          return file;
        }),
      };

    case ACTIONS.PAUSED:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (action.payload.includes(file)) {
            file.status = fileStatusOptions.paused;
          }
          return file;
        }),
      };

    case ACTIONS.PAUSED_ALL:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            file.status === fileStatusOptions.uploading &&
            checkFileTypeOtherZip(file)
          ) {
            file.status = fileStatusOptions.paused;
          }
          return file;
        }),
      };

    case ACTIONS.FAILED: {
      const payload = action.payload.map((file: any) => file.file);

      return {
        ...state,
        files: state.files.map((file: any) => {
          if (payload.includes(file.file)) {
            file.status = fileStatusOptions.failed;
          }
          return file;
        }),
      };
    }

    case ACTIONS.INPROGRESS_TO_FAILED: {
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (file.status === fileStatusOptions.uploading) {
            file.status = fileStatusOptions.failed;
          }
          return file;
        }),
      };
    }

    case ACTIONS.COMPLETED: {
      const payload = action.payload.map((file: any) => file.file);

      return {
        ...state,
        files: state.files.map((file: any) => {
          if (payload.includes(file.file)) {
            file.status = fileStatusOptions.completed;
          }
          return file;
        }),
      };
    }

    case ACTIONS.RETRY_ALL:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (file.status === fileStatusOptions.failed) {
            file.status = fileStatusOptions.uploading;
          }
          return file;
        }),
      };

    case ACTIONS.UPDATE_PROGRESS_FILE:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (action.payload.file.includes(file)) {
            file.progress = action.payload.progress;
          }
          return file;
        }),
      };

    case ACTIONS.SELECT: {
      const selectedFile = state.files.map((file: any) => {
        const findFile = action.payload.includes(file.file);

        if (findFile) {
          if (
            file.status === fileStatusOptions.save ||
            file.status === fileStatusOptions.paused ||
            file.status === fileStatusOptions.failed
          ) {
            file.checked = !file.checked;
          }
        }
        return file;
      });

      return {
        ...state,
        files: selectedFile,
      };
    }

    case ACTIONS.SELECT_ALL:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            file.status === fileStatusOptions.save ||
            file.status === fileStatusOptions.paused ||
            file.status === fileStatusOptions.failed
          ) {
            file.checked = true;
          }
          return file;
        }),
      };

    case ACTIONS.UNSELECT_ALL:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            file.status === fileStatusOptions.save ||
            file.status === fileStatusOptions.paused ||
            file.status === fileStatusOptions.failed
          ) {
            file.checked = false;
          }
          return file;
        }),
      };

    case ACTIONS.RESET: {
      state.files.forEach((file: any) => {
        const fileType = file.file.type;

        if (fileType[fileType.search("image")]) {
          URL.revokeObjectURL(file.file);
        }
      });

      state.progressing = false;

      return initialState;
    }

    case ACTIONS.ADD_FILE_INPROGRESS: {
      const fullPath = action.payload?.fullPath;

      const filesInProgress: any = state.filesInProgress;

      if (!fullPath || filesInProgress.includes(fullPath)) {
        return { ...state };
      }

      return {
        ...state,
        filesInProgress: [...filesInProgress, fullPath],
      };
    }

    case ACTIONS.REMOVE_FILE_INPROGRESS: {
      const fullPath = action.payload?.fullPath;

      if (!fullPath) {
        return { ...state };
      }

      return {
        ...state,
        filesInProgress: [
          ...state.filesInProgress.filter((key: any) => key !== fullPath),
        ],
      };
    }

    case ACTIONS.CLOSE:
      return { ...initialState, close: true };

    default:
      return state;
  }
};
