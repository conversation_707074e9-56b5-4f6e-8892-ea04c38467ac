import IconUpload from "@/ai_platform_v2/assets/Icons/IconUpload";
import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import "./DropzoneDes.styl";
import IconFileMedical from "@/ai_platform_v2/assets/Icons/IconFileMedical";

interface Props {
  className?: string;
  icon?: any;
  hovered?: boolean;
  dataTypes?: any;
  subDescription?: any;
}

export const DropzoneDes = (props: Props) => {
  const {
    className = "",
    icon = <IconUpload />,
    hovered = false,
    dataTypes = [],
    subDescription = "",
  } = props;

  return (
    <Block name="dropzone-description" className={className}>
      {!hovered ? (
        <Elem name="content">
          <Elem name="icon-upload">{icon}</Elem>
          <Elem name="upload-description">
            <Elem name="content-blue" style={{ marginRight: 3 }}>
              Click to upload
            </Elem>
            {`or drag & drop ${subDescription}`}
          </Elem>
          {!!dataTypes?.length && (
            <Elem name="supported-types">
              {`Supported types: ${dataTypes?.join(", ")}`}
            </Elem>
          )}
        </Elem>
      ) : (
        <Elem name="content-hovered">
          <IconFileMedical />
          <Elem name="content-blue">Add files to Project</Elem>
        </Elem>
      )}
    </Block>
  );
};
