import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import "./FileItem.styl";
import IconClose from "@/ai_platform_v2/assets/Icons/IconClose";
import {
  Children,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { But<PERSON> } from "@taureau/ui";
import IconDot from "@/ai_platform_v2/assets/Icons/IconDot";
import { fileStatusOptions } from "../BatchImportDataReducer";
import IconFolderUpload from "@/ai_platform_v2/assets/Icons/IconFolderUpload";
import IconZipUpload from "@/ai_platform_v2/assets/Icons/IconZipUpload";
import IconRefresh from "@/ai_platform_v2/assets/Icons/IconRefresh";
import { Tooltip, Typography } from "antd";
import IconCsvUpload from "@/ai_platform_v2/assets/Icons/IconCsvUpload";

const formatBytes = (bytes: any, decimals: any = 2) => {
  if (!+bytes) return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

interface Props {
  className?: string;
  widthNameTooltip?: any;
  level?: number;
  data?: any;
  expand?: any;
  progressing?: boolean;
  onExpand?: () => void;
  onRemove?: (files: any) => void;
  onSelect?: (files: any) => void;
  onUpload?: (isRetry: boolean, fileRetry: any) => void;
}

export const FileItem = (props: Props) => {
  const {
    className,
    widthNameTooltip = 660,
    level = 0,
    data,
    expand = false,
    progressing,
    onExpand,
    onRemove,
    onSelect,
    onUpload,
  } = props;
  const { file, fullPath, status } = data;

  // const { progress = "12%" } = file;

  const [_fileURL, setFileUrl] = useState("");

  const isChecked = useMemo(() => {
    return data?.checked;
  }, [data?.checked]);

  const fileStatus = useMemo(() => {
    return status;
  }, [status]);

  const showProgress = useMemo(() => {
    return status !== fileStatusOptions.save;
  }, [status]);

  const showRemoveAction = useMemo(() => {
    return !(
      status === fileStatusOptions.uploading ||
      status === fileStatusOptions.completed
    );
  }, [status]);

  const showRetryAction = useMemo(() => {
    return status === fileStatusOptions.failed && !progressing;
  }, [status, progressing]);

  const progressContent = useMemo(() => {
    const progress = data?.progress;

    switch (status) {
      case fileStatusOptions.save:
        return "";
      case fileStatusOptions.uploading:
        return progress !== null
          ? progress >= 0 && `${Math.ceil(progress)}%`
          : "In Progress";
      case fileStatusOptions.paused:
        return "Paused";
      case fileStatusOptions.failed:
        return "Failed";
      case fileStatusOptions.completed:
        return "Completed";
    }
  }, [data?.progress, status]);

  const observerTarget = useRef(null);

  const observer = useMemo(() => {
    return new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          if (data?.type === "image") {
            if (!_fileURL) {
              setFileUrl(URL.createObjectURL(file));
            }
          }
        } else {
          URL.revokeObjectURL(_fileURL);
        }
      },
      {
        threshold: 0.2,
        root: document.querySelector("#listFileImport"),
      }
    );
  }, [_fileURL, data?.type, file]);

  useEffect(() => {
    if (observerTarget.current) {
      observer.observe(observerTarget.current);
    }

    return () => {
      if (observerTarget.current) {
        observer.unobserve(observerTarget.current);
      }
    };
  }, [observerTarget, observer]);

  const imagePreview = useMemo(() => {
    if (data?.type === "folder") {
      return <IconFolderUpload />;
    } else if (data?.type === "zip") {
      return <IconZipUpload />;
    } else if (data?.type === "csv") {
      return <IconCsvUpload />;
    } else {
      return <img ref={observerTarget} src={_fileURL} />;
    }
  }, [data?.type, _fileURL]);

  const handleActionRemove = useCallback(
    (e) => {
      e.stopPropagation();
      onRemove?.(
        data?.children
          ? data?.children.map((file: any) => file.file)
          : [data?.file]
      );
    },
    [data]
  );

  const handelActionSelect = useCallback(() => {
    if (!showRemoveAction) {
      return;
    }

    onSelect?.(
      data?.children
        ? data?.children.map((file: any) => file.file)
        : [data?.file]
    );
  }, [data, showRemoveAction]);

  const handleRetryFile = useCallback(
    (e) => {
      e.stopPropagation();
      onUpload?.(
        true,
        data?.children
          ? data?.children.filter(
              (file: any) => file.status === fileStatusOptions.failed
            )
          : [data]
      );
    },
    [data]
  );

  const backgroundColorProgressBar = useMemo(() => {
    switch (status) {
      case fileStatusOptions.save:
        return "#fff";
      case fileStatusOptions.uploading:
        return "rgba(51, 97, 255, 0.05)";
      case fileStatusOptions.paused:
        return "rgba(255, 203, 51, 0.1)";
      case fileStatusOptions.failed:
        return "#fff";
      case fileStatusOptions.completed:
        return "#fff";
    }
  }, [data]);

  const fileType = useMemo(() => {
    const type = file?.type ?? data?.type;

    if (type[type.search("image")]) {
      return "Image";
    }

    if (type[type.search("zip")]) {
      return "Zip";
    }

    if (type[type.search("folder")]) {
      return "Folder";
    }

    if (type[type.search("csv")]) {
      return "CSV";
    }

    return type;
  }, [data]);

  return (
    <Block
      name="file-item"
      className={`${fileStatus} ${isChecked ? "checked" : ""}`}
      style={{
        marginLeft: level * 42,
        backgroundImage: `linear-gradient(to right, ${backgroundColorProgressBar} ${
          data?.progress ?? 0
        }%, #fff 0)`,
      }}
      onClick={handelActionSelect}
    >
      <Elem name="content">
        <Elem name="info">
          <Elem name="image-preview">{imagePreview}</Elem>
          <Elem name="file">
            <Elem name="name">
              <Typography.Text
                style={{
                  maxWidth: widthNameTooltip,
                  color: "#346",
                  textAlign: "start",
                }}
                ellipsis={{ ellipsis: true, tooltip: true }}
              >
                {fullPath}
              </Typography.Text>
            </Elem>
            <Elem name="progress-bar">
              <Elem name="size">{formatBytes(file?.size ?? data?.size)}</Elem>
              {showProgress && (
                <>
                  <IconDot className="progress-dot-icon" />
                  <Elem name="progress">{progressContent}</Elem>
                </>
              )}
            </Elem>
          </Elem>
        </Elem>
        <Elem name="tag-type">{fileType}</Elem>
      </Elem>
      <Elem name="action">
        <Elem name="action-item">
          {showRetryAction && (
            <Tooltip overlayClassName="import-tooltip" title="Retry">
              <Button
                className="border-0"
                size="xs"
                icon={<IconRefresh size={20} color="#C3CAD9" />}
                onClick={handleRetryFile}
              />
            </Tooltip>
          )}
          {showRemoveAction && (
            <Tooltip overlayClassName="import-tooltip" title="Remove">
              <Button
                className="border-0"
                size="xs"
                icon={<IconClose size={20} color="#C3CAD9" />}
                onClick={handleActionRemove}
              />
            </Tooltip>
          )}
        </Elem>
        {!!data?.children?.length && (
          <Elem
            name="expand-action"
            onClick={(event: any) => {
              event.stopPropagation();
              onExpand?.();
            }}
          >
            {!expand ? "Detail" : "Close"}
          </Elem>
        )}
      </Elem>
    </Block>
  );
};
