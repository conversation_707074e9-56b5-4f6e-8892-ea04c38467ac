.file-item
    cursor pointer
    display: flex;
    padding: 10px 16px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;
    border-radius: 5px;
    border: 1px solid var(--gray-blue-grey-blue-90, #DADEE6);
    // background: #FFF;
    transition: all .2s
    animation fadein .2s

    :global(.progress-dot-icon)
        margin -5px

    &__content
        display: flex;
        align-items: center;
        gap: 30px;

    &__info
        display: flex;
        align-items: center;
        gap: 16px;

    &__image-preview
        display: flex
        width: 60px;
        height: 60px;
        flex-shrink: 0;
        border-radius: 5px;
        align-items: center
        // background: lightgray 0px 0px / 100% 100% no-repeat;
        transition: all .3s

        img
            height: 60px
            width: 60px
            object-fit: cover
            border-radius: 5px;

    &__file
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;

    &__name
        color: var(--gray-blue-grey-blue-40, #346);
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;
        
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: 25px;

    .visible
        visibility: hidden;

    &__progress-bar
        display: flex;
        width: fit-content;
        align-items: center;

    &__size
        color: var(--gray-blue-grey-blue-40, #346);
        
        font-size: 12px;
        font-style: normal;
        font-weight: 300;
        line-height: normal;
        width: fit-content

    &__progress
        color: var(--gray-blue-grey-blue-40, #346);
        text-align: center;
        
        font-size: 12px;
        font-style: normal;
        font-weight: 300;
        line-height: normal;

    &__tag-type
        display: flex;
        padding: 5px 10px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 10px;
        background: var(--gray-blue-grey-blue-95, #EDEFF2);

        color: var(--gray-blue-grey-blue-60, #6B7A99);
        text-align: center;
        font-feature-settings: 'clig' off, 'liga' off;

        /* Medium/Medium 13 */
        
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: 25px; /* 192.308% */

    &__action
        display: flex
        flex-direction: column
        align-items: flex-end

    &__action-item
        display: flex
        flex-direction: row
        align-items: flex-end

    &__expand-action
        cursor pointer
        // margin-top: 50px;
        // right: 3.5%;
        // bottom: 7px;
        color: var(--blue-blue, #3361FF);
        text-align: center;
        
        font-size: 12px;
        font-style: normal;
        font-weight: 300;
        line-height: normal;

.file-list, .dropzone
    .uploading
        :global(.ls-file-item__progress-background)
            background: rgba(51, 97, 255, 0.05)

    .paused
        border: 1px solid var(--yellow-yellow-40, rgba(255, 203, 51, 0.40));

    .failed
        border: 1px solid var(--red-red, #E62E2E);

        :global(.progress-dot-icon)
            rect
                fill #CC7429

        :global(.ls-file-item__progress)
            color: var(--red-red-dark-1, #CC1414);


    .completed
        border: 1px solid var(--yellow-green-green, #29CC39);

        :global(.ls-file-item__progress)
            color: var(--red-red-dark-1, #13BF24);

    .checked
        border: 1px solid var(--blue-blue, #3361FF);

