import IconClose from "@/ai_platform_v2/assets/Icons/IconClose";
import IconExpandLess from "@/ai_platform_v2/assets/Icons/IconExpandLess";
import IconFullScreen from "@/ai_platform_v2/assets/Icons/IconFullScreen";
import { Block, Elem } from "@/ai_platform_v2/utils/bem";

import "./Progress.styl";
import IconPauseCircleFilled from "@/ai_platform_v2/assets/Icons/IconPauseCircleFilled";
import { ProgressBar } from "@/ai_platform_v2/component/ProgressBar/ProgressBar";
import { useCallback, useMemo, useState } from "react";
import { Tooltip } from "antd";
import IconPlayCircleFilled from "@/ai_platform_v2/assets/Icons/IconPlayCircleFilled";
import { fileStatusOptions } from "../BatchImportDataReducer";
import { useImportData } from "@/ai_platform_v2/providers/ImportDataProvider";

interface Props {
  open?: boolean;
  enabledPause?: boolean;
  paused?: boolean;
  percent?: number;
  data?: any;
  onFullScreen?: () => void;
  onPause?: () => void;
  onClose?: () => void;
}
export const Progress = (props: Props) => {
  const {
    open,
    enabledPause,
    paused,
    percent = 0,
    data = { completed: 0, failed: 0 },
    onFullScreen,
    onPause,
    onClose,
  } = props;
  const { completed = 0, failed = 0 } = data;

  const { expandMiniBar, setExpandMiniBar } = useImportData();

  const percentFinal = useMemo(() => Math.floor(percent), [percent]);

  const percentContent = useMemo(
    () => (paused ? "Paused" : `${percentFinal}%`),
    [paused, percentFinal]
  );

  const completedContent = useMemo(
    () => `${completed} ${completed > 1 ? "items" : "item"} `,
    [completed]
  );

  const failedContent = useMemo(
    () => `${failed} ${failed > 1 ? "items" : "item"} `,
    [failed]
  );

  const statusUpload = useMemo(() => {
    if (paused) {
      return fileStatusOptions.paused;
    }
    return percentFinal === 100
      ? fileStatusOptions.completed
      : fileStatusOptions.uploading;
  }, [percentFinal, paused]);

  const backgroundColorProgressBar = useMemo(() => {
    switch (statusUpload) {
      case fileStatusOptions.save:
        return "#fff";
      case fileStatusOptions.uploading:
        return "rgba(51, 97, 255, 0.05)";
      case fileStatusOptions.paused:
        return "rgba(255, 203, 51, 0.1)";
      case fileStatusOptions.failed:
        return "#fff";
      case fileStatusOptions.completed:
        return "rgba(41, 204, 57, 0.1)";
    }
  }, [statusUpload]);

  const colorByStatus = useMemo(() => {
    switch (statusUpload) {
      case fileStatusOptions.uploading:
        return "#3361FF";
      case fileStatusOptions.paused:
        return "#E6B117";
      case fileStatusOptions.completed:
        return "#13BF24";
    }
  }, [statusUpload]);

  const handleClose = useCallback(() => {
    onClose?.();
  }, [onClose]);

  return (
    <>
      {open && (
        <Block name="import-progress" className={`${statusUpload}`}>
          <Elem
            name="header"
            style={{
              backgroundImage: `linear-gradient(to right, ${backgroundColorProgressBar} ${
                expandMiniBar ? 100 : percentFinal
              }%, #fff 0)`,
            }}
          >
            <Elem name="title">
              <Elem name="title-content">Import Progress</Elem>
              <Elem
                name="title-progress-content"
                className={`${
                  expandMiniBar ? "hide-title-progress-content" : ""
                }`}
              >
                {percentContent}
              </Elem>
            </Elem>
            <Elem name="action">
              <Tooltip
                overlayClassName="import-tooltip"
                title={expandMiniBar ? "Minimize" : "Maximize"}
              >
                <Elem
                  name="expandMiniBar"
                  className={`${expandMiniBar ? "collapse" : ""}`}
                  onClick={() => setExpandMiniBar(!expandMiniBar)}
                >
                  <IconExpandLess size={27} />
                </Elem>
              </Tooltip>
              <Tooltip overlayClassName="import-tooltip" title="Full Screen">
                <Elem name="full-screen" onClick={onFullScreen}>
                  <IconFullScreen size={21} />
                </Elem>
              </Tooltip>
              <Tooltip
                overlayClassName="import-tooltip"
                title="Stop Importing"
                placement="topRight"
              >
                <Elem name="close" onClick={handleClose}>
                  <IconClose size={21} color="#CC1414" />
                </Elem>
              </Tooltip>
            </Elem>
          </Elem>

          <Elem name="body" className={`${expandMiniBar ? "body-expand" : ""}`}>
            <Elem name="body-detail">
              <Elem name="file-detail">
                <Elem name="item">
                  <Elem name="item-content">{completedContent}</Elem>
                  <Elem name="item-completed">Completed</Elem>
                </Elem>
                <Elem name="item">
                  <Elem name="item-content">{failedContent}</Elem>
                  <Elem name="item-failed">Failed</Elem>
                </Elem>
              </Elem>
              {enabledPause && percentFinal !== 100 && (
                <Tooltip
                  overlayClassName="import-tooltip"
                  title={paused ? "Continue" : "Pause"}
                >
                  <Elem name="pause-action" onClick={onPause}>
                    {paused ? (
                      <IconPlayCircleFilled size={36} />
                    ) : (
                      <IconPauseCircleFilled size={36} />
                    )}
                  </Elem>
                </Tooltip>
              )}
            </Elem>

            <Elem name="body-progress">
              <ProgressBar
                percent={percentFinal}
                strokeColor={colorByStatus}
                paused={paused}
              />
            </Elem>
          </Elem>
        </Block>
      )}
    </>
  );
};
