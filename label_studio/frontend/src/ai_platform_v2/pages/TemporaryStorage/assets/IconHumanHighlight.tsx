const IconHumanHighlight = ({ size = "38" }: any) => (
  <svg
    width="37"
    height="36"
    viewBox="0 0 37 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="4.1875"
      y="1.66797"
      width="28.6667"
      height="28.6667"
      rx="14.3333"
      stroke="url(#paint0_linear_17303_117721)"
      strokeWidth="2"
    />
    <g filter="url(#filter0_d_17303_117721)">
      <rect
        x="5.1875"
        y="2.66797"
        width="26.6667"
        height="26.6667"
        rx="13.3333"
        fill="#EDB7ED"
      />
      <rect
        x="5.6875"
        y="3.16797"
        width="25.6667"
        height="25.6667"
        rx="12.8333"
        stroke="#F9F3FB"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.259 12.2989C19.259 13.9353 17.9324 15.2619 16.296 15.2619C14.6596 15.2619 13.3331 13.9353 13.3331 12.2989C13.3331 10.6625 14.6596 9.33594 16.296 9.33594C17.9324 9.33594 19.259 10.6625 19.259 12.2989ZM18.5182 22.6693H14.0738C13.485 22.6675 12.9207 22.4328 12.5044 22.0165C12.088 21.6001 11.8533 21.0359 11.8516 20.447V19.7063C11.8516 18.724 12.2418 17.782 12.9364 17.0874C13.6309 16.3928 14.573 16.0026 15.5553 16.0026H17.0367C18.019 16.0026 18.9611 16.3928 19.6557 17.0874C20.3502 17.782 20.7405 18.724 20.7405 19.7063V20.447C20.7387 21.0359 20.504 21.6001 20.0876 22.0165C19.6713 22.4328 19.1071 22.6675 18.5182 22.6693ZM22.2219 15.2619C23.4492 15.2619 24.4442 14.267 24.4442 13.0397C24.4442 11.8124 23.4492 10.8175 22.2219 10.8175C20.9946 10.8175 19.9997 11.8124 19.9997 13.0397C19.9997 14.267 20.9946 15.2619 22.2219 15.2619ZM20.7775 16.1211C21.0041 16.044 21.2418 16.004 21.4812 16.0026H22.9627C23.5515 16.0044 24.1157 16.2391 24.5321 16.6554C24.9484 17.0718 25.1831 17.636 25.1849 18.2248V18.9656C25.1837 19.3581 25.0273 19.7343 24.7497 20.0118C24.4721 20.2894 24.096 20.4459 23.7034 20.447H22.2219V19.7063C22.224 18.3691 21.706 17.0834 20.7775 16.1211Z"
        fill="white"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_17303_117721"
        x="0.1875"
        y="1.66797"
        width="36.6641"
        height="36.668"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2.5" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.729412 0 0 0 0.15 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_17303_117721"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_17303_117721"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_17303_117721"
        x1="10.6875"
        y1="3.66797"
        x2="26.6875"
        y2="28.168"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#00FFBA" />
        <stop offset="0.199458" stopColor="#13FEC8" />
        <stop offset="0.360405" stopColor="#22FDD3" />
        <stop offset="0.442688" stopColor="#2AFCD8" />
        <stop offset="0.549503" stopColor="#34FCE0" />
        <stop offset="0.650457" stopColor="#3EFBE7" />
        <stop offset="0.80623" stopColor="#4DFAF2" />
        <stop offset="0.908414" stopColor="#56FAF9" />
        <stop offset="1" stopColor="#5FF9FF" />
      </linearGradient>
    </defs>
  </svg>
);

export default IconHumanHighlight;
