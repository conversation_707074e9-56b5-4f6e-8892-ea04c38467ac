const IconPreProcessingHighlight = ({ size = "38" }: any) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 38 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="4.66406"
      y="1.66797"
      width="28.6667"
      height="28.6667"
      rx="14.3333"
      stroke="url(#paint0_linear_17303_117701)"
      strokeWidth="2"
    />
    <g filter="url(#filter0_d_17303_117701)">
      <rect
        x="5.66406"
        y="2.66797"
        width="26.6667"
        height="26.6667"
        rx="13.3333"
        fill="#818CF8"
      />
      <rect
        x="6.16406"
        y="3.16797"
        width="25.6667"
        height="25.6667"
        rx="12.8333"
        stroke="#F9FAFB"
      />
      <path
        d="M13.9953 18.2266C13.9223 18.2263 13.85 18.2404 13.7824 18.268C13.7149 18.2957 13.6535 18.3364 13.6017 18.3878C13.5499 18.4393 13.5087 18.5004 13.4806 18.5677C13.4524 18.635 13.4378 18.7073 13.4375 18.7802V20.1706C13.4375 20.9311 14.0673 21.5609 14.8278 21.5609H16.214C16.2874 21.5612 16.36 21.5469 16.4278 21.519C16.4956 21.4911 16.5572 21.45 16.6091 21.3982C16.6609 21.3463 16.702 21.2847 16.7299 21.2169C16.7579 21.1491 16.7721 21.0764 16.7718 21.0031C16.7716 20.9301 16.7569 20.8579 16.7287 20.7906C16.7006 20.7232 16.6594 20.6621 16.6076 20.6107C16.5558 20.5593 16.4944 20.5186 16.4269 20.4909C16.3594 20.4632 16.287 20.4491 16.214 20.4494H14.8278C14.6678 20.4494 14.5489 20.3306 14.5489 20.1706V18.7802C14.5484 18.6335 14.4899 18.493 14.3862 18.3893C14.2825 18.2856 14.142 18.2271 13.9953 18.2266Z"
        fill="white"
      />
      <path
        d="M13.9953 14.3398C13.8487 14.3396 13.7078 14.3973 13.6036 14.5004L12.4913 15.6127C12.4396 15.6643 12.3986 15.7256 12.3705 15.7931C12.3425 15.8606 12.3281 15.933 12.3281 16.006C12.3281 16.0791 12.3425 16.1515 12.3705 16.219C12.3986 16.2865 12.4396 16.3478 12.4913 16.3994L13.6036 17.5116C13.7077 17.6151 13.8485 17.6732 13.9953 17.6732C14.1421 17.6732 14.2829 17.6151 14.387 17.5116L15.4982 16.3994C15.5499 16.3478 15.591 16.2865 15.619 16.219C15.647 16.1515 15.6614 16.0791 15.6614 16.006C15.6614 15.933 15.647 15.8606 15.619 15.7931C15.591 15.7256 15.5499 15.6643 15.4982 15.6127L14.387 14.5004C14.2828 14.3973 14.142 14.3396 13.9953 14.3398Z"
        fill="white"
      />
      <path
        d="M14.8278 10.4492C14.0673 10.4492 13.4375 11.079 13.4375 11.8395V13.2299C13.4378 13.3028 13.4524 13.3751 13.4806 13.4424C13.5087 13.5097 13.5499 13.5708 13.6017 13.6223C13.6535 13.6737 13.7149 13.7144 13.7824 13.7421C13.85 13.7697 13.9223 13.7838 13.9953 13.7835C14.142 13.783 14.2825 13.7245 14.3862 13.6208C14.4899 13.5171 14.5484 13.3766 14.5489 13.2299V11.8395C14.5489 11.6795 14.6678 11.5607 14.8278 11.5607H16.214C16.287 11.5609 16.3594 11.5469 16.4269 11.5192C16.4944 11.4915 16.5558 11.4508 16.6076 11.3994C16.6594 11.348 16.7006 11.2869 16.7287 11.2195C16.7569 11.1522 16.7716 11.08 16.7718 11.007C16.7721 10.9337 16.7579 10.861 16.7299 10.7932C16.702 10.7254 16.6609 10.6638 16.6091 10.6119C16.5572 10.5601 16.4956 10.519 16.4278 10.4911C16.36 10.4632 16.2874 10.4489 16.214 10.4492H14.8278Z"
        fill="white"
      />
      <path
        d="M15.1046 15.4453C15.0313 15.445 14.9587 15.4593 14.8909 15.4872C14.8231 15.5151 14.7615 15.5562 14.7096 15.6081C14.6578 15.6599 14.6167 15.7215 14.5888 15.7893C14.5608 15.8571 14.5466 15.9298 14.5469 16.0031C14.5471 16.0761 14.5618 16.1483 14.5899 16.2156C14.6181 16.283 14.6593 16.3441 14.7111 16.3955C14.7629 16.4469 14.8243 16.4876 14.8918 16.5153C14.9593 16.5429 15.0317 16.557 15.1046 16.5568H16.212C16.285 16.557 16.3573 16.5429 16.4248 16.5153C16.4924 16.4876 16.5538 16.4469 16.6056 16.3955C16.6574 16.3441 16.6985 16.283 16.7267 16.2156C16.7549 16.1483 16.7695 16.0761 16.7697 16.0031C16.77 15.9298 16.7558 15.8571 16.7279 15.7893C16.6999 15.7215 16.6589 15.6599 16.607 15.608C16.5552 15.5562 16.4936 15.5151 16.4258 15.4872C16.358 15.4593 16.2853 15.445 16.212 15.4453H15.1046Z"
        fill="white"
      />
      <path
        d="M18.9981 9.33594C18.0844 9.33594 17.3281 10.0911 17.3281 11.0048C17.3281 11.9186 18.0844 12.6705 18.9981 12.6705H23.9972C24.911 12.6705 25.6618 11.9186 25.6618 11.0048C25.6618 10.091 24.911 9.33594 23.9972 9.33594H18.9981Z"
        fill="white"
      />
      <path
        d="M18.9981 19.3398C18.0844 19.3398 17.3281 20.0917 17.3281 21.0055C17.3281 21.9193 18.0844 22.6701 18.9981 22.6701H23.9972C24.911 22.6701 25.6618 21.9193 25.6618 21.0055C25.6618 20.0917 24.911 19.3398 23.9972 19.3398H18.9981Z"
        fill="white"
      />
      <path
        d="M18.997 14.3398C18.0833 14.3398 17.3281 15.0917 17.3281 16.0055C17.3281 16.9193 18.0833 17.6701 18.997 17.6701H23.9972C24.911 17.6701 25.6618 16.9193 25.6618 16.0055C25.6618 15.0917 24.911 14.3398 23.9972 14.3398H18.997Z"
        fill="white"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_17303_117701"
        x="0.664062"
        y="1.66797"
        width="36.6641"
        height="36.668"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2.5" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.729412 0 0 0 0.15 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_17303_117701"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_17303_117701"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_17303_117701"
        x1="11.1641"
        y1="3.66797"
        x2="27.1641"
        y2="28.168"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#00FFBA" />
        <stop offset="0.199458" stopColor="#13FEC8" />
        <stop offset="0.360405" stopColor="#22FDD3" />
        <stop offset="0.442688" stopColor="#2AFCD8" />
        <stop offset="0.549503" stopColor="#34FCE0" />
        <stop offset="0.650457" stopColor="#3EFBE7" />
        <stop offset="0.80623" stopColor="#4DFAF2" />
        <stop offset="0.908414" stopColor="#56FAF9" />
        <stop offset="1" stopColor="#5FF9FF" />
      </linearGradient>
    </defs>
  </svg>
);

export default IconPreProcessingHighlight;
