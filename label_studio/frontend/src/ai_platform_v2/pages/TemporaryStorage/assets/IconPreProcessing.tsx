const IconPreProcessing = ({ size = "38" }: any) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 38 37"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g filter="url(#filter0_d_17273_83889)">
      <rect
        x="5.66406"
        y="3.66797"
        width="26.6667"
        height="26.6667"
        rx="13.3333"
        fill="#818CF8"
      />
      <path
        d="M13.9953 19.2266C13.9223 19.2263 13.85 19.2404 13.7824 19.268C13.7149 19.2957 13.6535 19.3364 13.6017 19.3878C13.5499 19.4393 13.5087 19.5004 13.4806 19.5677C13.4524 19.635 13.4378 19.7073 13.4375 19.7802V21.1706C13.4375 21.9311 14.0673 22.5609 14.8278 22.5609H16.214C16.2874 22.5612 16.36 22.5469 16.4278 22.519C16.4956 22.4911 16.5572 22.45 16.6091 22.3982C16.6609 22.3463 16.702 22.2847 16.7299 22.2169C16.7579 22.1491 16.7721 22.0764 16.7718 22.0031C16.7716 21.9301 16.7569 21.8579 16.7287 21.7906C16.7006 21.7232 16.6594 21.6621 16.6076 21.6107C16.5558 21.5593 16.4944 21.5186 16.4269 21.4909C16.3594 21.4632 16.287 21.4491 16.214 21.4494H14.8278C14.6678 21.4494 14.5489 21.3306 14.5489 21.1706V19.7802C14.5484 19.6335 14.4899 19.493 14.3862 19.3893C14.2825 19.2856 14.142 19.2271 13.9953 19.2266Z"
        fill="white"
      />
      <path
        d="M13.9953 15.3398C13.8487 15.3396 13.7078 15.3973 13.6036 15.5004L12.4913 16.6127C12.4396 16.6643 12.3986 16.7256 12.3705 16.7931C12.3425 16.8606 12.3281 16.933 12.3281 17.006C12.3281 17.0791 12.3425 17.1515 12.3705 17.219C12.3986 17.2865 12.4396 17.3478 12.4913 17.3994L13.6036 18.5116C13.7077 18.6151 13.8485 18.6732 13.9953 18.6732C14.1421 18.6732 14.2829 18.6151 14.387 18.5116L15.4982 17.3994C15.5499 17.3478 15.591 17.2865 15.619 17.219C15.647 17.1515 15.6614 17.0791 15.6614 17.006C15.6614 16.933 15.647 16.8606 15.619 16.7931C15.591 16.7256 15.5499 16.6643 15.4982 16.6127L14.387 15.5004C14.2828 15.3973 14.142 15.3396 13.9953 15.3398Z"
        fill="white"
      />
      <path
        d="M14.8278 11.4492C14.0673 11.4492 13.4375 12.079 13.4375 12.8395V14.2299C13.4378 14.3028 13.4524 14.3751 13.4806 14.4424C13.5087 14.5097 13.5499 14.5708 13.6017 14.6223C13.6535 14.6737 13.7149 14.7144 13.7824 14.7421C13.85 14.7697 13.9223 14.7838 13.9953 14.7835C14.142 14.783 14.2825 14.7245 14.3862 14.6208C14.4899 14.5171 14.5484 14.3766 14.5489 14.2299V12.8395C14.5489 12.6795 14.6678 12.5607 14.8278 12.5607H16.214C16.287 12.5609 16.3594 12.5469 16.4269 12.5192C16.4944 12.4915 16.5558 12.4508 16.6076 12.3994C16.6594 12.348 16.7006 12.2869 16.7287 12.2195C16.7569 12.1522 16.7716 12.08 16.7718 12.007C16.7721 11.9337 16.7579 11.861 16.7299 11.7932C16.702 11.7254 16.6609 11.6638 16.6091 11.6119C16.5572 11.5601 16.4956 11.519 16.4278 11.4911C16.36 11.4632 16.2874 11.4489 16.214 11.4492H14.8278Z"
        fill="white"
      />
      <path
        d="M15.1046 16.4453C15.0313 16.445 14.9587 16.4593 14.8909 16.4872C14.8231 16.5151 14.7615 16.5562 14.7096 16.6081C14.6578 16.6599 14.6167 16.7215 14.5888 16.7893C14.5608 16.8571 14.5466 16.9298 14.5469 17.0031C14.5471 17.0761 14.5618 17.1483 14.5899 17.2156C14.6181 17.283 14.6593 17.3441 14.7111 17.3955C14.7629 17.4469 14.8243 17.4876 14.8918 17.5153C14.9593 17.5429 15.0317 17.557 15.1046 17.5568H16.212C16.285 17.557 16.3573 17.5429 16.4248 17.5153C16.4924 17.4876 16.5538 17.4469 16.6056 17.3955C16.6574 17.3441 16.6985 17.283 16.7267 17.2156C16.7549 17.1483 16.7695 17.0761 16.7697 17.0031C16.77 16.9298 16.7558 16.8571 16.7279 16.7893C16.6999 16.7215 16.6589 16.6599 16.607 16.608C16.5552 16.5562 16.4936 16.5151 16.4258 16.4872C16.358 16.4593 16.2853 16.445 16.212 16.4453H15.1046Z"
        fill="white"
      />
      <path
        d="M18.9981 10.3359C18.0844 10.3359 17.3281 11.0911 17.3281 12.0048C17.3281 12.9186 18.0844 13.6705 18.9981 13.6705H23.9972C24.911 13.6705 25.6618 12.9186 25.6618 12.0048C25.6618 11.091 24.911 10.3359 23.9972 10.3359H18.9981Z"
        fill="white"
      />
      <path
        d="M18.9981 20.3398C18.0844 20.3398 17.3281 21.0917 17.3281 22.0055C17.3281 22.9193 18.0844 23.6701 18.9981 23.6701H23.9972C24.911 23.6701 25.6618 22.9193 25.6618 22.0055C25.6618 21.0917 24.911 20.3398 23.9972 20.3398H18.9981Z"
        fill="white"
      />
      <path
        d="M18.997 15.3398C18.0833 15.3398 17.3281 16.0917 17.3281 17.0055C17.3281 17.9193 18.0833 18.6701 18.997 18.6701H23.9972C24.911 18.6701 25.6618 17.9193 25.6618 17.0055C25.6618 16.0917 24.911 15.3398 23.9972 15.3398H18.997Z"
        fill="white"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_17273_83889"
        x="0.664062"
        y="0.667969"
        width="36.6641"
        height="36.668"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="2" />
        <feGaussianBlur stdDeviation="2.5" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.14902 0 0 0 0 0.2 0 0 0 0 0.301961 0 0 0 0.15 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_17273_83889"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_17273_83889"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);

export default IconPreProcessing;
