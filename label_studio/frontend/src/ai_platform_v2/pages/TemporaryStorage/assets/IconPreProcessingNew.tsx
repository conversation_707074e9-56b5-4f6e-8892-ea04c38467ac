const IconPreProcessingNew = ({ size = "24" }: any) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 25 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_14235_175916)">
      <g filter="url(#filter0_d_14235_175916)">
        <rect x="4.60156" y="4" width="16" height="16" rx="8" fill="#818CF8" />
        <path
          d="M9.60029 13.3359C9.5565 13.3358 9.5131 13.3442 9.47258 13.3608C9.43206 13.3774 9.39521 13.4019 9.36413 13.4327C9.33305 13.4636 9.30836 13.5002 9.29146 13.5406C9.27456 13.581 9.26578 13.6244 9.26562 13.6681V14.5023C9.26562 14.9586 9.64351 15.3365 10.0998 15.3365H10.9315C10.9755 15.3367 11.0191 15.3282 11.0598 15.3114C11.1005 15.2946 11.1375 15.27 11.1686 15.2389C11.1997 15.2078 11.2243 15.1708 11.2411 15.1301C11.2578 15.0895 11.2664 15.0459 11.2662 15.0019C11.2661 14.9581 11.2573 14.9147 11.2404 14.8743C11.2235 14.8339 11.1988 14.7973 11.1677 14.7664C11.1366 14.7356 11.0998 14.7112 11.0593 14.6945C11.0187 14.6779 10.9753 14.6695 10.9315 14.6697H10.0998C10.0038 14.6697 9.93249 14.5983 9.93249 14.5023V13.6681C9.93218 13.5801 9.89708 13.4958 9.83484 13.4336C9.77261 13.3713 9.6883 13.3362 9.60029 13.3359Z"
          fill="white"
        />
        <path
          d="M9.60187 11C9.51388 10.9998 9.42939 11.0345 9.36683 11.0964L8.69949 11.7637C8.66845 11.7947 8.64382 11.8315 8.62702 11.872C8.61021 11.9125 8.60156 11.9559 8.60156 11.9997C8.60156 12.0436 8.61021 12.087 8.62702 12.1275C8.64382 12.168 8.66845 12.2048 8.69949 12.2357L9.36683 12.9031C9.4293 12.9652 9.5138 13 9.60187 13C9.68995 13 9.77445 12.9652 9.83691 12.9031L10.5036 12.2357C10.5346 12.2048 10.5593 12.168 10.5761 12.1275C10.5929 12.087 10.6015 12.0436 10.6015 11.9997C10.6015 11.9559 10.5929 11.9125 10.5761 11.872C10.5593 11.8315 10.5346 11.7947 10.5036 11.7637L9.83691 11.0964C9.77435 11.0345 9.68987 10.9998 9.60187 11Z"
          fill="white"
        />
        <path
          d="M10.0998 8.66407C9.64351 8.66407 9.26562 9.04195 9.26562 9.49826V10.3324C9.26578 10.3762 9.27456 10.4196 9.29146 10.46C9.30836 10.5004 9.33305 10.537 9.36413 10.5679C9.39521 10.5987 9.43206 10.6232 9.47258 10.6398C9.5131 10.6564 9.5565 10.6648 9.60029 10.6646C9.6883 10.6643 9.77261 10.6293 9.83484 10.567C9.89708 10.5048 9.93218 10.4205 9.93249 10.3324V9.49826C9.93249 9.40225 10.0038 9.33093 10.0998 9.33093H10.9315C10.9753 9.3311 11.0187 9.32264 11.0593 9.30604C11.0998 9.28944 11.1366 9.26502 11.1677 9.23417C11.1988 9.20332 11.2235 9.16665 11.2404 9.12625C11.2573 9.08586 11.2661 9.04253 11.2662 8.99874C11.2664 8.95474 11.2578 8.91113 11.2411 8.87045C11.2243 8.82977 11.1997 8.79281 11.1686 8.7617C11.1375 8.73059 11.1005 8.70594 11.0598 8.68918C11.0191 8.67243 10.9755 8.66389 10.9315 8.66407H10.0998Z"
          fill="white"
        />
        <path
          d="M10.2722 11.6641C10.2282 11.6639 10.1846 11.6724 10.1439 11.6892C10.1032 11.7059 10.0663 11.7306 10.0351 11.7617C10.004 11.7928 9.97939 11.8298 9.96263 11.8705C9.94587 11.9111 9.93733 11.9547 9.9375 11.9987C9.93765 12.0425 9.94644 12.0859 9.96334 12.1263C9.98024 12.1666 10.0049 12.2033 10.036 12.2342C10.0671 12.265 10.1039 12.2894 10.1445 12.306C10.185 12.3226 10.2284 12.3311 10.2722 12.3309H10.9366C10.9804 12.3311 11.0238 12.3226 11.0643 12.306C11.1048 12.2894 11.1416 12.265 11.1727 12.2342C11.2038 12.2033 11.2285 12.1666 11.2454 12.1263C11.2623 12.0859 11.2711 12.0425 11.2712 11.9987C11.2714 11.9547 11.2629 11.9111 11.2461 11.8705C11.2293 11.8298 11.2047 11.7928 11.1736 11.7617C11.1425 11.7306 11.1055 11.7059 11.0648 11.6892C11.0241 11.6724 10.9806 11.6639 10.9366 11.6641H10.2722Z"
          fill="white"
        />
        <path
          d="M12.6036 8C12.0553 8 11.6016 8.45307 11.6016 9.00134C11.6016 9.5496 12.0553 10.0007 12.6036 10.0007H15.603C16.1513 10.0007 16.6018 9.5496 16.6018 9.00134C16.6018 8.45306 16.1513 8 15.603 8H12.6036Z"
          fill="white"
        />
        <path
          d="M12.6036 14C12.0553 14 11.6016 14.4511 11.6016 14.9994C11.6016 15.5477 12.0553 15.9981 12.6036 15.9981H15.603C16.1513 15.9981 16.6018 15.5477 16.6018 14.9994C16.6018 14.4511 16.1513 14 15.603 14H12.6036Z"
          fill="white"
        />
        <path
          d="M12.6029 11C12.0547 11 11.6016 11.4511 11.6016 11.9994C11.6016 12.5477 12.0547 12.9981 12.6029 12.9981H15.603C16.1513 12.9981 16.6018 12.5477 16.6018 11.9994C16.6018 11.4511 16.1513 11 15.603 11H12.6029Z"
          fill="white"
        />
      </g>
    </g>
    <defs>
      <filter
        id="filter0_d_14235_175916"
        x="-0.398438"
        y="1"
        width="26"
        height="26"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="2" />
        <feGaussianBlur stdDeviation="2.5" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.14902 0 0 0 0 0.2 0 0 0 0 0.301961 0 0 0 0.15 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_14235_175916"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_14235_175916"
          result="shape"
        />
      </filter>
      <clipPath id="clip0_14235_175916">
        <rect x="0.601562" width="24" height="24" rx="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export default IconPreProcessingNew;
