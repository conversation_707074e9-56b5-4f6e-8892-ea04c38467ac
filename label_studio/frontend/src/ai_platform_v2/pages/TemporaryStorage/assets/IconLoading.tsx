import React from "react";
import { Props } from "./base";

function IconLoading({
  className,
  color = "#00BEF2",
  size = 26,
}: Partial<Props>) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 100 100"
      preserveAspectRatio="xMidYMid"
      width={size}
      height={size}
    >
      <g data-idx="1">
        <circle
          fill="none"
          strokeWidth="10"
          stroke="rgba(102, 204, 167, 0.2)"
          r="30"
          cy="50"
          cx="50"
          data-idx="2"
        ></circle>
        <circle
          fill="none"
          strokeLinecap="round"
          strokeWidth="10"
          stroke="#66cca7"
          r="30"
          cy="50"
          cx="50"
          data-idx="3"
          transform="matrix(0.1775607847914215,0.9841098351831743,-0.9841098351831743,0.1775607847914215,90.32745251958764,-8.083530998729792)"
          strokeDasharray="121.194px, 67.3016px"
        ></circle>
        <g data-idx="6"></g>
      </g>
    </svg>
  );
}

export default IconLoading;
