const IconTransfer = ({ size = "30" }: any) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15 5C9.48649 5 5 9.48649 5 15C5 20.5135 9.48649 25 15 25C20.5135 25 25 20.5135 25 15C25 9.48649 20.5135 5 15 5Z"
      fill="#33BFFF"
    />
    <path
      d="M15 5C9.48649 5 5 9.48649 5 15C5 20.5135 9.48649 25 15 25C20.5135 25 25 20.5135 25 15C25 9.48649 20.5135 5 15 5Z"
      fill="#33BFFF"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.393 9.566L9.64336 11.3156C9.45231 11.4648 9.32812 11.6969 9.32812 11.9545C9.32812 12.0078 9.33343 12.06 9.34354 12.1105C9.37152 12.2681 9.44566 12.4187 9.56596 12.539L11.393 14.366C11.7065 14.6795 12.2254 14.6795 12.5389 14.366C12.8524 14.0525 12.8524 13.5336 12.5389 13.2201L12.0842 12.7653H18.1389C18.647 12.7653 19.0579 13.1761 19.0579 13.6842V15.5978C19.0579 16.041 19.4254 16.4086 19.8687 16.4086C20.3119 16.4086 20.6795 16.041 20.6795 15.5978V13.6842C20.6795 12.2896 19.5443 11.1437 18.1389 11.1437H12.1072L12.5389 10.7119C12.8524 10.3984 12.8524 9.87951 12.5389 9.566C12.3768 9.40383 12.1714 9.32812 11.966 9.32812C11.7606 9.32812 11.5443 9.40383 11.393 9.566ZM18.6146 21.5761L20.3642 19.8265C20.5553 19.6773 20.6795 19.4452 20.6795 19.1876C20.6795 19.1343 20.6742 19.0822 20.6641 19.0316C20.6361 18.874 20.562 18.7234 20.4416 18.6031L18.6146 16.7761C18.3011 16.4626 17.7822 16.4626 17.4687 16.7761C17.1552 17.0896 17.1552 17.6085 17.4687 17.922L17.9234 18.3768H11.8687C11.3606 18.3768 10.9497 17.966 10.9497 17.4579V15.5443C10.9497 15.1011 10.5822 14.7335 10.1389 14.7335C9.69569 14.7335 9.32812 15.1011 9.32812 15.5443V17.4579C9.32812 18.8525 10.4633 19.9984 11.8687 19.9984H17.9004L17.4687 20.4302C17.1552 20.7437 17.1552 21.2626 17.4687 21.5761C17.6308 21.7383 17.8362 21.814 18.0416 21.814C18.247 21.814 18.4633 21.7383 18.6146 21.5761Z"
      fill="white"
    />
  </svg>
);

export default IconTransfer;
