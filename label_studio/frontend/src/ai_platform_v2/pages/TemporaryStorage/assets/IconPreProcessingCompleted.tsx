const IconPreProcessingCompleted = ({ size = "24" }: any) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={size}
    height={size}
    viewBox="0 0 25 24"
    fill="none"
  >
    <g clipPath="url(#clip0_14235_175895)">
      <g filter="url(#filter0_d_14235_175895)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.6016 4C8.18556 4 4.60156 7.584 4.60156 12C4.60156 16.416 8.18556 20 12.6016 20C17.0176 20 20.6016 16.416 20.6016 12C20.6016 7.584 17.0176 4 12.6016 4ZM15.7067 9.03194L11.0027 13.7359L9.49872 12.2319C9.34925 12.0821 9.14633 11.998 8.93472 11.998C8.7231 11.998 8.52018 12.0821 8.37072 12.2319C8.05872 12.5439 8.05872 13.0479 8.37072 13.3599L10.4427 15.4319C10.7547 15.7439 11.2587 15.7439 11.5707 15.4319L16.8427 10.1599C17.1547 9.84794 17.1547 9.34394 16.8427 9.03194C16.5307 8.71994 16.0187 8.71994 15.7067 9.03194Z"
          fill="#15803D"
        />
        <path
          d="M15.7067 9.03194L11.0027 13.7359L9.49872 12.2319C9.34925 12.0821 9.14633 11.998 8.93472 11.998C8.7231 11.998 8.52018 12.0821 8.37072 12.2319C8.05872 12.5439 8.05872 13.0479 8.37072 13.3599L10.4427 15.4319C10.7547 15.7439 11.2587 15.7439 11.5707 15.4319L16.8427 10.1599C17.1547 9.84794 17.1547 9.34394 16.8427 9.03194C16.5307 8.71994 16.0187 8.71994 15.7067 9.03194Z"
          fill="white"
        />
      </g>
    </g>
    <defs>
      <filter
        id="filter0_d_14235_175895"
        x="-0.398438"
        y="1"
        width="26"
        height="26"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="2" />
        <feGaussianBlur stdDeviation="2.5" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.14902 0 0 0 0 0.2 0 0 0 0 0.301961 0 0 0 0.15 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_14235_175895"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_14235_175895"
          result="shape"
        />
      </filter>
      <clipPath id="clip0_14235_175895">
        <rect x="0.601562" width="24" height="24" rx="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export default IconPreProcessingCompleted;
