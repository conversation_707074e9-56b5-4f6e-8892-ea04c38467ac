const IconCompletedHighlight = ({ size = "38" }: any) => (
  <svg
    width="38"
    height="36"
    viewBox="0 0 38 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="4.95312"
      y="1.66797"
      width="28.6667"
      height="28.6667"
      rx="14.3333"
      stroke="url(#paint0_linear_17303_224843)"
      strokeWidth="2"
    />
    <g filter="url(#filter0_d_17303_224843)">
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.2865 2.66797C11.9265 2.66797 5.95312 8.6413 5.95312 16.0013C5.95312 23.3613 11.9265 29.3346 19.2865 29.3346C26.6465 29.3346 32.6198 23.3613 32.6198 16.0013C32.6198 8.6413 26.6465 2.66797 19.2865 2.66797Z"
        fill="#15803D"
      />
      <path
        d="M6.45312 16.0013C6.45312 8.91744 12.2026 3.16797 19.2865 3.16797C26.3703 3.16797 32.1198 8.91744 32.1198 16.0013C32.1198 23.0852 26.3703 28.8346 19.2865 28.8346C12.2026 28.8346 6.45312 23.0852 6.45312 16.0013Z"
        stroke="#F9F3FB"
      />
    </g>
    <path
      d="M26.947 10.9766C26.85 10.8788 26.7346 10.8012 26.6074 10.7482C26.4803 10.6952 26.3439 10.668 26.2061 10.668C26.0684 10.668 25.932 10.6952 25.8048 10.7482C25.6776 10.8012 25.5622 10.8788 25.4652 10.9766L17.6908 18.7615L14.4245 15.4847C14.3238 15.3875 14.2049 15.3109 14.0746 15.2596C13.9443 15.2083 13.8052 15.1831 13.6652 15.1855C13.5251 15.1879 13.387 15.2179 13.2585 15.2737C13.1301 15.3295 13.0139 15.4101 12.9166 15.5108C12.8193 15.6116 12.7428 15.7305 12.6915 15.8608C12.6401 15.991 12.6149 16.1302 12.6173 16.2702C12.6198 16.4102 12.6497 16.5484 12.7056 16.6768C12.7614 16.8053 12.842 16.9215 12.9427 17.0188L16.9499 21.026C17.0469 21.1238 17.1623 21.2014 17.2895 21.2544C17.4167 21.3074 17.553 21.3346 17.6908 21.3346C17.8286 21.3346 17.965 21.3074 18.0921 21.2544C18.2193 21.2014 18.3347 21.1238 18.4317 21.026L26.947 12.5107C27.053 12.4129 27.1375 12.2943 27.1953 12.1623C27.2531 12.0303 27.283 11.8878 27.283 11.7437C27.283 11.5995 27.2531 11.457 27.1953 11.325C27.1375 11.193 27.053 11.0744 26.947 10.9766Z"
      fill="white"
    />
    <defs>
      <filter
        id="filter0_d_17303_224843"
        x="0.953125"
        y="1.66797"
        width="36.6641"
        height="36.668"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="4" />
        <feGaussianBlur stdDeviation="2.5" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 1 0 0 0 0 0.729412 0 0 0 0.15 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_17303_224843"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_17303_224843"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_17303_224843"
        x1="11.4531"
        y1="3.66797"
        x2="27.4531"
        y2="28.168"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#00FFBA" />
        <stop offset="0.199458" stopColor="#13FEC8" />
        <stop offset="0.360405" stopColor="#22FDD3" />
        <stop offset="0.442688" stopColor="#2AFCD8" />
        <stop offset="0.549503" stopColor="#34FCE0" />
        <stop offset="0.650457" stopColor="#3EFBE7" />
        <stop offset="0.80623" stopColor="#4DFAF2" />
        <stop offset="0.908414" stopColor="#56FAF9" />
        <stop offset="1" stopColor="#5FF9FF" />
      </linearGradient>
    </defs>
  </svg>
);

export default IconCompletedHighlight;
