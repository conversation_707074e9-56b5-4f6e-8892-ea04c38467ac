import React from "react";
import { twMerge } from "tailwind-merge";

const PreProcessingNewIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="29"
      height="29"
      viewBox="0 0 29 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="1" y="1" width="27" height="27" rx="13.5" fill="#818CF8" />
      <rect
        x="1"
        y="1"
        width="27"
        height="27"
        rx="13.5"
        stroke="#F5F6F7"
        strokeWidth="2"
      />
      <path
        d="M9.81197 16.5859C9.74355 16.5857 9.67574 16.5989 9.61243 16.6248C9.54911 16.6508 9.49154 16.6889 9.44298 16.7371C9.39442 16.7853 9.35584 16.8426 9.32943 16.9058C9.30303 16.9689 9.2893 17.0366 9.28906 17.105V18.4084C9.28906 19.1214 9.8795 19.7119 10.5925 19.7119H11.8921C11.9608 19.7121 12.0289 19.6988 12.0925 19.6726C12.156 19.6464 12.2138 19.6079 12.2624 19.5593C12.311 19.5107 12.3495 19.4529 12.3757 19.3894C12.4019 19.3258 12.4152 19.2577 12.415 19.1889C12.4147 19.1205 12.401 19.0528 12.3746 18.9897C12.3482 18.9266 12.3096 18.8693 12.2611 18.8211C12.2125 18.7729 12.1549 18.7347 12.0916 18.7088C12.0283 18.6828 11.9605 18.6696 11.8921 18.6699H10.5925C10.4425 18.6699 10.331 18.5585 10.331 18.4084V17.105C10.3306 16.9675 10.2757 16.8357 10.1785 16.7385C10.0812 16.6413 9.94949 16.5864 9.81197 16.5859Z"
        fill="white"
      />
      <path
        d="M9.81299 12.9414C9.6755 12.9412 9.54349 12.9953 9.44574 13.092L8.40302 14.1347C8.35451 14.1831 8.31603 14.2406 8.28977 14.3038C8.26352 14.3671 8.25 14.435 8.25 14.5035C8.25 14.572 8.26352 14.6398 8.28977 14.7031C8.31603 14.7664 8.35451 14.8239 8.40302 14.8722L9.44574 15.915C9.54334 16.012 9.67536 16.0664 9.81299 16.0664C9.95061 16.0664 10.0826 16.012 10.1802 15.915L11.2219 14.8722C11.2704 14.8239 11.3089 14.7664 11.3352 14.7031C11.3614 14.6398 11.375 14.572 11.375 14.5035C11.375 14.435 11.3614 14.3671 11.3352 14.3038C11.3089 14.2406 11.2704 14.1831 11.2219 14.1347L10.1802 13.092C10.0825 12.9953 9.95048 12.9412 9.81299 12.9414Z"
        fill="white"
      />
      <path
        d="M10.5925 9.29297C9.8795 9.29297 9.28906 9.88341 9.28906 10.5964V11.8998C9.2893 11.9682 9.30303 12.0359 9.32943 12.0991C9.35584 12.1622 9.39442 12.2195 9.44298 12.2677C9.49154 12.3159 9.54911 12.3541 9.61243 12.38C9.67574 12.4059 9.74355 12.4192 9.81197 12.4189C9.94949 12.4184 10.0812 12.3636 10.1785 12.2663C10.2757 12.1691 10.3306 12.0373 10.331 11.8998V10.5964C10.331 10.4464 10.4425 10.3349 10.5925 10.3349H11.8921C11.9605 10.3352 12.0283 10.322 12.0916 10.2961C12.1549 10.2701 12.2125 10.232 12.2611 10.1838C12.3096 10.1356 12.3482 10.0783 12.3746 10.0151C12.401 9.95202 12.4147 9.88432 12.415 9.8159C12.4152 9.74715 12.4019 9.67902 12.3757 9.61545C12.3495 9.55188 12.311 9.49413 12.2624 9.44552C12.2138 9.39691 12.156 9.3584 12.0925 9.33222C12.0289 9.30604 11.9608 9.2927 11.8921 9.29297H10.5925Z"
        fill="white"
      />
      <path
        d="M10.8588 13.9805C10.7901 13.9802 10.722 13.9935 10.6584 14.0197C10.5949 14.0459 10.5371 14.0844 10.4885 14.133C10.4399 14.1816 10.4014 14.2394 10.3752 14.303C10.349 14.3665 10.3357 14.4347 10.3359 14.5034C10.3362 14.5718 10.3499 14.6395 10.3763 14.7026C10.4027 14.7658 10.4413 14.823 10.4899 14.8712C10.5384 14.9194 10.596 14.9576 10.6593 14.9836C10.7226 15.0095 10.7904 15.0227 10.8588 15.0224H11.897C11.9654 15.0227 12.0332 15.0095 12.0965 14.9836C12.1598 14.9576 12.2174 14.9195 12.266 14.8713C12.3145 14.8231 12.3531 14.7658 12.3795 14.7026C12.4059 14.6395 12.4196 14.5718 12.4199 14.5034C12.4202 14.4346 12.4068 14.3665 12.3806 14.3029C12.3544 14.2394 12.3159 14.1816 12.2673 14.133C12.2187 14.0844 12.161 14.0459 12.0974 14.0197C12.0338 13.9935 11.9657 13.9802 11.897 13.9805H10.8588Z"
        fill="white"
      />
      <path
        d="M14.5031 8.25C13.6465 8.25 12.9375 8.95792 12.9375 9.81459C12.9375 10.6712 13.6465 11.3761 14.5031 11.3761H19.1898C20.0465 11.3761 20.7504 10.6712 20.7504 9.81459C20.7504 8.9579 20.0465 8.25 19.1898 8.25H14.5031Z"
        fill="white"
      />
      <path
        d="M14.5031 17.6289C13.6465 17.6289 12.9375 18.3338 12.9375 19.1905C12.9375 20.0471 13.6465 20.751 14.5031 20.751H19.1898C20.0465 20.751 20.7504 20.0471 20.7504 19.1905C20.7504 18.3338 20.0465 17.6289 19.1898 17.6289H14.5031Z"
        fill="white"
      />
      <path
        d="M14.5021 12.9414C13.6455 12.9414 12.9375 13.6463 12.9375 14.503C12.9375 15.3596 13.6455 16.0635 14.5021 16.0635H19.1898C20.0465 16.0635 20.7504 15.3596 20.7504 14.503C20.7504 13.6463 20.0465 12.9414 19.1898 12.9414H14.5021Z"
        fill="white"
      />
    </svg>
  );
};

export default PreProcessingNewIcon;
