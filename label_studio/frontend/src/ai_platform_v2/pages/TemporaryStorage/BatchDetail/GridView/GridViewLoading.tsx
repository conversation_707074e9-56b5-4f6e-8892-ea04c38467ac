import React, { memo } from "react";
import { Checkbox } from "@taureau/ui";

interface IProps {
  sizeGrid: number;
}

const GridViewLoading = (props: IProps) => {
  const { sizeGrid } = props;

  return (
    <div
      className="grid-card"
      style={{
        width: sizeGrid,
        aspectRatio: "1 / 1",
      }}
    >
      <div className="w-full flex flex-row justify-between">
        <Checkbox />
      </div>
      <div className="w-full h-full min-h-[80px] overflow-hidden rounded-[10px] mt-[10px]">
        <div className="w-full h-full group relative">
          <div className="w-full h-full rounded-[10px] skeleton" />
        </div>
      </div>

      <div className="flex flex-row items-center justify-between w-full mt-[5px] gap-2.5">
        <div className="w-3/4">
          <div className="h-2.5 w-full skeleton" />
        </div>
      </div>
    </div>
  );
};

export default memo(GridViewLoading);
