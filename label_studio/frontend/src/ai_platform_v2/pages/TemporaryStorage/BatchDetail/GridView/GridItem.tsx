import React, { useCallback, useEffect, useMemo } from "react";

import { Checkbox } from "@taureau/ui";
import Tooltip from "@/ai_platform_v2/component/Tooltip/Tooltip";

import classNames from "classnames";

import { getTempStorageUrl } from "@/ai_platform_v2/pages/DataManager/helper";
import { getCurrentProject } from "@/pages/DataSet/Const";
import LoadingImage from "@/components/image/LoadImage";

import HumanApprovalIcon from "./icons/HumanApprovalIcon";
import PreProcessingCompletedIcon from "./icons/PreProcessingCompletedIcon";
import PreProcessingNewIcon from "./icons/PreProcessingNewIcon";
import UserAvatar from "@/ai_platform_v2/pages/SettingsAccount/component/SettingProfile/UserAvatar";
import {
  DATE_TIME_FORMAT,
  convertUTCToLocalTime,
} from "@/ai_platform_v2/utils/dateTime";

interface Props {
  file: any;
  checked?: boolean;
  sizeGrid: number;
  onChangeCheckbox?: (checked: boolean, file: any) => void;
  [key: string]: any;
}

const GridItem = ({
  file,
  onChangeCheckbox,
  checked,
  sizeGrid,
  handleOpenDataProcessor,
  isLockEditor,
  isBatchDone,
}: Props) => {
  const {
    id,
    isCompleted,
    avatar,
    fullName,
    fileName,
    transferedBy,
    transfedDate,
    workflowState,
    transferStatus,
    stepStatus,
    workflowStepId,
  } = file;

  const projectID = getCurrentProject();

  const isShortVersion = useMemo(() => sizeGrid < 190, [sizeGrid]);

  const onChange = (e: any) => {
    onChangeCheckbox && onChangeCheckbox(e.target.checked, file);
  };

  const renderCurrentWorkflowStep = useCallback((step: string) => {
    switch (step) {
      case "PreProcessingNew":
        return (
          <div className="min-w-[25px]">
            <PreProcessingNewIcon />
          </div>
        );

      case "HumanApproval":
        return (
          <div className="min-w-[25px]">
            <HumanApprovalIcon />
          </div>
        );
      case "PreProcessingCompleted":
        return (
          <div className="min-w-[25px]">
            <PreProcessingCompletedIcon />
          </div>
        );
      default:
        return (
          <div className="min-w-[25px]">
            <PreProcessingNewIcon />
          </div>
        );
    }
  }, []);

  const renderTransferUser = useCallback(() => {
    if (!isCompleted || transferStatus !== "Completed") return;
    return (
      <div className="w-[27px] h-[27px] min-w-[27px] flex justify-center items-center rounded-full border-[2px] border-solid border-[#F5F6F7] overflow-hidden">
        <Tooltip
          title={
            <div
              style={{
                whiteSpace: "pre-wrap",
                textAlign: "center",
                maxWidth: 200,
              }}
            >{`${fullName} transferred at \n ${convertUTCToLocalTime(transfedDate, DATE_TIME_FORMAT.MMMDDYYYY_HHMM)}`}</div>
          }
        >
          {fullName === "System" ? (
            <svg
              width="26"
              height="25"
              viewBox="0 0 26 25"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g id="Frame 629441">
                <rect
                  x="-0.015625"
                  y="-1"
                  width="27"
                  height="27"
                  rx="13.5"
                  fill="white"
                />
                <rect
                  x="-0.015625"
                  y="-1"
                  width="27"
                  height="27"
                  rx="13.5"
                  fill="#3361FF"
                  fill-opacity="0.1"
                />
                <rect
                  x="-0.015625"
                  y="-1"
                  width="27"
                  height="27"
                  rx="13.5"
                  stroke="#F5F6F7"
                  stroke-width="2"
                />
                <path
                  id="S"
                  d="M13.1662 18.66C12.4302 18.66 11.8009 18.5693 11.2782 18.388C10.7555 18.2067 10.3182 17.972 9.96619 17.684C9.62485 17.396 9.35285 17.092 9.15019 16.772C8.94752 16.4413 8.79285 16.132 8.68619 15.844C8.59019 15.556 8.52619 15.3213 8.49419 15.14C8.46219 14.9587 8.44619 14.868 8.44619 14.868H10.6542C10.6542 14.868 10.6649 14.932 10.6862 15.06C10.7182 15.188 10.7822 15.3533 10.8782 15.556C10.9742 15.748 11.1129 15.94 11.2942 16.132C11.4862 16.324 11.7369 16.4893 12.0462 16.628C12.3662 16.756 12.7609 16.82 13.2302 16.82C13.9555 16.82 14.5049 16.66 14.8782 16.34C15.2622 16.0093 15.4542 15.604 15.4542 15.124C15.4542 14.7187 15.3102 14.3987 15.0222 14.164C14.7342 13.9187 14.3075 13.7373 13.7422 13.62L12.1742 13.284C11.5662 13.156 10.9955 12.964 10.4622 12.708C9.93952 12.452 9.51819 12.1053 9.19819 11.668C8.87819 11.2307 8.71819 10.6707 8.71819 9.988C8.71819 9.27333 8.89419 8.65467 9.24619 8.132C9.60885 7.60933 10.1049 7.20933 10.7342 6.932C11.3635 6.644 12.0995 6.5 12.9422 6.5C13.7422 6.5 14.4035 6.61733 14.9262 6.852C15.4489 7.08667 15.8649 7.37467 16.1742 7.716C16.4942 8.05733 16.7289 8.39867 16.8782 8.74C17.0275 9.08133 17.1235 9.36933 17.1662 9.604C17.2195 9.83867 17.2462 9.956 17.2462 9.956H15.1182C15.1182 9.956 15.0969 9.876 15.0542 9.716C15.0115 9.54533 14.9155 9.35333 14.7662 9.14C14.6169 8.92667 14.3929 8.74 14.0942 8.58C13.7955 8.40933 13.3955 8.324 12.8942 8.324C12.1795 8.324 11.6569 8.484 11.3262 8.804C11.0062 9.124 10.8462 9.47067 10.8462 9.844C10.8462 10.228 10.9955 10.5267 11.2942 10.74C11.5929 10.9533 11.9929 11.1187 12.4942 11.236L14.1742 11.588C14.8035 11.7267 15.3795 11.9347 15.9022 12.212C16.4249 12.4893 16.8409 12.8573 17.1502 13.316C17.4595 13.764 17.6142 14.324 17.6142 14.996C17.6142 15.6787 17.4435 16.2973 17.1022 16.852C16.7609 17.4067 16.2595 17.8493 15.5982 18.18C14.9369 18.5 14.1262 18.66 13.1662 18.66Z"
                  fill="#3361FF"
                />
              </g>
            </svg>
          ) : (
            <div className="w-full h-full">
              <UserAvatar
                src={avatar}
                user={{
                  firstName: fullName,
                  userId: transferedBy,
                }}
                className="w-full h-full rounded-full"
                textClassName="text-[14px]"
              />
            </div>
          )}
        </Tooltip>
      </div>
    );
  }, [avatar, fullName, isCompleted, transferedBy]);

  const renderActionBtn = useCallback(() => {
    if (isLockEditor) return <></>;
    return (
      <div className="absolute top-0 bottom-0 right-0 left-0 bg-black-30 rounded-lg flex justify-center items-center opacity-0 group-hover:opacity-100">
        <div
          className="px-[15px] py-[5px] rounded-[15px] text-[#3361FF] bg-[#EFF2FF] cursor-pointer text-[12px] font-medium leading-[18px] hover:bg-[#DDE4FF]"
          style={{
            boxShadow: "0px 3px 3px 0px rgba(0, 0, 0, 0.05)",
          }}
          onClick={() => {
            handleOpenDataProcessor(file);
          }}
        >
          Open
        </div>
      </div>
    );
  }, [file, isLockEditor]);

  const renderStepStatus = useCallback(() => {
    if (isCompleted) {
      if (transferStatus === "Completed")
        return (
          <div className="whitespace-nowrap text-ellipsis overflow-hidden font-medium text-[14px] text-[#33BFFF]">
            Transferred
          </div>
        );

      if (transferStatus === "ReadyTransfer")
        return (
          <div className="whitespace-nowrap text-ellipsis overflow-hidden font-medium text-[14px] text-[#33BFFF]">
            Transferring
          </div>
        );
    }

    if (workflowState === "HumanApproval" && stepStatus === "Reject") {
      return (
        <div className="whitespace-nowrap text-ellipsis overflow-hidden font-medium text-[14px] text-[#DE5462]">
          Rejected
        </div>
      );
    }

    return (
      <div className="whitespace-nowrap text-ellipsis overflow-hidden font-medium text-[14px] text-[#2EE6CA]">
        Ready
      </div>
    );
  }, [isCompleted, stepStatus, transferStatus, workflowState]);

  return (
    <>
      <div
        className="grid-card min-w-[180px]"
        style={{
          width: sizeGrid,
          aspectRatio: "1 / 1",
        }}
      >
        <div className="w-full flex flex-row justify-between">
          {isBatchDone ? (
            <div />
          ) : (
            <Checkbox
              onChange={onChange}
              checked={Boolean(checked)}
              size={isShortVersion ? "xs" : "sm"}
            />
          )}

          {!isShortVersion && (
            <div className="flex justify-center items-center gap-1">
              {renderCurrentWorkflowStep(workflowState ?? "PreProcessingNew")}
              {renderTransferUser()}
            </div>
          )}
        </div>

        <div className="w-full h-full min-h-[80px] overflow-hidden rounded-[10px] mt-[10px]">
          <div className="w-full h-full group relative">
            <LoadingImage
              src={getTempStorageUrl(projectID, id, "Original")}
              alt=""
              className="w-full h-full object-cover rounded-[10px]"
              rederImageError={() => {
                return (
                  <div className="no-image-preview w-full h-full text-[13px]">
                    Error preview
                  </div>
                );
              }}
            />

            {renderActionBtn()}
          </div>
        </div>

        {!isShortVersion && (
          <>
            <div className="flex flex-row items-center justify-between w-full mt-[5px]">
              <div className="flex flex-col w-full">
                <Tooltip title={fileName}>
                  <div className="whitespace-nowrap text-ellipsis overflow-hidden  font-medium text-[14px] text-gray-blue-grey-blue-40 select-text">
                    {fileName || id}
                  </div>
                </Tooltip>

                {renderStepStatus()}
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default GridItem;
