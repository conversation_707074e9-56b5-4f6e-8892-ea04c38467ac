import React, { memo, useEffect, useState } from "react";
import classNames from "classnames";
import { <PERSON>lider } from "antd";
import { Checkbox } from "@taureau/ui";
import { CheckboxChangeEvent } from "@taureau/ui/dist/Checkbox";
import AutoSizer from "react-virtualized-auto-sizer";
import { difference, differenceBy, isEmpty, uniq } from "lodash";
import GridItem from "./GridItem";

import GridViewLoading from "./GridViewLoading";

const GRID_GAP = 10;

const GridView = ({
  setBatchDetailListSelected,
  batchDetailListSelected,
  batchDetailList,
  isLoading,
  handleOpenDataProcessor,
  isLockEditor,
  isBatchDone,
}: any) => {
  const [sizeGrid, setSizeGrid] = useState(5);

  const [checkedAll, setCheckedAll] = useState<boolean>(false);

  const changeSizeGrid = (value: number) => {
    setSizeGrid(value);
  };

  const onChangeCheckbox = (checked: boolean, file: any) => {
    if (checked) {
      setBatchDetailListSelected([...batchDetailListSelected, file]);
    } else {
      setBatchDetailListSelected(
        batchDetailListSelected.filter((item: any) => item.id !== file.id)
      );
    }
  };

  const handleChangeSelectAll = (e: CheckboxChangeEvent) => {
    const checked = e.target.checked;

    if (checked) {
      setBatchDetailListSelected(
        uniq([...batchDetailListSelected, ...batchDetailList])
      );
    } else {
      setBatchDetailListSelected(
        differenceBy(batchDetailListSelected, batchDetailList, "id")
      );
    }
  };

  useEffect(() => {
    setCheckedAll(
      !differenceBy(batchDetailList, batchDetailListSelected, "id")?.length
    );
  }, [batchDetailList, batchDetailListSelected]);

  return (
    <div className={classNames("py-2.5 w-full h-full")}>
      <div className="flex items-center justify-between px-1.5 py-1.5">
        {isBatchDone ? (
          <div />
        ) : (
          <Checkbox
            checked={checkedAll}
            onChange={handleChangeSelectAll}
            labelClassName="font-black"
          >
            Select All
          </Checkbox>
        )}

        <div className="flex px-[10px] py-[8px] flex-col items-start gap-2.5 wrap-slider w-[96px]">
          <div className="w-full">
            <Slider
              min={1}
              max={10}
              onChange={(e) => changeSizeGrid(e)}
              value={sizeGrid}
              style={{
                margin: 0,
              }}
              railStyle={{
                backgroundColor: "rgba(237, 239, 242, 1)",
                height: "5px",
                borderRadius: "40px",
              }}
              trackStyle={{
                backgroundColor: "rgba(51, 97, 255, 1)",
                height: "5px",
                borderRadius: "40px",
              }}
            />
          </div>
        </div>
      </div>

      <div className="p-2.5 h-[calc(100vh-280px)] overflow-y-auto">
        <AutoSizer>
          {({ width }) => {
            const maxItemSize = Math.floor(width / sizeGrid) - GRID_GAP;

            return (
              <div
                className="grid-cards gap-[10px]"
                style={{
                  width,
                }}
              >
                {isLoading
                  ? [...Array(8).keys()].map((i) => (
                      <GridViewLoading key={i} sizeGrid={maxItemSize} />
                    ))
                  : batchDetailList?.map((file: any) => (
                      <GridItem
                        key={file.id}
                        file={file}
                        onChangeCheckbox={onChangeCheckbox}
                        checked={
                          !isEmpty(
                            batchDetailListSelected?.find(
                              (item: any) => item.id === file.id
                            )
                          )
                        }
                        sizeGrid={maxItemSize}
                        handleOpenDataProcessor={handleOpenDataProcessor}
                        isLockEditor={isLockEditor}
                        isBatchDone={isBatchDone}
                      />
                    ))}
              </div>
            );
          }}
        </AutoSizer>
      </div>
    </div>
  );
};

export default memo(GridView);
