import React from "react";
import { twMerge } from "tailwind-merge";

const PreProcessingCompletedIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="30"
      height="29"
      viewBox="0 0 30 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.4844 2C8.58438 2 2.98438 7.6 2.98438 14.5C2.98438 21.4 8.58438 27 15.4844 27C22.3844 27 27.9844 21.4 27.9844 14.5C27.9844 7.6 22.3844 2 15.4844 2ZM20.3362 9.86241L12.9862 17.2124L10.6362 14.8624C10.4026 14.6283 10.0856 14.4968 9.75493 14.4968C9.42428 14.4968 9.10722 14.6283 8.87368 14.8624C8.38618 15.3499 8.38618 16.1374 8.87368 16.6249L12.1112 19.8624C12.5987 20.3499 13.3862 20.3499 13.8737 19.8624L22.1112 11.6249C22.5987 11.1374 22.5987 10.3499 22.1112 9.86241C21.6237 9.37491 20.8237 9.37491 20.3362 9.86241Z"
        fill="#15803D"
      />
      <path
        d="M20.3362 9.86241L12.9862 17.2124L10.6362 14.8624C10.4026 14.6283 10.0856 14.4968 9.75493 14.4968C9.42428 14.4968 9.10722 14.6283 8.87368 14.8624C8.38618 15.3499 8.38618 16.1374 8.87368 16.6249L12.1112 19.8624C12.5987 20.3499 13.3862 20.3499 13.8737 19.8624L22.1112 11.6249C22.5987 11.1374 22.5987 10.3499 22.1112 9.86241C21.6237 9.37491 20.8237 9.37491 20.3362 9.86241Z"
        fill="white"
      />
      <path
        d="M15.4844 1C8.03209 1 1.98438 7.04772 1.98438 14.5C1.98438 21.9523 8.03209 28 15.4844 28C22.9367 28 28.9844 21.9523 28.9844 14.5C28.9844 7.04772 22.9367 1 15.4844 1Z"
        stroke="#F5F6F7"
        strokeWidth="2"
      />
    </svg>
  );
};

export default PreProcessingCompletedIcon;
