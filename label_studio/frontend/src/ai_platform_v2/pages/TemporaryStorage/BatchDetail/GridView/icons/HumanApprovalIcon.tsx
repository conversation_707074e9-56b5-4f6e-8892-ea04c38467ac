import React from "react";
import { twMerge } from "tailwind-merge";

const HumanApprovalIcon = ({ className }: { className?: string }) => {
  return (
    <svg
      width="30"
      height="29"
      viewBox="0 0 30 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="1.49219" y="1" width="27" height="27" rx="13.5" fill="#EDB7ED" />
      <rect
        x="1.49219"
        y="1"
        width="27"
        height="27"
        rx="13.5"
        stroke="#F5F6F7"
        strokeWidth="2"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M15.6866 11.0278C15.6866 12.5619 14.443 13.8056 12.9089 13.8056C11.3747 13.8056 10.1311 12.5619 10.1311 11.0278C10.1311 9.49365 11.3747 8.25 12.9089 8.25C14.443 8.25 15.6866 9.49365 15.6866 11.0278ZM14.9922 20.75H10.8255C10.2735 20.7484 9.74455 20.5283 9.3542 20.138C8.96386 19.7476 8.74384 19.2187 8.74219 18.6667V17.9722C8.74219 17.0513 9.10801 16.1682 9.75918 15.517C10.4103 14.8658 11.2935 14.5 12.2144 14.5H13.6033C14.5242 14.5 15.4074 14.8658 16.0585 15.517C16.7097 16.1682 17.0755 17.0513 17.0755 17.9722V18.6667C17.0739 19.2187 16.8538 19.7476 16.4635 20.138C16.0732 20.5283 15.5442 20.7484 14.9922 20.75ZM18.4644 13.8056C19.615 13.8056 20.5478 12.8729 20.5478 11.7223C20.5478 10.5717 19.615 9.63893 18.4644 9.63893C17.3138 9.63893 16.3811 10.5717 16.3811 11.7223C16.3811 12.8729 17.3138 13.8056 18.4644 13.8056ZM17.1102 14.6111C17.3227 14.5388 17.5455 14.5013 17.77 14.5H19.1588C19.7109 14.5016 20.2398 14.7217 20.6302 15.112C21.0205 15.5024 21.2405 16.0313 21.2422 16.5833V17.2778C21.2411 17.6458 21.0944 17.9984 20.8342 18.2587C20.5739 18.5189 20.2213 18.6656 19.8533 18.6667H18.4644V17.9722C18.4663 16.7186 17.9807 15.5133 17.1102 14.6111Z"
        fill="white"
      />
    </svg>
  );
};

export default HumanApprovalIcon;
