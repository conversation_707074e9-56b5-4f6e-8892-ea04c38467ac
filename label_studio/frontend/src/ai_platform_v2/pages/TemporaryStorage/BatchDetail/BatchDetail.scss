.batch-detail-title {
  .input-label-content {
    line-height: 21px !important;
  }
}

.deduplicateBtn {
  display: flex;
  height: 40px;
  padding: 5px 15px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  line-height: 21px;
  cursor: pointer;
  border: none;
  outline: none;
  width: 128px;
}

.viewReportBtn {
  width: 178px !important;
}

.deduplicateBtnLoading {
  width: fit-content;
  background-color: #fff !important;
  border: none !important ;
  animation: button-loading 1.8s linear infinite;
  background-image: repeating-linear-gradient(
    115deg,
    rgba(51, 97, 255, 0.4) 24px,
    rgba(51, 97, 255, 0.6) 0px,
    rgba(51, 97, 255, 0.6) 36px,
    rgba(51, 97, 255, 0.4) 28px,
    rgba(51, 97, 255, 0.4) 46.9px
  ) !important;
}

.cancelingBtn {
  color: #cc1414;
  width: fit-content;
  background-color: #fff !important;
  border: none !important ;
  animation: button-loading 1.8s linear infinite;
  background-image: repeating-linear-gradient(
    115deg,
    rgba(255, 0, 0, 0.15) 24px,
    rgba(255, 0, 0, 0.4) 0px,
    rgba(255, 0, 0, 0.4) 36px,
    rgba(255, 0, 0, 0.15) 28px,
    rgba(255, 0, 0, 0.15) 46.9px
  ) !important;
}

@keyframes button-loading {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 100px 0;
  }
}

.deduplicateBtnSecondary {
  background: #ebf0ff;
  box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.03);
  color: #3361ff;

  &:hover {
    background: var(--blue-blue-20, rgba(51, 97, 255, 0.4));
  }

  &:disabled {
    opacity: 0.6;
    background: #ebf0ff;
  }
}

.deduplicateBtnNegative {
  background: #fdebeb;
  box-shadow: 0px 2px 5px 0px rgba(253, 235, 235, 0.1);
  color: #cc1414;
}
