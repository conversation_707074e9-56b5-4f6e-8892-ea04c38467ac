import React, { memo, useCallback, useMemo, useState } from "react";
import { getCurrentProject } from "@/pages/DataSet/Const";
import { ModalConfirmBig } from "@taureau/ui";
import Message from "@/ai_platform_v2/component/Message/Message";
import useDeduplicationStore from "../store";
import FloatButton from "./FloatButton";
import { useAPI } from "@/providers/ApiProvider";
import IconRemoveFromCluster from "../icons/IconRemoveFromCluster";
import IconDelete from "../icons/IconDelete";

const MenuBar = ({ refetchClusterDetail, batchId, clusterId }: any) => {
  const projectId = getCurrentProject();

  const api = useAPI();

  const [openMenuBar, setOpenMenuBar] = useState(false);

  const {
    selectedFile,
    selectedCluster,
    listCluster,
    setListCluster,
    setSelectedFile,
  } = useDeduplicationStore((state) => ({
    selectedFile: state.selectedFile,
    selectedCluster: state.selectedCluster,
    listCluster: state.listCluster,
    setListCluster: state.setListCluster,
    setSelectedFile: state.setSelectedFile,
  }));

  const updateCountFileThresholds = useCallback(() => {
    const newListCluster = listCluster.map((item: any) => {
      if (item.id === selectedCluster) {
        return {
          ...item,
          countFileThresholds: item.countFileThresholds - selectedFile?.length,
        };
      }
      return item;
    });

    setListCluster(newListCluster);
  }, [listCluster, selectedCluster, selectedFile?.length]);

  const isAllFileSelectedFromTemporaryStorage = useMemo(
    () => selectedFile.every((file: any) => file.storageArea === "TempStorage"),
    [selectedFile]
  );

  const deleteFiles = useCallback(async () => {
    try {
      if (!isAllFileSelectedFromTemporaryStorage) {
        Message.error({
          content:
            "You can only delete files located in Temporary Storage here",
        });
        return;
      }

      const res = await api.callApi("deleteFilesTemporaryStorage", {
        params: {
          batchId,
          clusterId: selectedCluster,
          projectId,
        },

        body: {
          ids: selectedFile.map((file: any) => file.id),
        },
      });

      if (res?.success) {
        Message.success({
          content:
            selectedFile?.length === 1
              ? `${selectedFile?.[0]?.fileId} deleted from Temporary Storage`
              : `${selectedFile?.length} files deleted from Temporary Storage`,
        });
        refetchClusterDetail();
        updateCountFileThresholds();
        setSelectedFile([]);
      } else {
        Message.error({ content: res?.message });
      }
    } catch (error: any) {
      console.log(error?.message);
    }
  }, [
    batchId,
    isAllFileSelectedFromTemporaryStorage,
    projectId,
    selectedCluster,
    selectedFile,
  ]);

  const removeFiles = useCallback(async () => {
    try {
      const res = await api.callApi("removeFilesFromCluster", {
        params: {
          batchId,
          clusterId: selectedCluster,
          projectId,
        },

        body: {
          ids: selectedFile.map((file: any) => file.id),
        },
      });

      if (res?.success) {
        Message.success({
          content:
            selectedFile?.length === 1
              ? `${selectedFile?.[0]?.fileId} removed from cluster ${clusterId}`
              : `${selectedFile?.length} files removed from cluster ${clusterId}`,
        });
        refetchClusterDetail();
        updateCountFileThresholds();
        setSelectedFile([]);
      } else {
        Message.error({ content: res?.message });
      }
    } catch (error: any) {
      console.log(error?.message);
    }
  }, [
    batchId,
    isAllFileSelectedFromTemporaryStorage,
    projectId,
    selectedCluster,
    selectedFile,
  ]);

  const handleDelete = () => {
    ModalConfirmBig.warning({
      title:
        selectedFile?.length === 1
          ? `Delete  ${selectedFile?.[0]?.fileId} ?`
          : `Delete ${selectedFile?.length} selected files?`,
      content:
        "If continue, this will be permanently deleted from Temporary Storage.",
      okText: "Continue",
      onOk: deleteFiles,
    });
  };

  const handleRemove = () => {
    ModalConfirmBig.warning({
      title:
        selectedFile?.length === 1
          ? `Remove ${selectedFile?.[0]?.fileId} from cluster?`
          : `Remove ${selectedFile?.length} selected files from cluster?`,
      content:
        "If continue, these files will be permanently removed from cluster but still stayed in Temporary Storage.",
      okText: "Continue",
      onOk: removeFiles,
    });
  };

  const actions = [
    {
      label: "Remove from cluster",
      icon: <IconRemoveFromCluster />,
      onClick: handleRemove,
    },
    {
      label: "Delete from Temporary Storage",
      icon: <IconDelete />,
      onClick: handleDelete,
      background:
        "linear-gradient(0deg, rgba(230, 46, 46, 0.10) 0%, rgba(230, 46, 46, 0.10) 100%), #FFF",
    },
  ];

  return (
    <FloatButton
      badge={selectedFile.length}
      open={openMenuBar}
      onChangeOpen={() => {
        setOpenMenuBar(!openMenuBar);
      }}
      actions={actions}
    />
  );
};

export default memo(MenuBar);
