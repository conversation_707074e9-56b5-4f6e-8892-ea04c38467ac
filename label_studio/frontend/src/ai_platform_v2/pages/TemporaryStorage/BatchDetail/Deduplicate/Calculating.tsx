import React, { memo } from "react";
import classNames from "classnames";

import styles from "./styles.module.scss";
import useDeduplicationStore from "./store";
import IconCanceling from "./icons/IconCanceling";
import IconCalculating from "./icons/IconCalculating";

const Calculating = ({
  handleCancelDeduplicateReport,
}: {
  handleCancelDeduplicateReport: () => void;
}) => {
  const { isCancelingReport } = useDeduplicationStore((state) => ({
    isCancelingReport: state.isCancelingReport,
  }));

  return (
    <div className="w-full h-full flex flex-col items-center justify-center">
      {isCancelingReport ? (
        <>
          <IconCanceling />

          <div className={styles.canceling}>Cancelling deduplication</div>
        </>
      ) : (
        <>
          <IconCalculating />
          <div className={styles.calculating}>Calculating</div>
          <button
            className={classNames("w-[128px] h-[38px] mt-4", {
              [styles.btn]: true,
              [styles.btnNegative]: true,
            })}
            onClick={handleCancelDeduplicateReport}
          >
            Cancel
          </button>
        </>
      )}
    </div>
  );
};

export default memo(Calculating);
