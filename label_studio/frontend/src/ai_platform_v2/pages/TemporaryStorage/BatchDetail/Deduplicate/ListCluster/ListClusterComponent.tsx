import React, { memo, useCallback, useMemo, useState } from "react";

import SearchNodata from "@/ai_platform_v2/component/SearchNodata";
import useDeduplicationStore from "../store";
import ClusterItem from "./ClusterItem";
import ItemLoading from "./ClusterItem/ItemLoading";
import classNames from "classnames";

interface Props {
  width: number;
  height: number;
}

const GRID_GAP = 20;

const ListClusterComponent = (props: Props) => {
  const { width, height } = props;

  const { listCluster, isLoadingListCluster, selectedCluster } =
    useDeduplicationStore((state) => ({
      listCluster: state.listCluster,
      isLoadingListCluster: state.isLoadingListCluster,
      selectedCluster: state.selectedCluster,
    }));

  const itemNo = useMemo(() => {
    if (selectedCluster) return 1;

    return 7;
  }, [selectedCluster]);

  const renderContent = useCallback(() => {
    if (!listCluster?.length) {
      return <SearchNodata />;
    }
    return (
      <div
        dir="ltr"
        className={classNames("grid w-full justify-items-center	py-2", {
          "gap-5": !selectedCluster,
          "gap-3": selectedCluster,
        })}
        style={{
          gridTemplateColumns:
            itemNo === 1
              ? "repeat(1, minmax(auto, 1fr)"
              : `repeat(${itemNo}, minmax(auto, calc((100% - ${
                  (itemNo - 1) * GRID_GAP
                }px) / ${itemNo})))`,
        }}
      >
        {listCluster.map((item: any) => {
          return <ClusterItem key={item?.id} item={item} />;
        })}
      </div>
    );
  }, [itemNo, listCluster]);

  return (
    <div
      className="flex flex-col bg-white overflow-hidden rounded-[10px] scrollbar-v-sm"
      style={{ width, height }}
    >
      <div
        className="h-full w-full overflow-auto"
        dir={selectedCluster ? "rtl" : "ltr"}
      >
        {isLoadingListCluster ? (
          <div
            className={classNames("grid w-full justify-items-center	py-2", {
              "gap-5": !selectedCluster,
              "gap-3": selectedCluster,
            })}
            style={{
              gridTemplateColumns:
                itemNo === 1
                  ? "repeat(1, minmax(auto, 1fr)"
                  : `repeat(${itemNo}, minmax(auto, calc((100% - ${
                      (itemNo - 1) * GRID_GAP
                    }px) / ${itemNo})))`,
            }}
          >
            {[...Array(10).keys()].map((i) => (
              <ItemLoading key={i} />
            ))}
          </div>
        ) : (
          renderContent()
        )}
      </div>
    </div>
  );
};

export default memo(ListClusterComponent);
