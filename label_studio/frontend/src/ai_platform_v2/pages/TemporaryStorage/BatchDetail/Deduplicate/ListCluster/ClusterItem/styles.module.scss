.clusterItem {
  // grid-column-end: span 1;
  box-sizing: border-box;
  border-radius: 5px;
  width: 150px;
  height: 150px;
  padding: 8px 4px;
  box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.15);

  display: flex;
  flex-direction: column;
  cursor: pointer;

  &:hover {
    background-color: #f5f6f7;
  }
}

.clusterItemActive {
  background-color: #3ea6ec4d !important;
  border: 1px solid var(--Light-Blue, #3ea6ec);
  box-shadow: 0px 2px 5px 0px rgba(62, 166, 236, 0.5);
}
