.deduplicate-menu-bar-container {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  flex-direction: column-reverse;
  position: absolute;
  right: 12px;
  bottom: 12px;
  max-height: 52px;
  gap: 8px;

  &.open {
    max-height: max-content;
  }

  li {
    min-width: 50px;
    min-height: 50px;
    border-radius: 100%;
    box-shadow: 0px 2px 10px 0px rgba(13, 17, 26, 0.1);
    display: grid;
    place-items: center;
    font-size: 24px;
    cursor: pointer;
    position: relative;

    transition:
      min-width 0.6s ease-out,
      min-height 0.6s ease-out;

    svg {
      transition: all 0.6s ease-out;
    }
  }

  .fab-button {
    background: var(--blue-blue-dark-1, #194dff);
    box-shadow: 0px 2px 10px 0px rgba(13, 17, 26, 0.1);
    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10 and IE 11 */
    user-select: none; /* Standard syntax */

    .count-file-selected {
      transition: font-size 0.6s;
    }

    svg {
      fill: white;
    }
  }

  .fab-action {
    transform: translateY(50px) scale(0);
    transition:
      transform 300ms,
      opacity 300ms;
    background-color: white;

    &:hover {
      .tooltip {
        transform: translateX(-100%) scale(1);
      }
    }

    &.open {
      transform: translateY(0) scale(1);
      opacity: 1;
    }

    .tooltip {
      padding: 4px 6px;
      font-size: 12px;
      position: absolute;
      left: -12px;
      transform: translateX(-75%);
      background-color: #353b48;
      border-radius: 4px;
      color: white;
      opacity: 0;
      transition:
        transform 300ms,
        opacity 300ms;
      user-select: none;
    }
  }
}
.popover-menubar {
  .ant-popover-inner {
    border-radius: 10px;

    &-content {
      overflow: auto;
      max-height: 400px;

      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background-color: rgb(98 112 140 / 1);
      }
    }
  }
}

@media screen and (max-height: 1000px) {
  .deduplicate-menu-bar-container {
    .fab-button {
      .count-file-selected {
        font-size: 12px !important;
      }

      svg {
        width: 15px;
        height: 15px;
      }
    }

    li {
      min-width: 40px;
      min-height: 40px;

      svg {
        width: 24px;
        height: 24px;
      }

      .reviewer-icon,
      .annotator-icon {
        width: 20px;
        height: 20px;
      }
    }
  }
}
