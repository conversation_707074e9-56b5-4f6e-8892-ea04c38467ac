.btn {
  display: flex;
  padding: 4px 12px;
  justify-content: center;
  align-items: center;
  border-radius: 100px;
  font-size: 12px;
  font-weight: 500;
  line-height: 18px;
  cursor: pointer;
  border: none;
  outline: none;
}

.btnSecondary {
  background: #ebf0ff;
  box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.03);
  color: #3361ff;

  &:hover {
    background: var(--blue-blue-20, rgba(51, 97, 255, 0.4));
  }

  &:disabled {
    opacity: 0.6;
    background: #ebf0ff;
  }
}

.btnNegative {
  background: #fee;
  box-shadow: 0px 2px 5px 0px rgba(230, 46, 46, 0.03);
  color: #cc1414;

  &:hover {
    background: var(--red-red-20, rgba(230, 46, 46, 0.4));
  }

  &:disabled {
    opacity: 0.6;
    background: #fee;
  }
}

.deduplicateModal {
  width: 90vw !important;
  max-width: 1245px !important;

  .ant-modal-header {
    border-bottom: none;
  }
}

.modalContent {
  height: 80vh;
  min-height: 640px;
  display: flex;
  // overflow: auto;
}

.calculating {
  font-size: 20px;
  font-weight: 600;
  line-height: 27px;

  background: var(
    --Gradient-1,
    linear-gradient(
      180deg,
      #5257e5 0%,
      #566ce4 15%,
      #5c87e4 34.5%,
      #5f95e3 44.5%,
      #63abe3 60%,
      #66b7e2 68.5%,
      #69c8e2 80.5%,
      #6dd8e1 92%,
      #6fe3e1 100%
    )
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.canceling {
  font-size: 20px;
  font-weight: 600;
  line-height: 27px;

  background: var(
    --Gradient-10,
    linear-gradient(180deg, #74276c 0%, #c53364 58%, #fd8263 100%)
  );

  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.contentWrapper {
  display: flex;
  padding: 8px;
  flex-direction: column;
  border-radius: 15px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 100%;
  overflow: auto;
  // transition: width 0.8s ease-in-out;
}

.refreshBtn {
  svg path {
    transition: fill 0.3s;
  }

  &:hover {
    svg path {
      fill: #3361ff;
    }
  }
}
