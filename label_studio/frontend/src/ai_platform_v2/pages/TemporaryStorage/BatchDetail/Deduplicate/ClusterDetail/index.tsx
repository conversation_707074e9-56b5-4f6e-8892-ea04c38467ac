import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import classNames from "classnames";
import AutoSizer from "react-virtualized-auto-sizer";
import useDeduplicationStore from "../store";
import ListFile from "./ListFile";
import { ModalConfirmBig } from "@taureau/ui";
import Checkbox from "./FileItem/Checkbox";
import { useAPI } from "@/providers/ApiProvider";

import styles from "../styles.module.scss";
import Message from "@/ai_platform_v2/component/Message/Message";

interface IProps {
  [key: string]: any;
}

const ClusterDetail = (props: IProps) => {
  const { batchId, projectId } = props;
  const api = useAPI();
  const {
    selectedCluster,
    selectedFile,
    listFile,
    setSelectedFile,
    threshold,
    setNeedRefreshClusterList,
    listCluster,
  } = useDeduplicationStore((state) => ({
    selectedCluster: state.selectedCluster,
    selectedFile: state.selectedFile,
    setSelectedFile: state.setSelectedFile,
    listFile: state.listFile,
    threshold: state.threshold,
    setNeedRefreshClusterList: state.setNeedRefreshClusterList,
    listCluster: state.listCluster,
  }));

  const currentCluster = useMemo(
    () => listCluster?.find((item: any) => item?.id === selectedCluster),
    [listCluster, selectedCluster]
  );

  const isConfirmedCluster = useMemo(
    () => currentCluster?.status === "Confirmed",
    [currentCluster?.status]
  );

  const [isLoading, setLoading] = useState(false);

  useEffect(() => {
    setSelectedFile([]);
  }, [selectedCluster]);

  const isSelectAllFile = useMemo(
    () => selectedFile?.length && listFile?.length === selectedFile?.length,
    [listFile?.length, selectedFile?.length]
  );

  const isIndeterminate = useMemo(
    () => selectedFile?.length && !isSelectAllFile,
    [isSelectAllFile, selectedFile?.length]
  );

  const onSelectFile = useCallback(() => {
    if (isSelectAllFile) {
      setSelectedFile([]);
    } else {
      setSelectedFile(listFile);
    }
  }, [isSelectAllFile, listFile]);

  const confirmChecking = useCallback(async () => {
    try {
      if (isLoading) return;

      setLoading(true);

      const res = await api.callApi("confirmCluster", {
        params: {
          projectId,
          batchId,
          clusterId: selectedCluster,
        },
        body: {
          threshold,
          status: "Confirmed",
        },
      });

      if (res?.success) {
        setNeedRefreshClusterList(true);
        Message.success({
          content: `Cluster ${currentCluster?.clusterId} confirmed checking`,
        });
      } else {
        Message.error({ content: res?.message });
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    } finally {
      setLoading(false);
    }
  }, [
    batchId,
    isLoading,
    projectId,
    selectedCluster,
    threshold,
    currentCluster,
  ]);

  const handleConfirmChecking = useCallback(() => {
    ModalConfirmBig.confirm({
      title: `Confirm checking of cluster ${currentCluster?.clusterId}`,
      content:
        "If you continue, this cluster will be officially marked as confirmed. No deduplication or removal actions will be carried out after this",
      okText: "Continue",
      onOk: confirmChecking,
    });
  }, [confirmChecking, currentCluster?.clusterId]);

  const renderClusterInfo = useCallback(() => {
    return (
      <div className="flex justify-between pb-2 border-b border-b-[#0000001A]">
        <div className="flex gap-5 items-center pl-[15px]">
          {!isConfirmedCluster ? (
            <Checkbox
              indeterminate={isIndeterminate}
              checked={isSelectAllFile}
              onChange={onSelectFile}
            />
          ) : null}
          <span className="text-[#346] font-medium text-[12px] leading-[18px]">
            Cluster: {currentCluster?.clusterId}
          </span>
        </div>

        {!isConfirmedCluster ? (
          <div className="flex gap-2 items-center justify-center">
            <button
              className={classNames(`${styles.btn} ${styles.btnSecondary}`)}
              onClick={handleConfirmChecking}
            >
              Confirm checking
            </button>
          </div>
        ) : null}
      </div>
    );
  }, [
    isConfirmedCluster,
    isIndeterminate,
    isSelectAllFile,
    onSelectFile,
    currentCluster?.clusterId,
    handleConfirmChecking,
  ]);

  return (
    <div className={classNames("", { [styles.contentWrapper]: true })}>
      {renderClusterInfo()}
      <div className="w-full h-full mt-2">
        <AutoSizer>
          {({ width, height }) => {
            return (
              <ListFile
                width={width}
                height={height}
                batchId={batchId}
                projectId={projectId}
                isConfirmedCluster={isConfirmedCluster}
                clusterId={currentCluster?.clusterId}
              />
            );
          }}
        </AutoSizer>
      </div>
    </div>
  );
};

export default memo(ClusterDetail);
