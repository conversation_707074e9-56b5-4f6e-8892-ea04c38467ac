import React, { memo, useCallback, useEffect, useRef, useState } from "react";
import { Tooltip } from "antd";
import IconInfo2CircleFill from "../../icons/IconInfo2CircleFill";
import useDeduplicationStore from "../../store";
import classNames from "classnames";
import Message from "@/ai_platform_v2/component/Message/Message";
import { MAX_THRESHOLD } from "../../constants";
import { ModalConfirmBig } from "@taureau/ui";

import styles from "./styles.module.scss";
import commonStyles from "../../styles.module.scss";
import { useAPI } from "@/providers/ApiProvider";

const regex = /^(0(\.\d+)?|1(\.0+)?)$/;

const Threshold = ({
  batchId,
  projectId,
  isDeduplicationReportCompleted = false,
  fetchBatchDetail,
  workflowStepId,
}: {
  batchId: string;
  projectId: string;
  isDeduplicationReportCompleted?: boolean;
  fetchBatchDetail: any;
  workflowStepId: string;
}) => {
  const {
    threshold,
    setThreshold,
    minThreshold,
    isCalculating,
    setCalculating,
  } = useDeduplicationStore((state) => ({
    threshold: state.threshold,
    setThreshold: state.setThreshold,
    minThreshold: state.minThreshold,
    isCalculating: state.isCalculating,
    setCalculating: state.setCalculating,
  }));

  const [isChanged, setChanged] = useState(false);

  const [isValidThreshold, setValidThreshold] = useState(true);

  const inpRef = useRef(null);

  const api = useAPI();

  useEffect(() => {
    if (inpRef.current) inpRef.current.value = threshold;
  }, [threshold]);

  const handleCreateDeduplicateReport = useCallback(async () => {
    if (isCalculating) return;
    try {
      const res: any = await api.callApi("createDeduplicateReport", {
        params: {
          projectId,
          batchId,
        },
        body: {
          threshold: Number(inpRef?.current?.value),
          workflowStepId,
        },
      });

      if (res.success) {
        setCalculating(true);
        fetchBatchDetail();
        setThreshold(inpRef?.current?.value);
        setChanged(false);
      } else {
        Message.error({ content: res?.message });
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    }
  }, [batchId, isCalculating, projectId, workflowStepId]);

  const validateThreshold = useCallback(
    (hasMessageValidate = false, e) => {
      if (inpRef?.current) {
        const value = inpRef?.current?.value;

        if (!value) {
          setValidThreshold(false);
          if (hasMessageValidate) {
            Message.warning({
              content: "Threshold is required",
            });
          }
        } else if (value > MAX_THRESHOLD) {
          setValidThreshold(false);
          if (hasMessageValidate) {
            Message.warning({
              content: "Cannot set threshold more than 1",
            });
          }
        } else if (value < minThreshold) {
          setValidThreshold(false);
          if (hasMessageValidate) {
            Message.warning({
              content:
                "Cannot set threshold less than configured minimum value",
            });
          }
        } else if (!regex.test(value)) {
          setValidThreshold(false);
          if (hasMessageValidate) {
            Message.warning({
              content: "Threshold must be a number",
            });
          }
        } else {
          setValidThreshold(true);
        }

        if (value?.toString()?.length > 4) {
          inpRef.current.value = Number(value?.toString()?.slice(0, 4));
        }
      }
    },
    [minThreshold]
  );

  const setNewThreshold = useCallback(() => {
    if (isValidThreshold) {
      ModalConfirmBig.warning({
        title: "Apply new threshold?",
        content:
          "If continue, this report will be renewed and restored all files previously deduplicated or removed from the cluster.",
        okText: "Continue",
        onOk: handleCreateDeduplicateReport,
      });
    }
  }, [isValidThreshold]);

  const renderSetNewBtn = useCallback(() => {
    return (
      <button
        className={classNames(
          `${commonStyles.btn} ${commonStyles.btnSecondary}`
        )}
        disabled={!(isValidThreshold && isChanged)}
        onClick={setNewThreshold}
      >
        Set new
      </button>
    );
  }, [isChanged, isValidThreshold]);

  return (
    <div className="flex justify-between pb-2 border-b border-b-[#0000001A]">
      <div className="flex gap-1 items-center">
        <Tooltip title={`Configured min value: ${minThreshold}`}>
          <div className="flex items-center">
            <IconInfo2CircleFill />
          </div>
        </Tooltip>
        <span className="text-[#346] font-medium text-[12px] leading-[18px]">
          Threshold
        </span>
        <input
          ref={inpRef}
          className={styles.thresholdInput}
          onChange={(e: any) => {
            validateThreshold(false, e);
            setChanged(true);
          }}
          maxLength={4}
          onBlur={() => {
            if (!isValidThreshold) {
              inpRef.current.value = threshold;
            }
          }}
          disabled={isDeduplicationReportCompleted}
          type="number"
        />
      </div>
      {!isDeduplicationReportCompleted ? renderSetNewBtn() : null}
    </div>
  );
};

export default memo(Threshold);
