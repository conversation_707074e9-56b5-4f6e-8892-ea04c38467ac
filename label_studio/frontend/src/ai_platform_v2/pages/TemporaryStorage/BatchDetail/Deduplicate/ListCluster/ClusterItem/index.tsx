import React, { memo, useCallback, useEffect, useMemo, useRef } from "react";
import classNames from "classnames";
import useDeduplicationStore from "../../store";
import LoadingImage from "@/components/image/LoadImage";
import IconConfirmed from "../../icons/IconConfirmed";

import styles from "./styles.module.scss";
import {
  getTempStorageUrl,
  getImageUrl,
} from "@/ai_platform_v2/pages/DataManager/helper";
import { getCurrentModule, getCurrentProject } from "@/pages/DataSet/Const";

interface IProps {
  [key: string]: any;
}

const ClusterItem = (props: IProps) => {
  const { item } = props;

  const ref = useRef(null);

  const projectId = getCurrentProject();
  const moduleId = getCurrentModule();

  const { selectedCluster, setSelectedCluster } = useDeduplicationStore(
    (state) => ({
      selectedCluster: state.selectedCluster,
      setSelectedCluster: state.setSelectedCluster,
    })
  );

  const isSelectedCluster = useMemo(
    () => item?.id === selectedCluster,
    [item?.id, selectedCluster]
  );

  useEffect(() => {
    if (isSelectedCluster) {
      ref?.current?.scrollIntoView({
        behavior: "smooth",
        block: "center",
      });
    }
  }, [isSelectedCluster, ref]);

  const renderBadge = useCallback(() => {
    return (
      <div className="h-5 min-w-[20px] p-1 rounded-[15px] bg-[#E53E3E] flex items-center justify-center absolute right-[-2px] top-[-5px] text-[10px] leading-[15px] font-semibold text-[#fff]">
        {item?.countFileThresholds}
      </div>
    );
  }, [item?.countFileThresholds]);

  const renderFlag = useCallback(() => {
    if (item?.status === "Confirmed") {
      return (
        <div className="absolute left-[5.5px] top-0">
          <IconConfirmed />
        </div>
      );
    }

    return <></>;
  }, [item?.status]);

  return (
    <div
      className={classNames("relative", {
        [styles.clusterItem]: true,
        [styles.clusterItemActive]: isSelectedCluster,
      })}
      onClick={() => setSelectedCluster(isSelectedCluster ? null : item?.id)}
      ref={ref}
    >
      <div className="grow w-full overflow-hidden rounded-[5px] min-h-[80px]">
        <div className="w-full h-full relative">
          <LoadingImage
            src={
              item?.storageArea === "TempStorage"
                ? getTempStorageUrl(projectId, item?.fileAvatarId, "Original")
                : getImageUrl(
                    moduleId,
                    projectId,
                    item?.fileAvatarId,
                    "Original"
                  )
            }
            alt=""
            className="w-full h-full object-cover rounded-[5px]"
            rederImageError={() => {
              return (
                <div className="no-image-preview w-full h-full text-[13px]">
                  Error preview
                </div>
              );
            }}
            loading="lazy"
          />
        </div>
      </div>
      <span className="text-[11px] leading-[13px] text-[#346] mt-1">
        {item.clusterId}
      </span>
      {renderBadge()}
      {renderFlag()}
    </div>
  );
};

export default memo(ClusterItem);
