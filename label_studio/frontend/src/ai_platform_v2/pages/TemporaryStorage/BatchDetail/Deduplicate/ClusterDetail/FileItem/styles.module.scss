.fileItem {
  // grid-column-end: span 1;
  border-radius: 5px;
  width: 150px;
  height: 150px;
  padding: 8px 4px;
  box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.15);

  display: flex;
  flex-direction: column;
  cursor: pointer;

  &:hover {
    background-color: #f5f6f7;
  }
}

.fileItemActive {
  border: 1px solid #3361ff;
}

.checkbox {
  height: 18px;

  input[type="checkbox"] {
    position: relative;
    cursor: pointer;

    transition: all 0.3s;

    width: 0;
  }

  input[type="checkbox"]:before {
    content: "";
    display: block;
    position: absolute;
    width: 20px;
    height: 20px;
    top: -4px;
    left: -6px;

    border-radius: 5px;
    border: 2px solid var(--gray-blue-grey-blue-96, #f2f3f5);
    background: transparent;
    box-shadow: 0px 2px 10px 0px rgba(38, 51, 77, 0.03);
  }

  input[type="checkbox"]:checked:before {
    content: "";
    display: block;
    position: absolute;
    width: 20px;
    height: 20px;
    top: -4px;
    left: -6px;

    border-radius: 5px;
    border: none;
    box-shadow: 0px 2px 10px 0px rgba(51, 97, 255, 0.15);
    background-color: #3361ff;
  }

  input[type="checkbox"]:checked:after {
    content: "";
    display: block;
    width: 5px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    position: absolute;
    left: 2px;
  }
}

.checkboxIndeterminate {
  input[type="checkbox"]:after {
    content: "";
    display: block;
    position: absolute;
    width: 8px;
    height: 2px;
    top: 5px;

    border-radius: 10px;
    background: #3361ff;
  }
}
