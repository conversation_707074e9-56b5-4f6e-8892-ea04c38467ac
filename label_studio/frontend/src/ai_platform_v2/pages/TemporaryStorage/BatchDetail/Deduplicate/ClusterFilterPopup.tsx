import useOnClickOutside from "@/hooks/useOnClickOutside";
import { Button } from "@taureau/ui";
import { Tooltip } from "antd";
import classNames from "classnames";
import React, { PropsWithChildren, useRef } from "react";

interface Props {
  visible: boolean;
  setVisible: () => void;
  setClusterStatus: (value: string) => void;
}

const ClusterFilterPopup = (props: PropsWithChildren<Props>): JSX.Element => {
  const { visible, setVisible, setClusterStatus, children } = props;
  const ref = useRef<HTMLDivElement>(null);

  const handleOutsideClick = () => {
    if (visible) setVisible();
  };

  useOnClickOutside(ref, handleOutsideClick);

  const onClickAll = () => {
    setVisible();
    setClusterStatus("all");
  };

  const onClickReady = () => {
    setVisible();
    setClusterStatus("Ready");
  };

  const onClickCompleted = () => {
    setVisible();
    setClusterStatus("Confirmed");
  };

  if (!visible) {
    return <div>{children}</div>;
  }

  return (
    <div
      ref={ref}
      className={classNames(
        "absolute top-0 left-0",
        "flex flex-col gap-1 items-center justify-between rounded-[20px] shadow-Shadows/Gray-Blue-10/15%/10b z-10"
      )}
    >
      <Tooltip
        placement="left"
        title={<span className="text-[12px]">All cluster</span>}
      >
        <Button
          onClick={onClickAll}
          icon={
            <svg
              width="30"
              height="30"
              viewBox="0 0 34 34"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g filter="url(#filter0_d_16764_246837)">
                <rect
                  x="2"
                  y="2"
                  width="30"
                  height="30"
                  rx="15"
                  fill="#3361FF"
                />
                <rect
                  x="2"
                  y="2"
                  width="30"
                  height="30"
                  rx="15"
                  stroke="white"
                  strokeWidth="2"
                />
                <path
                  fillRule="evenodd"
                  clipRule="evenodd"
                  d="M16.2008 21.8H17.8008C18.2408 21.8 18.6008 21.44 18.6008 21C18.6008 20.56 18.2408 20.2 17.8008 20.2H16.2008C15.7608 20.2 15.4008 20.56 15.4008 21C15.4008 21.44 15.7608 21.8 16.2008 21.8ZM9.80078 13C9.80078 13.44 10.1608 13.8 10.6008 13.8H23.4008C23.8408 13.8 24.2008 13.44 24.2008 13C24.2008 12.56 23.8408 12.2 23.4008 12.2H10.6008C10.1608 12.2 9.80078 12.56 9.80078 13ZM13.0008 17.8H21.0008C21.4408 17.8 21.8008 17.44 21.8008 17C21.8008 16.56 21.4408 16.2 21.0008 16.2H13.0008C12.5608 16.2 12.2008 16.56 12.2008 17C12.2008 17.44 12.5608 17.8 13.0008 17.8Z"
                  fill="white"
                />
              </g>
              <defs>
                <filter
                  id="filter0_d_16764_246837"
                  x="-9"
                  y="-8"
                  width="52"
                  height="52"
                  filterUnits="userSpaceOnUse"
                  colorInterpolationFilters="sRGB"
                >
                  <feFlood floodOpacity="0" result="BackgroundImageFix" />
                  <feColorMatrix
                    in="SourceAlpha"
                    type="matrix"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                    result="hardAlpha"
                  />
                  <feOffset dy="1" />
                  <feGaussianBlur stdDeviation="5" />
                  <feColorMatrix
                    type="matrix"
                    values="0 0 0 0 0.2 0 0 0 0 0.380392 0 0 0 0 1 0 0 0 0.05 0"
                  />
                  <feBlend
                    mode="normal"
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_16764_246837"
                  />
                  <feBlend
                    mode="normal"
                    in="SourceGraphic"
                    in2="effect1_dropShadow_16764_246837"
                    result="shape"
                  />
                </filter>
              </defs>
            </svg>
          }
          size="xs"
          corner="Circle"
          className="hover:bg-[#f5f6f7]"
        />
      </Tooltip>

      <Tooltip
        placement="left"
        title={<span className="text-[12px]">Ready cluster</span>}
      >
        <Button
          className={classNames("hover:bg-[#f5f6f7]", {
            // "bg-[#C3CAD9]": moduleFilter === "Active",
          })}
          corner="Circle"
          icon={
            <svg
              width="30"
              height="30"
              viewBox="0 0 30 30"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M14.99 5C9.47 5 5 9.48 5 15C5 20.52 9.47 25 14.99 25C20.52 25 25 20.52 25 15C25 9.48 20.52 5 14.99 5ZM18.22 20.39L15 18.45L11.78 20.39C11.4 20.62 10.93 20.28 11.03 19.85L11.88 16.19L9.05 13.74C8.72 13.45 8.9 12.9 9.34 12.86L13.08 12.54L14.54 9.09C14.71 8.68 15.29 8.68 15.46 9.09L16.92 12.53L20.66 12.85C21.1 12.89 21.28 13.44 20.94 13.73L18.11 16.18L18.96 19.85C19.06 20.28 18.6 20.62 18.22 20.39Z"
                fill="#33BFFF"
              />
            </svg>
          }
          size="xs"
          onClick={onClickReady}
        />
      </Tooltip>

      <Tooltip
        placement="left"
        title={<span className="text-[12px]">Confirmed cluster</span>}
      >
        <Button
          className={classNames("hover:bg-[#f5f6f7]", {
            // "bg-[#C3CAD9]": moduleFilter === "Inactive",
          })}
          corner="Circle"
          icon={
            <svg
              width="30"
              height="30"
              viewBox="0 0 30 30"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect x="5" y="5" width="20" height="20" rx="10" fill="#66CCA7" />
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M20.2122 11.2092L20.3022 11.2992C20.6922 11.6892 20.6922 12.3192 20.3022 12.7092L14.7222 18.2992C14.3322 18.6892 13.7022 18.6892 13.3122 18.2992L10.2222 15.2092C10.0349 15.0224 9.92969 14.7687 9.92969 14.5042C9.92969 14.2397 10.0349 13.9861 10.2222 13.7992L10.3122 13.7092C10.7022 13.3192 11.3322 13.3192 11.7222 13.7092L14.0222 16.0092L18.8022 11.2192C19.1822 10.8192 19.8222 10.8192 20.2122 11.2092Z"
                fill="white"
              />
            </svg>
          }
          size="xs"
          onClick={onClickCompleted}
        />
      </Tooltip>
    </div>
  );
};

export default ClusterFilterPopup;
