import { memo, useCallback, useEffect, useMemo } from "react";

import Message from "@/ai_platform_v2/component/Message/Message";
import { useAPI } from "@/providers/ApiProvider";
import { isNil } from "lodash";
import useDeduplicationStore from "../store";
import FileItem from "./FileItem";
import ItemLoading from "./FileItem/ItemLoading";
import MenuBar from "./MenuBar";

interface Props {
  width: number;
  height: number;
  [key: string]: any;
}

const GRID_GAP = 12;

const ListFile = (props: Props) => {
  const { width, height, batchId, projectId, isConfirmedCluster, clusterId } =
    props;
  const api = useAPI();
  const {
    listFile,
    isLoadingClusterDetail,
    selectedCluster,
    setLoadingClusterDetail,
    setListFile,
    selectedFile,
    setNeedRefreshClusterList,
    setSelectedCluster,
    listCluster,
    setListCluster,
  } = useDeduplicationStore((state) => ({
    listFile: state.listFile,
    isLoadingClusterDetail: state.isLoadingClusterDetail,
    selectedCluster: state.selectedCluster,
    setLoadingClusterDetail: state.setLoadingClusterDetail,
    setListFile: state.setListFile,
    selectedFile: state.selectedFile,
    setNeedRefreshClusterList: state.setNeedRefreshClusterList,
    setSelectedCluster: state.setSelectedCluster,
    listCluster: state.listCluster,
    setListCluster: state.setListCluster,
  }));

  const itemNo = useMemo(() => {
    return 6;
  }, [width]);

  const fetchClusterDetail = useCallback(async () => {
    try {
      if (isLoadingClusterDetail) return;
      setLoadingClusterDetail(true);

      const res: any = await api.callApi("getListFileInCluster", {
        params: {
          projectId,
          batchId,
          clusterId: selectedCluster,
          page: 1,
          pageSize: 1000,
        },
      });

      if (res?.success) {
        const { items } = res;

        setListFile(items);

        // Update file count when fetch list file in cluster detail
        const newListCluster = listCluster.map((item: any) => {
          if (item.id === selectedCluster) {
            return {
              ...item,
              countFileThresholds: items?.length || 0,
            };
          }
          return item;
        });

        setListCluster(newListCluster);
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    } finally {
      setLoadingClusterDetail(false);
    }
  }, [
    batchId,
    isLoadingClusterDetail,
    projectId,
    selectedCluster,
    listCluster,
  ]);

  const updateCentrol = useCallback(
    async (fileThresholdId: string, isCentrolId: boolean, fileName: string) => {
      try {
        const res: any = await api.callApi("updateCentrol", {
          params: {
            projectId,
            batchId,
            clusterId: selectedCluster,
            fileThresholdId,
          },
          body: {
            id: fileThresholdId,
            isCentrolId,
          },
        });

        if (res?.success) {
          Message.success({
            content: `${fileName} ${
              isCentrolId
                ? "is set as the centroid image"
                : "is no longer set as the centroid image"
            }`,
          });

          const newListFile = listFile.map((file: any) => {
            if (file?.id === fileThresholdId) {
              return { ...file, isCentrolId };
            }
            return file;
          });

          setListFile(newListFile);
        }
      } catch (error: any) {
        console.log("error: ", error?.message);
      }
    },
    [batchId, listFile, projectId, selectedCluster]
  );

  useEffect(() => {
    if (selectedCluster) fetchClusterDetail();
  }, [selectedCluster]);

  useEffect(() => {
    return () => {
      setListFile();
      setSelectedCluster(null);
    };
  }, []);

  useEffect(() => {
    if (!isNil(listFile) && !listFile.length) {
      setSelectedCluster(null);
      setNeedRefreshClusterList(true);
      setListFile(null);
    }
  }, [listFile]);

  const renderContent = useCallback(() => {
    return (
      <div
        className="grid justify-items-center gap-[12px] h-fit w-full py-2"
        style={{
          gridTemplateColumns:
            itemNo === 1
              ? "repeat(1, minmax(auto, 1fr)"
              : `repeat(${itemNo}, minmax(auto, calc((100% - ${
                  (itemNo - 1) * GRID_GAP
                }px) / ${itemNo})))`,
        }}
      >
        {listFile?.map((item: any) => {
          return (
            <FileItem
              key={item?.id}
              item={item}
              updateCentrol={updateCentrol}
              isConfirmedCluster={isConfirmedCluster}
            />
          );
        })}
      </div>
    );
  }, [batchId, itemNo, listFile, updateCentrol, isConfirmedCluster]);

  return (
    <div
      className="flex flex-col bg-white overflow-hidden rounded-[10px] scrollbar-v-sm relative"
      style={{ width, height }}
    >
      <div className="h-full w-full overflow-auto ">
        {isLoadingClusterDetail ? (
          <div
            className="grid gap-[12px] w-full justify-items-center py-2"
            style={{
              gridTemplateColumns:
                itemNo === 1
                  ? "repeat(1, minmax(auto, 1fr)"
                  : `repeat(${itemNo}, minmax(auto, calc((100% - ${
                      (itemNo - 1) * GRID_GAP
                    }px) / ${itemNo})))`,
            }}
          >
            {[...Array(10).keys()].map((i) => (
              <ItemLoading key={i} />
            ))}
          </div>
        ) : (
          renderContent()
        )}
      </div>
      {!!selectedFile?.length && (
        <MenuBar
          refetchClusterDetail={fetchClusterDetail}
          batchId={batchId}
          clusterId={clusterId}
        />
      )}
    </div>
  );
};

export default memo(ListFile);
