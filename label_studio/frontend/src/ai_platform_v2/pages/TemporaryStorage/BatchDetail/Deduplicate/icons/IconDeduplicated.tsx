import React from "react";

const IconDeduplicated = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="30"
      viewBox="0 0 24 30"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.04215 29.4353L11.8559 24.2568C11.9921 24.1206 12.1284 24.1206 12.2647 24.2568L19.0784 29.4353C20.4412 30.3892 22.349 30.1166 23.4392 28.7539C23.848 28.2088 23.9843 27.5274 23.9843 26.9823V0H0V26.9823C0 28.6176 1.36274 30.1166 3.13431 29.9804C3.81568 29.9804 4.49705 29.8441 5.04215 29.4353Z"
        fill="#818CF8"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.5902 6.07005V4.00678C12.5347 4.00228 12.4788 4 12.4228 4H7.07005C5.92679 4 5 4.92679 5 6.07005V15.7303C5 16.8735 5.92679 17.8003 7.07005 17.8003H13.9702C15.1135 17.8003 16.0402 16.8735 16.0402 15.7303V7.6175C16.0402 7.56142 16.038 7.50557 16.0335 7.45008H13.9702C13.208 7.45008 12.5902 6.83222 12.5902 6.07005ZM7.81229 8.19236C7.94702 8.05763 8.16548 8.05763 8.30017 8.19236L10.52 10.4121L12.7397 8.19237C12.8745 8.05764 13.0929 8.05764 13.2276 8.19237C13.3624 8.3271 13.3624 8.54558 13.2276 8.68027L11.0079 10.9L13.2277 13.1198C13.3625 13.2546 13.3624 13.473 13.2277 13.6077C13.0929 13.7425 12.8745 13.7425 12.7398 13.6077L10.52 11.3879L8.30017 13.6078C8.16541 13.7425 7.94698 13.7425 7.81224 13.6078C7.6775 13.473 7.6775 13.2546 7.81224 13.1198L10.0321 10.9L7.81229 8.68027C7.67756 8.54551 7.67756 8.3271 7.81229 8.19236ZM13.2802 6.07099V4.18688C13.5037 4.28861 13.7095 4.43025 13.8865 4.60725L15.4339 6.15471C15.6109 6.33171 15.7526 6.53747 15.8543 6.76101H13.9702C13.5891 6.76101 13.2802 6.45207 13.2802 6.07099ZM8.95381 18.2428H14.3047C15.4138 18.2428 16.4851 17.0694 16.4851 15.9603V8.70312H17.2383C18.0702 8.70312 18.4935 9.12646 18.4935 9.95834V18.7449C18.4935 19.5768 18.0702 20.2512 17.2383 20.2512H10.209C9.37712 20.2512 8.95381 19.8278 8.95381 18.9959V18.2428Z"
        fill="#F3F4FF"
      />
    </svg>
  );
};

export default IconDeduplicated;
