import React, { memo } from "react";

const IconCalculating = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="200"
      height="200"
      viewBox="0 0 200 200"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M122.326 28.6678V48.2688C122.326 48.9055 122.826 49.4058 123.463 49.4058H143.064L122.326 28.6678ZM115.504 48.2688C115.504 52.6347 119.097 56.2275 123.463 56.2275H145.065V139.225C145.065 146.138 139.471 151.731 132.558 151.731H59.7933C53.9266 151.731 49.015 147.729 47.6961 142.272L65.6145 124.354C69.2527 125.445 73.0729 126.036 77.0749 126.036C99.4046 126.036 117.596 107.891 117.596 85.5608C117.596 63.2311 99.4046 45.0399 77.0749 45.0399C65.2961 45.0399 54.6543 50.1334 47.2868 58.183V39.1732C47.2868 32.2605 52.8806 26.6667 59.7933 26.6667H115.504V48.2688ZM23.6837 121.307L41.0563 103.934C44.8765 111.529 51.107 117.759 58.7018 121.625L41.3292 138.952C38.9189 141.408 35.6899 142.636 32.5065 142.636C29.323 142.636 26.0941 141.408 23.6837 138.952C21.3189 136.633 20 133.449 20 130.129C20 126.809 21.3189 123.626 23.6837 121.307ZM77.0749 119.26C95.6755 119.26 110.774 104.116 110.774 85.5608C110.774 66.9603 95.6755 51.8616 77.0749 51.8616C58.4744 51.8616 43.3757 66.9603 43.3757 85.5608C43.3757 104.161 58.4744 119.26 77.0749 119.26ZM91.7189 76.92C93.4925 76.2833 95.4026 77.2383 96.0393 79.012H96.0847C98.5406 86.243 96.676 94.3835 91.3096 99.7499C90.6274 100.432 89.7633 100.75 88.8992 100.75C88.0351 100.75 87.1711 100.432 86.4889 99.7499C85.17 98.4311 85.17 96.2481 86.4889 94.9293C90.0362 91.382 91.2641 86.0156 89.6269 81.2404C88.9902 79.4667 89.9452 77.5567 91.7189 76.92ZM80.2594 154.004H136.216C146.263 154.004 148.476 146.996 148.476 136.95V67.5959H155.298C162.833 67.5959 166.668 71.4304 166.668 78.9654V158.552C166.668 166.087 162.833 172.195 155.298 172.195H91.6289C84.0936 172.195 80.2594 168.361 80.2594 160.826V154.004Z"
        fill="url(#paint0_linear_16692_238630)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_16692_238630"
          x1="93.3338"
          y1="26.6667"
          x2="93.3338"
          y2="172.195"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#5257E5" />
          <stop offset="0.15" stopColor="#566CE4" />
          <stop offset="0.345" stopColor="#5C87E4" />
          <stop offset="0.445" stopColor="#5F95E3" />
          <stop offset="0.6" stopColor="#63ABE3" />
          <stop offset="0.685" stopColor="#66B7E2" />
          <stop offset="0.805" stopColor="#69C8E2" />
          <stop offset="0.92" stopColor="#6DD8E1" />
          <stop offset="1" stopColor="#6FE3E1" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default memo(IconCalculating);
