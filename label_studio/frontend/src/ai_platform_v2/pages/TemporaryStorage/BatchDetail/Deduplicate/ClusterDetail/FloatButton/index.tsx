import cn from "classnames";
import { Popover, Tooltip } from "antd";

import "./FloatButton.scss";

const FloatButton = ({ badge, open, onChangeOpen, actions }: any) => {
  const handleClick = () => onChangeOpen(!open);

  return (
    <ul className="deduplicate-menu-bar-container">
      {open ? (
        <li className="fab-button" onClick={handleClick}>
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M13.929 5.53759C13.8044 5.41276 13.6353 5.3426 13.459 5.3426C13.2826 5.3426 13.1135 5.41276 12.989 5.53759L9.72898 8.79093L6.46898 5.53093C6.34443 5.40609 6.17533 5.33594 5.99898 5.33594C5.82264 5.33594 5.65354 5.40609 5.52898 5.53093C5.26898 5.79093 5.26898 6.21093 5.52898 6.47093L8.78898 9.73093L5.52898 12.9909C5.26898 13.2509 5.26898 13.6709 5.52898 13.9309C5.78898 14.1909 6.20898 14.1909 6.46898 13.9309L9.72898 10.6709L12.989 13.9309C13.249 14.1909 13.669 14.1909 13.929 13.9309C14.189 13.6709 14.189 13.2509 13.929 12.9909L10.669 9.73093L13.929 6.47093C14.1823 6.21759 14.1823 5.79093 13.929 5.53759Z"
              fill="white"
            />
          </svg>
        </li>
      ) : (
        <li className="fab-button" onClick={handleClick}>
          <div className=" font-normal text-[14px] text-white count-file-selected">
            {badge}
          </div>
        </li>
      )}

      {actions.map((action: any, index: number) => {
        if (action.isHidden) return;

        return (
          <Tooltip key={action.label} placement="left" title={action.label}>
            {action.children ? (
              <Popover
                placement="left"
                content={action.children}
                trigger="click"
                overlayClassName={`popover-menubar ${action?.className ?? ""}`}
                // showArrow={false}
                destroyTooltipOnHide={action.destroyTooltipOnHide}
              >
                <li
                  style={{
                    transitionDelay: `${index * 25}ms`,
                    background: action?.background,
                  }}
                  className={cn("fab-action", { open })}
                >
                  {action.icon}
                  <span className="tooltip">{action.label}</span>
                </li>
              </Popover>
            ) : (
              <li
                style={{
                  transitionDelay: `${index * 25}ms`,
                  background: action?.background,
                }}
                className={cn("fab-action", { open })}
                onClick={action.onClick}
              >
                {action.icon}
                <span className="tooltip">{action.label}</span>
              </li>
            )}
          </Tooltip>
        );
      })}
    </ul>
  );
};

export default FloatButton;
