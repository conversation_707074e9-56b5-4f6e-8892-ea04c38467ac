import React, { memo } from "react";
import classNames from "classnames";

import styles from "./styles.module.scss";

const ItemLoading = () => {
  return (
    <div
      className={classNames("relative", {
        [styles.clusterItem]: true,
      })}
    >
      <div className="grow w-full h-full overflow-hidden rounded-[10px] min-h-[80px]">
        <div className="w-full h-full">
          <div className="w-full h-full rounded-[10px] skeleton" />
        </div>
      </div>
      <div className="w-3/4 mt-1">
        <div className="h-2.5 w-full skeleton" />
      </div>
    </div>
  );
};

export default memo(ItemLoading);
