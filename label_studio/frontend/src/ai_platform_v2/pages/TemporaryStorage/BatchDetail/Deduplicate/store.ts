import { create } from "zustand";
import { initialState } from "./constants";

interface IDeduplicationStore {
  [key: string]: any;
}

const useDeduplicationStore = create<IDeduplicationStore>((set) => ({
  listCluster: null,
  setListCluster: (listCluster: any) => set({ listCluster }),

  isCalculating: false,
  setCalculating: (isCalculating: boolean) => set({ isCalculating }),

  threshold: 0,
  setThreshold: (threshold: number) => set({ threshold }),

  minThreshold: 0,
  setMinThreshold: (minThreshold: number) => set({ minThreshold }),

  isLoadingListCluster: false,
  setLoadingListCluster: (isLoadingListCluster: boolean) =>
    set({ isLoadingListCluster }),

  selectedCluster: null,
  setSelectedCluster: (selectedCluster: string) => set({ selectedCluster }),

  listFile: null,
  setListFile: (listFile: string) => set({ listFile }),

  isLoadingClusterDetail: false,
  setLoadingClusterDetail: (isLoadingClusterDetail: boolean) =>
    set({ isLoadingClusterDetail }),

  selectedFile: [],
  setSelectedFile: (selectedFile: any) => set({ selectedFile }),

  isCancelingReport: false,
  setCancelingReport: (isCancelingReport: boolean) =>
    set({ isCancelingReport }),

  needRefreshClusterList: false,
  setNeedRefreshClusterList: (needRefreshClusterList: boolean) =>
    set({ needRefreshClusterList }),

  needForceClose: false,
  setNeedForceClose: (needForceClose: boolean) => set({ needForceClose }),

  needRefetchData: false,
  setNeedRefetchData: (needRefetchData: boolean) => set({ needRefetchData }),

  resetStore: () => set(initialState),
}));

export default useDeduplicationStore;
