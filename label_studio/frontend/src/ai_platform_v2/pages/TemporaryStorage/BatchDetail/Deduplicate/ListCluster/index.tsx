import React, { memo, useCallback, useEffect } from "react";
import classNames from "classnames";
import Threshold from "./Threshold";
import ListClusterComponent from "./ListClusterComponent";
import AutoSizer from "react-virtualized-auto-sizer";

import styles from "../styles.module.scss";
import { HomeNoData } from "@/ai_platform_v2/assets/Images/NoData";

interface Props {
  [key: string]: any;
}

const ListCluster = (props: Props) => {
  const {
    className = "",
    batchId,
    projectId,
    isDeduplicationReportCompleted,
    fetchBatchDetail,
    workflowStepId,
    isShowEmptyData,
  } = props;

  return (
    <div className={classNames(className, { [styles.contentWrapper]: true })}>
      <Threshold
        batchId={batchId}
        projectId={projectId}
        isDeduplicationReportCompleted={isDeduplicationReportCompleted}
        fetchBatchDetail={fetchBatchDetail}
        workflowStepId={workflowStepId}
      />

      <div className="w-full h-full mt-2">
        {isShowEmptyData ? (
          <div className="w-full h-full flex flex-col items-center justify-center">
            <img src={HomeNoData} />
            <span className="mt-[60px] text-[24px] font-medium leading-[36px] text-[#346]">
              Empty!
            </span>
            <span className="mt-2 text-[16px] leading-[24px] text-[#346]">
              Deduplication report has no clusters.
            </span>
          </div>
        ) : (
          <AutoSizer>
            {({ width, height }) => {
              return <ListClusterComponent width={width} height={height} />;
            }}
          </AutoSizer>
        )}
      </div>
    </div>
  );
};

export default memo(ListCluster);
