import React, { memo, useCallback } from "react";

import styles from "./styles.module.scss";
import classNames from "classnames";

interface IProps {
  checked: boolean;
  onChange?: (checked: any) => void;
  indeterminate?: boolean;
  isDisabled?: boolean;
}

const Checkbox = (props: IProps) => {
  const {
    checked,
    onChange,
    indeterminate = false,
    isDisabled = false,
  } = props;

  const handleChangeCheckbox = useCallback(
    (e) => {
      const checked = e.target.checked;

      onChange?.(checked);
    },
    [onChange]
  );

  return (
    <div
      className={classNames("", {
        [styles.checkbox]: true,
        [styles.checkboxIndeterminate]: indeterminate,
      })}
    >
      <input
        type="checkbox"
        checked={checked}
        onChange={handleChangeCheckbox}
        disabled={isDisabled}
      />
    </div>
  );
};

export default memo(Checkbox);
