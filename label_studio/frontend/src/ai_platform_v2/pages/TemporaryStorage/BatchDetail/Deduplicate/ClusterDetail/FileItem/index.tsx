import React, { memo, useCallback, useMemo, useState } from "react";
import classNames from "classnames";
import useDeduplicationStore from "../../store";
import {
  getImageUrl,
  getTempStorageUrl,
} from "@/ai_platform_v2/pages/DataManager/helper";
import LoadingImage from "@/components/image/LoadImage";
import IconDM from "../../icons/IconDM";
import IconTempStorage from "../../icons/IconTempStorage";
import { Tooltip } from "antd";
import IconCentroid from "../../icons/IconCentroid";
import IconUnCentroid from "../../icons/IconUnCentroid";
import Checkbox from "./Checkbox";
import { getCurrentModule, getCurrentProject } from "@/pages/DataSet/Const";

import styles from "./styles.module.scss";
import { isEmpty } from "lodash";

interface IProps {
  [key: string]: any;
}

const FileItem = (props: IProps) => {
  const { item, updateCentrol, isConfirmedCluster } = props;

  const currentModule = getCurrentModule();
  const currentProject = getCurrentProject();

  const { id, isCentrolId, storageArea, fileId, fileName } = item;

  const isDM = useMemo(
    () => storageArea === "PersistentStorage",
    [storageArea]
  );

  const [isHover, setHover] = useState(false);

  const { selectedFile, setSelectedFile } = useDeduplicationStore((state) => ({
    selectedFile: state.selectedFile,
    setSelectedFile: state.setSelectedFile,
  }));

  const isSelectedFile = useMemo(
    () => !isEmpty(selectedFile?.find((file: any) => file.id === id)),
    [id, selectedFile]
  );

  const handleSelectFile = useCallback(() => {
    if (isConfirmedCluster) return;
    if (isSelectedFile) {
      setSelectedFile(selectedFile.filter((file: any) => file?.id !== id));

      return;
    }
    const newSelectedFile = selectedFile ? [...selectedFile] : [];

    newSelectedFile.push(item);
    setSelectedFile(newSelectedFile);
  }, [isSelectedFile, item, selectedFile, isConfirmedCluster]);

  const renderBadge = useCallback(() => {
    return (
      <div className="absolute right-[-10px] top-[-8px]">
        {isDM ? <IconDM /> : <IconTempStorage />}
      </div>
    );
  }, [isDM]);

  const renderAction = useCallback(() => {
    return (
      <div className="absolute top-0 bottom-0 right-0 left-0 bg-black-30 rounded-lg flex justify-center items-center opacity-0 group-hover:opacity-100">
        <div
          className={classNames(
            "cursor-pointer w-[34px] aspect-square rounded-full flex items-center justify-center bg-[#fff]"
          )}
          onClick={(e) => {
            e.stopPropagation();
            updateCentrol(id, !isCentrolId, fileName);
          }}
          style={
            isHover
              ? {
                  background: isCentrolId
                    ? "linear-gradient(180deg, #74276C 0%, #C53364 58%, #FD8263 100%)"
                    : "linear-gradient(180deg, #5257E5 0%, #566CE4 15%, #5C87E4 34.5%, #5F95E3 44.5%, #63ABE3 60%, #66B7E2 68.5%, #69C8E2 80.5%, #6DD8E1 92%, #6FE3E1 100%)",
                }
              : {}
          }
          onMouseOver={() => setHover(true)}
          onMouseOut={() => setHover(false)}
        >
          <Tooltip
            title={
              <span className="text-[10px] leading-[20px]">
                {isCentrolId ? "Remove centroid" : "Set centroid"}
              </span>
            }
            placement="bottom"
          >
            <div>{isCentrolId ? <IconUnCentroid /> : <IconCentroid />}</div>
          </Tooltip>
        </div>

        <div className="absolute left-[10px] top-[6px]">
          <Checkbox checked={isSelectedFile} />
        </div>
      </div>
    );
  }, [isCentrolId, isHover, isSelectedFile, isDM]);

  return (
    <div
      className={classNames("relative", {
        [styles.fileItem]: true,
        [styles.fileItemActive]: isSelectedFile,
      })}
      onClick={handleSelectFile}
    >
      <div className="grow w-full overflow-hidden rounded-[5px] min-h-[80px]">
        <div className="w-full h-full group relative">
          <LoadingImage
            src={
              isDM
                ? getImageUrl(currentModule, currentProject, fileId, "Original")
                : getTempStorageUrl(currentProject, fileId, "Original")
            }
            alt=""
            className="w-full h-full object-cover rounded-[5px]"
            rederImageError={() => {
              return (
                <div className="no-image-preview w-full h-full text-[13px]">
                  Error preview
                </div>
              );
            }}
            loading="lazy"
          />
          {!isConfirmedCluster && renderAction()}
          {isSelectedFile && !isHover && !isConfirmedCluster ? (
            <div className="absolute left-[10px] top-[6px]">
              <Checkbox checked={isSelectedFile} />
            </div>
          ) : null}
        </div>
      </div>
      <div className="flex justify-between w-full mt-1 text-[11px] leading-[13px] text-[#346] gap-1">
        <Tooltip title={fileName}>
          <span className="text-ellipsis overflow-hidden whitespace-nowrap">
            {fileName}
          </span>
        </Tooltip>
        {isCentrolId && (
          <span className="shrink-0 text-[#3361FF]">Centroid</span>
        )}
      </div>
      {renderBadge()}
    </div>
  );
};

export default memo(FileItem);
