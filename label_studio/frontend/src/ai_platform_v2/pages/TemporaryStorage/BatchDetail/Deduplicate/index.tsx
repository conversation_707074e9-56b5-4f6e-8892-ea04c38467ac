import IconClose from "@/ai_platform_v2/assets/Icons/IconClose";
import IconRefresh from "@/ai_platform_v2/assets/Icons/IconRefresh";
import Message from "@/ai_platform_v2/component/Message/Message";
import { findNextNodeByCurrentNodeID } from "@/ai_platform_v2/utils/helpers";
import { useAPI } from "@/providers/ApiProvider";
import { Button, ModalConfirmBig } from "@taureau/ui";
import { Modal, Tooltip } from "antd";
import classNames from "classnames";
import { isNil } from "lodash";
import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import Calculating from "./Calculating";
import ClusterDetail from "./ClusterDetail";
import ClusterFilterPopup from "./ClusterFilterPopup";
import ListCluster from "./ListCluster";
import useDeduplicationStore from "./store";

import styles from "./styles.module.scss";

interface Props {
  [key: string]: any;
}

const Deduplicate = forwardRef((props: Props, ref: any) => {
  const {
    isOpen = false,
    closeModal,
    batchName,
    batchId,
    projectId,
    handleCancelDeduplicateReport,
    workflow,
    isDeduplicationReportCompleted,
    fetchBatchDetail,
  } = props;

  useImperativeHandle(ref, () => ({
    incompleteOwner: () => {
      if (isCalculating) {
        setCalculating(false);
      }

      Message.success({
        content: "Deduplication report calculated with new threshold",
      });

      setNeedRefreshClusterList(true);
      setSelectedCluster(null);
    },
    incompleteMembers: () => {
      if (isCalculating) {
        setModalConfirm(true);
      }
    },
    completedMembers: () => {
      setModalConfirm(true);
    },
    confirmedMembers: (clusterId: string) => {
      setModalConfirm(true);
      setClusterIdConfirmed(clusterId);
    },
    forceCloseModal: () => {
      setOpenForceCloseModal(true);
    },
  }));

  const [isShowFilter, setIsShowFilter] = useState(false);

  const [clusterStatus, setClusterStatus] = useState("all");

  const [isHover, setHover] = useState(false);

  const [isLoading, setLoading] = useState(false);

  const [isOpenModalConfirm, setModalConfirm] = useState(false);

  const [clusterIdConfirmed, setClusterIdConfirmed] = useState<any>();

  const [isOpenForceCloseModal, setOpenForceCloseModal] = useState(false);

  const api = useAPI();

  useEffect(() => {
    fetchBatchDetail();
  }, []);

  const newNode = useMemo(
    () => workflow?.find((node: any) => node?.type === "PreProcessingNew"),
    [workflow]
  );

  const nextNode = useMemo(
    () => findNextNodeByCurrentNodeID(newNode?.id, workflow),
    [newNode?.id, workflow]
  );

  const {
    isCalculating,
    setCalculating,
    selectedCluster,
    resetStore,
    isLoadingListCluster,
    setLoadingListCluster,
    setListCluster,
    setThreshold,
    isCancelingReport,
    needRefreshClusterList,
    setNeedRefreshClusterList,
    listCluster,
    setSelectedCluster,
  } = useDeduplicationStore((state) => ({
    isCalculating: state.isCalculating,
    setCalculating: state.setCalculating,
    selectedCluster: state.selectedCluster,
    resetStore: state.resetStore,
    isLoadingListCluster: state.isLoadingListCluster,
    setLoadingListCluster: state.setLoadingListCluster,
    setListCluster: state.setListCluster,
    setThreshold: state.setThreshold,
    isCancelingReport: state.isCancelingReport,
    needRefreshClusterList: state.needRefreshClusterList,
    setNeedRefreshClusterList: state.setNeedRefreshClusterList,
    listCluster: state.listCluster,
    setSelectedCluster: state.setSelectedCluster,
  }));

  useEffect(() => {
    setSelectedCluster();
  }, [clusterStatus]);

  const fetchListCluster = useCallback(async () => {
    try {
      if (isLoadingListCluster || isCalculating) return;
      setLoadingListCluster(true);

      const params: any = { projectId, batchId };

      if (clusterStatus === "all") {
        delete params.clusterStatus;
      } else {
        params.clusterStatus = clusterStatus;
      }

      const res: any = await api.callApi("getDeduplicateReport", {
        params,
      });

      if (res?.success) {
        const { items } = res;

        setListCluster(items);

        if (items?.length) {
          setThreshold(items[0]?.threshold);
        } else {
          setSelectedCluster();
        }

        if (needRefreshClusterList) {
          setNeedRefreshClusterList(false);
        }
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    } finally {
      setLoadingListCluster(false);
    }
  }, [
    batchId,
    isCalculating,
    isLoadingListCluster,
    projectId,
    clusterStatus,
    needRefreshClusterList,
  ]);

  const completeBatch = useCallback(async () => {
    try {
      if (isLoading) return;

      setLoading(true);

      const res = await api.callApi("completeDeduplication", {
        params: {
          projectId,
          batchId,
        },
        body: {
          workflowStep: nextNode?.name,
          workflowStepId: nextNode?.id,
          workflowState: nextNode?.type,
        },
      });

      if (res?.success) {
        fetchBatchDetail();
        Message.success({
          content: `Data deduplication completed`,
        });
      } else {
        Message.error({ content: res?.message });
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    } finally {
      setLoading(false);
    }
  }, [
    batchId,
    isLoading,
    nextNode?.id,
    nextNode?.name,
    nextNode?.type,
    projectId,
  ]);

  const handleCompleteBatch = useCallback(() => {
    const isConfirmedAll = listCluster?.every(
      (file: any) => file.status === "Confirmed"
    );

    if (!isConfirmedAll) {
      Message.error({
        content:
          "All clusters need to be confirmed before completing deduplication",
      });
      return;
    }

    ModalConfirmBig.confirm({
      title: `Complete deduplication process?`,
      content:
        "If you continue, all files in this data batch will be officially clustered. No deduplication or removal actions will be carried out after this",
      okText: "Continue",
      onOk: completeBatch,
    });
  }, [listCluster]);

  const renderContent = useCallback(() => {
    if (isCalculating || isCancelingReport) {
      return (
        <Calculating
          handleCancelDeduplicateReport={handleCancelDeduplicateReport}
        />
      );
    }

    return (
      <div className={classNames("mt-4 w-full flex gap-2")}>
        <ListCluster
          className={selectedCluster ? "w-[218px] shrink-0" : ""}
          batchId={batchId}
          projectId={projectId}
          isDeduplicationReportCompleted={isDeduplicationReportCompleted}
          fetchBatchDetail={fetchBatchDetail}
          workflowStepId={newNode?.id}
          isShowEmptyData={
            !isNil(listCluster) &&
            !listCluster?.length &&
            clusterStatus === "all"
          }
        />
        {selectedCluster && (
          <ClusterDetail batchId={batchId} projectId={projectId} />
        )}
      </div>
    );
  }, [
    isCalculating,
    isCancelingReport,
    listCluster,
    selectedCluster,
    batchId,
    projectId,
    isDeduplicationReportCompleted,
    fetchBatchDetail,
    newNode?.id,
    handleCancelDeduplicateReport,
  ]);

  const IconFilter = useMemo(() => {
    switch (clusterStatus) {
      case "all":
        return (
          <svg
            width="24"
            height="25"
            viewBox="0 0 24 25"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M11.2008 17.3H12.8008C13.2408 17.3 13.6008 16.94 13.6008 16.5C13.6008 16.06 13.2408 15.7 12.8008 15.7H11.2008C10.7608 15.7 10.4008 16.06 10.4008 16.5C10.4008 16.94 10.7608 17.3 11.2008 17.3ZM4.80078 8.49995C4.80078 8.93995 5.16078 9.29995 5.60078 9.29995H18.4008C18.8408 9.29995 19.2008 8.93995 19.2008 8.49995C19.2008 8.05995 18.8408 7.69995 18.4008 7.69995H5.60078C5.16078 7.69995 4.80078 8.05995 4.80078 8.49995ZM8.00078 13.3H16.0008C16.4408 13.3 16.8008 12.94 16.8008 12.5C16.8008 12.06 16.4408 11.7 16.0008 11.7H8.00078C7.56078 11.7 7.20078 12.06 7.20078 12.5C7.20078 12.94 7.56078 13.3 8.00078 13.3Z"
              fill={isHover ? "#3361FF" : "#C3CAD9"}
            />
          </svg>
        );
      case "Ready":
        return (
          <svg
            width="30"
            height="30"
            viewBox="0 0 30 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect x="5" y="5" width="20" height="20" rx="10" fill="#33BFFF" />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M18.2226 20.2667L15.0026 18.3267L11.7826 20.2667C11.4026 20.4967 10.9326 20.1567 11.0326 19.7267L11.8826 16.0667L9.05263 13.6167C8.72263 13.3267 8.90263 12.7767 9.34263 12.7367L11.2126 12.5767L13.0826 12.4167L14.5426 8.96668C14.7126 8.55668 15.2926 8.55668 15.4626 8.96668L16.9226 12.4067L20.6626 12.7267C21.1026 12.7667 21.2826 13.3167 20.9426 13.6067L18.1126 16.0567L18.9626 19.7267C19.0626 20.1567 18.6026 20.4967 18.2226 20.2667Z"
              fill="#33BFFF"
            />
            <path
              d="M18.2226 20.2667L15.0026 18.3267L11.7826 20.2667C11.4026 20.4967 10.9326 20.1567 11.0326 19.7267L11.8826 16.0667L9.05263 13.6167C8.72263 13.3267 8.90263 12.7767 9.34263 12.7367L11.2126 12.5767L13.0826 12.4167L14.5426 8.96668C14.7126 8.55668 15.2926 8.55668 15.4626 8.96668L16.9226 12.4067L20.6626 12.7267C21.1026 12.7667 21.2826 13.3167 20.9426 13.6067L18.1126 16.0567L18.9626 19.7267C19.0626 20.1567 18.6026 20.4967 18.2226 20.2667Z"
              fill="white"
            />
          </svg>
        );
      case "Confirmed":
        return (
          <svg
            width="30"
            height="30"
            viewBox="0 0 30 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect x="5" y="5" width="20" height="20" rx="10" fill="#66CCA7" />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M20.2122 11.2092L20.3022 11.2992C20.6922 11.6892 20.6922 12.3192 20.3022 12.7092L14.7222 18.2992C14.3322 18.6892 13.7022 18.6892 13.3122 18.2992L10.2222 15.2092C10.0349 15.0224 9.92969 14.7687 9.92969 14.5042C9.92969 14.2397 10.0349 13.9861 10.2222 13.7992L10.3122 13.7092C10.7022 13.3192 11.3322 13.3192 11.7222 13.7092L14.0222 16.0092L18.8022 11.2192C19.1822 10.8192 19.8222 10.8192 20.2122 11.2092Z"
              fill="white"
            />
          </svg>
        );
      default:
        break;
    }
  }, [clusterStatus, isHover]);

  useEffect(() => {
    fetchListCluster();
  }, [clusterStatus, needRefreshClusterList]);

  useEffect(() => {
    return () => {
      resetStore();
    };
  }, []);

  return (
    <Modal
      centered
      footer={null}
      open={isOpen}
      bodyStyle={{
        padding: 12,
      }}
      closable={false}
      className={styles.deduplicateModal}
    >
      <div className="relative flex flex-col">
        <div className="flex justify-between items-center pl-2 pr-[36px]">
          <div className="flex flex-col text-[#346]">
            <span className="text-[14px] font-medium leading-[21px]">
              Deduplicate
            </span>
            <span className="text-[12px] leading-[18px] text-[#4D5E80]">{`Batch: ${batchName}`}</span>
          </div>
          {!isCalculating && !isCancelingReport ? (
            <div className="flex gap-2">
              {isShowFilter ? (
                <div className="relative w-[30px] h-[30px]">
                  <ClusterFilterPopup
                    visible={isShowFilter}
                    setVisible={() => {
                      setIsShowFilter(false);
                      setHover(false);
                    }}
                    setClusterStatus={setClusterStatus}
                  >
                    <Button
                      onClick={() => {
                        setIsShowFilter(!isShowFilter);
                      }}
                      icon={IconFilter}
                      size="xs"
                      corner="Circle"
                      className="hover:bg-[#EBF0FF]"
                      onMouseOver={() => setHover(true)}
                      onMouseLeave={() => setHover(false)}
                    />
                  </ClusterFilterPopup>
                </div>
              ) : (
                <Tooltip
                  title="Filter cluster"
                  overlayInnerStyle={{ fontSize: 14 }}
                >
                  <div className="relative w-[30px] h-[30px]">
                    <ClusterFilterPopup
                      visible={isShowFilter}
                      setVisible={() => {
                        setIsShowFilter(false);
                        setHover(false);
                      }}
                      setClusterStatus={setClusterStatus}
                    >
                      <Button
                        onClick={() => {
                          setIsShowFilter(!isShowFilter);
                        }}
                        icon={IconFilter}
                        size="xs"
                        corner="Circle"
                        className="hover:bg-[#EBF0FF]"
                        onMouseOver={() => setHover(true)}
                        onMouseLeave={() => setHover(false)}
                      />
                    </ClusterFilterPopup>
                  </div>
                </Tooltip>
              )}

              <Tooltip title="Refresh report">
                <Button
                  size="xs"
                  icon={<IconRefresh color="#C3CAD9" size={25} />}
                  corner="Circle"
                  className={classNames("hover:bg-[#EBF0FF]", {
                    [styles.refreshBtn]: true,
                  })}
                  onClick={() => {
                    setNeedRefreshClusterList(true);
                    setSelectedCluster(null);
                    fetchBatchDetail();
                  }}
                ></Button>
              </Tooltip>
              <Button
                size="xs"
                corner="Rounded"
                theme="Primary"
                onClick={handleCompleteBatch}
                disabled={isDeduplicationReportCompleted}
                className="border-none"
              >
                Complete
              </Button>
            </div>
          ) : null}
        </div>
        <div
          className="absolute top-[-10px] right-[-10px] cursor-pointer"
          onClick={closeModal}
        >
          <IconClose color="#C3CAD9" />
        </div>
        <div className={styles.modalContent}>{renderContent()}</div>
      </div>

      {/* Data change modal */}
      <Modal
        open={isOpenModalConfirm}
        centered
        closable={false}
        footer={null}
        bodyStyle={{ padding: "16px 20px" }}
        width={398}
        zIndex={1001}
      >
        <div className="flex flex-col">
          <div className="flex flex-row items-center gap-[11px]">
            <div className="w-10 aspect-square rounded-full flex items-center justify-center bg-[#0000001A]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <path
                  d="M10.5 7.375C10.3342 7.375 10.1753 7.44085 10.0581 7.55806C9.94085 7.67527 9.875 7.83424 9.875 8V10.5C9.875 10.6658 9.94085 10.8247 10.0581 10.9419C10.1753 11.0592 10.3342 11.125 10.5 11.125C10.6658 11.125 10.8247 11.0592 10.9419 10.9419C11.0592 10.8247 11.125 10.6658 11.125 10.5V8C11.125 7.83424 11.0592 7.67527 10.9419 7.55806C10.8247 7.44085 10.6658 7.375 10.5 7.375ZM11.075 12.7625C11.0613 12.7227 11.0424 12.6848 11.0188 12.65L10.9438 12.5562C10.8559 12.4695 10.7443 12.4108 10.623 12.3874C10.5018 12.3641 10.3763 12.3771 10.2625 12.425C10.1868 12.4567 10.117 12.501 10.0563 12.5562C9.99833 12.6146 9.9525 12.6839 9.9214 12.7601C9.89029 12.8362 9.87453 12.9177 9.875 13C9.87599 13.0817 9.89298 13.1624 9.925 13.2375C9.95307 13.3151 9.99786 13.3855 10.0562 13.4438C10.1145 13.5021 10.1849 13.5469 10.2625 13.575C10.3373 13.6081 10.4182 13.6251 10.5 13.6251C10.5818 13.6251 10.6627 13.6081 10.7375 13.575C10.8151 13.5469 10.8855 13.5021 10.9438 13.4438C11.0021 13.3855 11.0469 13.3151 11.075 13.2375C11.107 13.1624 11.124 13.0817 11.125 13C11.1281 12.9584 11.1281 12.9166 11.125 12.875C11.1142 12.8351 11.0974 12.7972 11.075 12.7625ZM10.5 4.25C9.26387 4.25 8.0555 4.61656 7.02769 5.30331C5.99988 5.99007 5.1988 6.96619 4.72576 8.10823C4.25271 9.25027 4.12894 10.5069 4.37009 11.7193C4.61125 12.9317 5.20651 14.0453 6.08059 14.9194C6.95466 15.7935 8.06831 16.3888 9.28069 16.6299C10.4931 16.8711 11.7497 16.7473 12.8918 16.2742C14.0338 15.8012 15.0099 15.0001 15.6967 13.9723C16.3834 12.9445 16.75 11.7361 16.75 10.5C16.75 9.67924 16.5883 8.86651 16.2743 8.10823C15.9602 7.34994 15.4998 6.66095 14.9194 6.08058C14.3391 5.50022 13.6501 5.03984 12.8918 4.72575C12.1335 4.41166 11.3208 4.25 10.5 4.25ZM10.5 15.5C9.5111 15.5 8.5444 15.2068 7.72215 14.6573C6.89991 14.1079 6.25904 13.327 5.88061 12.4134C5.50217 11.4998 5.40315 10.4945 5.59608 9.52455C5.789 8.55464 6.26521 7.66373 6.96447 6.96447C7.66373 6.2652 8.55465 5.789 9.52455 5.59607C10.4945 5.40315 11.4998 5.50216 12.4134 5.8806C13.3271 6.25904 14.1079 6.8999 14.6574 7.72215C15.2068 8.54439 15.5 9.51109 15.5 10.5C15.5 11.8261 14.9732 13.0979 14.0355 14.0355C13.0979 14.9732 11.8261 15.5 10.5 15.5Z"
                  fill="black"
                  fillOpacity="0.8"
                />
              </svg>
            </div>

            <span className="text-[16px] font-medium leading-[24px] text-[#000000CC]">
              Persistent notification
            </span>
          </div>

          <div className="mt-[14px] text-[14px] font-medium leading-[21px] text-[#000000CC]">
            Data has been changed!
          </div>

          <div className="mt-[4px] text-[12px] leading-[21px] text-[#000000CC]">
            Data
            {clusterIdConfirmed ? (
              <>
                {" "}
                in cluster{" "}
                <span className="font-bold">{clusterIdConfirmed}</span>
              </>
            ) : null}{" "}
            on Deduplication Report has been updated by other member.
            <br /> Click OK to renew data.
          </div>

          <div
            className="w-full h-[40px] mt-[16px] rounded-full bg-[#000000CC] flex items-center justify-center text-[14px] font-medium leading-[30px] text-[#fff] cursor-pointer"
            onClick={() => {
              setNeedRefreshClusterList(true);
              setSelectedCluster(null);
              setModalConfirm(false);
              fetchBatchDetail();
              if (clusterIdConfirmed) {
                setClusterIdConfirmed(null);
              }
            }}
          >
            OK
          </div>
        </div>
      </Modal>

      {/* Force close modal */}
      <Modal
        open={isOpenForceCloseModal}
        centered
        closable={false}
        footer={null}
        bodyStyle={{ padding: "16px 20px" }}
        width={398}
        zIndex={1001}
      >
        <div className="flex flex-col">
          <div className="flex flex-row items-center gap-[11px]">
            <div className="w-10 aspect-square rounded-full flex items-center justify-center bg-[#0000001A]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <path
                  d="M10.5 7.375C10.3342 7.375 10.1753 7.44085 10.0581 7.55806C9.94085 7.67527 9.875 7.83424 9.875 8V10.5C9.875 10.6658 9.94085 10.8247 10.0581 10.9419C10.1753 11.0592 10.3342 11.125 10.5 11.125C10.6658 11.125 10.8247 11.0592 10.9419 10.9419C11.0592 10.8247 11.125 10.6658 11.125 10.5V8C11.125 7.83424 11.0592 7.67527 10.9419 7.55806C10.8247 7.44085 10.6658 7.375 10.5 7.375ZM11.075 12.7625C11.0613 12.7227 11.0424 12.6848 11.0188 12.65L10.9438 12.5562C10.8559 12.4695 10.7443 12.4108 10.623 12.3874C10.5018 12.3641 10.3763 12.3771 10.2625 12.425C10.1868 12.4567 10.117 12.501 10.0563 12.5562C9.99833 12.6146 9.9525 12.6839 9.9214 12.7601C9.89029 12.8362 9.87453 12.9177 9.875 13C9.87599 13.0817 9.89298 13.1624 9.925 13.2375C9.95307 13.3151 9.99786 13.3855 10.0562 13.4438C10.1145 13.5021 10.1849 13.5469 10.2625 13.575C10.3373 13.6081 10.4182 13.6251 10.5 13.6251C10.5818 13.6251 10.6627 13.6081 10.7375 13.575C10.8151 13.5469 10.8855 13.5021 10.9438 13.4438C11.0021 13.3855 11.0469 13.3151 11.075 13.2375C11.107 13.1624 11.124 13.0817 11.125 13C11.1281 12.9584 11.1281 12.9166 11.125 12.875C11.1142 12.8351 11.0974 12.7972 11.075 12.7625ZM10.5 4.25C9.26387 4.25 8.0555 4.61656 7.02769 5.30331C5.99988 5.99007 5.1988 6.96619 4.72576 8.10823C4.25271 9.25027 4.12894 10.5069 4.37009 11.7193C4.61125 12.9317 5.20651 14.0453 6.08059 14.9194C6.95466 15.7935 8.06831 16.3888 9.28069 16.6299C10.4931 16.8711 11.7497 16.7473 12.8918 16.2742C14.0338 15.8012 15.0099 15.0001 15.6967 13.9723C16.3834 12.9445 16.75 11.7361 16.75 10.5C16.75 9.67924 16.5883 8.86651 16.2743 8.10823C15.9602 7.34994 15.4998 6.66095 14.9194 6.08058C14.3391 5.50022 13.6501 5.03984 12.8918 4.72575C12.1335 4.41166 11.3208 4.25 10.5 4.25ZM10.5 15.5C9.5111 15.5 8.5444 15.2068 7.72215 14.6573C6.89991 14.1079 6.25904 13.327 5.88061 12.4134C5.50217 11.4998 5.40315 10.4945 5.59608 9.52455C5.789 8.55464 6.26521 7.66373 6.96447 6.96447C7.66373 6.2652 8.55465 5.789 9.52455 5.59607C10.4945 5.40315 11.4998 5.50216 12.4134 5.8806C13.3271 6.25904 14.1079 6.8999 14.6574 7.72215C15.2068 8.54439 15.5 9.51109 15.5 10.5C15.5 11.8261 14.9732 13.0979 14.0355 14.0355C13.0979 14.9732 11.8261 15.5 10.5 15.5Z"
                  fill="black"
                  fillOpacity="0.8"
                />
              </svg>
            </div>

            <span className="text-[16px] font-medium leading-[24px] text-[#000000CC]">
              Action required
            </span>
          </div>

          <div className="mt-[14px] text-[14px] font-medium leading-[21px] text-[#000000CC]">
            Image Deduplication has been turned off
          </div>

          <div className="mt-[4px] text-[12px] leading-[21px] text-[#000000CC]">
            To apply new changes, this deduplication report will
            <br /> be automatically closed.
          </div>

          <div
            className="w-full h-[40px] mt-[16px] rounded-full bg-[#000000CC] flex items-center justify-center text-[14px] font-medium leading-[30px] text-[#fff] cursor-pointer"
            onClick={() => {
              setOpenForceCloseModal(false);
              closeModal();
            }}
          >
            OK
          </div>
        </div>
      </Modal>
    </Modal>
  );
});

export default memo(Deduplicate);
