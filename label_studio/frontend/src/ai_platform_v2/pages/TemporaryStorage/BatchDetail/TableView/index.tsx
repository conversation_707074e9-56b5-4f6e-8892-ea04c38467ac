import Message from "@/ai_platform_v2/component/Message/Message";
import {
  convertUTCToLocalTime,
  DATE_TIME_FORMAT,
} from "@/ai_platform_v2/utils/dateTime";
import { getCurrentProject } from "@/pages/DataSet/Const";
import classNames from "classnames";
import { differenceBy, uniq } from "lodash";
import { useCallback, useMemo, useState } from "react";
import IconExclude from "../../assets/IconExclude";
import IconVector from "../../assets/IconVector";
import { CreatedByColumn } from "../../component/BatchColumn/BatchColumn";
import {
  FilesColumn,
  PreProgressStepColumn,
} from "../../component/BatchDetailColumn/BatchDetailColumn";
import { BatchNotFound } from "../../component/BatchNotFound/BatchNotFound";
import { Table } from "../../component/Table/Table";

const TableView = ({
  batchDetailList,
  handleDeleteFiles,
  setBatchDetailListSelected,
  batchDetailListSelected,
  isLoading,
  handleOpenDataProcessor,
  isLockEditor,
  isBatchDone,
}: any) => {
  const projectId = getCurrentProject();
  const [sortColumn, setSortColumn] = useState({
    column: "",
    sort: "",
  }); // asc | desc

  const disabledSortColumn = useMemo(
    () => !batchDetailList?.length,
    [batchDetailList]
  );

  const handleSortColumn = useCallback(
    (column) => {
      if (disabledSortColumn) {
        return;
      }

      if (sortColumn.column === column) {
        if (sortColumn.sort === "desc") {
          setSortColumn({ column, sort: "asc" });
        } else if (sortColumn.sort === "asc") {
          setSortColumn({ column, sort: "" });
        } else {
          setSortColumn({ column, sort: "desc" });
        }
      } else {
        setSortColumn({ column, sort: "desc" });
      }
    },
    [disabledSortColumn, sortColumn]
  );

  const finalColumns = [
    {
      title: (
        <div
          className={classNames(
            `taureau-col-sort ${
              sortColumn.column === "fileName"
                ? `taureau-col-sort-${sortColumn.sort}`
                : ""
            } pl-[16px]`,
            {
              "taureau-col-sort-disabled": disabledSortColumn,
            }
          )}
          onClick={() => handleSortColumn("fileName")}
        >
          <div className="content" style={{ color: "#346" }}>
            FILES
          </div>
          <IconVector className="sort-icon" />
        </div>
      ),
      key: "id",
      render: (record) => (
        <div
          className="w-full flex"
          onClick={() => {
            if (isLockEditor) {
              Message.error({
                content: "Data is being processed",
              });
            } else {
              handleOpenDataProcessor(record);
            }
          }}
        >
          <FilesColumn
            imgUrl={`/api/projects/${projectId}/temp_storage_batch_files/${record?.id}/image?type=Original`}
            fileName={record?.fileName}
          />
        </div>
      ),
      width: 210,
    },
    {
      title: <div className="pl-[16px]">PRE-PROCESSING STEP</div>,
      dataIndex: "preProcessingStep",
      key: "preProcessingStep",
      width: 200,
      render: (
        _,
        { workflowState, workflowStep, stepStatus, transferStatus }
      ) => (
        <PreProgressStepColumn
          step={workflowStep}
          workflowState={workflowState}
          status={
            workflowState === "HumanApproval"
              ? stepStatus
              : workflowState === "PreProcessingCompleted"
              ? transferStatus === "Completed"
                ? "Transferred"
                : transferStatus && transferStatus !== "Completed"
                ? "Transferring"
                : null
              : workflowState === "PreProcessingNew" && isLockEditor
              ? "Processing"
              : "Ready"
          }
        />
      ),
    },
    {
      title: <div className="pl-[10px]">TRANSFERRED BY</div>,
      key: "transferredBy",
      width: 200,
      render: ({ fullName, avatar, transfedDate }) => (
        <>
          {transfedDate && (fullName || avatar) ? (
            <CreatedByColumn user={{ username: fullName, avatar }} />
          ) : (
            "--"
          )}
        </>
      ),
    },
    {
      title: (
        <div
          className={classNames(
            `taureau-col-sort ${
              sortColumn.column === "transfedDate"
                ? `taureau-col-sort-${sortColumn.sort}`
                : ""
            } pl-[10px]`,
            {
              "taureau-col-sort-disabled": disabledSortColumn,
            }
          )}
          onClick={() => handleSortColumn("transfedDate")}
        >
          <div className="content">TRANSFERRED DATE</div>
          <IconVector className="sort-icon" />
        </div>
      ),
      key: "transfedDate",
      width: 180,
      render: (_, { transfedDate }) =>
        transfedDate
          ? convertUTCToLocalTime(transfedDate, DATE_TIME_FORMAT.MMMDDYYYY_HHMM)
          : "--",
    },
    {
      title: (
        <div
          className={classNames(
            `taureau-col-sort ${
              sortColumn.column === "modifiedAt"
                ? `taureau-col-sort-${sortColumn.sort}`
                : ""
            } pl-[10px]`,
            {
              "taureau-col-sort-disabled": disabledSortColumn,
            }
          )}
          onClick={() => handleSortColumn("modifiedAt")}
        >
          <div className="content">MODIFIED DATE</div>
          <IconVector className="sort-icon" />
        </div>
      ),
      key: "modifiedAt",
      width: 150,
      render: (_, { modifiedAt }) =>
        modifiedAt
          ? convertUTCToLocalTime(modifiedAt, DATE_TIME_FORMAT.MMMDDYYYY_HHMM)
          : "--",
    },
    {
      title: (
        <div
          className={classNames(
            `taureau-col-sort ${
              sortColumn.column === "createdAt"
                ? `taureau-col-sort-${sortColumn.sort}`
                : ""
            } pl-[10px]`,
            {
              "taureau-col-sort-disabled": disabledSortColumn,
            }
          )}
          onClick={() => handleSortColumn("createdAt")}
        >
          <div className="content">CREATED DATE</div>
          <IconVector className="sort-icon" />
        </div>
      ),
      key: "createdAt",
      width: 150,
      render: (_, { createdAt }) =>
        createdAt
          ? convertUTCToLocalTime(createdAt, DATE_TIME_FORMAT.MMMDDYYYY_HHMM)
          : "--",
    },
    {
      title: "",
      width: "0px",
      render: (_, record) => {
        if (isBatchDone) return;
        return (
          <div
            className="cursor-pointer delete-action-btn"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteFiles([record], false);
            }}
          >
            <IconExclude />
          </div>
        );
      },
      className: "delete-action",
    },
  ];

  const batchListFinal = useMemo(() => {
    const sortBatchList = batchDetailList ? [...batchDetailList] : [];

    if (sortColumn.sort === "asc") {
      sortBatchList?.sort((a: any, b: any) => {
        const colA = a?.[sortColumn.column]?.toLowerCase() ?? " ";
        const colB = b?.[sortColumn.column]?.toLowerCase() ?? " ";

        if (colA < colB) {
          return -1;
        }
        if (colA > colB) {
          return 1;
        }
        return 0;
      });
    } else if (sortColumn.sort === "desc") {
      sortBatchList?.sort((a: any, b: any) => {
        const colA = a?.[sortColumn.column]?.toLowerCase() ?? " ";
        const colB = b?.[sortColumn.column]?.toLowerCase() ?? " ";

        if (colA < colB) {
          return 1;
        }
        if (colA > colB) {
          return -1;
        }
        return 0;
      });
    }
    return sortBatchList;
  }, [sortColumn, batchDetailList]);

  const onSelectChange = useCallback(
    (record, selected, selectedRows) => {
      if (selected) {
        setBatchDetailListSelected([...batchDetailListSelected, record]);
      } else {
        setBatchDetailListSelected(
          batchDetailListSelected.filter((item: any) => item.id !== record.id)
        );
      }
    },
    [batchDetailListSelected]
  );

  const onSelectChangeAll = useCallback(
    (selected, selectedRows, changeRows) => {
      if (selected) {
        setBatchDetailListSelected(
          uniq([...batchDetailListSelected, ...changeRows])
        );
      } else {
        setBatchDetailListSelected(
          differenceBy(batchDetailListSelected, changeRows, "id")
        );
      }
    },
    [batchDetailListSelected]
  );

  return (
    <Table
      loading={isLoading}
      locale={{ emptyText: <BatchNotFound /> }}
      style={{ width: "100%" }}
      columns={finalColumns}
      dataSource={batchListFinal}
      rowSelection={
        isBatchDone
          ? undefined
          : {
              selectedRowKeys: batchDetailListSelected.map(
                (item: any) => item.id
              ),
              columnWidth: 50,
              onSelect: onSelectChange,
              onSelectAll: onSelectChangeAll,
            }
      }
      scroll={batchDetailList?.length ? undefined : { y: "100%" }}
    />
  );
};

export default TableView;
