import IconPlus from "@/ai_platform_v2/assets/Icons/IconPlus";
import IconSearch from "@/ai_platform_v2/assets/Icons/IconSearch";
import { Button, Input, ModalConfirmBig } from "@taureau/ui";
import classNames from "classnames";
import { debounce, isArray } from "lodash";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { Pagination } from "@/ai_platform_v2/component/Pagination/Pagination";
import { useBatchImportData } from "@/ai_platform_v2/providers/BatchImportDataProvider";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { getCurrentModule, getCurrentProject } from "@/pages/DataSet/Const";
import { useAPI } from "@/providers/ApiProvider";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { useHistory } from "react-router";
import { BatchDetailFilter } from "../component/BatchDetailFilter/BatchDetailFIlter";
import { BatchEmpty } from "../component/BatchEmpty/BatchEmpty";
import { InputLabel } from "../component/InputLabel/InputLabel";
import SkeletonLoading from "../component/SkeletonLoading";
import { DEFAULT_PAGE_SIZE, INIT_BATCH_DETAIL_PARAMS } from "../util/const";

import Message from "@/ai_platform_v2/component/Message/Message";
import { useCurrentUser } from "@/providers/CurrentUser";
import { useProject } from "@/providers/ProjectProvider";
import IconFormatListBulleted from "@v2/assets/Icons/IconFormatListBulleted";
import IconViewModule from "@v2/assets/Icons/IconViewModule";
import { Segmented } from "antd";
import {
  checkHavePermissionInListMembers,
  findNextNodeByCurrentNodeID,
  orderPreProcessingWorkflow,
} from "../../TaskManagers/helper";
import BatchDetailMenuBar from "../component/BatchDetailMenuBar/BatchDetailMenuBar";
import DataProcessor from "../DataProcessor";
import Deduplicate from "./Deduplicate";
import useDeduplicationStore from "./Deduplicate/store";
import GridView from "./GridView";
import TableView from "./TableView";
import { useTempStorageWS } from "./TempStorageWSProvider";

import { useImportData } from "@/ai_platform_v2/providers/ImportDataProvider";
import "./BatchDetail.scss";

interface BatchDetailProps {
  batchId?: any;
}

enum EnumView {
  Table,
  Grid,
}

const BatchDetail = (props: BatchDetailProps) => {
  const { batchId } = props;

  const [batchName, setBatchName] = useState();

  const [batchDetail, setBatchDetail] = useState<any>();

  const [isOpenDeduplicateModal, setOpenDeduplicateModal] = useState(false);

  const projectId = getCurrentProject();

  const ref = useRef(null);

  const importData = useImportData();
  const batchImportData = useBatchImportData();

  const {
    openImport,
    handleOpenImportModal: handleOpenBatchImportModal,
    setFuncFetchDataSets,
  } = batchImportData;

  const { importState, handleOpenImportModal: handleOpenDataImportModal } =
    importData;

  const { files: filesData, preAttached } = importState;

  const [batchDetailListSelected, setBatchDetailListSelected] = useState([]);
  const [batchDetailList, setBatchDetailList] = useState<any>();
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  const [batchParams, setBatchParams] = useState(INIT_BATCH_DETAIL_PARAMS);
  const [valueSearchName, setValueSearchName] = useState("");

  const [openFilter, setOpenFilter] = useState(false);
  const [isFocusSearch, setFocusSearch] = useState(false);

  const [view, setView] = useState(EnumView.Table);

  const [isCreatingReport, setCreatingReport] = useState(false);

  const [isShowBtnCancel, setShowBtnCancel] = useState(false);

  const [isOpenDataProcessorModal, setOpenDataProcessorModal] = useState(false);

  const [selectedFileId, setSelectedFileId] = useState();

  const history = useHistory();

  const moduleId = getCurrentModule();

  const { user } = useCurrentUser();

  const api = useAPI();

  const { hasPermissionAllScopeInProject } = useCheckPermission();

  const canDataProceed = hasPermissionAllScopeInProject(
    ABILITY_NEW.can_data_proceed,
    moduleId,
    projectId
  );

  const canViewDataset = hasPermissionAllScopeInProject(
    ABILITY_NEW.can_view_dataset,
    moduleId,
    projectId
  );

  const { project, fetchProject, fetchProjectDetail } = useProject();

  const [workflow, setWorkflow] = useState<any>();

  const preProcessingCompleted = useMemo(
    () => workflow?.find((item: any) => item.type === "PreProcessingCompleted"),
    [workflow]
  );

  const preProcessingNew = useMemo(
    () => workflow?.find((item: any) => item.type === "PreProcessingNew"),
    [workflow]
  );

  const isDeduplicateOn = useMemo(
    () =>
      preProcessingNew?.settings?.isCopilot &&
      preProcessingNew?.settings?.isSimilarityCheck,
    [preProcessingNew]
  );

  const isLockEditor = useMemo(
    () => isDeduplicateOn && batchDetail?.deduplicationStatus === "Complete",
    [batchDetail?.deduplicationStatus, isDeduplicateOn]
  );

  const isReadyToNextStep = useMemo(() => {
    if (isDeduplicateOn) {
      return batchDetail?.deduplicationStatus === "ReadyToNextStep";
    }
    return true;
  }, [batchDetail?.deduplicationStatus, isDeduplicateOn]);

  const isManualTransfer =
    preProcessingCompleted?.settings?.isManualConfirmation;

  const isBatchDone = useMemo(
    () => batchDetail?.batchFileStatus === "Done",
    [batchDetail?.batchFileStatus]
  );

  const {
    setMinThreshold,
    isCancelingReport,
    setCancelingReport,
    needForceClose,
    setNeedForceClose,
    isCalculating,
    setCalculating,
    needRefetchData,
    setNeedRefetchData,
  } = useDeduplicationStore((state) => ({
    setMinThreshold: state.setMinThreshold,
    isCancelingReport: state.isCancelingReport,
    setCancelingReport: state.setCancelingReport,
    needForceClose: state.needForceClose,
    setNeedForceClose: state.setNeedForceClose,
    isCalculating: state.isCalculating,
    setCalculating: state.setCalculating,
    needRefetchData: state.needRefetchData,
    setNeedRefetchData: state.setNeedRefetchData,
  }));

  const handleOpenImportModal = useCallback(
    (batchDetail) => {
      if (
        !!filesData?.length ||
        preAttached.zipFile ||
        preAttached.formattedFile
      ) {
        handleOpenDataImportModal();
      } else {
        handleOpenBatchImportModal(batchDetail);
      }
    },
    [
      filesData.length,
      JSON.stringify(preAttached),
      handleOpenDataImportModal,
      handleOpenBatchImportModal,
    ]
  );

  const fetchBatchDetail = useCallback(async () => {
    const result: any = await api.callApi("getBatchDetail", {
      params: {
        projectId,
        batchId,
      },
    });

    if (result?.success) {
      setBatchDetail(result?.data);
    }
  }, [projectId, batchId]);

  useEffect(() => {
    fetchBatchDetail();
  }, [batchId]);

  const handleUpdateBatchName = useCallback(
    async (batchId, batchName, reBatchName) => {
      if (!batchName) {
        Message.error({
          content: "Batch name cannot be blank",
        });
      }

      const result: any = await api.callApi("updateDataBatch", {
        params: {
          pk: projectId,
        },
        body: { batchFileId: batchId, batchName },
      });

      if (result?.success) {
        Message.success({
          content: "Batch name updated",
        });

        fetchBatchDetail();
      } else {
        if (result?.errorCode === "Batch name already existed") {
          Message.error({
            content: "Batch name already exists!",
          });
        }
        reBatchName?.();
      }
    },
    [projectId]
  );

  const fetchDataBatch = useCallback(async () => {
    try {
      if (loading) return;

      const params: any = {
        pk: projectId,
        tempId: batchId,
        page: batchParams.currentPage,
        pageSize: batchParams.pageSize || DEFAULT_PAGE_SIZE,
        sortBy: "updatedAt",
        orderBy: "desc",
      };

      if (batchParams.searchValue) {
        params.keyword = batchParams.searchValue;
      }

      if (batchParams.filterSelected?.length) {
        params.workflowStepIds = batchParams.filterSelected
          .map((value: any) => value.workflowStepId.join(","))
          .filter((item, pos, self) => self.indexOf(item) === pos)
          .join(",");

        params.stepStatus = batchParams.filterSelected
          .map((value: any) => value.workflowStepStatus)
          .filter((item, pos, self) => self.indexOf(item) === pos)
          .join(",");
      }

      setLoading(true);

      const result: any = await api.callApi("dataBatchDetail", {
        params,
      });

      setLoading(false);

      if (result?.success) {
        setBatchDetailList(
          result?.items?.map((item: any) => ({ ...item, key: item.id }))
        );
        setTotal(result.total);
      } else {
        setBatchDetailList([]);
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    } finally {
      setLoading(false);
    }
  }, [projectId, batchId, batchParams, loading]);

  const minThreshold = useMemo(
    () =>
      workflow?.find((item: any) => item.type === "PreProcessingNew")?.settings
        ?.threshold,
    [workflow]
  );

  useEffect(() => {
    setMinThreshold(minThreshold);
  }, [minThreshold]);

  useEffect(() => {
    if (isOpenDeduplicateModal && needForceClose) {
      ref?.current?.forceCloseModal();
    }
    setNeedForceClose(false);
    fetchBatchDetail();
    fetchDataBatch();

    fetchProject(projectId, true);
  }, [isOpenDeduplicateModal, needForceClose, projectId]);

  useEffect(() => {
    if (needRefetchData) {
      setNeedRefetchData(false);
      fetchBatchDetail();
      fetchDataBatch();

      fetchProject(projectId, true);
    }
  }, [needRefetchData, projectId]);

  useEffect(() => {
    if (project?.preProccessConfig) {
      const workflow = JSON.parse(project?.preProccessConfig);

      setWorkflow(orderPreProcessingWorkflow(workflow));
    }
  }, [project?.preProccessConfig]);

  const { tempStorageWS } = useTempStorageWS();

  tempStorageWS.onmessage = (data: any) => {
    const msg = JSON.parse(data.data)?.message;

    const { type, projectId: projectIdWS, batchId: batchIdWS, ClusterId } = msg;

    const isCurrentProject = projectId === projectIdWS;
    const isCurrentBatch = batchId === batchIdWS;

    if (isCurrentProject && isCurrentBatch) {
      switch (type) {
        case "Deduplication.InCompleted.Owner":
          if (isCreatingReport) {
            setCreatingReport(false);
          }

          setBatchDetail({
            ...batchDetail,
            deduplicationStatus: "Incompleted",
          });

          if (isOpenDeduplicateModal) {
            ref?.current?.incompleteOwner();
          } else {
            setOpenDeduplicateModal(true);
            Message.success({ content: "Deduplication report created" });
          }

          break;

        case "Deduplication.InCompleted.Members":
          if (isCreatingReport) {
            setCancelingReport(false);
          }

          setBatchDetail({
            ...batchDetail,
            deduplicationStatus: "Incompleted",
          });

          if (isOpenDeduplicateModal) {
            ref?.current?.incompleteMembers();
          }

          break;

        case "Deduplication.Completed.Members":
          if (isOpenDeduplicateModal) {
            ref?.current?.completedMembers();
          }

          break;

        case "Cluster.Confirmed.Members":
          if (isOpenDeduplicateModal) {
            ref?.current?.confirmedMembers(ClusterId);
          }

          break;

        case "Deduplication.Cancelled.Owner":
        case "Deduplication.Cancelled.Members":
          if (isCancelingReport) {
            setCancelingReport(false);
            setBatchDetail({
              ...batchDetail,
              deduplicationStatus: null,
            });
            if (isOpenDeduplicateModal) {
              setOpenDeduplicateModal(false);
            }
            Message.success({ content: "Deduplication progress cancelled" });
          }

          if (isCalculating) {
            setCalculating(false);
          }

          break;

        case "Deduplication.Cancelling":
          setCancelingReport(true);
          break;

        default:
          break;
      }
    }
  };

  const handleOpenDataProcessor = useCallback((file) => {
    setSelectedFileId(file.id);
    setOpenDataProcessorModal(true);
  }, []);

  useEffect(() => {
    if (batchDetail?.deduplicationStatus === "Inprogress") {
      setCreatingReport(true);
    } else if (batchDetail?.deduplicationStatus === "Cancelling") {
      setCancelingReport(true);
    } else {
      setCreatingReport(false);
      setCancelingReport(false);
    }
  }, [batchDetail]);

  const handleDeleteFiles = useCallback(
    (filesDeleted = batchDetailListSelected, isMultiple = true) => {
      const filesDeletedLength = filesDeleted?.length;

      if (!filesDeletedLength) {
        return;
      }

      ModalConfirmBig.warning({
        title: isMultiple
          ? `Delete ${filesDeletedLength} selected files in ${batchDetail?.batchName}?`
          : `Delete file ${filesDeleted[0].fileName} in ${batchDetail?.batchName}?`,
        content: `If continue, ${
          isMultiple ? "these files" : "this file"
        } will be permanently removed.`,
        okText: "Delete",
        cancelText: "Cancel",
        onOk: async () => {
          const result: any = await api.callApi("deleteDataBatchFiles", {
            params: {
              pk: projectId,
              tempId: batchDetail.id,
            },
            body: {
              fileIds: [...filesDeleted.map((item: any) => item.id)],
            },
          });

          if (result?.success) {
            Message.success({
              content: isMultiple
                ? `${filesDeletedLength} files deleted`
                : `File deleted`,
            });
            setBatchDetailListSelected(
              batchDetailListSelected.filter(
                (fileId: any) => !filesDeleted?.includes(fileId)
              )
            );

            const currentPageItemsLength = batchDetailList?.length;

            if (
              filesDeletedLength === currentPageItemsLength &&
              batchParams.currentPage > 1
            ) {
              setBatchParams({
                ...batchParams,
                currentPage: batchParams.currentPage - 1,
              });
            } else {
              fetchDataBatch();
            }
          }
        },
      });
    },
    [
      projectId,
      batchDetail,
      batchParams,
      batchDetailList,
      batchDetailListSelected,
    ]
  );

  const handleStartProcessing = useCallback(
    (fileSelected = batchDetailListSelected, isMultiple = true) => {
      if (!fileSelected?.length) {
        return;
      }

      if (!isReadyToNextStep) {
        Message.error({
          content: "Data need to be deduplicated first",
        });

        return;
      }

      const onStartProcessing = async () => {
        const newNode = workflow?.find(
          (node: any) => node?.type === "PreProcessingNew"
        );

        const nextNode = findNextNodeByCurrentNodeID(newNode?.id, workflow);

        const tempStorageFiles = fileSelected?.map((file: any) =>
          nextNode?.type === "PreProcessingCompleted"
            ? {
                id: file?.id,
                workflowStep: nextNode?.name,
                workflowStepId: nextNode?.id,
                workflowState: nextNode?.type,
                isCompleted: true,
              }
            : {
                id: file?.id,
                workflowStep: nextNode?.name,
                workflowStepId: nextNode?.id,
                workflowState: nextNode?.type,
              }
        );

        const result: any = await api.callApi("updateMultipleFiles", {
          params: {
            projectId,
            tempId: batchId,
          },
          body: {
            tempStorageFiles,
          },
        });

        if (result?.success) {
          Message.success({
            content: isMultiple
              ? `${fileSelected?.length} files started processing`
              : `${fileSelected[0].fileName} started processing`,
          });

          if (
            nextNode?.type === "PreProcessingCompleted" &&
            !isManualTransfer
          ) {
            const ids = fileSelected?.map((file: any) => file.id);
            const result: any = await api.callApi("transferMultipleFiles", {
              params: {
                projectId,
                tempId: batchId,
              },
              body: {
                ids,
                isAutoTransfer: true,
              },
            });
          }
          setBatchDetailListSelected([]);
          fetchDataBatch();
        }
      };

      if (isMultiple) {
        if (
          !fileSelected?.every(
            (item: any) =>
              !item.workflowState || item.workflowState === "PreProcessingNew"
          )
        ) {
          Message.error({
            content: "Please choose files having the same New step",
          });

          return;
        }
        ModalConfirmBig.confirm({
          title: "Start processing?",
          content:
            "If continue, these files will be moved to the next step in workflow and ready to proceed.",
          okText: "Continue",
          cancelText: "Cancel",
          onOk: onStartProcessing,
        });
      } else {
        onStartProcessing();
      }
    },
    [
      batchDetailListSelected,
      workflow,
      projectId,
      batchId,
      isManualTransfer,
      isReadyToNextStep,
    ]
  );

  const handleApprove = useCallback(
    (fileSelected = batchDetailListSelected, isMultiple = true) => {
      if (!fileSelected?.length) {
        return;
      }

      const currentNodeId = fileSelected?.[0]?.workflowStepId;

      const currentNode = workflow?.find(
        (item: any) => item.id === currentNodeId
      );

      const isHumanApprovalNode = currentNode?.type === "HumanApproval";

      const isSameNode = fileSelected.every(
        (item: any) => item.workflowStepId === currentNodeId
      );

      if (!isHumanApprovalNode || !isSameNode) {
        Message.error({
          content: "Please choose files having the same Human Approval step",
        });

        return;
      }

      const isAllReadyNode = fileSelected.every(
        (item: any) => item.stepStatus !== "Reject"
      );

      if (!isAllReadyNode) {
        Message.error({
          content:
            "Please choose files having Human Approval step that are in Ready status",
        });

        return;
      }

      if (!checkHavePermissionInListMembers(currentNode?.members, user.id)) {
        Message.error({
          content:
            "Please choose files having Human Approval step that you are allowed to proceed",
        });

        return;
      }

      const onApprove = async (nodeId: string) => {
        const nextNode = findNextNodeByCurrentNodeID(nodeId, workflow);

        const tempStorageFiles = fileSelected?.map((file: any) =>
          nextNode?.type === "PreProcessingCompleted"
            ? {
                id: file?.id,
                workflowStep: nextNode?.name,
                workflowStepId: nextNode?.id,
                workflowState: nextNode?.type,
                isCompleted: true,
              }
            : {
                id: file?.id,
                workflowStep: nextNode?.name,
                workflowStepId: nextNode?.id,
                workflowState: nextNode?.type,
              }
        );

        const result: any = await api.callApi("updateMultipleFiles", {
          params: {
            projectId,
            tempId: batchId,
          },
          body: {
            tempStorageFiles,
          },
        });

        if (result?.success) {
          Message.success({
            content: isMultiple
              ? `${fileSelected?.length} files approved`
              : `${fileSelected[0].fileName} approved`,
          });
          if (
            nextNode?.type === "PreProcessingCompleted" &&
            !isManualTransfer
          ) {
            const ids = fileSelected?.map((file: any) => file.id);
            const result: any = await api.callApi("transferMultipleFiles", {
              params: {
                projectId,
                tempId: batchId,
              },
              body: {
                ids,
                isAutoTransfer: true,
              },
            });
          }
          setBatchDetailListSelected([]);
          fetchDataBatch();
        }
      };

      if (isMultiple) {
        ModalConfirmBig.confirm({
          title: "Approve data?",
          content:
            "If continue, these files will be approved and moved to the next step in workflow.",
          okText: "Continue",
          cancelText: "Cancel",
          onOk: () => onApprove(currentNodeId),
        });
      } else {
        onApprove(currentNodeId);
      }
    },
    [
      batchDetailListSelected,
      workflow,
      user.id,
      projectId,
      batchId,
      isManualTransfer,
    ]
  );

  const handleTransfer = useCallback(
    (fileSelected = batchDetailListSelected, isMultiple = true) => {
      if (!fileSelected?.length) {
        return;
      }

      const currentNode = workflow.find(
        (node: any) => node.type === "PreProcessingCompleted"
      );

      const isSameNode = fileSelected.every(
        (item: any) => item.workflowState === "PreProcessingCompleted"
      );

      if (!isSameNode) {
        Message.error({
          content: "Please choose files having the same Completed step",
        });

        return;
      }

      const isNotAllReadyNode = fileSelected.some(
        (item: any) =>
          item.isCompleted &&
          (item.transferStatus === "Completed" ||
            item.transferStatus === "ReadyTransfer")
      );

      if (isNotAllReadyNode) {
        Message.error({
          content:
            "Please choose files having Completed step that are in Ready status",
        });

        return;
      }

      if (!checkHavePermissionInListMembers(currentNode?.members, user.id)) {
        Message.error({
          content:
            "Please choose files having Completed step that you are allowed to proceed",
        });

        return;
      }

      const onTransfer = async () => {
        const ids = fileSelected?.map((file: any) => file.id);

        const result: any = await api.callApi("transferMultipleFiles", {
          params: {
            projectId,
            tempId: batchId,
          },
          body: {
            ids,
          },
        });

        if (result?.success) {
          Message.success({
            content: isMultiple
              ? `${fileSelected?.length} files transferring`
              : `${fileSelected[0].fileName} transferring`,
          });
          setBatchDetailListSelected([]);
          fetchDataBatch();
        }
      };

      if (isMultiple) {
        ModalConfirmBig.confirm({
          title: "Transfer data?",
          content:
            "If continue, these files in this batch will be officially imported into main storage",
          okText: "Continue",
          cancelText: "Cancel",
          onOk: onTransfer,
        });
      } else {
        onTransfer();
      }
    },
    [batchDetailListSelected, workflow, user.id, projectId, batchId]
  );

  const handleReject = useCallback(
    (fileSelected = batchDetailListSelected, isMultiple = true) => {
      if (!fileSelected?.length) {
        return;
      }

      const currentNodeId = fileSelected?.[0]?.workflowStepId;

      const currentNode = workflow?.find(
        (item: any) => item.id === currentNodeId
      );

      const isHumanApprovalNode = currentNode?.type === "HumanApproval";

      const isSameNode = fileSelected.every(
        (item: any) => item.workflowStepId === currentNodeId
      );

      if (!isHumanApprovalNode || !isSameNode) {
        Message.error({
          content: "Please choose files having the same Human Approval step",
        });

        return;
      }

      const isAllReadyNode = fileSelected.every(
        (item: any) => item.stepStatus !== "Reject"
      );

      if (!isAllReadyNode) {
        Message.error({
          content:
            "Please choose files having Human Approval step that are in Ready status",
        });

        return;
      }

      if (!checkHavePermissionInListMembers(currentNode?.members, user.id)) {
        Message.error({
          content:
            "Please choose files having Human Approval step that you are allowed to proceed",
        });

        return;
      }

      const onReject = async () => {
        const tempStorageFiles = fileSelected?.map((file: any) => ({
          id: file?.id,
          stepStatus: "Reject",
        }));

        const result: any = await api.callApi("updateMultipleFiles", {
          params: {
            projectId,
            tempId: batchId,
          },
          body: {
            tempStorageFiles,
          },
        });

        if (result?.success) {
          Message.success({
            content: isMultiple
              ? `${fileSelected?.length} files rejected`
              : `${fileSelected[0].fileName} rejected`,
          });
          setBatchDetailListSelected([]);
          fetchDataBatch();
        }
      };

      if (isMultiple) {
        ModalConfirmBig.confirm({
          title: "Reject data?",
          content:
            "If continue, these files will be updated status to rejected and cannot be proceeded in other steps ",
          okText: "Continue",
          cancelText: "Cancel",
          onOk: onReject,
        });
      } else {
        onReject();
      }
    },
    [batchDetailListSelected, workflow, user.id, projectId, batchId]
  );

  const debounceUpdateSearchValueStore = useCallback(
    debounce((value: string | any) => {
      setBatchParams({ ...batchParams, currentPage: 1, searchValue: value });
    }, 300),
    [batchParams]
  );

  const onChangeSearchValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value: any = e.target.value;

    setValueSearchName(value);

    debounceUpdateSearchValueStore(value.trim());
  };

  const onChangeFilter = useCallback(
    (value: any) => {
      setBatchParams({
        ...batchParams,
        currentPage: 1,
        filterSelected: value,
      });
    },
    [batchParams]
  );

  useEffect(() => {
    if (canDataProceed && canViewDataset && projectId && batchId) {
      fetchDataBatch();
    }
  }, [
    canDataProceed,
    canViewDataset,
    projectId,
    batchId,
    batchParams.currentPage,
    batchParams.pageSize,
    batchParams.searchValue,
    batchParams.filterSelected.length,
  ]);

  useEffect(() => {
    if (batchDetail?.id) {
      setBatchName(batchDetail?.batchName);
    }
  }, [JSON.stringify(batchDetail)]);

  useEffect(() => {
    if (openImport) {
      setFuncFetchDataSets(() => fetchDataBatch);
    }
  }, [openImport]);

  const handleOpenImportModalFinal = useCallback(() => {
    if (isDeduplicateOn) {
      if (
        !batchDetail?.deduplicationStatus ||
        batchDetail?.deduplicationStatus === "Ready"
      ) {
        fetchProjectDetail();
        handleOpenImportModal(batchDetail);
      } else {
        Message.error({
          content:
            "Cannot add more files after deduplicating data batch. Please retry with a new batch!",
        });
      }
    } else {
      handleOpenImportModal(batchDetail);
    }
  }, [isDeduplicateOn, handleOpenImportModal, JSON.stringify(batchDetail)]);

  const renderContent = useMemo(() => {
    if (!isArray(batchDetailList)) return <SkeletonLoading />;

    if (
      !loading &&
      !openFilter &&
      !batchDetailList?.length &&
      !batchParams.searchValue &&
      !batchParams.filterSelected.length
    ) {
      return (
        <BatchEmpty
          buttonContent="Add files"
          onClick={() => handleOpenImportModalFinal()}
        />
      );
    }

    return (
      <div className="w-full h-full flex flex-col justify-between">
        {view === EnumView.Table ? (
          <TableView
            isLoading={loading}
            batchDetailList={batchDetailList}
            batchDetailListSelected={batchDetailListSelected}
            handleDeleteFiles={handleDeleteFiles}
            setBatchDetailListSelected={setBatchDetailListSelected}
            handleOpenDataProcessor={handleOpenDataProcessor}
            isLockEditor={isLockEditor}
            isBatchDone={isBatchDone}
          />
        ) : (
          <GridView
            setBatchDetailListSelected={setBatchDetailListSelected}
            batchDetailListSelected={batchDetailListSelected}
            batchDetailList={batchDetailList}
            isLoading={loading}
            isLockEditor={isLockEditor}
            handleOpenDataProcessor={handleOpenDataProcessor}
            isBatchDone={isBatchDone}
          />
        )}

        {!!batchDetailList?.length && (
          <div className="w-full flex justify-end">
            <Pagination
              size="large"
              page={batchParams.currentPage}
              pageSize={batchParams.pageSize}
              totalItems={total}
              totalPages={Math.ceil(total / batchParams.pageSize)}
              pageSizeOptions={[5, 10, 30, 50, 100]}
              onChange={(page, pageSize) => {
                setBatchParams({
                  ...batchParams,
                  currentPage: page,
                  pageSize,
                });
              }}
            />
          </div>
        )}
      </div>
    );
  }, [
    openFilter,
    batchDetailList,
    loading,
    batchParams,
    view,
    batchDetailListSelected,
    handleDeleteFiles,
    handleOpenDataProcessor,
    isLockEditor,
    handleApprove,
    handleStartProcessing,
    handleTransfer,
    handleReject,
    isManualTransfer,
    total,
    isBatchDone,
    handleOpenImportModalFinal,
  ]);

  const isShowFilter = useMemo(
    () =>
      openFilter ||
      loading ||
      batchDetailList?.length ||
      batchParams.searchValue ||
      batchParams.filterSelected.length,
    [openFilter, loading, batchDetailList, batchParams]
  );

  const handleCreateDeduplicateReport = useCallback(async () => {
    if (isBatchDone) {
      Message.error({
        content: "Cannot deduplicate data after marking batch as done.",
      });
      return;
    }
    if (isCreatingReport) return;
    try {
      const res: any = await api.callApi("createDeduplicateReport", {
        params: {
          projectId,
          batchId,
        },
        body: {
          threshold: minThreshold,
          workflowStepId: preProcessingNew?.id,
        },
      });

      if (res.success) {
        fetchBatchDetail();
      } else {
        Message.error({
          content: res?.message,
        });
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    }
  }, [batchId, isCreatingReport, minThreshold, projectId, preProcessingNew]);

  const handleCancelDeduplicateReport = useCallback(() => {
    const cancelDeduplicateReport = async () => {
      try {
        const resBatchDetail: any = await api.callApi("getBatchDetail", {
          params: {
            projectId,
            batchId,
          },
        });

        if (resBatchDetail?.success) {
          setBatchDetail(resBatchDetail?.data);

          if (resBatchDetail?.data?.eventDuplicationProcessId) {
            const res: any = await api.callApi("cancelDeduplicateReport", {
              params: {
                projectId,
                batchId,
              },
              body: {
                eventDuplicationProccessId:
                  resBatchDetail?.data?.eventDuplicationProcessId,
              },
            });

            if (res?.success) {
              setCancelingReport(true);
            } else {
              Message.error({
                content: res?.message,
              });
            }
          } else {
            Message.error({ content: "Please refetch data and try again!" });
          }
        }
      } catch (error: any) {
        console.log("error: ", error?.message);
      }
    };

    ModalConfirmBig.warning({
      title: "Cancel deduplication progress?",
      content:
        "If continue, please be informed that the ongoing process will be cancelled immediately",
      onOk: cancelDeduplicateReport,
      okText: "Continue",
    });
  }, [batchId, projectId]);

  const renderBtnAction = useCallback(() => {
    if (!isDeduplicateOn) return;
    if (isCancelingReport) {
      return (
        <button
          className={classNames("deduplicateBtn cancelingBtn")}
          onMouseLeave={() => {
            setShowBtnCancel(false);
          }}
        >
          Cancelling
        </button>
      );
    }
    if (isShowBtnCancel)
      return (
        <button
          className={classNames("deduplicateBtn deduplicateBtnNegative")}
          onClick={() => handleCancelDeduplicateReport()}
          onMouseLeave={() => {
            setShowBtnCancel(false);
          }}
        >
          Cancel
        </button>
      );

    return (
      <button
        className={classNames("deduplicateBtn deduplicateBtnSecondary", {
          deduplicateBtnLoading: isCreatingReport,
          viewReportBtn:
            batchDetail?.deduplicationStatus === "Incompleted" ||
            batchDetail?.deduplicationStatus === "Complete" ||
            batchDetail?.deduplicationStatus === "ReadyToNextStep" ||
            batchDetail?.deduplicationStatus === "Done",
        })}
        onClick={() =>
          batchDetail?.deduplicationStatus === "Incompleted" ||
          batchDetail?.deduplicationStatus === "Complete" ||
          batchDetail?.deduplicationStatus === "ReadyToNextStep" ||
          batchDetail?.deduplicationStatus === "Done"
            ? setOpenDeduplicateModal(true)
            : handleCreateDeduplicateReport()
        }
        onMouseOver={() => {
          if (isCreatingReport) {
            setShowBtnCancel(true);
          }
        }}
      >
        {isCreatingReport
          ? "Deduplicating"
          : batchDetail?.deduplicationStatus === "Incompleted" ||
              batchDetail?.deduplicationStatus === "Complete" ||
              batchDetail?.deduplicationStatus === "ReadyToNextStep" ||
              batchDetail?.deduplicationStatus === "Done"
            ? "Deduplication Report"
            : "Deduplicate"}
      </button>
    );
  }, [
    batchDetail?.deduplicationStatus,
    handleCancelDeduplicateReport,
    handleCreateDeduplicateReport,
    isCreatingReport,
    isShowBtnCancel,
    isDeduplicateOn,
    isCancelingReport,
  ]);

  return (
    <div className="h-full w-full flex p-[10px]">
      <div className="h-full w-full flex gap-[8px] px-[8px] pt-[12px] pb-[4px] flex-col items-end rounded-[10px] bg-white shadow-Shadows/Gray-Blue-30/5%/5b">
        <div className="w-full flex justify-between items-center">
          <div className="flex flex-col items-start gap-[2px]">
            <div className="flex gap-[6px] items-center text-[#ADB8CC] text-[18px] font-medium	leading-[21px]">
              <div
                className="flex cursor-pointer"
                onClick={() => {
                  history.push("temp-storage");
                }}
              >
                Data Batch
              </div>
              /
              {batchDetail?.batchName && (
                <InputLabel
                  classNameLabel="batch-detail-title"
                  value={batchName}
                  onChange={(e) => setBatchName(e.target.value)}
                  onEnter={() => {
                    const batchNameTrim = batchName
                      ? batchName?.trim()
                      : batchName;

                    if (batchDetail?.batchName === batchNameTrim) {
                      return;
                    } else if (
                      batchNameTrim === "" ||
                      batchName === undefined
                    ) {
                      setBatchName(batchDetail?.batchName);
                      Message.error({
                        content: "Batch name cannot be blank",
                      });
                    } else {
                      handleUpdateBatchName?.(batchId, batchName, () =>
                        setBatchName(batchDetail?.batchName)
                      );
                    }
                  }}
                />
              )}
            </div>
            <div className="text-[#7D8FB3] text-[12px] font-medium	leading-[21px]">
              Prepare your data prior to official import.
            </div>
          </div>
          {!!isShowFilter && (
            <div className="flex py-[2px] justify-end items-center gap-[12px]">
              <Input
                placeholder="Search"
                className={classNames(
                  "bg-[#F3F6F9] min-w-[215px] h-[40px] rounded-[10px] p-[8px] border-[#E4E4E4] batch-search",
                  {
                    "border-[#3361FF]": isFocusSearch || valueSearchName,
                  }
                )}
                inputClassName={classNames(
                  "h-full w-full text-[#346] text-[12px] leading-[21px] bg-[#F3F6F9]",
                  {
                    "text-[#3361FF]": !isFocusSearch,
                  }
                )}
                iconLeft={
                  <IconSearch
                    size={24}
                    color={
                      isFocusSearch || valueSearchName ? "#3361FF" : "#C3CAD9"
                    }
                  />
                }
                iconLeftClassName="flex m-0 mr-[4px]"
                size="sm"
                value={valueSearchName}
                onChange={onChangeSearchValue}
                maxLength={100}
                autoFocus={isFocusSearch}
                onFocus={() => setFocusSearch(true)}
                onBlur={() => setFocusSearch(false)}
              />
              <BatchDetailFilter
                open={openFilter}
                setOpen={setOpenFilter}
                valueFilterStatus={batchParams.filterSelected}
                onClickFilterStatus={(statusArray, status) => {
                  const isOptionSelected = batchParams?.filterSelected?.some(
                    (item: any) =>
                      item.workflowStepId.some((stepId: any) =>
                        status.workflowStepId.includes(stepId)
                      ) && item.workflowStepStatus === status.workflowStepStatus
                  );

                  if (isOptionSelected) {
                    const filterSelected = [
                      ...batchParams?.filterSelected?.filter(
                        (item: any) =>
                          !(
                            item.workflowStepId.some((stepId: any) =>
                              status.workflowStepId.includes(stepId)
                            ) &&
                            item.workflowStepStatus ===
                              status.workflowStepStatus
                          )
                      ),
                    ];

                    setBatchParams({
                      ...batchParams,
                      currentPage: 1,
                      filterSelected,
                    });
                  } else {
                    const filterSelected: any = [
                      ...batchParams?.filterSelected,
                      status,
                    ];

                    setBatchParams({
                      ...batchParams,
                      currentPage: 1,
                      filterSelected,
                    });
                  }
                }}
              />
              {renderBtnAction()}
              {!isBatchDone && (
                <Button
                  theme="Primary"
                  size="sm"
                  onClick={() => handleOpenImportModalFinal()}
                  className="pl-2.5 pr-[15px]"
                  iconLeft={<IconPlus size={28} color="#fff" />}
                >
                  File
                </Button>
              )}
              <Segmented
                className="my-custom-segmented bg-[#E4E7F0] rounded-[5px] h-[40px]"
                value={view}
                onChange={(e) => {
                  setView(e as any);
                }}
                options={[
                  {
                    value: EnumView.Table,
                    icon: <IconFormatListBulleted />,
                  },
                  {
                    value: EnumView.Grid,
                    icon: <IconViewModule />,
                  },
                ]}
              />
            </div>
          )}
        </div>

        {renderContent}
      </div>

      {isOpenDataProcessorModal && (
        <DataProcessor
          open={isOpenDataProcessorModal}
          selectedFileId={selectedFileId}
          batchName={batchDetail?.batchName}
          batchId={batchId}
          projectId={projectId}
          closeModal={() => {
            setOpenDataProcessorModal(false);
            fetchDataBatch();
            fetchBatchDetail();
          }}
          isManualTransfer={isManualTransfer}
          workflow={workflow}
          isReadyToNextStep={isReadyToNextStep}
          isBatchDone={isBatchDone}
        />
      )}

      {isOpenDeduplicateModal && (
        <Deduplicate
          ref={ref}
          batchName={batchDetail?.batchName}
          batchId={batchId}
          projectId={projectId}
          isOpen={isOpenDeduplicateModal}
          closeModal={() => {
            setOpenDeduplicateModal(false);
            fetchDataBatch();
            fetchBatchDetail();
          }}
          handleCancelDeduplicateReport={handleCancelDeduplicateReport}
          workflow={workflow}
          isDeduplicationReportCompleted={
            batchDetail?.deduplicationStatus === "Complete" ||
            batchDetail?.deduplicationStatus === "ReadyToNextStep" ||
            batchDetail?.deduplicationStatus === "Done"
          }
          fetchBatchDetail={fetchBatchDetail}
        />
      )}

      {!!batchDetailListSelected?.length && !isBatchDone ? (
        <BatchDetailMenuBar
          listSelected={batchDetailListSelected}
          onDeleteFiles={handleDeleteFiles}
          handleStartProcessing={handleStartProcessing}
          handleApprove={handleApprove}
          handleTransfer={handleTransfer}
          handleReject={handleReject}
          isManualTransfer={isManualTransfer}
        />
      ) : null}
    </div>
  );
};

export default BatchDetail;
