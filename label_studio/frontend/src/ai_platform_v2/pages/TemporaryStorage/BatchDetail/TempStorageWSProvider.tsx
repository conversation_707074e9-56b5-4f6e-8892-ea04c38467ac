import React, {
  createContext,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { w3cwebsocket } from "websocket";
import { useCurrentUser } from "@/providers/CurrentUser";

const baseURL = new URL(APP_SETTINGS.hostname || location.origin);
const ws_scheme = window.location.protocol === "https:" ? "wss" : "ws";

export const TempStorageWebSocketContext = createContext();

const maxReconnectTime = 2;

export const TempStorageWebSocketProvider = ({ children }: any) => {
  const [resetClient, setResetClient] = useState(0);

  const tempStorageWS = useMemo(
    () => new w3cwebsocket(`${ws_scheme}://${baseURL.host}/ws/temp_storage/`),
    [resetClient]
  );

  tempStorageWS.onclose = () => {
    if (resetClient < maxReconnectTime) {
      setResetClient(resetClient + 1);
    }
  };

  useEffect(() => {
    return () => tempStorageWS.close();
  }, [tempStorageWS]);

  return (
    <TempStorageWebSocketContext.Provider
      value={{
        tempStorageWS,
      }}
    >
      {children}
    </TempStorageWebSocketContext.Provider>
  );
};

export const useTempStorageWS = () => {
  return useContext(TempStorageWebSocketContext);
};
