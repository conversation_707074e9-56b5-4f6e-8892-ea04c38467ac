.bath-search {
  input::placeholder {
    color: var(--Gray-<PERSON>-Grey-Blue-85, #c3cad9);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
  }
}

.taureau-action-transfer {
  cursor: pointer;
  display: flex;
  width: 130px;
  padding: 8px;
  justify-content: center;
  align-items: center;
  gap: 8px;

  border-radius: 8px;
  background: var(--blue-blue-10, rgba(51, 97, 255, 0.1));

  color: var(--Blue-Blue, #3361ff);
  text-align: center;

  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 18px;

  transition: background 0.3s;

  &-disabled {
    opacity: 0.5;
  }

  &:hover {
    background: var(--blue-blue-20, rgba(51, 97, 255, 0.2));
  }
}

.taureau-action-mark-done {
  cursor: pointer;
  display: flex;
  width: 130px;
  padding: 8px;
  justify-content: center;
  align-items: center;
  gap: 5px;

  border-radius: 8px;
  background: #f0faf7;

  color: var(--<PERSON><PERSON>-<PERSON>, #66cca7);
  text-align: center;

  font-size: 12px;
  font-style: normal;
  font-weight: 600;
  line-height: 18px;

  transition: background 0.3s;

  &-disabled {
    opacity: 0.5;
  }

  &:hover {
    background: rgba(240, 250, 247, 0.2);
  }
}

.taureau-col-sort {
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;

  .content {
    color: var(--Gray-Blue-Grey-Blue-70, #7d8fb3);
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    letter-spacing: 0.36px;
  }

  .sort-icon path {
    fill: #c3cad9;
    transition: fill 0.1s;
  }
}

.taureau-col-sort-asc,
.taureau-col-sort-desc {
  .sort-icon path {
    fill: #3361ff;
  }
}

.taureau-col-sort-asc .sort-icon {
  transform: scaleY(-1);
}

.taureau-col-sort-disabled {
  cursor: not-allowed;
}

.taureau-action-transfer-disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: var(--blue-blue-10, rgba(51, 97, 255, 0.1)) !important;
}

.taureau-action-mark-done-disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: rgba(240, 250, 247, 0.9) !important;
}

.taureau-temp-storage-table {
  .ant-table-tbody {
    .ant-table-row-selected .delete-action {
      display: none !important;
    }
    .ant-table-row {
      position: relative;

      .delete-action,
      .delete-action-btn {
        position: absolute;
        display: none;
      }

      &:hover .delete-action {
        right: 0;
        display: flex;
        height: 100%;
        justify-content: center;
        align-items: center;
        padding: 0px !important;

        .delete-action-btn {
          right: 0;
          cursor: pointer;
          display: flex;
          padding: 3px;
        }
      }
    }
  }
}

.taureau-action-mark-done-loading {
  circle {
    transform-box: fill-box;
    transform-origin: 50% 50%;
    animation-duration: 1s;
    animation-name: rotate;
    animation-iteration-count: infinite;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
