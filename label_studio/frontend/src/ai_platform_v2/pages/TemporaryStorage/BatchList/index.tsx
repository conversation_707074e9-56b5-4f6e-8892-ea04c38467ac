import IconPlus from "@/ai_platform_v2/assets/Icons/IconPlus";
import IconSearch from "@/ai_platform_v2/assets/Icons/IconSearch";
import Message from "@/ai_platform_v2/component/Message/Message";
import { Pagination } from "@/ai_platform_v2/component/Pagination/Pagination";
import { useBatchImportData } from "@/ai_platform_v2/providers/BatchImportDataProvider";
import { useImportData } from "@/ai_platform_v2/providers/ImportDataProvider";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { getCurrentModule, getCurrentProject } from "@/pages/DataSet/Const";
import { useAPI } from "@/providers/ApiProvider";
import { useCurrentUser } from "@/providers/CurrentUser";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { useProject } from "@/providers/ProjectProvider";
import { Input, ModalConfirmBig } from "@taureau/ui";
import { convertUTCToLocalTime, DATE_TIME_FORMAT } from "@v2/utils/dateTime";
import classNames from "classnames";
import { debounce } from "lodash";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useHistory } from "react-router";
import { fileStatusOptions } from "../../BatchImportData/BatchImportDataReducer";
import IconExclude from "../assets/IconExclude";
import IconLoading from "../assets/IconLoading";
import IconVector from "../assets/IconVector";
import {
  BatchNameColumn,
  CreatedByColumn,
  UploadingProgressColumn,
} from "../component/BatchColumn/BatchColumn";
import { BatchEmpty } from "../component/BatchEmpty/BatchEmpty";
import BatchMenuBar from "../component/BatchMenuBar/BatchMenuBar";
import { BatchNotFound } from "../component/BatchNotFound/BatchNotFound";
import { Button } from "../component/Button/Button";
import SkeletonLoading from "../component/SkeletonLoading";
import { Table } from "../component/Table/Table";
import {
  DEFAULT_PAGE_SIZE,
  INIT_BATCH_PARAMS,
  INIT_TRANSFER_STATUS,
  isNumeric,
} from "../util/const";
import "./BatchList.scss";

const BatchList = () => {
  const history = useHistory();
  const moduleId = getCurrentModule();
  const projectId = getCurrentProject();
  const api = useAPI();

  const { user } = useCurrentUser();
  const { projectDetail, fetchProjectDetail } = useProject();

  const isAssignedStepPreCompleted = useMemo(() => {
    if (projectDetail?.preProccessConfig === undefined) return undefined;
    if (!projectDetail?.preProccessConfig) return false;

    const preProcessConfig = JSON.parse(projectDetail.preProccessConfig);

    const stepPreCompleted = preProcessConfig.node.find(
      (step: any) => step.type === "PreProcessingCompleted"
    );

    if (stepPreCompleted?.settings?.isManualConfirmation) {
      const stepPreCompletedMember = stepPreCompleted?.members;

      if (stepPreCompletedMember?.length) {
        return stepPreCompletedMember.some(
          (member: any) =>
            member === "all" || member === (user?.uuid ?? user?.id)
        );
      }
    }

    return false;
  }, [projectDetail.preProccessConfig, user]);

  const { hasPermissionAllScopeInProject } = useCheckPermission();

  const canDataProceed = hasPermissionAllScopeInProject(
    ABILITY_NEW.can_data_proceed,
    moduleId,
    projectId
  );

  const canViewDataset = hasPermissionAllScopeInProject(
    ABILITY_NEW.can_view_dataset,
    moduleId,
    projectId
  );

  const importData = useImportData();
  const batchImportData = useBatchImportData();

  const {
    batchImportState,
    openImport,
    handleOpenImportModal: handleOpenBatchImportModal,
    setFuncFetchDataSets,
    failedFiles,
  } = batchImportData;
  const { currentProject, batchData, files } = batchImportState;

  const { importState, handleOpenImportModal: handleOpenDataImportModal } =
    importData;

  const { files: filesData, preAttached } = importState;

  const [transferringBatchList, setTransferringBatchList] = useState<any>([]);
  const [batchListSelected, setBatchListSelected] = useState([]);
  const [batchList, setBatchList] = useState<any>();
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [loadingMarkDone, setLoadingMarkDone] = useState(false);

  const [batchParams, setBatchParams] = useState(INIT_BATCH_PARAMS);
  const [valueSearchName, setValueSearchName] = useState("");

  const [isFocusSearch, setFocusSearch] = useState(false);

  const [sortColumn, setSortColumn] = useState({
    column: "",
    sort: "",
  }); // asc | desc

  const handleOpenImportModal = useCallback(() => {
    if (
      !!filesData?.length ||
      preAttached.zipFile ||
      preAttached.formattedFile
    ) {
      handleOpenDataImportModal();
    } else {
      handleOpenBatchImportModal();
    }
  }, [
    filesData.length,
    JSON.stringify(preAttached),
    handleOpenDataImportModal,
    handleOpenBatchImportModal,
  ]);

  const disabledSortColumn = useMemo(
    () => !batchList?.items?.length,
    [batchList]
  );

  const handleOpenImportBatch = useCallback(() => {
    fetchProjectDetail();
    handleOpenImportModal();
  }, [fetchProjectDetail, handleOpenImportModal]);

  const handleSortColumn = useCallback(
    (column) => {
      if (disabledSortColumn) {
        return;
      }

      if (sortColumn.column === column) {
        if (sortColumn.sort === "desc") {
          setSortColumn({ column, sort: "asc" });
        } else if (sortColumn.sort === "asc") {
          setSortColumn({ column, sort: "" });
        } else {
          setSortColumn({ column, sort: "desc" });
        }
      } else {
        setSortColumn({ column, sort: "desc" });
      }
    },
    [disabledSortColumn, sortColumn]
  );

  const batchListFinal = useMemo(() => {
    const sortBatchList = batchList?.items ? [...batchList?.items] : [];

    if (sortColumn.sort === "asc") {
      sortBatchList?.sort((a: any, b: any) => {
        const fullNameA = isNumeric(a?.[sortColumn.column])
          ? a?.[sortColumn.column]
          : a?.[sortColumn.column]?.toLowerCase() ?? " ";
        const fullNameB = isNumeric(b?.[sortColumn.column])
          ? b?.[sortColumn.column]
          : b?.[sortColumn.column]?.toLowerCase() ?? " ";

        if (fullNameA < fullNameB) {
          return -1;
        }
        if (fullNameA > fullNameB) {
          return 1;
        }
        return 0;
      });
    } else if (sortColumn.sort === "desc") {
      sortBatchList?.sort((a: any, b: any) => {
        const fullNameA = isNumeric(a?.[sortColumn.column])
          ? a?.[sortColumn.column]
          : a?.[sortColumn.column]?.toLowerCase() ?? " ";
        const fullNameB = isNumeric(b?.[sortColumn.column])
          ? b?.[sortColumn.column]
          : b?.[sortColumn.column]?.toLowerCase() ?? " ";

        if (fullNameA < fullNameB) {
          return 1;
        }
        if (fullNameA > fullNameB) {
          return -1;
        }
        return 0;
      });
    }
    return sortBatchList;
  }, [sortColumn, batchList?.items]);

  const checkTransferCompleted = useCallback(
    (items: any) => {
      items?.forEach((item: any) => {
        const transferringBatch = transferringBatchList?.find(
          (transferringBatch: any) => item.id === transferringBatch.id
        );

        if (
          transferringBatch?.id &&
          item?.transferStatus === INIT_TRANSFER_STATUS.completed
        ) {
          Message.success({
            // content: `${transferringBatch.readyToTransfer} ${transferringBatch.readyToTransfer > 1 ? "files" : "file"} ${
            content: `${transferringBatch.readyToTransfer} ${
              transferringBatch.readyToTransfer > 1 ? "files" : "file"
            } in data batch ${item.batchName} transferred`,
          });
          // Remove transferred batch
          setTransferringBatchList(
            transferringBatchList.filter(
              (itemTransfer: any) => item.id !== itemTransfer.id
            )
          );
        } else if (
          !transferringBatch?.id &&
          item?.transferStatus === INIT_TRANSFER_STATUS.inProgress
        ) {
          setTransferringBatchList([
            ...transferringBatchList,
            { id: item?.id, readyToTransfer: item?.readyToTransfer },
          ]);
        }
      });
    },
    [transferringBatchList]
  );

  const fetchDataBatch = useCallback(
    async (useLoading = true) => {
      const params: any = {
        pk: projectId,
        page: batchParams.currentPage,
        pageSize: batchParams.pageSize || DEFAULT_PAGE_SIZE,
        sortBy: "updatedAt",
        orderBy: "desc",
      };

      if (batchParams.searchValue) {
        params.batchName = batchParams.searchValue;
      }

      useLoading && setLoading(true);

      const result: any = await api.callApi("dataBatch", {
        params,
      });

      useLoading && setLoading(false);

      if (result?.success) {
        checkTransferCompleted(result.items);
        setBatchList({
          ...result,
          items: result.items.map((item: any) => ({ ...item, key: item.id })),
        });
        setTotal(result.total);
      }
    },
    [projectId, batchParams, checkTransferCompleted]
  );

  const handleTransferData = useCallback(
    (tempId: string, readyToTransfer: number) => {
      ModalConfirmBig.confirm({
        title: "Transfer data?",
        content:
          "If continue, all qualified data in this batch will be officially imported into main storage",
        okText: "Continue",
        cancelText: "Cancel",
        onOk: async () => {
          const result: any = await api.callApi("transferData", {
            params: {
              pk: projectId,
              tempId,
            },
          });

          if (result?.success) {
            setTransferringBatchList([
              ...transferringBatchList,
              { id: tempId, readyToTransfer },
            ]);
            fetchDataBatch(false);
          }
        },
      });
    },
    [transferringBatchList, fetchDataBatch, projectId]
  );

  const handleMarkDoneBatch = useCallback((batch) => {
    ModalConfirmBig.confirm({
      title: `Mark done data batch ${batch?.batchName}`,
      content:
        "If you continue, all actions that make changes on file will be prohibited.",
      okText: "Continue",
      cancelText: "Cancel",
      onOk: async () => {
        setLoadingMarkDone(true);

        const result: any = await api.callApi("updateBatch", {
          params: {
            projectId,
            batchId: batch?.id,
          },
          body: {
            batchFileStatus: "Done",
          },
        });

        setLoadingMarkDone(false);

        if (result?.success) {
          Message.success({ content: "Data batch marked as done" });
          fetchDataBatch();
        } else {
          Message.error({ content: result?.message });
        }
      },
    });
  }, []);

  const handleDeleteDataBatches = useCallback(
    (dataBatchDeleted = batchListSelected) => {
      const dataBatchDeletedLength = dataBatchDeleted?.length;

      if (!dataBatchDeletedLength) {
        return;
      }

      ModalConfirmBig.warning({
        title:
          dataBatchDeletedLength > 1
            ? `Delete ${dataBatchDeletedLength} selected data batches?`
            : `Delete data batch ${
                dataBatchDeleted[0]?.batchName ??
                batchList?.items?.find(
                  (batch: any) => batch.id === dataBatchDeleted[0]
                )?.batchName
              }?`,
        content: `If continue, all data in ${
          dataBatchDeletedLength > 1 ? "these batches" : "this batch"
        } will be permanently removed.`,
        okText: "Delete",
        cancelText: "Cancel",
        onOk: async () => {
          let result: any = {};

          if (dataBatchDeletedLength > 1) {
            result = await api.callApi("deleteDataBatches", {
              params: {
                pk: projectId,
              },
              body: {
                ids: dataBatchDeleted,
              },
            });
          } else {
            result = await api.callApi("deleteDataBatch", {
              params: {
                pk: projectId,
                tempId: dataBatchDeleted[0]?.id ?? dataBatchDeleted[0],
              },
            });
          }

          if (result?.success) {
            Message.success({
              content: "Data batch deleted",
            });
            setBatchListSelected(
              batchListSelected.filter(
                (batchId: any) =>
                  !(
                    dataBatchDeleted?.includes(batchId) ||
                    batchId === dataBatchDeleted[0]
                  )
              )
            );

            const currentPageItemsLength = batchList?.items?.length;

            if (
              dataBatchDeletedLength === currentPageItemsLength &&
              batchParams.currentPage > 1
            ) {
              setBatchParams({
                ...batchParams,
                currentPage: batchParams.currentPage - 1,
              });
            } else {
              fetchDataBatch(false);
            }
          }
        },
      });
    },
    [projectId, batchParams, batchList, batchListSelected, fetchDataBatch]
  );

  const finalColumns = [
    {
      title: (
        <div
          className={classNames(
            `taureau-col-sort ${
              sortColumn.column === "batchName"
                ? `taureau-col-sort-${sortColumn.sort}`
                : ""
            } pl-[16px]`,
            {
              "taureau-col-sort-disabled": disabledSortColumn,
            }
          )}
          onClick={() => handleSortColumn("batchName")}
        >
          <div className="content" style={{ color: "#346" }}>
            BATCHES
          </div>
          <IconVector className="sort-icon" />
        </div>
      ),
      key: "id",
      dataIndex: "id",
      render: (_, record) => (
        <BatchNameColumn
          imgUrl={`/api/projects/${projectId}/temp_storage_batch_files/${record?.batchAvatarId}/image?type=Original`}
          batchName={record?.batchName}
          numOfFiles={record?.totalUploadSuccessfully}
        />
      ),
      width: 210,
    },
    {
      title: <div className="w-full flex items-center px-[16px]">STATUS</div>,
      dataIndex: "batchFileStatus",
      key: "batchFileStatus",
      width: 180,
      render: (_, { id, batchFileStatus }) => {
        if (id === batchData?.id && files?.length > 0) {
          const uploadStatus = () => {
            if (
              files?.find(
                (file: any) =>
                  file.status === fileStatusOptions.uploading ||
                  file.status === fileStatusOptions.paused
              )
            ) {
              return "Uploading";
            } else if (
              files?.filter(
                (file: any) => file.status === fileStatusOptions.failed
              )?.length === files?.length &&
              files?.length > 0
            ) {
              return "Upload failed";
            }

            return "Upload completed";
          };

          return (
            <UploadingProgressColumn
              status={uploadStatus()}
              uploadingFiles={
                files?.filter(
                  (file: any) =>
                    file.status === fileStatusOptions.completed ||
                    file.status === fileStatusOptions.failed
                )?.length
              }
              completedFiles={
                files?.filter(
                  (file: any) => file.status === fileStatusOptions.completed
                )?.length
              }
              failedFiles={failedFiles?.length}
              totalFiles={files?.length}
              files={failedFiles}
              currentProject={currentProject}
            />
          );
        }
        return (
          <UploadingProgressColumn
            status={batchFileStatus === "Done" ? "Done" : "Pre-processing"}
          />
        );
      },
    },
    {
      title: (
        <div
          className={classNames(
            `taureau-col-sort ${
              sortColumn.column === "readyToTransfer"
                ? `taureau-col-sort-${sortColumn.sort}`
                : ""
            } justify-center`,
            {
              "taureau-col-sort-disabled": disabledSortColumn,
            }
          )}
          onClick={() => handleSortColumn("readyToTransfer")}
        >
          <div className="content">READY TO TRANSFERRED</div>
          <IconVector className="sort-icon" />
        </div>
      ),
      key: "readyToTransfer",
      width: 200,
      render: (_, { readyToTransfer }) => readyToTransfer,
      align: "center",
    },
    {
      title: (
        <div
          className={classNames(
            `taureau-col-sort ${
              sortColumn.column === "totalTransfered"
                ? `taureau-col-sort-${sortColumn.sort}`
                : ""
            } justify-center`,
            {
              "taureau-col-sort-disabled": disabledSortColumn,
            }
          )}
          onClick={() => handleSortColumn("totalTransfered")}
        >
          <div className="content">TRANSFERRED</div>
          <IconVector className="sort-icon" />
        </div>
      ),
      key: "totalTransfered",
      width: 140,
      render: (_, { totalTransfered }) => totalTransfered,
      align: "center",
    },
    {
      title: (
        <div
          className={classNames(
            `taureau-col-sort ${
              sortColumn.column === "createdAt"
                ? `taureau-col-sort-${sortColumn.sort}`
                : ""
            } justify-center`,
            {
              "taureau-col-sort-disabled": disabledSortColumn,
            }
          )}
          onClick={() => handleSortColumn("createdAt")}
        >
          <div className="content">CREATED DATE</div>
          <IconVector className="sort-icon" />
        </div>
      ),
      key: "createdAt",
      width: 150,
      render: (_, { createdAt }) =>
        createdAt
          ? convertUTCToLocalTime(createdAt, DATE_TIME_FORMAT.MMMDDYYYY_HHMM)
          : createdAt,
      align: "center",
    },
    {
      title: <div className="flex pl-[16px]">CREATED BY</div>,
      key: "createdBy",
      width: 160,
      render: (record) => (
        <CreatedByColumn
          // className="justify-start"
          user={{ username: record?.fullName, avatar: record?.avatar }}
        />
      ),
    },
    {
      title: "ACTION",
      key: "action",
      width: 150,
      render: (_, record) => {
        const batchFileStatus = record?.batchFileStatus;
        const isDone = batchFileStatus === "Done";

        if (isDone) {
          return (
            <div className="flex w-full justify-center">
              <div className="taureau-action-mark-done taureau-action-mark-done-disabled">
                Mark done
              </div>
            </div>
          );
        }

        // Validate "Mark Done"
        // show mark done if all files having batchFileStatus != Ready, or not show transfer
        const totalReject = record?.totalReject;
        const totalTransfered = record?.totalTransfered;
        const totalUploadSuccessfully = record?.totalUploadSuccessfully;

        const isMarkDone =
          totalReject + totalTransfered >= totalUploadSuccessfully;

        if (isMarkDone && totalUploadSuccessfully > 0) {
          return (
            <div className="flex w-full justify-center">
              <div
                className={classNames("taureau-action-mark-done", {
                  "cursor-wait": loadingMarkDone,
                })}
                onClick={(e) => {
                  e.stopPropagation();

                  handleMarkDoneBatch(record);
                }}
              >
                {loadingMarkDone && (
                  <IconLoading className="taureau-action-mark-done-loading" />
                )}
                Mark done
              </div>
            </div>
          );
        }

        const readyToTransfer = record?.readyToTransfer;
        const transferStatus = record?.transferStatus;
        const totalTransfering = record?.totalTransfering;

        const finalTransferContent = () => {
          if (!readyToTransfer) {
            if (
              !transferStatus ||
              transferStatus === INIT_TRANSFER_STATUS.readyTransfer
            ) {
              return "Transfer";
            } else if (transferStatus === INIT_TRANSFER_STATUS.completed) {
              return "Transferred";
            }
          }
          if (
            transferStatus === INIT_TRANSFER_STATUS.inProgress &&
            totalTransfering
          ) {
            return `Transferring ${totalTransfering}`;
          }

          return "Transfer";
        };

        return (
          <div className="flex w-full justify-center">
            {isAssignedStepPreCompleted && (
              <div
                className={classNames("taureau-action-transfer", {
                  "taureau-action-transfer-disabled": !readyToTransfer,
                  "cursor-wait":
                    transferStatus === INIT_TRANSFER_STATUS.inProgress &&
                    totalTransfering,
                })}
                onClick={(e) => {
                  e.stopPropagation();
                  if (
                    readyToTransfer &&
                    !totalTransfering &&
                    (!transferStatus ||
                      transferStatus === INIT_TRANSFER_STATUS.readyTransfer ||
                      transferStatus === INIT_TRANSFER_STATUS.completed)
                  ) {
                    handleTransferData(record?.id, readyToTransfer);
                  }
                }}
              >
                {finalTransferContent()}
              </div>
            )}
          </div>
        );
      },
      align: "center",
      // hidden: !isAssignedStepPreCompleted,
    },
    {
      title: "",
      width: "0px",
      render: (_, record) => (
        <div
          className="cursor-pointer delete-action-btn"
          onClick={(e) => {
            e.stopPropagation();
            handleDeleteDataBatches([record]);
          }}
        >
          <IconExclude />
        </div>
      ),
      className: "delete-action",
    },
  ].filter((col) => !col?.hidden);

  const debounceUpdateSearchValueStore = useCallback(
    debounce((value: string | any) => {
      setBatchParams({ ...batchParams, currentPage: 1, searchValue: value });
    }, 300),
    [batchParams]
  );

  const onChangeSearchValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value: any = e.target.value;

    setValueSearchName(value);

    debounceUpdateSearchValueStore(value.trim());
  };

  const onSelectChange = useCallback((newSelectedRowKeys) => {
    setBatchListSelected(newSelectedRowKeys);
  }, []);

  useEffect(() => {
    if (canDataProceed && canViewDataset && projectId) {
      fetchDataBatch();
    }
  }, [
    canDataProceed,
    canViewDataset,
    projectId,
    batchParams.currentPage,
    batchParams.pageSize,
    batchParams.searchValue,
  ]);

  useEffect(() => {
    if (openImport) {
      setFuncFetchDataSets(() => fetchDataBatch);
    }
  }, [openImport]);

  const renderContent = useMemo(() => {
    if (!loading) {
      if (!batchListFinal?.length && !batchParams.searchValue) {
        return <BatchEmpty onClick={handleOpenImportModal} />;
      } else {
        return (
          <div className="w-full h-full flex flex-col justify-between">
            <Table
              locale={{ emptyText: <BatchNotFound /> }}
              style={{ width: "100%" }}
              columns={finalColumns}
              dataSource={batchListFinal}
              rowSelection={{
                selectedRowKeys: batchListSelected,
                columnWidth: 50,
                onChange: onSelectChange,
              }}
              scroll={batchList?.items?.length ? undefined : { y: "100%" }}
              onRow={(record, index) => {
                return {
                  onClick: (event) => {
                    event.preventDefault();
                    history.push(`temp-storage?batchId=${record.id}`);
                  },
                };
              }}
            />

            {!!batchList?.items?.length && (
              <div className="w-full flex justify-end">
                <Pagination
                  size="large"
                  page={batchParams.currentPage}
                  pageSize={batchParams.pageSize}
                  totalItems={total}
                  totalPages={Math.ceil(total / batchParams.pageSize)}
                  pageSizeOptions={[5, 10, 30, 50, 100]}
                  onChange={(page, pageSize) => {
                    setBatchParams({
                      ...batchParams,
                      currentPage: page,
                      pageSize,
                    });
                  }}
                />
              </div>
            )}
          </div>
        );
      }
    }

    return <SkeletonLoading />;
  }, [
    loading,
    finalColumns,
    batchParams,
    batchListSelected,
    total,
    batchList,
    batchListFinal,
  ]);

  const isShowFilter = useMemo(
    () => batchList?.items?.length || batchParams.searchValue,
    [batchList, batchParams]
  );

  useEffect(() => {
    let timer: any = null;

    if (
      batchList?.items?.some(
        (item: any) =>
          item.transferStatus === INIT_TRANSFER_STATUS.inProgress &&
          item.totalTransfering
      )
    ) {
      timer = setTimeout(() => fetchDataBatch(false), 5000);
    }

    return () => {
      clearTimeout(timer);
    };
  }, [batchList]);

  return (
    <div className="h-full w-full flex p-[10px]">
      <div className="h-full w-full flex gap-[8px] px-[8px] pt-[12px] pb-[4px] flex-col items-end rounded-[10px] bg-white shadow-Shadows/Gray-Blue-30/5%/5b">
        <div className="w-full flex justify-between items-center">
          <div className="flex flex-col items-start gap-[2px]">
            <div className="text-[#212121] text-[18px] font-medium	leading-[21px]">
              Data Batch
            </div>
            <div className="text-[#7D8FB3] text-[12px] font-medium	leading-[21px]">
              Prepare your data prior to official import.
            </div>
          </div>
          {!!isShowFilter && (
            <div className="flex py-[2px] justify-end items-center gap-[12px]">
              <Input
                placeholder="Search"
                className={classNames(
                  "bg-[#F3F6F9] min-w-[215px] h-[40px] rounded-[10px] p-[8px] border-[#E4E4E4] batch-search",
                  {
                    "border-[#3361FF]": isFocusSearch || valueSearchName,
                  }
                )}
                inputClassName={classNames(
                  "h-full w-full text-[#346] text-[12px] leading-[21px] bg-[#F3F6F9]",
                  {
                    "text-[#3361FF]": !isFocusSearch,
                  }
                )}
                iconLeft={
                  <IconSearch
                    size={24}
                    color={
                      isFocusSearch || valueSearchName ? "#3361FF" : "#C3CAD9"
                    }
                  />
                }
                iconLeftClassName="flex m-0 mr-[4px]"
                size="sm"
                value={valueSearchName}
                onChange={onChangeSearchValue}
                maxLength={100}
                autoFocus={isFocusSearch}
                onFocus={() => setFocusSearch(true)}
                onBlur={() => setFocusSearch(false)}
              />

              <Button
                prefix={<IconPlus size={28} color="#fff" />}
                type="primary"
                onClick={() => handleOpenImportBatch()}
              >
                Batch
              </Button>
            </div>
          )}
        </div>

        {renderContent}
      </div>

      {batchListSelected?.length ? (
        <BatchMenuBar
          listSelected={batchListSelected}
          onDeleteDataBatches={handleDeleteDataBatches}
        />
      ) : null}
    </div>
  );
};

export default BatchList;
