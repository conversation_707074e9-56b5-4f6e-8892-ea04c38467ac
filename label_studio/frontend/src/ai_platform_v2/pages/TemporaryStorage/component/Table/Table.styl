.taureau-temp-storage-table
    height: 100%
    width: 100%
    display: flex
    flex-direction: column

    &__footer
        display: flex;
        padding: 10px 10px 10px 20px;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;

        border-radius: 0px 0px 10px 10px;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        background: #FFF;

        &-left, &-right
            display: flex;
            align-items: center;
            gap: 10px;

    &__selected
        display: flex;
        align-items: center;
        gap: 7px;

    &__icon-dot
      display: flex

    &__selected-content
      color: var(--blue-blue, #3361FF);
      
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;


    &__delete-action
      color: var(--red-red-dark-1, #CC1414);

    &__revert-action
      color: var(--blue-blue, #3361FF);

    &__delete-action, &__revert-action
        position relative
        cursor pointer
        
        font-size: 13px;
        font-style: normal;
        font-weight: 500;
        line-height: 20px;

        &:hover 
          .taureau-temp-storage-table__delete-action-hover, .taureau-temp-storage-table__revert-action-hover
            opacity: 1

    &__delete-action-hover
      background: #CC1414

    &__revert-action-hover
      background: var(--blue-blue, #3361FF);

    &__delete-action-hover, &__revert-action-hover
        opacity: 0
        display: flex
        position absolute
        width: 100%
        height: 2px
        transition: opacity .2s

    &__total
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 10px;

        &-label
            color: var(--gray-blue-grey-blue-40, #346);
            
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;

        &-content
            color: var(--gray-blue-grey-blue-60, #6B7A99);
            text-align: center;
            
            font-size: 13px;
            font-style: normal;
            font-weight: 500;
            line-height: 12px;

            display: flex;
            padding: 3px 10px;
            justify-content: center;
            align-items: center;
            gap: 10px;

            border-radius: 15px;
            border: 2px solid var(--gray-blue-grey-blue-97, #F5F6F7);
            background: var(--gray-blue-grey-blue-97, #F5F6F7);
            box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.03);

:global(.taureau-temp-storage-table)
  :global(.ant-table-header)
    padding: 8px 4px;
    border-radius: 10px;
    background: #F3F6F9

  :global(.ant-table-thead)
    :global(.ant-table-cell)
      padding: 0px !important
      color #7D8FB3
      line-height 30px
      font-weight 600
      font-size 12px
      background: #F3F6F9
      letter-spacing: 0.36px;
      border: none

      :global(.ant-table-column-title)
        transition: color .1s

    :global(.ant-table-selection-column)
      line-height: normal
    :global(.ant-table-cell)::before
      background-color rgba(0, 0, 0, 0) !important

    :global(.ant-table-column-sort)
      :global(.ant-table-column-title)
        color: rgb(51, 97, 255);

  :global(.ant-table-tbody) 
    :global(.ant-table-placeholder)
      :global(.ant-table-cell)
        border-bottom: none !important
    :global(.ant-table-row)
        height: 78px
        padding: 4px 8px 4px 4px;
        margin-bottom: 4px
        :global(.ant-table-cell)
            color: var(--gray-blue-grey-blue-40, #334466);
            font-feature-settings: 'clig' off, 'liga' off;
            /* Bold/Bold 12 */
            
            font-size: 12px !important;
            font-style: normal;
            font-weight: 500;
            line-height: 30px; /* 166.667% */

            border-bottom none

    :global(.ant-table-cell)
      padding-top 0px !important
      padding-bottom 0px !important

    :global(.ant-table-row:last-child)
      :global(.ant-table-cell)
        border-bottom none

    :global(.ant-table-row-selected)
        :global(.ant-table-cell)
            color: var(--blue-blue, #3361FF);
            background: var(--blue-blue-5, rgba(51, 97, 255, 0.05)) !important;
            
            :global(.col-content-text)
              color: var(--blue-blue, #3361FF) !important;

            :global(.taureau-temp-storage-record-email)
                color: var(--blue-blue, #3361FF) !important;

  :global(.ant-checkbox)

    :global(.ant-checkbox-inner)
      display: flex;
      width: 20px;
      height: 20px;
      padding: 8px;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 10px; 
      flex-shrink: 0;
      border-radius: 5px;
      border: 2px solid var(--gray-blue-grey-blue-96, #F2F3F5);
      /* Shadows/Gray Blue 30/3%/10b */
      box-shadow: 0px 2px 10px 0px rgba(38, 51, 77, 0.03);

  :global(.ant-checkbox-checked)
    &:after
      border: 1px solid var(--blue-blue, #3361FF);

    :global(.ant-checkbox-inner)
      background-color: var(--blue-blue, #3361FF);

  :global(.ant-checkbox-wrapper)
    &:hover, &:focus 
      :global(.ant-checkbox-inner)
        border: 2px solid var(--blue-blue, #3361FF);

  :global(.ant-checkbox-input:focus+.ant-checkbox-inner)
    border: 2px solid var(--blue-blue, #3361FF);

  
  :global(.ant-table-body) 
    margin-top 8px

    &::-webkit-scrollbar
      width 6px
      height 6px

    &::-webkit-scrollbar-thumb
      border-radius 10px
      background-color rgb(98 112 140 / 1)

