import { Block } from "@/ai_platform_v2/utils/bem";
import { Table as TableAnt, TableProps } from "antd";
import "./Table.styl";
import { TableRowSelection } from "antd/lib/table/interface";

interface Props extends TableProps<any> {
  pagination?: {
    total: number;
    current: number;
    pageSize: number;
  };
  rowSelection?: TableRowSelection<any>;
  scroll?: any;
}

export const Table = (props: Props) => {
  return (
    <Block name="taureau-temp-storage-table">
      <TableAnt
        {...props}
        className="taureau-temp-storage-table"
        scroll={props?.scroll || { y: "calc(100vh - 244px)" }}
        pagination={false}
      />
    </Block>
  );
};
