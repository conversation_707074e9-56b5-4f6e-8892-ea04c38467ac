import { useCallback, useMemo, useState } from "react";
import { UserpicV2 } from "@v2/component/Userpic/Userpic";
import "./BatchColumn.scss";
import IconDot from "../../assets/IconDot";
import IconInformation from "../../assets/IconInformation";
import { Tooltip, Typography } from "antd";
import { CSVLink } from "react-csv";
import { getYYYYMMDDNow } from "@/ai_platform_v2/utils/helpers";
import { csvHeaders } from "@/ai_platform_v2/utils/const";

const { Text, Paragraph } = Typography;

const DefaultImg = () => (
  <div
    className="flex w-[80px] h-[70px] px-[14px] py-[9px] justify-center items-center rounded-[10px] bg-[#F3F6F9]"
    style={{ border: "1px solid rgba(0, 0, 0, 0.05)" }}
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="52"
      height="52"
      viewBox="0 0 52 52"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M41.5964 10.3984H10.3964C8.66302 10.3984 6.92969 12.1318 6.92969 13.8651V38.1318C6.92969 40.0384 8.48969 41.5984 10.3964 41.5984H41.5964C43.3297 41.5984 45.063 39.8651 45.063 38.1318V13.8651C45.063 12.1318 43.3297 10.3984 41.5964 10.3984ZM14.955 33.2611L19.271 27.7144C19.6177 27.2811 20.2764 27.2638 20.623 27.6971L24.263 32.0824L29.6364 25.1664C29.983 24.7158 30.6764 24.7158 31.023 25.1838L37.107 33.2958C37.5404 33.8678 37.1244 34.6824 36.4137 34.6824H15.631C14.9204 34.6651 14.5044 33.8331 14.955 33.2611Z"
        fill="#C3CAD9"
      />
    </svg>
  </div>
);

interface BatchNameColumnProps {
  imgUrl?: any;
  batchName?: any;
  numOfFiles?: any;
}

export const BatchNameColumn = (props: BatchNameColumnProps) => {
  const { imgUrl, batchName, numOfFiles } = props;

  const [defaultVisible, setDefaultVisible] = useState(false);

  const numOfFilesFinal = useMemo(() => {
    if (!numOfFiles && numOfFiles !== 0) {
      return "";
    }

    return `${numOfFiles} ${numOfFiles > 1 ? "files" : "file"}`;
  }, [numOfFiles]);

  const onImageLoaded = useCallback(() => {
    setDefaultVisible(false);
  }, []);

  const onImageErrored = useCallback(() => {
    setDefaultVisible(true);
  }, []);

  return (
    <div className="w-full flex items-center gap-[15px]">
      {defaultVisible ? (
        <DefaultImg />
      ) : (
        <img
          className="w-[80px] h-[70px] rounded-[10px] object-cover"
          style={{ border: "1px solid rgba(0, 0, 0, 0.05)" }}
          src={imgUrl}
          onLoad={onImageLoaded}
          onError={onImageErrored}
        />
      )}
      <div
        className="flex flex-col justify-center items-start gap-[2px]"
        style={{ width: "calc(100% - 95px)" }}
      >
        <div className="w-full text-[#334466] text-[13px] font-semibold leading-[19.5px]">
          <Paragraph
            className="col-content-text"
            style={{
              width: "100%",
              color: "#334466",
              fontWeight: 500,
              lineHeight: "19.5px",
              margin: 0,
            }}
            ellipsis={{ rows: 3, tooltip: batchName }}
          >
            {batchName}
          </Paragraph>
        </div>
        <div className="text-[#7D8FB3] text-[12px] font-medium leading-[18px] col-content-text">
          {numOfFilesFinal}
        </div>
      </div>
    </div>
  );
};

const UploadingProgressOptions: any = {
  Uploading: { className: "uploading", content: "Uploading" },
  "Upload completed": { className: "completed", content: "Upload completed" },
  "Upload failed": { className: "failed", content: "Upload failed" },
  "Pre-processing": { className: "uploading", content: "Pre-processing" },
  Done: { className: "completed", content: "Done" },
};

interface UploadingProgressColumnProps {
  status?: string | any;
  uploadingFiles?: any;
  failedFiles?: any;
  completedFiles?: any;
  totalFiles?: any;
  files?: any;
  currentProject?: any;
}

export const UploadingProgressColumn = (
  props: UploadingProgressColumnProps
) => {
  const {
    currentProject,
    files,
    status,
    failedFiles,
    completedFiles,
    uploadingFiles,
    totalFiles,
  } = props;

  const statusFinal = useMemo(() => {
    return (
      UploadingProgressOptions?.[status] ?? UploadingProgressOptions.Completed
    );
  }, [status]);

  const uploadingProgress = useMemo(
    () => `${uploadingFiles}/${totalFiles}`,
    [uploadingFiles, totalFiles]
  );

  const completedProgress = useMemo(
    () => `${completedFiles}/${totalFiles}`,
    [completedFiles, totalFiles]
  );

  return (
    <div className="flex flex-col items-start gap-[2px]">
      <div
        className={`flex items-center gap-[8px] uploading-progress-${statusFinal.className}`}
      >
        <div className="flex">
          <IconDot />
        </div>
        <div className="flex text-[#454B54] text-[12px] font-medium leading-[18px] col-content-text">
          {statusFinal.content}
        </div>
      </div>
      {!!uploadingFiles && statusFinal.content === "Uploading" && (
        <div className="flex px-[18px] items-center">
          <div className="flex text-[#7D8FB3] text-[11px] font-medium leading-[12px]">
            {uploadingProgress}
          </div>
        </div>
      )}
      {!!failedFiles && uploadingFiles === totalFiles && (
        <div className="flex px-[18px] items-center">
          <div className="flex text-[#DE5462] text-[11px] font-medium leading-[12px]">
            {completedProgress}
          </div>
          <Tooltip
            overlayClassName="import-tooltip"
            title="Click to download list of failed files"
          >
            <div className="flex">
              <CSVLink
                className="flex"
                data={files}
                headers={csvHeaders}
                filename={`${getYYYYMMDDNow()}_Failed list_${currentProject?.id}.csv`}
                onClick={(event: any) => {
                  event.stopPropagation();
                }}
              >
                <IconInformation />
              </CSVLink>
            </div>
          </Tooltip>
        </div>
      )}
    </div>
  );
};

interface CreatedByColumnProps {
  user?: any;
  className?: string;
}

export const CreatedByColumn = (props: CreatedByColumnProps) => {
  const { user, className } = props;

  const fullName: string = useMemo(() => {
    if (user?.username) {
      return user?.username;
    }

    return `${user?.firstName ?? ""} ${user?.lastName ?? ""}`;
  }, [user]);

  return (
    <div className={`w-full flex items-center gap-[8px] ${className}`}>
      {fullName === "System" ? (
        <svg
          width="31"
          height="30"
          viewBox="0 0 31 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect x="1.39844" y="1" width="28" height="28" rx="14" fill="white" />
          <rect
            x="1.39844"
            y="1"
            width="28"
            height="28"
            rx="14"
            fill="#3361FF"
            fillOpacity="0.1"
          />
          <rect
            x="1.39844"
            y="1"
            width="28"
            height="28"
            rx="14"
            stroke="#F5F6F7"
            strokeWidth="2"
          />
          <path
            d="M15.6159 23.1063C14.6959 23.1063 13.9093 22.9929 13.2559 22.7662C12.6026 22.5396 12.0559 22.2462 11.6159 21.8862C11.1893 21.5262 10.8493 21.1462 10.5959 20.7462C10.3426 20.3329 10.1493 19.9462 10.0159 19.5862C9.89594 19.2262 9.81594 18.9329 9.77594 18.7062C9.73594 18.4796 9.71594 18.3662 9.71594 18.3662H12.4759C12.4759 18.3662 12.4893 18.4462 12.5159 18.6062C12.5559 18.7662 12.6359 18.9729 12.7559 19.2262C12.8759 19.4662 13.0493 19.7062 13.2759 19.9462C13.5159 20.1862 13.8293 20.3929 14.2159 20.5662C14.6159 20.7262 15.1093 20.8062 15.6959 20.8062C16.6026 20.8062 17.2893 20.6062 17.7559 20.2062C18.2359 19.7929 18.4759 19.2862 18.4759 18.6862C18.4759 18.1796 18.2959 17.7796 17.9359 17.4862C17.5759 17.1796 17.0426 16.9529 16.3359 16.8062L14.3759 16.3862C13.6159 16.2262 12.9026 15.9862 12.2359 15.6662C11.5826 15.3462 11.0559 14.9129 10.6559 14.3662C10.2559 13.8196 10.0559 13.1196 10.0559 12.2662C10.0559 11.3729 10.2759 10.5996 10.7159 9.94625C11.1693 9.29292 11.7893 8.79292 12.5759 8.44625C13.3626 8.08625 14.2826 7.90625 15.3359 7.90625C16.3359 7.90625 17.1626 8.05292 17.8159 8.34625C18.4693 8.63958 18.9893 8.99958 19.3759 9.42625C19.7759 9.85292 20.0693 10.2796 20.2559 10.7062C20.4426 11.1329 20.5626 11.4929 20.6159 11.7862C20.6826 12.0796 20.7159 12.2262 20.7159 12.2262H18.0559C18.0559 12.2262 18.0293 12.1262 17.9759 11.9262C17.9226 11.7129 17.8026 11.4729 17.6159 11.2062C17.4293 10.9396 17.1493 10.7062 16.7759 10.5062C16.4026 10.2929 15.9026 10.1862 15.2759 10.1862C14.3826 10.1862 13.7293 10.3862 13.3159 10.7862C12.9159 11.1862 12.7159 11.6196 12.7159 12.0862C12.7159 12.5663 12.9026 12.9396 13.2759 13.2062C13.6493 13.4729 14.1493 13.6796 14.7759 13.8262L16.8759 14.2662C17.6626 14.4396 18.3826 14.6996 19.0359 15.0462C19.6893 15.3929 20.2093 15.8529 20.5959 16.4262C20.9826 16.9862 21.1759 17.6862 21.1759 18.5262C21.1759 19.3796 20.9626 20.1529 20.5359 20.8462C20.1093 21.5396 19.4826 22.0929 18.6559 22.5062C17.8293 22.9062 16.8159 23.1063 15.6159 23.1063Z"
            fill="#3361FF"
          />
        </svg>
      ) : (
        <UserpicV2 user={user} />
      )}
      <div
        className="flex text-[#334466] text-[12px] font-medium leading-[18px]"
        // style={{ width: "calc(100% - 40px)" }}
      >
        <Paragraph
          className="col-content-text"
          style={{
            width: "100%",
            color: "#334466",
            fontWeight: 500,
            lineHeight: "18px",
            margin: 0,
          }}
          ellipsis={{ rows: 3, tooltip: fullName }}
        >
          {fullName}
        </Paragraph>
      </div>
    </div>
  );
};
