import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import { useMemo } from "react";
import "./Button.styl";

interface ButtonProps {
  children?: any;
  prefix?: any;
  className?: string;
  type?: string;
  onClick?: () => void;
}

const BUTTON_TYPE = {
  primary: "primary",
};

export const Button = (props: ButtonProps) => {
  const { children, prefix, className, type, onClick } = props;

  const classNameType = useMemo(() => {
    if (type === BUTTON_TYPE.primary) {
      return "taureau-temp-storage-button-primary";
    }

    return "";
  }, [type]);

  return (
    <Block
      name="taureau-temp-storage-button"
      className={classNameType}
      onClick={onClick}
    >
      <Elem name="prefix">{prefix}</Elem>
      <Elem name="content">{children}</Elem>
    </Block>
  );
};
