import { useCallback, useMemo, useState } from "react";
import "./BatchDetailColumn.scss";
import { Typography } from "antd";
import IconPreProcessingNew from "../../assets/IconPreProcessingNew";
import IconPreProcessingHuman from "../../assets/IconPreProcessingHuman";
import IconPreProcessingCompleted from "../../assets/IconPreProcessingCompleted";

const { Text, Paragraph } = Typography;

const DefaultImg = () => (
  <div
    className="flex w-[80px] h-[70px] px-[14px] py-[9px] justify-center items-center rounded-[10px] bg-[#F3F6F9]"
    style={{ border: "1px solid rgba(0, 0, 0, 0.05)" }}
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="52"
      height="52"
      viewBox="0 0 52 52"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M41.5964 10.3984H10.3964C8.66302 10.3984 6.92969 12.1318 6.92969 13.8651V38.1318C6.92969 40.0384 8.48969 41.5984 10.3964 41.5984H41.5964C43.3297 41.5984 45.063 39.8651 45.063 38.1318V13.8651C45.063 12.1318 43.3297 10.3984 41.5964 10.3984ZM14.955 33.2611L19.271 27.7144C19.6177 27.2811 20.2764 27.2638 20.623 27.6971L24.263 32.0824L29.6364 25.1664C29.983 24.7158 30.6764 24.7158 31.023 25.1838L37.107 33.2958C37.5404 33.8678 37.1244 34.6824 36.4137 34.6824H15.631C14.9204 34.6651 14.5044 33.8331 14.955 33.2611Z"
        fill="#C3CAD9"
      />
    </svg>
  </div>
);

interface FilesColumnProps {
  imgUrl?: any;
  fileName?: any;
}

export const FilesColumn = (props: FilesColumnProps) => {
  const { imgUrl, fileName } = props;

  const [defaultVisible, setDefaultVisible] = useState(false);

  const onImageLoaded = useCallback(() => {
    setDefaultVisible(false);
  }, []);

  const onImageErrored = useCallback(() => {
    setDefaultVisible(true);
  }, []);

  return (
    <div className="w-full flex items-center gap-[15px]">
      {defaultVisible ? (
        <DefaultImg />
      ) : (
        <img
          className="w-[80px] h-[70px] rounded-[10px] object-cover"
          style={{ border: "1px solid rgba(0, 0, 0, 0.05)" }}
          src={imgUrl}
          onLoad={onImageLoaded}
          onError={onImageErrored}
        />
      )}
      <div
        className="flex flex-col justify-center items-start gap-[2px]"
        style={{ width: "calc(100% - 95px)" }}
      >
        <div className="w-full text-[#346] text-[12px] font-semibold leading-[19.5px]">
          <Paragraph
            className="col-content-text"
            style={{
              width: "100%",
              color: "#346",
              fontWeight: 500,
              lineHeight: "18px",
              margin: 0,
            }}
            ellipsis={{ rows: 3, tooltip: fileName }}
          >
            {fileName}
          </Paragraph>
        </div>
      </div>
    </div>
  );
};

const preProgressStepOptions = {
  PreProcessingNew: { className: "new", icon: <IconPreProcessingNew /> },
  HumanApproval: {
    className: "humanApproval",
    icon: <IconPreProcessingHuman />,
  },
  PreProcessingCompleted: {
    className: "completed",
    icon: <IconPreProcessingCompleted />,
  },
};

interface PreProgressStepColumnProps {
  step?: string | any;
  status?: string | any;
  workflowState?: string | any;
}

export const PreProgressStepColumn = (props: PreProgressStepColumnProps) => {
  const { step, status, workflowState } = props;

  const statusFinal = useMemo(() => {
    return (
      preProgressStepOptions?.[workflowState] ??
      preProgressStepOptions.PreProcessingNew
    );
  }, [workflowState]);

  return (
    <div className="flex items-center gap-[4px]">
      <div className="flex">{statusFinal.icon}</div>
      <div className="flex text-[#334466] text-[12px] font-medium leading-[18px] col-content-text">
        {`${step ?? "New"} | ${status ?? "Ready"}`}
      </div>
    </div>
  );
};
