import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import { Empty, Tooltip } from "antd";
import { useLayoutEffect, useMemo, useRef, useState } from "react";
import "./Select.styl";

const IconFilterList = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.33333 14.5H10.6667C11.0333 14.5 11.3333 14.2 11.3333 13.8333C11.3333 13.4667 11.0333 13.1667 10.6667 13.1667H9.33333C8.96667 13.1667 8.66667 13.4667 8.66667 13.8333C8.66667 14.2 8.96667 14.5 9.33333 14.5ZM4 7.16667C4 7.53333 4.3 7.83333 4.66667 7.83333H15.3333C15.7 7.83333 16 7.53333 16 7.16667C16 6.8 15.7 6.5 15.3333 6.5H4.66667C4.3 6.5 4 6.8 4 7.16667ZM6.66667 11.1667H13.3333C13.7 11.1667 14 10.8667 14 10.5C14 10.1333 13.7 9.83333 13.3333 9.83333H6.66667C6.3 9.83333 6 10.1333 6 10.5C6 10.8667 6.3 11.1667 6.66667 11.1667Z"
      fill="#C3CAD9"
    />
  </svg>
);

interface SelectProbs {
  placeholder?: any;
  value?: any;
  disabled?: boolean;
  options?: any;
  style?: any;
  onChange?: (value: any) => void;
}

export const Select = (props: SelectProbs) => {
  const {
    placeholder = "Uploading progress",
    value,
    disabled,
    options = [],
    style,
    onChange,
  } = props;

  const refButton = useRef<any>(null);
  const [dropdownWidth, setDropdownWidth] = useState(0);

  const [open, setOpen] = useState(false);

  const finalContent = useMemo(() => {
    const valueLength = value?.length;

    if (valueLength) {
      return `${value[0]}${valueLength > 1 ? ` (+${valueLength - 1})` : ""}`;
    }

    return "";
  }, [value]);

  const handleSelectSingleItem = (valueSelect: any) => {
    if (value?.includes(valueSelect)) {
      onChange?.(value.filter((item: string) => item !== valueSelect));
    } else {
      onChange?.([...value, valueSelect]);
    }
  };

  useLayoutEffect(() => {
    setDropdownWidth(refButton.current.offsetWidth);
  }, []);

  return (
    <Tooltip
      open={open}
      trigger="click"
      color="white"
      placement="bottom"
      // maxHeight={300}
      overlayClassName="taureau-temp-storage-dropdown"
      overlayStyle={{ width: dropdownWidth }}
      title={
        <Block name="taureau-temp-storage-list-option">
          {options?.length > 0 ? (
            options.map((item: any) => (
              <Elem
                key={item?.value}
                name="item"
                className={value?.includes(item.value) ? "checked" : ""}
                onClick={(e: any) => {
                  e.stopPropagation();

                  handleSelectSingleItem(item.value);
                }}
              >
                <Elem name="title">
                  {item?.label}
                  {/* <Text
                      style={{
                        width: "100%",
                        color: "#7D8FB3",
                        textAlign: "start",
                      }}
                      ellipsis={{ ellipsis: true }}
                    >
                      {item?.label}
                    </Text> */}
                </Elem>
              </Elem>
            ))
          ) : (
            <div className="w-full flex justify-center">
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
            </div>
          )}
        </Block>
      }
      onOpenChange={(newOpen: any) => setOpen(newOpen)}
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
    >
      <Block
        name="taureau-temp-storage-filter"
        className={`${
          open || value?.length ? "taureau-temp-storage-filter-expand" : ""
        }`}
        ref={refButton}
      >
        <Elem name="icon">
          <IconFilterList />
        </Elem>
        {value?.length ? (
          <Elem name="content">{finalContent}</Elem>
        ) : (
          <Elem name="placeholder">{placeholder}</Elem>
        )}
      </Block>
    </Tooltip>
  );
};
