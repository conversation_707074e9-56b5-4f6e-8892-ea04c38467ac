.taureau-temp-storage-filter
    display: flex;
    min-width: 155px
    width: fit-content;
    height: 40px
    padding: 8px;
    align-items: center;
    gap: 4px;

    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.10);
    box-shadow: 0px 2px 10px 0px rgba(38, 51, 77, 0.05);

    transition: border .3s

    &__icon
        svg > path
            transition: fill .3s

    &__placeholder, &__content
        font-family: "Be Vietnam Pro";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;

    &__content
        color: var(--Blue-Blue, #3361FF);

    &__placeholder
        color: var(--Gray-Blue-Grey-Blue-85, #C3CAD9);

.taureau-temp-storage-filter-expand
    border: 1px solid var(--blue-blue, #3361FF);

    :global(.ls-taureau-temp-storage-filter__icon)
        svg > path
            fill #3361FF


.taureau-temp-storage-list-option
    display: flex;
    width: 100%;
    max-height: 156px;
    height: 100%;
    padding: 8px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    overflow: auto

    &__item
        cursor pointer
        width: 100%
        min-height: 36px

        display: flex;
        padding: 4px 8px;
        align-items: center;
        gap: 8px;
        border-radius: 5px;

        transition: background .3s, color .3s

        &:hover
            background: var(--gray-blue-grey-blue-97, #F5F6F7);

    &__title
        width 100%
        color: var(--Gray-Blue-Grey-Blue-40, #346);

        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 18px;

        transition: color .3s

    .checked
        background: var(--blue-blue-10, rgba(51, 97, 255, 0.10));

        :global(.ls-taureau-temp-storage-list-option__title)
            color: var(--blue-blue, #3361FF);


    &::-webkit-scrollbar {
        width: 7.5px;
        height: 7.5px;
    }
    
    &::-webkit-scrollbar-track {
        box-shadow: 0;
        /* border-radius: 10px; */
    }
    
    &::-webkit-scrollbar-thumb {
        background-color: rgb(98 112 140)
        border-radius: 10px;
    }



:global(.taureau-temp-storage-dropdown)
    padding 0px !important
    border-radius 10px !important
    border: 1px solid rgba(0, 0, 0, 0.10) !important;
    box-shadow: 0px 2px 10px 0px rgba(13, 17, 26, 0.15);

    :global(.ant-tooltip-arrow)
        display none

    :global(.ant-tooltip-inner)
        border-radius 10px !important
        padding 0px
        // width: fit-content
