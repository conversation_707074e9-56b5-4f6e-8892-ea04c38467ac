import { useCallback, useMemo, useRef, useState } from "react";
import IconCompleted from "../../assets/IconCompleted";
import IconHuman from "../../assets/IconHuman";
import IconPreProcessing from "../../assets/IconPreProcessing";
import IconPreProcessingVector from "../../assets/IconPreProcessingVector";
import IconPreProcessingHighlight from "../../assets/IconPreProcessingHighlight";
import IconHumanHighlight from "../../assets/IconHumanHighlight";
import IconCompletedHighlight from "../../assets/IconCompletedHighlight";
import { WorkflowNodeType } from "@/consts/workflow";
import "./WorkflowStep.scss";

interface WorkflowStepProps {
  [key: string]: any;
}
const WorkflowStep = (props: WorkflowStepProps) => {
  const { workflow, fileData } = props;
  const containerRef = useRef<any>(null);
  const [scrollInterval, setScrollInterval] = useState<any>(null);

  const miniWorkflow = useMemo(
    () =>
      workflow?.map((step: any) => {
        const node = {
          type: step?.type,
          current: false,
        };

        if (step?.type === WorkflowNodeType.PreProcessingNew) {
          if (!fileData?.workflowStepId) {
            node.current = true;
          } else if (
            fileData?.workflowState === WorkflowNodeType.PreProcessingNew
          ) {
            node.current = true;
          }
        } else {
          node.current = step.id === fileData?.workflowStepId;
        }

        return node;
      }),
    [JSON.stringify(fileData), workflow]
  );

  const getStepIcon = useCallback((item) => {
    if (item?.type === WorkflowNodeType.PreProcessingNew) {
      return item?.current ? (
        <IconPreProcessingHighlight />
      ) : (
        <IconPreProcessing />
      );
    }
    if (item?.type === WorkflowNodeType.HumanApproval) {
      return item?.current ? <IconHumanHighlight /> : <IconHuman />;
    }
    if (item?.type === WorkflowNodeType.PreProcessingCompleted) {
      return item?.current ? <IconCompletedHighlight /> : <IconCompleted />;
    }
    return "";
  }, []);

  const stopScroll = () => {
    clearInterval(scrollInterval);
  };

  const handleMouseOver = (e: any) => {
    if (!containerRef.current) {
      return;
    }
    const container = containerRef.current;
    const containerWidth = container.offsetWidth;
    const isRightEnd =
      e.nativeEvent.layerX > container.offsetLeft + containerWidth - 50;
    const isLeftEnd = e.nativeEvent.layerX < container.offsetLeft + 20;

    let scrollSpeed = 0;

    if (isRightEnd) {
      scrollSpeed = 2;
    } else if (isLeftEnd) {
      scrollSpeed = -2;
    }

    clearInterval(scrollInterval);

    setScrollInterval(
      setInterval(() => {
        container.scrollLeft += scrollSpeed;
      }, 10)
    );

    // TODO: add event listener everytime might not be a good idea
    container.addEventListener("mouseleave", stopScroll);
    container.addEventListener("mouseup", stopScroll);
  };

  return (
    <div
      className="flex items-center w-fit max-w-[304px] overflow-hidden"
      ref={containerRef}
      onMouseOver={handleMouseOver}
    >
      {miniWorkflow?.map((item: any, index: number) => (
        <>
          <div className="flex w-fit p-[6.667px] justify-center items-center">
            {getStepIcon(item)}
          </div>
          {miniWorkflow.length > index + 1 && (
            <div className="flex">
              <IconPreProcessingVector />
            </div>
          )}
        </>
      ))}
    </div>
  );
};

export default WorkflowStep;
