import React, { memo } from "react";

const IconVector = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="7"
    height="10"
    viewBox="0 0 7 10"
    fill="none"
  >
    <path
      className="icon-fill-skeleton"
      fillRule="evenodd"
      clipRule="evenodd"
      d="M0 1.54651V8.45318C0 8.97984 0.58 9.29984 1.02667 9.01318L6.45333 5.55984C6.86667 5.29984 6.86667 4.69984 6.45333 4.43318L1.02667 0.986509C0.58 0.699843 0 1.01984 0 1.54651Z"
      fill="#EFEFEF"
    />
  </svg>
);

const DataProcessorLoading = () => {
  return (
    <div className="w-full h-full flex justify-center items-center gap-[12px] pr-[12px] bg-[#F5F5F5]">
      <div className="w-[328px] h-full flex flex-col items-start justify-between gap-[10px] py-[20px] px-[16px] bg-[#F9FAFB]">
        <div className="flex flex-col items-start gap-[20px]">
          <div className="flex flex-col items-start gap-[4px]">
            <div className="w-[196px] h-[28px] rounded-[4px] bg-[#EFEFEF] skeleton" />
            <div className="w-[141px] h-[24px] rounded-[4px] bg-[#EFEFEF] skeleton" />
          </div>
          <div className="flex items-center">
            <div className="w-[40px] h-[40px] flex justify-center items-center p-[6px] rounded-[20px]">
              <div className="w-[27px] h-[27px] flex rounded-[100px] bg-[#EFEFEF] skeleton" />
            </div>
            <IconVector />
            <div className="w-[40px] h-[40px] flex justify-center items-center p-[6px] rounded-[20px]">
              <div className="w-[27px] h-[27px] flex rounded-[100px] bg-[#EFEFEF] skeleton" />
            </div>
            <IconVector />
            <div className="w-[40px] h-[40px] flex justify-center items-center p-[6px] rounded-[20px]">
              <div className="w-[27px] h-[27px] flex rounded-[100px] bg-[#EFEFEF] skeleton" />
            </div>
          </div>
          <div className="w-[104px] h-[24px] rounded-[4px] bg-[#EFEFEF] skeleton" />
        </div>
        <div className="flex justify-center items-center gap-[8px]">
          <div className="w-[40px] h-[40px] flex items-start rounded-[100px] bg-[#EFEFEF] skeleton" />
          <div className="flex flex-col items-start gap-[4px]">
            <div className="w-[174px] h-[24px] rounded-[4px] bg-[#EFEFEF] skeleton" />
            <div className="w-[103px] h-[16px] rounded-[4px] bg-[#EFEFEF] skeleton" />
          </div>
        </div>
      </div>
      <div className="w-full h-full flex flex-col justify-center items-center p-[12px]">
        <div className="w-full h-full flex rounded-[5px] bg-[#EFEFEF] skeleton" />
      </div>
    </div>
  );
};

export default memo(DataProcessorLoading);
