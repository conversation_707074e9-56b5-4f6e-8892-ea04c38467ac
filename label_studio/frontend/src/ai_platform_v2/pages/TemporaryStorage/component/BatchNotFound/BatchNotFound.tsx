const IconNotFound = () => (
  <div className="flex relative">
    <svg
      width="351"
      height="161"
      viewBox="0 0 351 161"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M335.829 0.398438C343.932 0.398438 350.5 7.57131 350.5 16.4195C350.5 25.2677 343.932 32.4406 335.829 32.4406H251.997C260.099 32.4406 266.668 39.6134 266.668 48.4616C266.668 57.3098 260.099 64.4827 251.997 64.4827H298.105C306.207 64.4827 312.775 71.6556 312.775 80.5037C312.775 89.3519 306.207 96.5248 298.105 96.5248H276.782C266.566 96.5248 258.284 103.698 258.284 112.546C258.284 118.445 262.476 123.785 270.859 128.567C278.962 128.567 285.53 135.74 285.53 144.588C285.53 153.436 278.962 160.609 270.859 160.609H96.9072C88.8048 160.609 82.2365 153.436 82.2365 144.588C82.2365 135.74 88.8048 128.567 96.9072 128.567H15.1707C7.06828 128.567 0.5 121.394 0.5 112.546C0.5 103.698 7.06828 96.5248 15.1707 96.5248H99.003C107.105 96.5248 113.674 89.3519 113.674 80.5037C113.674 71.6556 107.105 64.4827 99.003 64.4827H46.6078C38.5054 64.4827 31.9371 57.3098 31.9371 48.4616C31.9371 39.6134 38.5054 32.4406 46.6078 32.4406H130.44C122.338 32.4406 115.769 25.2677 115.769 16.4195C115.769 7.57131 122.338 0.398438 130.44 0.398438H335.829ZM335.829 64.4827C343.932 64.4827 350.5 71.6556 350.5 80.5037C350.5 89.3519 343.932 96.5248 335.829 96.5248C327.727 96.5248 321.159 89.3519 321.159 80.5037C321.159 71.6556 327.727 64.4827 335.829 64.4827Z"
        fill="#F3F7FF"
      />
    </svg>
    <svg
      className="absolute top-[18px] left-[36px]"
      width="255"
      height="125"
      viewBox="0 0 255 125"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M62.5 70.9482H47.1209M53.7046 54.4375H21.396H53.7046ZM10.4403 54.4375H2.5H10.4403Z"
        stroke="#75A4FE"
        strokeWidth="5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M114.579 97.5555C140.579 97.5555 161.657 76.4777 161.657 50.477C161.657 24.4762 140.579 3.39844 114.579 3.39844C88.5778 3.39844 67.5 24.4762 67.5 50.477C67.5 76.4777 88.5778 97.5555 114.579 97.5555Z"
        fill="#F3F7FF"
        stroke="#1F64E7"
        strokeWidth="5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M107.033 87.0056C109.505 87.4618 112.02 87.6989 114.577 87.7168C135.145 87.7168 151.818 71.0434 151.818 50.4756C151.818 29.9078 135.145 13.2344 114.577 13.2344C109.294 13.2344 104.267 14.3346 99.7141 16.3186C91.8022 19.7662 85.3204 25.8822 81.4058 33.53C78.8038 38.6131 77.3359 44.3729 77.3359 50.4756C77.3359 56.0126 78.5443 61.2674 80.7117 65.9906C82.2591 69.3626 84.2953 72.4637 86.7296 75.2032"
        fill="white"
      />
      <path
        d="M107.033 87.0056C109.505 87.4618 112.02 87.6989 114.577 87.7168C135.145 87.7168 151.818 71.0434 151.818 50.4756C151.818 29.9078 135.145 13.2344 114.577 13.2344C109.294 13.2344 104.267 14.3346 99.7141 16.3186C91.8022 19.7662 85.3204 25.8822 81.4058 33.53C78.8038 38.6131 77.3359 44.3729 77.3359 50.4756C77.3359 56.0126 78.5443 61.2674 80.7117 65.9906C82.2591 69.3626 84.2953 72.4637 86.7296 75.2032"
        stroke="#1F64E7"
        strokeWidth="5"
        strokeLinecap="round"
      />
      <path
        d="M91.1094 79.3906C94.1282 81.8443 97.5387 83.8347 101.233 85.2535"
        stroke="#1F64E7"
        strokeWidth="5"
        strokeLinecap="round"
      />
      <path
        d="M153.227 87.7188L161.659 96.1507"
        stroke="#1F64E7"
        strokeWidth="5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M160.292 94.7918C157.632 97.4517 157.632 101.764 160.292 104.424L175.87 120.003C178.53 122.662 182.843 122.662 185.503 120.003C188.162 117.343 188.162 113.03 185.503 110.37L169.924 94.7918C167.264 92.1319 162.952 92.1319 160.292 94.7918Z"
        fill="#E8F0FE"
        stroke="#1F64E7"
        strokeWidth="5"
      />
      <path
        d="M167.281 97.5547L182.74 113.013"
        stroke="white"
        strokeWidth="5"
        strokeLinecap="round"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M105.445 34.3166C105.445 50.6156 118.658 63.8285 134.957 63.8285C138.159 63.8285 141.242 63.3187 144.129 62.3756C139.417 74.0693 127.962 82.3236 114.58 82.3236C96.9922 82.3236 82.7344 68.0658 82.7344 50.4779C82.7344 34.5408 94.4413 21.3381 109.725 19C107.009 23.4665 105.445 28.7089 105.445 34.3166Z"
        fill="#E8F0FE"
      />
      <path
        d="M115.279 24.4766C113.49 24.4766 111.741 24.6526 110.051 24.9883M104.943 26.5509C95.3291 30.5909 88.5781 40.096 88.5781 51.1778"
        stroke="#75A4FE"
        strokeWidth="5"
        strokeLinecap="round"
      />
      <path
        d="M219.432 72.3489H199.141M235.127 53.0469H192.5H235.127ZM252.5 53.0469H246.987H252.5Z"
        stroke="#75A4FE"
        strokeWidth="5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  </div>
);

export const BatchNotFound = () => (
  <div
    className="w-full h-full flex flex-col justify-center items-center gap-[30px]"
    style={{ height: "calc(100vh - 211px)" }}
  >
    <div className="flex flex-col items-center gap-[16px]">
      <IconNotFound />
      <div className="flex flex-col items-center gap-[4px]">
        <div className="flex text-[#346] text-[24px] font-medium leading-[36px]">
          No Data Found
        </div>
        <div className="flex flex-col items-center text-[#62708C] text-[16px] font-light leading-[24px]">
          <div>Oops, we couldn’t find what you are looking for</div>
          <div>Please try again and remember to check spelling!</div>
        </div>
      </div>
    </div>
  </div>
);
