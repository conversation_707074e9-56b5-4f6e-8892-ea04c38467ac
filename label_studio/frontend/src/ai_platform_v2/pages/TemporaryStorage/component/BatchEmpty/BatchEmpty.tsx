import IconPlus from "@/ai_platform_v2/assets/Icons/IconPlus";
import { HomeNoData } from "@/ai_platform_v2/assets/Images/NoData";
import "./BatchEmpty.scss";

export const BatchEmpty = ({ buttonContent = "New batch", onClick }: any) => {
  return (
    <div className="w-full h-full flex flex-col justify-center items-center gap-[20px]">
      <div className="flex flex-col items-center gap-[16px]">
        <img src={HomeNoData} alt="no-data" />
        <div className="flex flex-col items-center">
          <div className="flex text-[#346] text-[24px] font-medium leading-[36px]">
            Empty!
          </div>
          <div className="flex text-[#62708C] text-[16px] font-light leading-[24px]">
            There’re no data here.
          </div>
        </div>
      </div>
      <div className="btn-add-new-ai-model cursor-pointer" onClick={onClick}>
        <IconPlus size={30} color="#3361FF" />
        {buttonContent}
      </div>
    </div>
  );
};
