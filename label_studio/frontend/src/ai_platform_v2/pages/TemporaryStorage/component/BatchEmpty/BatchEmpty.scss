.btn-add-new-ai-model {
  cursor: pointer;
  display: flex;
  width: 197px;
  height: 54px;
  padding: 12px 20px 12px 16px;
  justify-content: center;
  align-items: center;

  border-radius: 10px;
  border: 1px dashed rgba(0, 0, 0, 0.2);
  background: #fff;
  box-shadow: 0px 2px 4px 0px rgba(28, 39, 76, 0.08);

  color: var(--Blue-<PERSON>, #d9d9d9);
  text-align: center;

  font-size: 16px;
  font-style: normal;
  font-weight: 400;

  transition:
    border 0.3s,
    background 0.3s,
    color 0.3s;

  svg > path {
    fill: #d9d9d9;

    transition: fill 0.3s;
  }

  &:hover {
    color: var(--Blue-<PERSON>, #3361ff);
    border-color: #3361ff;
    background: linear-gradient(
        0deg,
        rgba(51, 97, 255, 0.05) 0%,
        rgba(51, 97, 255, 0.05) 100%
      ),
      #fff;

    svg > path {
      fill: #3361ff;
    }
  }
}
