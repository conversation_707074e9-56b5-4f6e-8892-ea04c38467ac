import { But<PERSON> } from "@taureau/ui";
import IconArrowDown from "@v2/assets/Icons/IconArrowDown";
import { Popover } from "antd";
import Filter from "./Filter";

interface BatchDetailFilterProps {
  open: boolean;
  setOpen: any;
  valueFilterStatus?: any;
  onClickFilterStatus?: (statusArray: string | any[], status: any) => void;
}

export const BatchDetailFilter = (props: BatchDetailFilterProps) => {
  const { open, setOpen, valueFilterStatus, onClickFilterStatus } = props;

  return (
    <Popover
      open={open}
      trigger="click"
      placement="bottom"
      showArrow={false}
      onOpenChange={(value) => {
        setOpen?.(value);
      }}
      content={
        <Filter
          valueFilterStatus={valueFilterStatus}
          onClickFilterStatus={onClickFilterStatus}
        />
      }
      overlayClassName="my-filter overflow-y-auto scrollbar-v-sm"
    >
      <Button
        active={open}
        className="!pr-5px border-[1px] min-w-[120px] h-[40px] text-[12px] font-medium leading-[30px]"
        size="xs"
      >
        <div className="w-full flex items-center justify-between">
          <span className={open ? "text-blue-blue" : ""}>Filter</span>
          <div className="flex items-center justify-between">
            {valueFilterStatus?.length > 0 && (
              <div className="w-[22px] h-[22px] bg-blue-blue  rounded-full flex items-center justify-center">
                <span className="text-white text-[10px]  font-black">
                  {valueFilterStatus?.length}
                </span>
              </div>
            )}
            <IconArrowDown />
          </div>
        </div>
      </Button>
    </Popover>
  );
};
