import IconCaretDown from "@/ai_platform_v2/assets/Icons/IconCaretDown";
import useFetchAssignedMemberWorkflow from "@/ai_platform_v2/hooks/useFetchAssignedMemberWorkflow";
import classNames from "classnames";
import { useState } from "react";
import { twMerge } from "tailwind-merge";
import IconCancel from "../../../assets/IconCancel";
import IconPreProcessingCompleted from "../../../assets/IconPreProcessingCompleted";
import IconPreProcessingHuman from "../../../assets/IconPreProcessingHuman";
import IconPreProcessingNew from "../../../assets/IconPreProcessingNew";
import IconStars from "../../../assets/IconStars";
import IconTransfer from "../../../assets/IconTransfer";

const PRE_PROCESSING_NEW_ID = "00000000-0000-0000-0000-000000000001";

export type CheckboxValueType = string | number | boolean;

interface Props {
  valueFilterStatus: any;
  onClickFilterStatus?: (statusArray: string | any[], status: any) => void;
}

const Filter = (props: Props) => {
  const { valueFilterStatus, onClickFilterStatus } = props;
  const [expandNew, setExpandNew] = useState(false);
  const [expandHumanApproval, setExpandHumanApproval] = useState(false);
  const [expandCompleted, setExpandCompleted] = useState(false);

  const { data: assignedMembersWorkflow } = useFetchAssignedMemberWorkflow();

  const optionNew = [
    {
      label: "Ready",
      value: ",Ready",
      icon: <IconStars />,
    },
  ];

  const optionHumanApproval = [
    {
      label: "Ready",
      value: ",Ready",
      icon: <IconStars />,
    },
    {
      label: "Rejected",
      value: "Reject",
      icon: <IconCancel />,
    },
  ];

  const optionCompleted = [
    {
      label: "Ready",
      value: ",Ready",
      icon: <IconStars />,
    },
    {
      label: "Transferred",
      value: "Completed",
      icon: <IconTransfer />,
    },
  ];

  return (
    <div className="flex flex-col w-[200px] bg-gray-blue-97 select-none">
      <div
        onClick={() => {
          setExpandNew(!expandNew);
        }}
        className="bg-white w-full p-[10px] flex justify-between items-center cursor-pointer hover:bg-gray-blue-95"
      >
        <div className="flex items-center gap-[5px]">
          <IconPreProcessingNew size={30} />
          <div
            className={classNames("font-medium text-[12px] text-gray-blue-40", {
              "!text-[#3361FF]":
                valueFilterStatus.filter((valueFilter: any) =>
                  valueFilter.workflowStepId.includes(PRE_PROCESSING_NEW_ID)
                ).length === optionNew.length,
            })}
          >
            New
          </div>
        </div>

        <IconCaretDown
          size={20}
          className={expandNew ? "rotate-180" : ""}
          color={!expandNew ? "#C3CAD9" : "#334466"}
        />
      </div>

      <div
        className={twMerge(
          "w-full gap-[10px] p-[10px] flex-col",
          expandNew ? "flex" : "hidden"
        )}
      >
        {optionNew.map((item) => {
          const valueSelected = !valueFilterStatus.some(
            (valueFilter: any) =>
              valueFilter.workflowStepId.includes(PRE_PROCESSING_NEW_ID) &&
              item.value === valueFilter.workflowStepStatus
          );

          return (
            <div
              onClick={() => {
                onClickFilterStatus?.(valueFilterStatus, {
                  workflowStepId: [PRE_PROCESSING_NEW_ID, ""],
                  workflowStepStatus: item.value,
                });
              }}
              key={item.label}
              style={{
                backgroundColor: valueSelected
                  ? "#ffffff"
                  : "rgba(51, 97, 255, 0.1)",
              }}
              className="w-full px-[10px] py-[5px] flex justify-between cursor-pointer items-center hover:bg-gray-blue-95 rounded-[5px]"
            >
              <div className="flex gap-[10px] w-full items-center justify-start">
                {item.icon}
                <div
                  style={{
                    color: valueSelected ? "#334466" : "#3361FF",
                  }}
                  className="font-normal text-[12px]"
                >
                  {item.label}
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {!!assignedMembersWorkflow?.HumanApproval?.length && (
        <>
          <div
            onClick={() => {
              setExpandHumanApproval(!expandHumanApproval);
            }}
            className="bg-white w-full p-[10px] flex justify-between items-center cursor-pointer hover:bg-gray-blue-95"
          >
            <div className="flex items-center gap-[5px]">
              <IconPreProcessingHuman size={30} />
              <div
                className={classNames(
                  "font-medium text-[12px] text-gray-blue-40",
                  {
                    "!text-[#3361FF]":
                      valueFilterStatus.filter((valueFilter: any) =>
                        assignedMembersWorkflow?.HumanApproval?.some(
                          (step: any) =>
                            valueFilter.workflowStepId.includes(step.id)
                        )
                      ).length === optionHumanApproval.length,
                  }
                )}
              >
                Human approval
              </div>
            </div>

            <IconCaretDown
              size={20}
              className={expandHumanApproval ? "rotate-180" : ""}
              color={!expandHumanApproval ? "#C3CAD9" : "#334466"}
            />
          </div>

          <div
            className={twMerge(
              "w-full gap-[10px] p-[10px] flex-col",
              expandHumanApproval ? "flex" : "hidden"
            )}
          >
            {optionHumanApproval.map((item) => {
              const valueSelected = !valueFilterStatus.some(
                (valueFilter: any) =>
                  assignedMembersWorkflow?.HumanApproval?.some((step: any) =>
                    valueFilter.workflowStepId.includes(step.id)
                  ) && item.value === valueFilter.workflowStepStatus
              );

              return (
                <div
                  onClick={() => {
                    onClickFilterStatus(valueFilterStatus, {
                      workflowStepId:
                        assignedMembersWorkflow?.HumanApproval?.map(
                          (step: any) => step.id
                        ),
                      workflowStepStatus: item.value,
                    });
                  }}
                  key={item.label}
                  style={{
                    backgroundColor: valueSelected
                      ? "#ffffff"
                      : "rgba(51, 97, 255, 0.1)",
                  }}
                  className="w-full px-[10px] py-[5px] flex justify-between cursor-pointer items-center hover:bg-gray-blue-95 rounded-[5px]"
                >
                  <div className="flex gap-[10px] w-full items-center justify-start">
                    {item.icon}
                    <div
                      style={{
                        color: valueSelected ? "#334466" : "#3361FF",
                      }}
                      className="font-normal text-[12px]"
                    >
                      {item.label}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </>
      )}

      <div
        onClick={() => {
          setExpandCompleted(!expandCompleted);
        }}
        className="bg-white w-full p-[10px] flex justify-between items-center cursor-pointer hover:bg-gray-blue-95"
      >
        <div className="flex items-center gap-[5px]">
          <IconPreProcessingCompleted size={30} />
          <div
            className={classNames("font-medium text-[12px] text-gray-blue-40", {
              "!text-[#3361FF]":
                valueFilterStatus.filter((valueFilter: any) =>
                  assignedMembersWorkflow?.PreProcessingCompleted?.some(
                    (step: any) => valueFilter.workflowStepId.includes(step.id)
                  )
                ).length === optionCompleted.length,
            })}
          >
            Completed
          </div>
        </div>

        <IconCaretDown
          size={20}
          className={expandCompleted ? "rotate-180" : ""}
          color={!expandCompleted ? "#C3CAD9" : "#334466"}
        />
      </div>

      <div
        className={twMerge(
          "w-full gap-[10px] p-[10px] flex-col",
          expandCompleted ? "flex" : "hidden"
        )}
      >
        {optionCompleted.map((item) => {
          const valueSelected = !valueFilterStatus.some(
            (valueFilter: any) =>
              assignedMembersWorkflow?.PreProcessingCompleted?.some(
                (step: any) => valueFilter.workflowStepId.includes(step.id)
              ) && item.value === valueFilter.workflowStepStatus
          );

          return (
            <div
              onClick={() => {
                onClickFilterStatus(valueFilterStatus, {
                  workflowStepId:
                    assignedMembersWorkflow?.PreProcessingCompleted?.map(
                      (step: any) => step.id
                    ),
                  workflowStepStatus: item.value,
                });
              }}
              key={item.label}
              style={{
                backgroundColor: valueSelected
                  ? "#ffffff"
                  : "rgba(51, 97, 255, 0.1)",
              }}
              className="w-full px-[10px] py-[5px] flex justify-between cursor-pointer items-center hover:bg-gray-blue-95 rounded-[5px]"
            >
              <div className="flex gap-[10px] w-full items-center justify-start">
                {item.icon}
                <div
                  style={{
                    color: valueSelected ? "#334466" : "#3361FF",
                  }}
                  className="font-normal text-[12px]"
                >
                  {item.label}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Filter;
