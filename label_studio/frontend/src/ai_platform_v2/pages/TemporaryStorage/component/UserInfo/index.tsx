import { Typography } from "antd";
import { useMemo } from "react";
import { UserpicV2 } from "@v2/component/Userpic/Userpic";
import {
  DATE_TIME_FORMAT,
  convertUTCToLocalTime,
} from "@/ai_platform_v2/utils/dateTime";

const { Paragraph } = Typography;

interface CreatedByColumnProps {
  user?: any;
  updatedAt?: any;
  className?: string;
}
const UserInfo = (props: CreatedByColumnProps) => {
  const { user, updatedAt, className } = props;

  const fullName: string = useMemo(() => {
    if (user?.username) {
      return user?.username;
    }

    return `${user?.firstName ?? ""} ${user?.lastName ?? ""}`;
  }, [user]);

  return (
    <div className="w-full flex items-center gap-[8px]">
      <UserpicV2
        user={user}
        size={46}
        imgSize={36}
        hightLightColor="#F5F6F7"
        useHightLight
      />
      <div
        className="flex flex-col items-start"
        style={{ width: "calc(100% - 54px)" }}
      >
        <Paragraph
          style={{
            width: "100%",
            color: "#334466",
            fontSize: 14,
            fontWeight: 500,
            lineHeight: "21px",
            margin: 0,
          }}
          ellipsis={{ tooltip: fullName }}
        >
          {fullName}
        </Paragraph>

        <div className="flex text-[#6B7A99] text-[10px] font-normal leading-[15px]">
          {`Updated at ${convertUTCToLocalTime(
            updatedAt,
            DATE_TIME_FORMAT.MMMDDYYYY_HHMM
          )}`}
        </div>
      </div>
    </div>
  );
};

export default UserInfo;
