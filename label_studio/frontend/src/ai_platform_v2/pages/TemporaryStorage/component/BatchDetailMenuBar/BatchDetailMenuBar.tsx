import FloatButton from "@/ai_platform_v2/component/FloatButton/FloatButton";
import React, { memo, useState } from "react";

interface BatchDetailMenuBarProps {
  listSelected?: any;
  onDeleteFiles?: (listSelected?: any) => void;
  handleStartProcessing?: (listSelected?: any) => void;
  handleApprove?: (listSelected?: any) => void;
  handleTransfer?: (listSelected?: any) => void;
  handleReject?: (listSelected?: any) => void;
  isManualTransfer?: boolean;
}

const BatchDetailMenuBar = (props: BatchDetailMenuBarProps) => {
  const {
    listSelected,
    onDeleteFiles,
    handleStartProcessing,
    handleApprove,
    handleTransfer,
    handleReject,
    isManualTransfer = false,
  } = props;
  const [openMenuBar, setOpenMenuBar] = useState(false);

  const actions = [
    {
      label: "Transfer",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M21.9998 9.7C21.9983 11.1696 21.9871 15.4165 21.9036 16.4414C21.8067 17.6308 21.6081 18.6246 21.1636 19.45C20.9676 19.814 20.7267 20.1401 20.4334 20.4334C19.601 21.2657 18.5405 21.6428 17.1966 21.8235C15.8835 22 14.2007 22 12.0534 22H11.9466C9.79929 22 8.11646 22 6.80345 21.8235C5.45951 21.6428 4.39902 21.2657 3.56664 20.4334C2.82871 19.6954 2.44763 18.777 2.24498 17.6376C2.04591 16.5184 2.00949 15.1259 2.00192 13.3967C2 12.9569 2 12.4917 2 12.0009V11.9466C1.99999 9.79929 1.99998 8.11646 2.17651 6.80345C2.3572 5.45951 2.73426 4.39902 3.56664 3.56664C4.39902 2.73426 5.45951 2.3572 6.80345 2.17651C7.97111 2.01952 14.4714 2.00215 16.3 2.00024C16.6853 1.99983 17 2.31236 17 2.69767C17 3.08299 16.6853 3.3952 16.3 3.39561C14.4456 3.39757 8.06751 3.41446 6.98937 3.55941C5.80016 3.7193 5.08321 4.02339 4.5533 4.5533C4.02339 5.08321 3.7193 5.80016 3.55941 6.98937C3.39683 8.19866 3.39535 9.7877 3.39535 12C3.39535 12.2702 3.39535 12.5314 3.39567 12.7844L4.32696 11.9696C5.17465 11.2278 6.45225 11.2704 7.24872 12.0668L11.2392 16.0573C11.8785 16.6966 12.8848 16.7837 13.6245 16.2639L13.9019 16.0689C14.9663 15.3209 16.4064 15.4076 17.3734 16.2779L20.0064 18.6476C20.2714 18.091 20.4288 17.3597 20.5128 16.3281C20.592 15.3561 20.6029 11.1776 20.6044 9.7C20.6048 9.31468 20.917 9 21.3023 9C21.6876 9 22.0002 9.31469 21.9998 9.7Z"
            fill="#334466"
          />
          <path
            d="M18.5 8.5L20.5 6.5M20.5 6.5L18.5 4.5M20.5 6.5L14.5 6.5"
            stroke="#334466"
            strokeWidth="1.25"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      onClick: () => handleTransfer?.(),
      isHidden: !isManualTransfer,
    },

    {
      label: "Reject",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M21.9998 9.7C21.9983 11.1696 21.9871 15.4165 21.9036 16.4414C21.8067 17.6308 21.6081 18.6246 21.1636 19.45C20.9676 19.814 20.7267 20.1401 20.4334 20.4334C19.601 21.2657 18.5405 21.6428 17.1966 21.8235C15.8835 22 14.2007 22 12.0534 22H11.9466C9.79929 22 8.11646 22 6.80345 21.8235C5.45951 21.6428 4.39902 21.2657 3.56664 20.4334C2.82871 19.6954 2.44763 18.777 2.24498 17.6376C2.04591 16.5184 2.00949 15.1259 2.00192 13.3967C2 12.9569 2 12.4917 2 12.0009V11.9466C1.99999 9.79929 1.99998 8.11646 2.17651 6.80345C2.3572 5.45951 2.73426 4.39902 3.56664 3.56664C4.39902 2.73426 5.45951 2.3572 6.80345 2.17651C7.97111 2.01952 14.4714 2.00215 16.3 2.00024C16.6853 1.99983 17 2.31236 17 2.69767C17 3.08299 16.6853 3.3952 16.3 3.39561C14.4456 3.39757 8.06751 3.41446 6.98937 3.55941C5.80016 3.7193 5.08321 4.02339 4.5533 4.5533C4.02339 5.08321 3.7193 5.80016 3.55941 6.98937C3.39683 8.19866 3.39535 9.7877 3.39535 12C3.39535 12.2702 3.39535 12.5314 3.39567 12.7844L4.32696 11.9696C5.17465 11.2278 6.45225 11.2704 7.24872 12.0668L11.2392 16.0573C11.8785 16.6966 12.8848 16.7837 13.6245 16.2639L13.9019 16.0689C14.9663 15.3209 16.4064 15.4076 17.3734 16.2779L20.0064 18.6476C20.2714 18.091 20.4288 17.3597 20.5128 16.3281C20.592 15.3561 20.6029 11.1776 20.6044 9.7C20.6048 9.31468 20.917 9 21.3023 9C21.6876 9 22.0002 9.31469 21.9998 9.7Z"
            fill="#334466"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M16.0303 4.46967C15.7374 4.17678 15.2626 4.17678 14.9697 4.46967C14.6768 4.76256 14.6768 5.23744 14.9697 5.53033L16.3143 6.875L14.9697 8.21967C14.6768 8.51256 14.6768 8.98744 14.9697 9.28033C15.2626 9.57322 15.7374 9.57322 16.0303 9.28033L17.375 7.93566L18.7197 9.28033C19.0126 9.57322 19.4874 9.57322 19.7803 9.28033C20.0732 8.98744 20.0732 8.51256 19.7803 8.21967L18.4357 6.875L19.7803 5.53033C20.0732 5.23744 20.0732 4.76256 19.7803 4.46967C19.4874 4.17678 19.0126 4.17678 18.7197 4.46967L17.375 5.81434L16.0303 4.46967Z"
            fill="#334466"
          />
        </svg>
      ),
      onClick: () => handleReject?.(),
    },

    {
      label: "Approve",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M21.9998 9.7C21.9983 11.1696 21.9871 15.4165 21.9036 16.4414C21.8067 17.6308 21.6081 18.6246 21.1636 19.45C20.9676 19.814 20.7267 20.1401 20.4334 20.4334C19.601 21.2657 18.5405 21.6428 17.1966 21.8235C15.8835 22 14.2007 22 12.0534 22H11.9466C9.79929 22 8.11646 22 6.80345 21.8235C5.45951 21.6428 4.39902 21.2657 3.56664 20.4334C2.82871 19.6954 2.44763 18.777 2.24498 17.6376C2.04591 16.5184 2.00949 15.1259 2.00192 13.3967C2 12.9569 2 12.4917 2 12.0009V11.9466C1.99999 9.79929 1.99998 8.11646 2.17651 6.80345C2.3572 5.45951 2.73426 4.39902 3.56664 3.56664C4.39902 2.73426 5.45951 2.3572 6.80345 2.17651C7.97111 2.01952 14.4714 2.00215 16.3 2.00024C16.6853 1.99983 17 2.31236 17 2.69767C17 3.08299 16.6853 3.3952 16.3 3.39561C14.4456 3.39757 8.06751 3.41446 6.98937 3.55941C5.80016 3.7193 5.08321 4.02339 4.5533 4.5533C4.02339 5.08321 3.7193 5.80016 3.55941 6.98937C3.39683 8.19866 3.39535 9.7877 3.39535 12C3.39535 12.2702 3.39535 12.5314 3.39567 12.7844L4.32696 11.9696C5.17465 11.2278 6.45225 11.2704 7.24872 12.0668L11.2392 16.0573C11.8785 16.6966 12.8848 16.7837 13.6245 16.2639L13.9019 16.0689C14.9663 15.3209 16.4064 15.4076 17.3734 16.2779L20.0064 18.6476C20.2714 18.091 20.4288 17.3597 20.5128 16.3281C20.592 15.3561 20.6029 11.1776 20.6044 9.7C20.6048 9.31468 20.917 9 21.3023 9C21.6876 9 22.0002 9.31469 21.9998 9.7Z"
            fill="#334466"
          />
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M20.7131 4.21848L20.7806 4.28599C21.0731 4.57854 21.0731 5.05111 20.7806 5.34366L16.5949 9.53682C16.3024 9.82937 15.8298 9.82937 15.5373 9.53682L13.2194 7.21895C13.0789 7.07881 13 6.88854 13 6.69012C13 6.4917 13.0789 6.30143 13.2194 6.16129L13.2869 6.09378C13.5795 5.80123 14.052 5.80123 14.3446 6.09378L16.0698 7.81905L19.6554 4.22598C19.9405 3.92593 20.4205 3.92593 20.7131 4.21848Z"
            fill="#334466"
          />
        </svg>
      ),
      onClick: () => handleApprove?.(),
    },

    {
      label: "Start processing",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
        >
          <path
            d="M21.9998 9.7C21.9983 11.1696 21.9871 15.4165 21.9036 16.4414C21.8067 17.6308 21.6081 18.6246 21.1636 19.45C20.9676 19.814 20.7267 20.1401 20.4334 20.4334C19.601 21.2657 18.5405 21.6428 17.1966 21.8235C15.8835 22 14.2007 22 12.0534 22H11.9466C9.79929 22 8.11646 22 6.80345 21.8235C5.45951 21.6428 4.39902 21.2657 3.56664 20.4334C2.82871 19.6954 2.44763 18.777 2.24498 17.6376C2.04591 16.5184 2.00949 15.1259 2.00192 13.3967C2 12.9569 2 12.4917 2 12.0009V11.9466C1.99999 9.79929 1.99998 8.11646 2.17651 6.80345C2.3572 5.45951 2.73426 4.39902 3.56664 3.56664C4.39902 2.73426 5.45951 2.3572 6.80345 2.17651C7.97111 2.01952 14.4714 2.00215 16.3 2.00024C16.6853 1.99983 17 2.31236 17 2.69767C17 3.08299 16.6853 3.3952 16.3 3.39561C14.4456 3.39757 8.06751 3.41446 6.98937 3.55941C5.80016 3.7193 5.08321 4.02339 4.5533 4.5533C4.02339 5.08321 3.7193 5.80016 3.55941 6.98937C3.39683 8.19866 3.39535 9.7877 3.39535 12C3.39535 12.2702 3.39535 12.5314 3.39567 12.7844L4.32696 11.9696C5.17465 11.2278 6.45225 11.2704 7.24872 12.0668L11.2392 16.0573C11.8785 16.6966 12.8848 16.7837 13.6245 16.2639L13.9019 16.0689C14.9663 15.3209 16.4064 15.4076 17.3734 16.2779L20.0064 18.6476C20.2714 18.091 20.4288 17.3597 20.5128 16.3281C20.592 15.3561 20.6029 11.1776 20.6044 9.7C20.6048 9.31468 20.917 9 21.3023 9C21.6876 9 22.0002 9.31469 21.9998 9.7Z"
            fill="#334466"
          />
          <path
            d="M15 8.52969L15 5.22031C15 4.67187 15.4752 4.32521 15.8619 4.59155L17.6718 5.91563L17.6718 5.42715C17.6718 4.94727 18.1074 4.64393 18.4618 4.87698L20.6639 6.32483C21.0286 6.56463 21.0286 7.18537 20.6639 7.42517L18.4619 8.87302C18.1074 9.10607 17.6718 8.80274 17.6718 8.32285V7.83437L15.8619 9.15845C15.4752 9.42479 15 9.07813 15 8.52969Z"
            fill="#334466"
          />
        </svg>
      ),
      onClick: () => handleStartProcessing?.(),
    },

    {
      label: "Delete",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="30"
          height="30"
          viewBox="0 0 30 30"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M9 22C9 23.1 9.9 24 11 24H19C20.1 24 21 23.1 21 22V12C21 10.9 20.1 10 19 10H11C9.9 10 9 10.9 9 12V22ZM21 7H18.5L17.79 6.29C17.61 6.11 17.35 6 17.09 6H12.91C12.65 6 12.39 6.11 12.21 6.29L11.5 7H9C8.45 7 8 7.45 8 8C8 8.55 8.45 9 9 9H21C21.55 9 22 8.55 22 8C22 7.45 21.55 7 21 7Z"
            fill="#CC1414"
          />
        </svg>
      ),
      onClick: () => onDeleteFiles?.(),
      background:
        "linear-gradient(0deg, rgba(230, 46, 46, 0.10) 0%, rgba(230, 46, 46, 0.10) 100%), #FFF",
    },
  ];

  return (
    <FloatButton
      badge={listSelected?.length}
      open={openMenuBar}
      onChangeOpen={() => {
        setOpenMenuBar(!openMenuBar);
      }}
      actions={actions}
    />
  );
};

export default memo(BatchDetailMenuBar);
