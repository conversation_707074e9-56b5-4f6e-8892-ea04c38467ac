import React, { memo } from "react";

import "./styles.scss";

const SkeletonLoadingItem = () => {
  return (
    <div className="flex flex-row items-center px-[10px] py-[12px] py-5 justify-between w-full">
      <div className="w-[80px] h-[80px] aspect-square rounded-[10px] skeleton"></div>
      <div className="w-[235px] h-[26px] rounded-[10px] skeleton"></div>
      <div className="w-[235px] h-[26px] rounded-[10px] skeleton"></div>
      <div className="w-[30px] aspect-square rounded-full skeleton"></div>
      <div className="w-[30px] aspect-square rounded-full skeleton"></div>
      <div className="w-[30px] aspect-square rounded-full skeleton"></div>
      <div className="w-[235px] h-[26px] rounded-[10px] skeleton"></div>
      <div className="w-[30px] aspect-square rounded-full skeleton"></div>
    </div>
  );
};

const SkeletonLoading = () => {
  return (
    <div className="w-full flex flex-col gap-[4px]">
      {[...Array(6).keys()].map((item) => (
        <SkeletonLoadingItem key={item} />
      ))}
    </div>
  );
};

export default memo(SkeletonLoading);
