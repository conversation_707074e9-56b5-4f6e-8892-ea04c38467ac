import { Input } from "@taureau/ui";
import classNames from "classnames";
import { useCallback, useLayoutEffect, useMemo, useRef, useState } from "react";
import "./InputLabel.scss";

const IconPencil = ({ color = "#334466" }: any) => (
  <svg
    width="12"
    height="13"
    viewBox="0 0 12 13"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.2241 5.17839L8.89039 3.84464L7.55665 2.5109L1.92529 8.14226C1.54051 8.52703 1.29091 9.02623 1.21396 9.56492L1.00649 11.0172C0.94721 11.4322 1.30287 11.7878 1.71782 11.7285L3.17011 11.5211C3.7088 11.4441 4.208 11.1945 4.59277 10.8097L10.2241 5.17839Z"
      fill={color}
    />
    <path
      d="M7.55665 2.5109L1.92529 8.14226C1.54051 8.52703 1.29091 9.02623 1.21396 9.56492L1.00649 11.0172C0.94721 11.4322 1.30287 11.7878 1.71782 11.7285L3.17011 11.5211C3.7088 11.4441 4.208 11.1945 4.59277 10.8097L10.2241 5.17839M7.55665 2.5109L8.25669 1.81086C8.98892 1.07862 10.184 1.08654 10.9163 1.81877C11.6485 2.55101 11.6564 3.74611 10.9242 4.47834L10.2241 5.17839M7.55665 2.5109L8.89039 3.84464L10.2241 5.17839"
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export interface InputLabelType {
  classNameLabel?: string;
  classNameInput?: string;
  placeholder?: string;
  value?: string;
  onChange?: (event: any) => void;
  onEnter?: () => void;
}

export const InputLabel = (props: InputLabelType) => {
  const {
    classNameLabel,
    classNameInput,
    placeholder,
    value,
    onChange,
    onEnter,
  } = props;

  const hiddenInputRef = useRef(null);
  const [inputWidth, setInputWidth] = useState(0);

  const [mode, setMode] = useState("label"); // edit | label
  const [isFocusSearch, setFocusSearch] = useState(false);

  const handleChangeMode = useCallback((value) => {
    setMode(value);
  }, []);

  useLayoutEffect(() => {
    setInputWidth(hiddenInputRef.current?.clientWidth ?? 0);
  }, [value]);

  const render = useMemo(
    () => (
      <div className={`flex items-center gap-[4px] ${classNameLabel ?? ""}`}>
        {mode === "label" ? (
          <pre className="flex text-[#334466] text-[18px] font-medium leading-[21px] overflow-hidden">
            {value}
          </pre>
        ) : (
          <Input
            placeholder={placeholder}
            className={classNames(
              `!h-[21px] flex items-center bg-white p-0 rounded-none border-0 shadow-none taureau-input-label ${
                classNameInput ?? ""
              }`
            )}
            inputClassName={classNames(
              `h-[21px] text-[#3361FF] text-[18px] font-medium leading-[21px] bg-white rounded-none`,
              {
                "taureau-input-disabled": !value && !isFocusSearch,
              }
            )}
            style={{ width: !value?.length ? "3px" : `${inputWidth}px` }}
            size="sm"
            value={value}
            onChange={onChange}
            onKeyUp={(e) => {
              if (e.key === "Enter") {
                handleChangeMode("label");
                setFocusSearch(false);
                onEnter?.();
              }
            }}
            maxLength={50}
            autoFocus={isFocusSearch}
            onFocus={() => setFocusSearch(true)}
            onBlur={() => {
              handleChangeMode("label");
              setFocusSearch(false);
              onEnter?.();
            }}
          />
        )}
        <div
          className="flex px-[1px] py-[2px] cursor-pointer"
          onClick={() => {
            setFocusSearch(true);
            handleChangeMode("edit");
          }}
        >
          <IconPencil color={mode === "label" ? "#334466" : "#3361FF"} />
        </div>
      </div>
    ),
    [
      inputWidth,
      classNameInput,
      classNameLabel,
      placeholder,
      mode,
      value,
      isFocusSearch,
      onChange,
      onEnter,
    ]
  );

  return (
    <>
      {render}
      <div className="taureau-label-input-hidden-input" ref={hiddenInputRef}>
        {value}
      </div>
    </>
  );
};
