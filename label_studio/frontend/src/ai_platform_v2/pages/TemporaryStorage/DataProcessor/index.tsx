import Message from "@/ai_platform_v2/component/Message/Message";
import {
  findNextNodeByCurrentNodeID,
  findPrevNodeByNodeID,
} from "@/ai_platform_v2/utils/helpers";
import { useAPI } from "@/providers/ApiProvider";
import { useCurrentUser } from "@/providers/CurrentUser";
import { Modal, Typography } from "antd";
import classNames from "classnames";
import { useCallback, useEffect, useMemo, useState } from "react";
import OutsideClickHandler from "react-outside-click-handler";
import { getTempStorageUrl } from "../../DataManager/helper";
import { checkHavePermissionInListMembers } from "../../TaskManagers/helper";
import IconClose from "../assets/IconClose";
import IconVector2 from "../assets/IconVector2";
import DataProcessorLoading from "../component/DataProcessorLoading";
import UserInfo from "../component/UserInfo";
import WorkflowStep from "../component/WorkflowStep";

const { Paragraph, Text } = Typography;

import "./DataProcessor.scss";

interface DataProcessorProps {
  [key: string]: any;
}
const DataProcessor = (props: DataProcessorProps) => {
  const { user } = useCurrentUser();
  const api = useAPI();
  const {
    open = false,
    closeModal,
    selectedFileId,
    batchName,
    batchId,
    projectId,
    workflow,
    isManualTransfer,
    isReadyToNextStep,
    isBatchDone,
  } = props;

  const [loadedImage, setLoadedImage] = useState(false);
  const [isLoadingFile, setLoadingFile] = useState(false);
  const [file, setFile] = useState<any>({});

  const [isLoadingStartProcessing, setLoadingStartProcessing] = useState(false);
  const [isLoadingApprove, setLoadingApprove] = useState(false);
  const [isLoadingTransfer, setLoadingTransfer] = useState(false);
  const [isLoadingReject, setLoadingReject] = useState(false);
  const [isLoadingSaveRejectReason, setLoadingSaveRejectReason] =
    useState(false);
  const [isLoadingCancel, setLoadingCancel] = useState(false);

  const [isOpenPopupRejectReason, setOpenPopupRejectReason] = useState(false);

  const [rejectReason, setRejectReason] = useState<any>();
  const [reasonId, setReasonId] = useState<any>();

  const fetchFileDetail = useCallback(async () => {
    try {
      if (isLoadingFile) return;
      setLoadingFile(true);

      const result: any = await api.callApi("dataBatchFileDetail", {
        params: {
          pk: projectId,
          tempFileId: selectedFileId,
        },
      });

      if (result?.success) {
        setFile(result.data);

        const res = await api.callApi("getReason", {
          params: {
            projectId,
            fileId: selectedFileId,
            reasonType: "Reject",
          },
        });

        if (res?.success) {
          const { items }: any = res;

          if (items?.length) {
            setReasonId(items?.[0]?.id);
            setRejectReason(items?.[0]?.contentReason);
          } else {
            setRejectReason(undefined);
            setReasonId(undefined);
          }
        }
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    } finally {
      setLoadingFile(false);
    }
  }, [projectId, selectedFileId, isLoadingFile]);

  useEffect(() => {
    fetchFileDetail();
    setLoadedImage(false);
  }, [selectedFileId]);

  const {
    id,
    isCompleted,
    fullNameUserUpdated,
    avatarUserUpdated,
    modifiedAt,
    fileName,
    workflowState,
    transferStatus,
    stepStatus,
    workflowStepId,
    userUpdated,
    timestamp,
  } = file;

  const currentStep = useMemo(() => {
    if (!workflowStepId || workflowState === "PreProcessingNew") {
      return workflow?.find((node: any) => node?.type === "PreProcessingNew");
    }

    if (isCompleted || workflowState === "PreProcessingCompleted") {
      return workflow?.find(
        (node: any) => node?.type === "PreProcessingCompleted"
      );
    }

    return workflow?.find((node: any) => node?.id === workflowStepId);
  }, [workflowStepId, workflowState, isCompleted, workflow]);

  useEffect(() => {
    if (
      currentStep?.type === "PreProcessingCompleted" &&
      transferStatus === "ReadyTransfer"
    ) {
      const fetchFileDetailBackground = async () => {
        const res: any = await api.callApi("dataBatchFileDetail", {
          params: {
            pk: projectId,
            tempFileId: selectedFileId,
          },
        });

        if (res?.success) {
          if (res?.data?.transferStatus === "Completed") {
            setFile({ ...file, transferStatus: "Completed" });
            Message.success({ content: `${fileName} transferred` });
          } else {
            fetchFileDetailBackground();
          }
        }
      };

      fetchFileDetailBackground();
    }
  }, [currentStep?.type, transferStatus, selectedFileId, projectId, file]);

  const nextStep = useMemo(
    () => findNextNodeByCurrentNodeID(currentStep?.id, workflow),
    [currentStep?.id, workflow]
  );

  const preStep = useMemo(
    () => findPrevNodeByNodeID(currentStep?.id, workflow),
    [currentStep?.id, workflow]
  );

  const isOwner = useMemo(
    () => user?.id === userUpdated,
    [user?.id, userUpdated]
  );

  const {
    settings: { isCancel, isRequireRejectReason },
  } = currentStep ?? {
    settings: { isCancel: undefined, isRequireRejectReason: undefined },
  };

  const isHasPermission = useMemo(() => {
    return checkHavePermissionInListMembers(currentStep?.members, user.id);
  }, [currentStep?.members, user.id]);

  const isHasPermissionPreStep = useMemo(() => {
    return checkHavePermissionInListMembers(preStep?.members, user.id);
  }, [preStep?.members, user.id]);

  const {
    settings: { isCancel: isCancelPre },
  } = preStep ?? {
    settings: { isCancel: undefined, isRequireRejectReason: undefined },
  };

  const currentStepStatus = useMemo(() => {
    if (currentStep?.type === "HumanApproval") {
      if (stepStatus === "Reject") {
        return "Rejected";
      }
      return "Ready";
    }
    if (isCompleted && currentStep?.type === "PreProcessingCompleted") {
      if (transferStatus === "Completed") return "Transferred";

      if (transferStatus === "ReadyTransfer") return "Transferring";

      return "Ready";
    }
    return "Ready";
  }, [currentStep?.type, isCompleted, stepStatus, transferStatus]);

  const handleStartProcessing = useCallback(async () => {
    try {
      if (isLoadingStartProcessing) return;

      if (!isReadyToNextStep) {
        Message.error({
          content: "Data need to be deduplicated first",
        });

        return;
      }

      setLoadingStartProcessing(true);

      const tempStorageFile =
        nextStep?.type === "PreProcessingCompleted"
          ? {
              id,
              workflowStep: nextStep?.name,
              workflowStepId: nextStep?.id,
              workflowState: nextStep?.type,
              isCompleted: true,
              timestamp,
            }
          : {
              id,
              workflowStep: nextStep?.name,
              workflowStepId: nextStep?.id,
              workflowState: nextStep?.type,
              timestamp,
            };

      const result: any = await api.callApi("updateTempStorageFile", {
        params: {
          projectId,
          tempId: batchId,
        },
        body: tempStorageFile,
      });

      if (result?.success) {
        Message.success({
          content: `${fileName} started processing`,
        });

        if (nextStep?.type === "PreProcessingCompleted" && !isManualTransfer) {
          const ids = [id];
          // Using transfer multiple to pass timestamp
          const result: any = await api.callApi("transferMultipleFiles", {
            params: {
              projectId,
              tempId: batchId,
            },
            body: {
              ids,
              isAutoTransfer: true,
            },
          });
        }
        fetchFileDetail();
      } else {
        Message.error({
          content: result?.message,
        });
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    } finally {
      setLoadingStartProcessing(false);
    }
  }, [
    batchId,
    fetchFileDetail,
    id,
    isLoadingStartProcessing,
    isManualTransfer,
    isReadyToNextStep,
    nextStep?.id,
    nextStep?.name,
    nextStep?.type,
    projectId,
    timestamp,
  ]);

  const handleApprove = useCallback(async () => {
    try {
      if (isLoadingApprove || isLoadingReject || isLoadingSaveRejectReason)
        return;

      setLoadingApprove(true);

      const tempStorageFile =
        nextStep?.type === "PreProcessingCompleted"
          ? {
              id,
              workflowStep: nextStep?.name,
              workflowStepId: nextStep?.id,
              workflowState: nextStep?.type,
              isCompleted: true,
              timestamp,
            }
          : {
              id,
              workflowStep: nextStep?.name,
              workflowStepId: nextStep?.id,
              workflowState: nextStep?.type,
              timestamp,
            };

      const result: any = await api.callApi("updateTempStorageFile", {
        params: {
          projectId,
          tempId: batchId,
        },
        body: tempStorageFile,
      });

      if (result?.success) {
        Message.success({
          content: `${fileName} approved`,
        });

        if (nextStep?.type === "PreProcessingCompleted" && !isManualTransfer) {
          const ids = [id];
          // Using transfer multiple to pass timestamp
          const result: any = await api.callApi("transferMultipleFiles", {
            params: {
              projectId,
              tempId: batchId,
            },
            body: {
              ids,
              isAutoTransfer: true,
            },
          });
        }
        fetchFileDetail();
      } else {
        Message.error({
          content: result?.message,
        });
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    } finally {
      setLoadingApprove(false);
    }
  }, [
    isLoadingApprove,
    isLoadingReject,
    isLoadingSaveRejectReason,
    nextStep?.type,
    nextStep?.name,
    nextStep?.id,
    id,
    projectId,
    batchId,
    isManualTransfer,
    fetchFileDetail,
    timestamp,
  ]);

  const togglePopupRejectReason = useCallback(() => {
    setOpenPopupRejectReason(!isOpenPopupRejectReason);

    if (!isOpenPopupRejectReason) {
      setRejectReason(undefined);
      setReasonId(undefined);
    }
  }, [isOpenPopupRejectReason]);

  const handleReject = useCallback(async () => {
    try {
      if (isLoadingReject || isLoadingApprove) return;

      setLoadingReject(true);

      const tempStorageFile = {
        id,
        stepStatus: "Reject",
        timestamp,
      };
      const result: any = await api.callApi("updateTempStorageFile", {
        params: {
          projectId,
          tempId: batchId,
        },
        body: tempStorageFile,
      });

      if (result?.success) {
        Message.success({
          content: `${fileName} rejected`,
        });

        fetchFileDetail();
      } else {
        Message.error({
          content: result?.message,
        });
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    } finally {
      setLoadingReject(false);
    }
  }, [
    isLoadingReject,
    isLoadingApprove,
    id,
    projectId,
    batchId,
    fetchFileDetail,
    timestamp,
  ]);

  const handleRejectWithReason = useCallback(async () => {
    try {
      if (isLoadingSaveRejectReason) return;

      setLoadingSaveRejectReason(true);
      const res = await api.callApi("createReason", {
        params: {
          projectId,
          fileId: selectedFileId,
        },
        body: {
          userId: user?.id,
          contentReason: rejectReason,
          reasonType: "Reject",
        },
      });

      if (res?.success) {
        const tempStorageFile = {
          id,
          stepStatus: "Reject",
          timestamp,
        };
        const result: any = await api.callApi("updateTempStorageFile", {
          params: {
            projectId,
            tempId: batchId,
          },
          body: tempStorageFile,
        });

        if (result?.success) {
          Message.success({
            content: `${fileName} rejected`,
          });

          setOpenPopupRejectReason(false);
          fetchFileDetail();
        } else {
          Message.error({
            content: result?.message,
          });
        }
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
      Message.error({
        content: "Something went wrong!",
      });
    } finally {
      setLoadingSaveRejectReason(false);
    }
  }, [
    batchId,
    fetchFileDetail,
    fileName,
    id,
    isLoadingSaveRejectReason,
    projectId,
    rejectReason,
    selectedFileId,
    user?.id,
    timestamp,
  ]);

  const handleTransfer = useCallback(async () => {
    try {
      if (
        isLoadingTransfer ||
        (isCompleted &&
          (transferStatus === "Completed" ||
            transferStatus === "ReadyTransfer"))
      )
        return;

      setLoadingTransfer(true);

      const result: any = await api.callApi("transferSingleFile", {
        params: {
          projectId,
          tempId: batchId,
        },
        body: {
          id,
          timestamp,
        },
      });

      if (result?.success) {
        Message.success({
          content: `${fileName} is being transferred`,
        });

        fetchFileDetail();
      } else {
        Message.error({
          content: result?.message,
        });
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
    } finally {
      setLoadingTransfer(false);
    }
  }, [
    batchId,
    fetchFileDetail,
    id,
    isCompleted,
    isLoadingTransfer,
    projectId,
    transferStatus,
  ]);

  const handleCancelAction = useCallback(async () => {
    try {
      if (isLoadingCancel) return;
      setLoadingCancel(true);

      // Cancel Reject action
      if (
        currentStep?.type === "HumanApproval" &&
        stepStatus === "Reject" &&
        isCancel &&
        isOwner
      ) {
        const tempStorageFile = {
          id,
          stepStatus: "Ready",
          timestamp,
        };

        const result: any = await api.callApi("updateTempStorageFile", {
          params: {
            projectId,
            tempId: batchId,
          },
          body: tempStorageFile,
        });

        // Delete reject reason
        if (reasonId) {
          await api.callApi("deleteReason", {
            params: {
              projectId,
              fileId: id,
              reasonId,
            },
          });
        }

        if (result?.success) {
          Message.success({ content: `Cancelled action` });
          fetchFileDetail();
        } else {
          Message.error({
            content: result?.message,
          });
        }
      } else {
        const prevNode = findPrevNodeByNodeID(currentStep?.id, workflow);
        const tempStorageFile =
          currentStep?.type === "PreProcessingCompleted"
            ? {
                id: file?.id,
                workflowStep: prevNode?.name,
                workflowStepId: prevNode?.id,
                workflowState: prevNode?.type,
                isCompleted: false,
                timestamp,
              }
            : {
                id: file?.id,
                workflowStep: prevNode?.name,
                workflowStepId: prevNode?.id,
                workflowState: prevNode?.type,
                timestamp,
              };

        const result: any = await api.callApi("updateTempStorageFile", {
          params: {
            projectId,
            tempId: batchId,
          },
          body: tempStorageFile,
        });

        // Delete reject reason
        if (reasonId) {
          await api.callApi("deleteReason", {
            params: {
              projectId,
              fileId: id,
              reasonId,
            },
          });
        }

        if (result?.success) {
          Message.success({ content: `Cancelled action` });
          fetchFileDetail();
        } else {
          Message.error({
            content: result?.message,
          });
        }
      }
    } catch (error) {
      Message.error({ content: "Something went wrong!" });
    } finally {
      setLoadingCancel(false);
    }
  }, [
    batchId,
    currentStep?.id,
    currentStep?.type,
    fetchFileDetail,
    file?.id,
    id,
    isCancel,
    isLoadingCancel,
    isOwner,
    projectId,
    reasonId,
    stepStatus,
    workflow,
  ]);

  const renderActionButton = useCallback(() => {
    if (isBatchDone) return;
    if (
      currentStep?.type === "HumanApproval" &&
      stepStatus === "Reject" &&
      isCancel &&
      isOwner
    ) {
      return (
        <div className="shrink-0 w-full h-[60px] flex items-center justify-center gap-4">
          <button
            onClick={handleCancelAction}
            className={classNames(
              "data-processor-btn data-processor-btn--negative",
              {
                "data-processor-btn--start-negative--loading": isLoadingCancel,
              }
            )}
          >
            Cancel action
          </button>
        </div>
      );
    }

    if (
      (currentStep?.type === "PreProcessingCompleted" &&
        (!isManualTransfer || transferStatus === "Completed")) ||
      (stepStatus === "Reject" && currentStep?.type === "HumanApproval")
    )
      return;

    if (!isHasPermission) {
      if (isOwner) {
        if (
          currentStep?.type === "PreProcessingCompleted" &&
          transferStatus === "ReadyTransfer"
        ) {
          return (
            <div className="shrink-0 w-full h-[60px] flex items-center justify-center gap-4">
              <button
                onClick={() => {}}
                className={classNames(
                  "data-processor-btn data-processor-btn--transfer",
                  {
                    "data-processor-btn--transfer--loading": true,
                  }
                )}
              >
                Transfer
              </button>
            </div>
          );
        }

        if (isHasPermissionPreStep && isCancelPre) {
          return (
            <div className="shrink-0 w-full h-[60px] flex items-center justify-center gap-4">
              <button
                onClick={handleCancelAction}
                className={classNames(
                  "data-processor-btn data-processor-btn--negative",
                  {
                    "data-processor-btn--start-negative--loading":
                      isLoadingCancel,
                  }
                )}
              >
                Cancel action
              </button>
            </div>
          );
        }
      }
      return;
    }
    return (
      <div className="shrink-0 w-full h-[60px] flex items-center justify-center gap-4">
        {currentStep?.type === "PreProcessingNew" && (
          <button
            onClick={handleStartProcessing}
            className={classNames(
              "data-processor-btn data-processor-btn--start-processing",
              {
                "data-processor-btn--start-processing--loading":
                  isLoadingStartProcessing,
              }
            )}
          >
            Start processing
          </button>
        )}
        {currentStep?.type === "HumanApproval" && (
          <>
            {preStep?.type === "HumanApproval" && isOwner && isCancelPre ? (
              <>
                <button
                  onClick={handleCancelAction}
                  className={classNames(
                    "data-processor-btn data-processor-btn--negative",
                    {
                      "data-processor-btn--start-negative--loading":
                        isLoadingCancel,
                    }
                  )}
                >
                  Cancel action
                </button>
                <div className="w-[2px] h-[26px] bg-[#0000001A]" />
              </>
            ) : null}

            <OutsideClickHandler
              onOutsideClick={() => {
                if (isOpenPopupRejectReason) togglePopupRejectReason();
              }}
            >
              <div className="relative flex justify-center">
                <button
                  onClick={
                    isRequireRejectReason
                      ? togglePopupRejectReason
                      : handleReject
                  }
                  className={classNames(
                    "w-[81px] data-processor-btn data-processor-btn--negative",
                    {
                      "data-processor-btn--negative--loading": isLoadingReject,
                      "data-processor-btn--negative--active":
                        isOpenPopupRejectReason,
                    }
                  )}
                >
                  Reject
                </button>
                {isOpenPopupRejectReason && (
                  <div className="absolute top-[-190px]">
                    <div className="reject-reason-modal">
                      <span className="reject-reason-modal__title">
                        Type in reject reason
                      </span>
                      <textarea
                        placeholder="Enter reason here"
                        className={classNames(
                          "w-full p-10px shadow-Shadows/Gray-Blue-30/3%/5b border-[2px] border-solid border-[#F5F6F7] rounded-[8px] focus:border-blue-100",
                          "max-h-[100px] min-h-[100px]",
                          "placeholder:text-[14px] placeholder:text-gray-blue-85 placeholder:font-[400]",
                          "font-[400] text-[12px] text-gray-blue-grey-blue-40 ",
                          "focus:outline-none",
                          "resize-none"
                        )}
                        maxLength={100}
                        onChange={(e) => {
                          const value = e.target.value;

                          // if two last character is \n or blank then remove it
                          if (
                            value.slice(-1) === "\n" ||
                            value.slice(-2) === "  "
                          ) {
                            setRejectReason(value.trim());
                            return;
                          }
                          setRejectReason(value);
                        }}
                      />
                      <div className="flex justify-end gap-2">
                        <button
                          onClick={() => setOpenPopupRejectReason(false)}
                          className={classNames(
                            "data-processor-btn data-processor-btn--cancel"
                          )}
                        >
                          Cancel
                        </button>

                        <button
                          onClick={handleRejectWithReason}
                          className={classNames(
                            "data-processor-btn data-processor-btn--save",
                            {
                              "data-processor-btn--save--loading":
                                isLoadingSaveRejectReason,
                              "data-processor-btn--save--disabled":
                                !rejectReason?.trim(),
                            }
                          )}
                          disabled={!rejectReason?.trim()}
                        >
                          Save
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </OutsideClickHandler>

            <button
              onClick={handleApprove}
              className={classNames(
                "data-processor-btn data-processor-btn--approve",
                {
                  "data-processor-btn--approve--loading": isLoadingApprove,
                }
              )}
            >
              Approve
            </button>
          </>
        )}

        {currentStep?.type === "PreProcessingCompleted" && (
          <>
            {preStep?.type === "HumanApproval" &&
            isOwner &&
            isCancelPre &&
            !(isLoadingTransfer || transferStatus === "ReadyTransfer") ? (
              <>
                <button
                  onClick={handleCancelAction}
                  className={classNames(
                    "data-processor-btn data-processor-btn--negative",
                    {
                      "data-processor-btn--start-negative--loading":
                        isLoadingCancel,
                    }
                  )}
                >
                  Cancel action
                </button>
                <div className="w-[2px] h-[26px] bg-[#0000001A]" />
              </>
            ) : null}
            <button
              onClick={handleTransfer}
              className={classNames(
                "data-processor-btn data-processor-btn--transfer",
                {
                  "data-processor-btn--transfer--loading":
                    isLoadingTransfer || transferStatus === "ReadyTransfer",
                }
              )}
            >
              Transfer
            </button>
          </>
        )}
      </div>
    );
  }, [
    currentStep?.type,
    handleApprove,
    handleCancelAction,
    handleReject,
    handleRejectWithReason,
    handleStartProcessing,
    handleTransfer,
    isCancel,
    isHasPermission,
    isLoadingApprove,
    isLoadingCancel,
    isLoadingReject,
    isLoadingSaveRejectReason,
    isLoadingStartProcessing,
    isLoadingTransfer,
    isManualTransfer,
    isOpenPopupRejectReason,
    isOwner,
    isRequireRejectReason,
    rejectReason,
    stepStatus,
    togglePopupRejectReason,
    transferStatus,
  ]);

  return (
    <Modal
      open={open}
      centered
      footer={null}
      bodyStyle={{
        padding: 0,
      }}
      closable={false}
      onCancel={() => closeModal?.()}
      className="data-processor-modal"
    >
      <div className="w-full h-full flex flex-col">
        <div className="w-full flex items-center justify-between py-[12px] pl-[28px] pr-[12px]">
          <div className="flex items-center">
            <div className="flex text-[#6B7A99] text-[14px] font-normal leading-[21px] max-w-[500px]">
              <Text ellipsis={{ tooltip: true }}>{batchName}</Text>
            </div>
            <div className="w-[16px] flex justify-center pb-[3px]">
              <IconVector2 />
            </div>
            <div className="flex text-[#334466] text-[14px] font-medium leading-[21px]  max-w-[500px]">
              <Text ellipsis={{ tooltip: true }}>{fileName}</Text>
            </div>
          </div>

          <div className="flex cursor-pointer" onClick={() => closeModal?.()}>
            <IconClose />
          </div>
        </div>
        {isLoadingFile ? (
          <DataProcessorLoading />
        ) : (
          <div
            className="h-full flex items-center gap-[12px] pr-[12px] bg-[#F5F5F5]"
            style={{ height: "calc(100% - 48px)" }}
          >
            <div className="w-[328px] h-full flex flex-col items-center gap-[10px] py-[20px] px-[12px] bg-[#F9FAFB]">
              <div className="w-full h-full flex flex-col items-center gap-[20px] overflow-auto">
                <div className="w-full flex flex-col items-start">
                  <div className="w-full flex text-[#334466] text-[22px] font-semibold">
                    <Paragraph
                      style={{
                        width: "100%",
                        color: "#334466",
                        fontSize: 22,
                        fontWeight: 600,
                        lineHeight: "21px",
                        margin: 0,
                      }}
                      ellipsis={{ tooltip: currentStep?.name }}
                    >
                      {currentStep?.name}
                    </Paragraph>
                  </div>
                  <div className="flex text-[#334466] text-[14px] font-normal">
                    Pre-processing
                  </div>
                </div>

                <div className="w-full flex flex-col items-start gap-[8px]">
                  <div className="flex text-[#334466] text-[14px] font-semibold">
                    Workflow
                  </div>
                  <div className="w-full flex overflow-auto">
                    <WorkflowStep workflow={workflow} fileData={file} />
                  </div>
                </div>

                <div className="w-full flex flex-col items-start gap-[4px]">
                  <div className="flex text-[#334466] text-[14px] font-semibold">
                    Status
                  </div>
                  <div
                    className={classNames(
                      "flex text-[#6B7A99] text-[12px] font-medium",
                      { "text-[#CC1414]": currentStepStatus === "Rejected" }
                    )}
                  >
                    {currentStepStatus}
                  </div>
                  {currentStepStatus === "Rejected" &&
                  isRequireRejectReason &&
                  rejectReason ? (
                    <div className="py-[2px] px-[8px] rounded-[5px] bg-[#FFF3F3] text-[#B20000] text-[12px] leading-[18px] whitespace-pre-wrap w-full">
                      {rejectReason}
                    </div>
                  ) : null}
                </div>
              </div>
              {fullNameUserUpdated && (
                <div className="w-full flex justify-start">
                  <UserInfo
                    user={{
                      avatar: avatarUserUpdated,
                      username: fullNameUserUpdated,
                    }}
                    updatedAt={modifiedAt}
                  />
                </div>
              )}
            </div>

            <div className="w-full h-full flex flex-col items-center bg-[#F5F5F5] overflow-auto">
              <div className="w-fit h-full flex justify-center items-end bg-[#F5F5F5] overflow-auto">
                <img
                  className={classNames(
                    "w-full flex object-contain p-[4px] bg-[#FCFCFD]",
                    { "!w-0 !h-0": !loadedImage }
                  )}
                  src={getTempStorageUrl(projectId, id, "Original")}
                  alt=""
                  style={{
                    height: "calc(100% - 25px)",
                    boxShadow:
                      "1.317px 1.317px 1.317px 0px rgba(0, 0, 0, 0.08), 0px 2.634px 5.269px 0px rgba(44, 43, 42, 0.10)",
                  }}
                  onLoad={() => setLoadedImage(true)}
                />
              </div>
              {renderActionButton()}
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default DataProcessor;
