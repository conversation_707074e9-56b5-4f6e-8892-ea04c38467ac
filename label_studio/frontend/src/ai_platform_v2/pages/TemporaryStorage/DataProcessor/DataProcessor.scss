.data-processor-modal {
  //   max-width: 1245px !important;
  width: 80vw !important;
  height: 90vh !important;

  .ant-modal-header {
    border-bottom: none;
  }

  .ant-modal-content {
    overflow: hidden;
    border-radius: 10px !important;
  }

  .ant-modal-content,
  .ant-modal-body {
    height: 100%;
  }
}

@keyframes button-loading {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 100px 0;
  }
}

.data-processor-btn {
  display: flex;
  box-sizing: border-box;
  height: 28px;
  padding: 5px 15px;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  outline: none;

  &--start-processing {
    color: #3361ff;
    box-shadow: 0.7px 0.7px 4px 0px rgba(38, 51, 77, 0.03);
    background: #ebf0ff;

    &:hover {
      background: var(--blue-blue-20, #3361ff66);
    }
    &--loading {
      width: fit-content;
      background-color: #fff !important;
      border: none !important ;
      animation: button-loading 1.8s linear infinite;
      background-image: repeating-linear-gradient(
        115deg,
        rgba(51, 97, 255, 0.4) 24px,
        rgba(51, 97, 255, 0.6) 0px,
        rgba(51, 97, 255, 0.6) 36px,
        rgba(51, 97, 255, 0.4) 28px,
        rgba(51, 97, 255, 0.4) 46.9px
      ) !important;
    }
  }

  &--negative {
    background: #fdebeb;
    box-shadow: 0.7px 0.7px 4px 0px rgba(0, 0, 0, 0.1);
    color: #cc1414;

    &:hover {
      background: rgba(222, 84, 98, 0.4);
    }

    &--active {
      border: 1px solid var(--Fade-Red, #de5462);
    }

    &--loading {
      width: fit-content;
      background-color: #fff !important;
      border: none !important ;
      animation: button-loading 1.8s linear infinite;
      background-image: repeating-linear-gradient(
        115deg,
        rgba(255, 0, 0, 0.15) 24px,
        rgba(255, 0, 0, 0.4) 0px,
        rgba(255, 0, 0, 0.4) 36px,
        rgba(255, 0, 0, 0.15) 28px,
        rgba(255, 0, 0, 0.15) 46.9px
      ) !important;
    }
  }

  &--approve {
    background: #f0faf7;
    box-shadow: 0.7px 0.7px 4px 0px rgba(0, 0, 0, 0.1);
    color: rgba(102, 204, 167, 1);

    &:hover {
      background: rgba(102, 204, 167, 0.4);
    }

    &--loading {
      width: fit-content;
      background-color: #fff !important;
      border: none !important ;
      animation: button-loading 1.8s linear infinite;
      background-image: repeating-linear-gradient(
        115deg,
        rgba(102, 204, 167, 0.15) 24px,
        rgba(102, 204, 167, 0.4) 0px,
        rgba(102, 204, 167, 0.4) 36px,
        rgba(102, 204, 167, 0.15) 28px,
        rgba(102, 204, 167, 0.15) 46.9px
      ) !important;
    }
  }

  &--transfer {
    background: #ebf9ff;
    box-shadow: 0.7px 0.7px 4px 0px rgba(0, 0, 0, 0.1);
    color: rgba(51, 191, 255, 1);

    &:hover {
      background: rgba(51, 191, 255, 0.4);
    }

    &--loading {
      width: fit-content;
      background-color: #fff !important;
      border: none !important ;
      animation: button-loading 1.8s linear infinite;
      background-image: repeating-linear-gradient(
        115deg,
        rgba(51, 191, 255, 0.15) 24px,
        rgba(51, 191, 255, 0.4) 0px,
        rgba(51, 191, 255, 0.4) 36px,
        rgba(51, 191, 255, 0.15) 28px,
        rgba(51, 191, 255, 0.15) 46.9px
      ) !important;
    }
  }

  &--cancel {
    border: 2px solid #f5f6f7;
    border-radius: 15px;
    background: #fff;
    color: #346;

    &:hover {
      filter: brightness(0.98);
    }
  }

  &--save {
    border-radius: 15px;
    background: rgba(51, 97, 255, 1);
    color: #fff;

    &:hover {
      background: rgba(51, 97, 255, 0.4);
    }

    &--loading {
      width: fit-content;
      background-color: #fff !important;
      border: none !important ;
      animation: button-loading 1.8s linear infinite;
      background-image: repeating-linear-gradient(
        115deg,
        rgba(51, 97, 255, 0.15) 24px,
        rgba(51, 97, 255, 0.4) 0px,
        rgba(51, 97, 255, 0.4) 36px,
        rgba(51, 97, 255, 0.15) 28px,
        rgba(51, 97, 255, 0.15) 46.9px
      ) !important;
    }
    &--disabled {
      cursor: not-allowed;
      background: rgba(0, 0, 0, 0.1);
      color: rgba(173, 184, 204, 1);

      &:hover {
        background: rgba(0, 0, 0, 0.1) !important;
      }
    }
  }
}

.reject-reason-modal {
  padding: 8px 12px;
  display: flex;
  width: 300px;
  flex-direction: column;
  gap: 8px;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.15);
  background: #fff;

  &__title {
    color: var(--Red-Red-Dark-1, #cc1414);
    font-size: 13px;
    font-weight: 500;
    line-height: 20px; /* 153.846% */
    text-align: center;
  }

  &__content {
  }
}
