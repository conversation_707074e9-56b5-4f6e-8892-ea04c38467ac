export const DEFAULT_PAGE_SIZE = 30;

export const INIT_BATCH_PARAMS = {
  currentPage: 1,
  pageSize: 30,
  searchValue: undefined,
};

export const INIT_UPLOADING_PROGRESS_OPTIONS = [
  { label: "Uploading", value: "Uploading" },
  { label: "Completed", value: "Completed" },
  { label: "Failed", value: "Failed" },
];

export const INIT_SORT_COLUMN = {
  batchName: undefined,
  readyToTransfer: undefined,
  totalTransfered: undefined,
  createdAt: undefined,
};

export const INIT_BATCH_DETAIL_PARAMS = {
  currentPage: 1,
  pageSize: 30,
  searchValue: undefined,
  filterSelected: [],
};

export function isNumeric(str: any) {
  if (typeof str === "string") return false; // we only process strings!
  return true;
}

export const INIT_TRANSFER_STATUS = {
  readyTransfer: "ReadyTransfer",
  inProgress: "InProgress",
  completed: "Completed",
};
