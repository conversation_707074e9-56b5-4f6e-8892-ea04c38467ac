import { PermissionMessage } from "@/components/PermissionMessage/PermissionMessage";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { getCurrentModule, getCurrentProject } from "@/pages/DataSet/Const";
import { useAPI } from "@/providers/ApiProvider";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { useProject } from "@/providers/ProjectProvider";
import { useEffect, useMemo, useState } from "react";
import { useLocation } from "react-router";
import { Project } from "../Project/Project";
import BatchDetail from "./BatchDetail";
import { TempStorageWebSocketProvider } from "./BatchDetail/TempStorageWSProvider";
import BatchList from "./BatchList";
import "./TemporaryStorage.scss";

const regUUID =
  /^[0-9a-f]{8}-[0-9a-f]{4}-[0-5][0-9a-f]{3}-[089ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

export const TemporaryStorage = () => {
  const moduleId = getCurrentModule();
  const projectId = getCurrentProject();
  const api = useAPI();
  const location = useLocation();

  const { projectDetail } = useProject();

  const isConfigPreProcessing = useMemo(() => {
    if (projectDetail?.preProccessConfig === undefined) return undefined;
    if (!projectDetail?.preProccessConfig) return false;

    return !!JSON.parse(projectDetail.preProccessConfig)?.node?.length;
  }, [projectDetail.preProccessConfig]);

  const { hasPermissionAllScopeInProject } = useCheckPermission();

  const canDataProceed = hasPermissionAllScopeInProject(
    ABILITY_NEW.can_data_proceed,
    moduleId,
    projectId
  );

  const canViewDataset = hasPermissionAllScopeInProject(
    ABILITY_NEW.can_view_dataset,
    moduleId,
    projectId
  );

  const [batchId, setDataBatchId] = useState<string | undefined>();

  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const activeBatchDetail = urlParams.get("batchId");

    if (activeBatchDetail) {
      setDataBatchId(activeBatchDetail);
    } else {
      setDataBatchId(undefined);
    }
  }, [JSON.stringify(location.search)]);

  return (
    <Project router="/temp-storage">
      {canDataProceed && canViewDataset && isConfigPreProcessing ? (
        batchId ? (
          batchId?.match(regUUID) ? (
            <TempStorageWebSocketProvider>
              <BatchDetail batchId={batchId} />
            </TempStorageWebSocketProvider>
          ) : (
            <PermissionMessage />
          )
        ) : (
          <BatchList />
        )
      ) : (
        (canDataProceed === false ||
          canViewDataset === false ||
          isConfigPreProcessing === false) && <PermissionMessage />
      )}
    </Project>
  );
};

export default TemporaryStorage;

TemporaryStorage.title = "Temporary Storage";
TemporaryStorage.path = "/temp-storage";
TemporaryStorage.exact = true;
