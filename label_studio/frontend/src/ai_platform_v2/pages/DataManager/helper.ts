type ImageType = "Original";
export const getImageUrl = (
  moduleId: string,
  projectId: string,
  fileId: string,
  type?: ImageType
) => {
  const _type = type ? `&type=${type}` : "";

  return `/api/dm/image/?module=${moduleId}&project=${projectId}&file_id=${fileId}${_type}`;
};

export const getTempStorageUrl = (
  projectId: string,
  fileId: string,
  type?: ImageType
) => {
  const _type = type ? `?type=${type}` : "";

  return `/api/projects/${projectId}/temp_storage_batch_files/${fileId}/image${_type}`;
};

export const getIDWorkflowSecurity = (
  assignedMembersWorkflow: any,
  user: any,
  addNewNode: boolean,
  permissionProject: any
) => {
  const listID: string[] = [];

  const nameNode = Object.keys(assignedMembersWorkflow);
  // assignedMembersWorkflow.map((item) =>)

  nameNode.map((node) => {
    if (node === "Labeling" || node === "Inreview" || node === "Completed") {
      assignedMembersWorkflow[node].map((item) => {
        if (
          item.members_info.find(
            (n) => user.id === n.userId && permissionProject !== "AI Scientist"
          )
        )
          listID.push(item.id);
      });
    }
  });

  if (addNewNode) {
    const idNodeNew = "00000000-0000-0000-0000-000000000000";

    for (let i = 0; i < listID.length; i++) {
      const edgeNew = assignedMembersWorkflow?.Edges.find(
        (n: { source: string }) => n.source === idNodeNew
      );

      if (edgeNew) {
        if (edgeNew.target === listID[i]) {
          listID.push(idNodeNew);
          break;
        }
      }
    }
  }

  return listID;
};
