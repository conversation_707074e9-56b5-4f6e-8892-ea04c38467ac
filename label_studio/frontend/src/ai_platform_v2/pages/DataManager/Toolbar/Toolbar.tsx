import { Button, Input } from "@taureau/ui";
import IconFormatListBulleted from "@v2/assets/Icons/IconFormatListBulleted";
import IconViewModule from "@v2/assets/Icons/IconViewModule";
import { Popover, Segmented } from "antd";
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import IconAdd from "../../../assets/Icons/IconAdd";
import IconSaveAlt from "../../../assets/Icons/IconSaveAlt";
import { EnumDataManagerView } from "../consts";
//@ts-expect-error
import IconArrowDown from "@v2/assets/Icons/IconArrowDown";
import {
  getStateOptionColumnsByView,
  setColumnsLocalStorage,
} from "../../../../pages/DataSet/Const";
import { SortOut } from "./SortOut/SortOut";

import IconSearch from "@/ai_platform_v2/assets/Icons/IconSearch";
import useDMStore from "@/ai_platform_v2/stores/dataManagerStore";
import debounce from "lodash.debounce";
import ColumnsGroup, { CheckboxValueType } from "./ColumnsGroup";

import { useBatchImportData } from "@/ai_platform_v2/providers/BatchImportDataProvider";
import { useImportData } from "@/ai_platform_v2/providers/ImportDataProvider";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { useProject } from "@/providers/ProjectProvider";
import classNames from "classnames";
import { ExportData } from "../../ExportData/ExportData";
import { ProjectType } from "../../Home/utils/const";
import {
  getCurrentModule,
  getCurrentProject,
} from "../../SettingsProject/SettingsProject";
import Filter from "./Filter";
import "./Toolbar.scss";

interface Props {
  valueSortOut: any;
  setValueSortOut: any;
  valueOrder: any;
  setValueOrder: any;
  view: EnumDataManagerView;
  onChangeView: (view: EnumDataManagerView) => void;
  onChangeOpenFolderPane: any;
  fetchDataSets?: () => void;
  valueFilterPredictionStatus?: any;
  onClickFilterPredictionStatus?: (value: string) => void;
  valueFilterStatus: any;
  onClickFilterStatus: (statusArray: string | any[], status: any) => void;
  valueFilterArchive: boolean;
  onClickFilterArchive: () => void;
  valueFilterPriority: any;
  onClickFilterPriority?: (
    priorityArray: string | any[],
    priority: any
  ) => void;
  tagsProject: any;
  onAddFilterTags: any;
  onChangeTypeFilterTags: any;
  valueFilterTags: any;
  typeTags: any;
}
const Toolbar = forwardRef((props: Props, ref) => {
  const {
    valueSortOut,
    setValueSortOut,
    valueOrder,
    setValueOrder,
    view,
    onChangeView,
    onChangeOpenFolderPane,
    fetchDataSets,
    valueFilterPredictionStatus,
    onClickFilterPredictionStatus,
    valueFilterStatus,
    onClickFilterStatus,
    valueFilterArchive,
    onClickFilterArchive,
    valueFilterPriority,
    onClickFilterPriority,
    tagsProject,
    onAddFilterTags,
    onChangeTypeFilterTags,
    valueFilterTags,
    typeTags,
  } = props;

  const [
    viewColumns,
    setViewColumns,
    setSearchFilenameValue,
    openFolderPane,
    isLoading,
    dataSetsList,
    searchFileNameValue,

    assignmentConfig,
  ] = useDMStore((state) => [
    state.viewColumns,
    state.setViewColumns,
    state.setSearchFilenameValue,
    state.openFolderPane,
    state.isLoading,
    state.dataSetsList,
    state.searchFileNameValue,
    state.assignmentConfig,
  ]);

  const { projectDetail } = useProject();

  const isConfigAIPrediction = useMemo(() => {
    if (projectDetail?.annotationWorkFlow === undefined) return undefined;
    if (!projectDetail?.annotationWorkFlow) return false;

    const annotationWorkFlow = JSON.parse(
      projectDetail.annotationWorkFlow
    )?.node;

    if (annotationWorkFlow)
      return annotationWorkFlow.some(
        (step: any) => step.type === "AIPrediction"
      );

    return false;
  }, [projectDetail.annotationWorkFlow]);

  const importData = useImportData();
  const batchImportData = useBatchImportData();

  const {
    openImport,
    setFuncFetchDataSets,
    handleOpenImportModal: handleOpenDataImportModal,
    handleCloseImportModal,
  } = importData;

  const {
    batchImportState,
    handleOpenImportModal: handleOpenBatchImportModal,
  } = batchImportData;
  const { batchData, files } = batchImportState;

  const [optionColumns, setOptionColumns] = useState();
  const [searchValue, setSearchValue] = useState<string>("");
  const [openExport, setOpenExport] = useState(false);

  const [openFilter, setOpenFilter] = useState(false);

  const [isOpenColumns, setOpenColumns] = useState(false);

  const [isFocusSearch, setFocusSearch] = useState(false);

  const handleOpenImportModal = useCallback(() => {
    if (files?.length) {
      handleOpenBatchImportModal();
    } else {
      handleOpenDataImportModal();
    }
  }, [files.length, handleOpenDataImportModal, handleOpenBatchImportModal]);

  useEffect(() => {
    if (projectDetail)
      setOptionColumns(
        getStateOptionColumnsByView(
          view,
          assignmentConfig?.enableAutoAssign ||
            assignmentConfig?.enableManualAssign,
          projectDetail?.projectType === ProjectType.NonAnnotation
        )
      );
  }, [assignmentConfig, projectDetail]);

  const latestViewColumns = useMemo(() => {
    let result = [...viewColumns];

    if (projectDetail?.projectType === ProjectType.NonAnnotation) {
      result = result.filter(
        (column) =>
          column !== "Step" && column !== "Priority" && column !== "Tags"
      );
    }
    if (
      !assignmentConfig?.enableAutoAssign &&
      !assignmentConfig?.enableManualAssign
    ) {
      result = result.filter(
        (column) => column !== "Annotator" && column !== "Reviewer"
      );
    }
    if (view === EnumDataManagerView.Grid) {
      //remove File ID add "Path folder"
      return result.filter(
        (item) => item !== "File ID" && item !== "Path folder"
      );
    }

    // setViewColumns(result);
    return result;
  }, [view, viewColumns, assignmentConfig, projectDetail]);

  const { hasPermissionAllScope } = useCheckPermission();
  const canUpload = hasPermissionAllScope(
    ABILITY_NEW.can_upload_dataset,
    getCurrentModule(),
    getCurrentProject()
  );
  const canExport = hasPermissionAllScope(
    ABILITY_NEW.can_export_version,
    getCurrentModule(),
    getCurrentProject()
  );

  const handleCheckboxColumns = (columns: CheckboxValueType[] | undefined) => {
    if (!columns) return;

    setColumnsLocalStorage(columns as string[]);
    setViewColumns(columns as string[]);
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debounceUpdateSearchValueStore = useCallback(
    debounce((value: string) => {
      setSearchFilenameValue(value);
    }, 300),
    []
  );

  const onChangeSearchValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    setSearchValue(value);

    debounceUpdateSearchValueStore(value.trim());
  };

  const onBlurSearchValue = () => {
    if (searchValue) {
      setSearchValue(searchValue.trim());
    }
  };

  useImperativeHandle(ref, () => ({
    canUpload,
    setOpenImport: handleCloseImportModal,
  }));

  const countFilter = useCallback(() => {
    let count = valueFilterStatus.length + valueFilterPriority.length;

    if (valueFilterArchive) count = count + 1;

    return count;
  }, [
    valueFilterArchive,
    valueFilterPriority.length,
    valueFilterStatus.length,
  ]);

  useEffect(() => {
    if (openImport && canUpload) {
      // set fetchDataSets
      setFuncFetchDataSets(() => fetchDataSets);
    }
  }, [openImport, canUpload]);

  return (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center justify-between">
        <Filter
          showPredictionStatusOption={isConfigAIPrediction}
          valueFilterPredictionStatus={valueFilterPredictionStatus}
          onClickFilterPredictionStatus={onClickFilterPredictionStatus}
          valueFilterStatus={valueFilterStatus}
          onClickFilterStatus={onClickFilterStatus}
          valueFilterArchive={valueFilterArchive}
          onClickFilterArchive={onClickFilterArchive}
          valueFilterPriority={valueFilterPriority}
          onClickFilterPriority={onClickFilterPriority}
          value={searchValue}
          onChange={onChangeSearchValue}
          tagsProject={tagsProject}
          onAddFilterTags={onAddFilterTags}
          onChangeTypeFilterTags={onChangeTypeFilterTags}
          valueFilterTags={valueFilterTags}
          typeTags={typeTags}
          onBlurSearchValue={onBlurSearchValue}
        />
        <Popover
          trigger="click"
          placement="bottom"
          showArrow={false}
          onOpenChange={(value) => {
            setOpenColumns(value);
          }}
          content={
            <ColumnsGroup
              value={latestViewColumns}
              options={optionColumns}
              onChange={handleCheckboxColumns}
            />
          }
          overlayClassName="my-columns overflow-y-auto scrollbar-v-sm"
        >
          <Button
            active={isOpenColumns}
            className="!pr-5px mr-10px min-w-[130px] h-[40px] text-[14px] font-medium leading-[30px]"
            size="xs"
          >
            <div className="w-full flex items-center justify-between">
              <span>Columns</span>

              <div className="flex items-center justify-between">
                {latestViewColumns.length > 0 && (
                  <div className="w-[22px] h-[22px] bg-blue-blue  rounded-full flex items-center justify-center">
                    <span className="text-white text-[10px]  font-black">
                      {latestViewColumns.length}
                    </span>
                  </div>
                )}
                <IconArrowDown />
              </div>
            </div>
          </Button>
        </Popover>

        <SortOut
          style={{ marginRight: 10 }}
          value={valueSortOut}
          valueOrder={valueOrder}
          onSelect={(value: any) => setValueSortOut(value)}
          onOrder={(value: any) => setValueOrder(value)}
        />

        {!isLoading &&
        !openFolderPane &&
        !dataSetsList?.length &&
        !searchFileNameValue ? null : (
          <Button
            className="mr-10px h-[40px] text-[14px] font-medium leading-[30px] min-w-[120px]"
            size="xs"
            theme={!openFolderPane ? "Light" : "Primary"}
            onClick={() => {
              onChangeOpenFolderPane();
            }}
          >
            Folder Pane
          </Button>
        )}
      </div>

      <div className="flex items-center justify-between">
        {canUpload && (
          <Button
            className="mr-10px h-[40px] text-[14px] font-medium leading-[30px]"
            size="xs"
            iconLeft={<IconAdd />}
            theme="Primary"
            onClick={() => {
              !isLoading && handleOpenImportModal();
            }}
          >
            Import
          </Button>
        )}
        {canExport && (
          <Button
            className="mr-10px h-[40px] text-[14px] font-medium leading-[30px]"
            size="xs"
            iconLeft={<IconSaveAlt />}
            onClick={() => {
              setOpenExport(true);
            }}
          >
            Export
          </Button>
        )}

        <Segmented
          className="my-custom-segmented bg-[#E4E7F0] rounded-[5px] h-[40px]"
          // style={{
          //   backgroundColor: 'red'
          // }}
          value={view}
          onChange={(e) => {
            const newOptions = getStateOptionColumnsByView(
              e as any,
              assignmentConfig?.enableAutoAssign ||
                assignmentConfig?.enableManualAssign,
              projectDetail?.projectType === ProjectType.NonAnnotation
            );

            setOptionColumns(newOptions);
            onChangeView(e as any);
          }}
          options={[
            {
              value: EnumDataManagerView.Table,
              icon: <IconFormatListBulleted />,
            },
            {
              value: EnumDataManagerView.Grid,
              icon: <IconViewModule />,
            },
          ]}
        />
      </div>

      {canExport && (
        <ExportData
          open={openExport}
          onOpen={() => setOpenExport(true)}
          onClose={() => setOpenExport(false)}
        />
      )}
    </div>
  );
});

export default Toolbar;
