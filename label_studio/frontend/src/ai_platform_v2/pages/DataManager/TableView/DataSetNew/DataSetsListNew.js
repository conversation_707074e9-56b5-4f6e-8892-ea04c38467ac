import { Table, Tag, Tooltip } from "antd";
import { useParams } from "../../../../../providers/RoutesProvider";
import "./DataSetsList.css";

import Highlighter from "react-highlight-words";

import { memo, useCallback, useEffect, useMemo, useState } from "react";
import LoadImage from "../../../../../components/image/LoadImage";
import { error } from "../../../../../components_lse/Popup/Popup";
import { ASSIGNMENT_TYPE } from "../../../../../config/Config";
import { UserpicsV2 } from "../../../../component/Userpics/Userpics";

import IconAllInbox from "@/ai_platform_v2/assets/Icons/IconAllInbox";
import { ProjectType } from "@/ai_platform_v2/pages/Home/utils/const";
import { ModalConfirmBig } from "@taureau/ui";
import { convertUTCToLocalTime, DATE_TIME_FORMAT } from "@v2/utils/dateTime";
import classNames from "classnames";
import { difference, uniq } from "lodash";
import { useDrag } from "react-dnd";
import { getEmptyImage } from "react-dnd-html5-backend";
import { FaRegCheckCircle } from "react-icons/fa";
import { MdContentPaste, MdWidgets } from "react-icons/md";
import { RiEdit2Fill } from "react-icons/ri";
import FolderImage from "../../../../../components/image/FolderImage";
import { useCurrentUser } from "../../../../../providers/CurrentUser";
import {
  getImageStyleAfterEdit,
  validateImageType,
  validateZipType,
} from "../../../../../utils/helpers";
import useEditorModalStore from "../../../../component/EditorModal/store";
import useDMStore from "../../../../stores/dataManagerStore";
import { AssigneeColumn } from "./FilterColumn/AssigneeColumn";
import { getItem, StepColumn, stepIcons } from "./FilterColumn/StepColumn";
import { TagsColumn } from "./FilterColumn/TagsColumn";
import IconPreviewZip from "@/ai_platform_v2/assets/Icons/IconPreviewZip";
import IconPreviewFile from "@/ai_platform_v2/assets/Icons/IconPreviewFile";

export const IconAIPrediction = ({ color = "#33BFFF", size = 20 }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
    >
      <g filter="url(#filter0_i_17957_285754)">
        <rect width="20" height="20" rx="10" fill={color} />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.68689 3.33594C9.25424 3.41331 8.84453 3.64512 8.5493 4.03132C8.14755 4.55689 7.49517 4.82711 6.83947 4.73956C5.63432 4.57865 4.60577 5.60719 4.76669 6.81234C4.85423 7.46806 4.584 8.12044 4.05846 8.52219C3.99539 8.57039 3.93646 8.62164 3.88163 8.67555H5.55776L6.63809 9.75589L6.17412 10.2199L5.28597 9.33171H3.45355C3.17763 10.0611 3.37926 10.9344 4.05846 11.4536C4.584 11.8553 4.85423 12.5077 4.76669 13.1634C4.60577 14.3686 5.63432 15.3971 6.83947 15.2361C7.49517 15.1487 8.14755 15.4189 8.5493 15.9444C8.68775 16.1256 8.85137 16.2726 9.03074 16.3858V15.4293L10.015 14.4451V13.3879H8.76456L7.78032 12.2845H7.00625C6.87114 12.6667 6.50657 12.9406 6.07802 12.9406C5.53444 12.9406 5.09379 12.4999 5.09379 11.9564C5.09379 11.4128 5.53444 10.9721 6.07802 10.9721C6.50657 10.9721 6.87114 11.246 7.00625 11.6282H8.0743L9.05854 12.7317H10.015V11.786L8.37459 10.4736V9.7191L6.85336 8.73486V7.63531C6.47109 7.50019 6.19721 7.13562 6.19721 6.70707C6.19721 6.16348 6.63786 5.72283 7.18145 5.72283C7.72503 5.72283 8.16568 6.16348 8.16568 6.70707C8.16568 7.13562 7.8918 7.50019 7.50953 7.63531V8.37787L8.37459 8.93758V7.57623L9.68689 5.93582V3.33594ZM9.6865 16.6408C10.336 16.7569 11.0371 16.5252 11.4803 15.9453C11.8412 15.4732 12.4044 15.2072 12.9905 15.2215L11.8617 14.2539H10.6707V14.7179L9.6865 15.7021V16.6408ZM12.1046 13.5967L13.9515 15.1799C14.7915 14.9345 15.3885 14.1026 15.2631 13.1634C15.1756 12.5077 15.4457 11.8553 15.9713 11.4536C16.6505 10.9344 16.8522 10.0611 16.5762 9.33171H13.6236V8.67555H16.1481C16.0933 8.62164 16.0344 8.57039 15.9713 8.52219C15.4457 8.12044 15.1756 7.46806 15.2631 6.81234C15.424 5.60719 14.3955 4.57865 13.1903 4.73956C12.5346 4.82711 11.8822 4.55689 11.4805 4.03133C11.1853 3.64512 10.7755 3.41331 10.3429 3.33594V6.16599L9.28975 7.48242H10.6188L10.9469 6.37898H12.1126C12.2478 5.99671 12.6123 5.72283 13.0409 5.72283C13.5844 5.72283 14.0251 6.16348 14.0251 6.70707C14.0251 7.25065 13.5844 7.69131 13.0409 7.69131C12.6123 7.69131 12.2478 7.41742 12.1126 7.03515H11.4364L11.1082 8.13857H9.03057V9.6598H11.146L12.1302 10.7632H12.9043C13.0395 10.3809 13.404 10.1071 13.8326 10.1071C14.3762 10.1071 14.8168 10.5477 14.8168 11.0913C14.8168 11.6349 14.3762 12.0756 13.8326 12.0756C13.404 12.0756 13.0395 11.8017 12.9043 11.4194H11.8364L10.8521 10.316H9.22767L10.671 11.4706V13.5967H12.1046ZM13.041 6.3763C12.8598 6.3763 12.713 6.52319 12.713 6.70439C12.713 6.88558 12.8598 7.03247 13.041 7.03247C13.2222 7.03247 13.3691 6.88558 13.3691 6.70439C13.3691 6.52319 13.2222 6.3763 13.041 6.3763ZM13.833 11.4205C13.6518 11.4205 13.5049 11.2736 13.5049 11.0924C13.5049 10.9112 13.6518 10.7643 13.833 10.7643C14.0142 10.7643 14.1611 10.9112 14.1611 11.0924C14.1611 11.2736 14.0142 11.4205 13.833 11.4205ZM6.4063 11.9583C6.4063 11.7771 6.25941 11.6302 6.07822 11.6302C5.89702 11.6302 5.75014 11.7771 5.75014 11.9583C5.75014 12.1395 5.89702 12.2865 6.07822 12.2865C6.25941 12.2865 6.4063 12.1395 6.4063 11.9583ZM7.18174 7.03247C7.36293 7.03247 7.50982 6.88558 7.50982 6.70439C7.50982 6.52319 7.36293 6.3763 7.18174 6.3763C7.00054 6.3763 6.85365 6.52319 6.85365 6.70439C6.85365 6.88558 7.00054 7.03247 7.18174 7.03247Z"
          fill="white"
        />
      </g>
      <defs>
        <filter
          id="filter0_i_17957_285754"
          x="0"
          y="0"
          width="20"
          height="22"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="2.5" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.14902 0 0 0 0 0.2 0 0 0 0 0.301961 0 0 0 0.03 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_17957_285754"
          />
        </filter>
      </defs>
    </svg>
  );
};

const IconAIPredictionWithoutRounded = ({ color = "#33BFFF" }) => {
  return (
    <svg
      width="19"
      height="18"
      viewBox="0 0 19 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.57502 0C7.99103 0.104434 7.43801 0.417321 7.03952 0.938609C6.49725 1.64801 5.61668 2.01275 4.73162 1.89458C3.10494 1.67738 1.71663 3.06569 1.93383 4.69238C2.052 5.57745 1.68724 6.45802 0.977875 7.00029C0.892751 7.06534 0.813207 7.13452 0.739205 7.20728H3.0016L4.45981 8.66551L3.83354 9.29177L2.63474 8.09296H0.161391C-0.211047 9.0775 0.0611143 10.2562 0.977875 10.957C1.68724 11.4992 2.052 12.3798 1.93383 13.2649C1.71663 14.8916 3.10494 16.2798 4.73162 16.0626C5.61668 15.9446 6.49725 16.3093 7.03952 17.0186C7.22639 17.2632 7.44724 17.4617 7.68935 17.6145V16.3234L9.01786 14.9949V13.5679H7.33007L6.00157 12.0785H4.95674C4.77437 12.5945 4.28229 12.9642 3.70384 12.9642C2.97013 12.9642 2.37534 12.3694 2.37534 11.6357C2.37534 10.9019 2.97013 10.3071 3.70384 10.3071C4.28229 10.3071 4.77437 10.6768 4.95674 11.1928H6.39837L7.72688 12.6821H9.01786V11.4057L6.80369 9.63431V8.61585L4.75038 7.28735V5.80319C4.2344 5.62082 3.86471 5.12872 3.86471 4.55028C3.86471 3.81656 4.4595 3.22177 5.19322 3.22177C5.92694 3.22177 6.52172 3.81656 6.52172 4.55028C6.52172 5.12872 6.15204 5.62082 5.63606 5.80319V6.80549L6.80369 7.56097V5.72345L8.57502 3.50927V0ZM8.57478 17.9646C9.45141 18.1213 10.3978 17.8086 10.996 17.0259C11.4831 16.3886 12.2433 16.0296 13.0344 16.0489L11.5109 14.7429H9.90328V15.3691L8.57478 16.6976V17.9646ZM11.8405 13.8498L14.3333 15.9867C15.4671 15.6555 16.273 14.5326 16.1037 13.2649C15.9856 12.3798 16.3502 11.4992 17.0596 10.957C17.9764 10.2562 18.2487 9.0775 17.8761 8.09296H13.8908V7.20728H17.2983C17.2243 7.13452 17.1448 7.06534 17.0596 7.00029C16.3502 6.45802 15.9856 5.57745 16.1037 4.69238C16.3209 3.06569 14.9326 1.67738 13.3059 1.89458C12.4209 2.01275 11.5403 1.64801 10.998 0.938626C10.5995 0.417321 10.0465 0.104434 9.46253 0V3.81994L8.04102 5.59683H9.83491L10.2778 4.10744H11.8513C12.0337 3.59146 12.5258 3.22177 13.1042 3.22177C13.8379 3.22177 14.4327 3.81656 14.4327 4.55028C14.4327 5.284 13.8379 5.87878 13.1042 5.87878C12.5258 5.87878 12.0337 5.5091 11.8513 4.99312H10.9385L10.4956 6.48249H7.69118V8.5358H10.5466L11.875 10.0252H12.9199C13.1024 9.5092 13.5944 9.13951 14.1728 9.13951C14.9065 9.13951 15.5013 9.7343 15.5013 10.468C15.5013 11.2018 14.9065 11.7965 14.1728 11.7965C13.5944 11.7965 13.1024 11.4269 12.9199 10.9109H11.4784L10.1498 9.42147H7.95723L9.90535 10.98V13.8498H11.8405ZM13.1011 4.10066C12.8565 4.10066 12.6584 4.29893 12.6584 4.5435C12.6584 4.78808 12.8565 4.98634 13.1011 4.98634C13.3456 4.98634 13.544 4.78808 13.544 4.5435C13.544 4.29893 13.3456 4.10066 13.1011 4.10066ZM14.1704 10.9129C13.9258 10.9129 13.7275 10.7145 13.7275 10.4699C13.7275 10.2254 13.9258 10.0271 14.1704 10.0271C14.4149 10.0271 14.6132 10.2254 14.6132 10.4699C14.6132 10.7145 14.4149 10.9129 14.1704 10.9129ZM4.14913 11.6318C4.14913 11.3873 3.95086 11.189 3.70629 11.189C3.46171 11.189 3.26344 11.3873 3.26344 11.6318C3.26344 11.8764 3.46171 12.0747 3.70629 12.0747C3.95086 12.0747 4.14913 11.8764 4.14913 11.6318ZM5.19278 4.98634C5.43735 4.98634 5.63562 4.78808 5.63562 4.5435C5.63562 4.29893 5.43735 4.10066 5.19278 4.10066C4.9482 4.10066 4.74994 4.29893 4.74994 4.5435C4.74994 4.78808 4.9482 4.98634 5.19278 4.98634Z"
        fill={color}
      />
    </svg>
  );
};

export const DataSetsList = ({
  project,
  dataSets,

  viewColumns,
  listAttribute,

  assignedMembersWorkflow,
  membersWorkflow,
  assignmentConfig,
  handleTaskDoneTotalTask,
  // setIndexFile,

  getRequestParams,
  retryAIPrediction,
  tagsProject,
}) => {
  const params = useParams();

  const [searchText] = useState("");
  const [searchedColumn] = useState(false);

  const isActiveProject = useMemo(
    () => project?.status === "Active",
    [project?.status]
  );

  const isAnnotationProject = useMemo(
    () => project?.projectType !== ProjectType.NonAnnotation,
    [project?.projectType]
  );

  const [valueStep, setValueStep] = useState([]);
  const [valueAnnotator, setValueAnnotator] = useState([]);
  const [valueReviewer, setValueReviewer] = useState([]);

  const [valueTag, setValueTag] = useState([]);
  const [relation, setRelation] = useState("or");

  const { user } = useCurrentUser();

  const {
    listSelect,
    setListSelect,
    setPathFolder,
    viewFolderPane,
    pathFolder,
    isLoading,
    openFolderPane,
    setReFreshData,
    workflow,
  } = useDMStore((state) => ({
    listSelect: state.listSelect,
    setListSelect: state.setListSelect,
    setPathFolder: state.setPathFolder,
    viewFolderPane: state.viewFolderPane,
    pathFolder: state.pathFolder,
    isLoading: state.isLoading,
    openFolderPane: state.openFolderPane,
    setReFreshData: state.setReFreshData,

    workflow: state.workflow,
  }));

  const {
    setOpenEditor,
    setCurrentFile,
    setIsHideScreen,
    setFetchUrl,
    setFetchParams,
    setInitEditor,
    setIsDataManager,
    setPrevPointer,
    setNextPointer,

    isHideScreen,
    setLastFileId,
  } = useEditorModalStore((state) => ({
    setOpenEditor: state.setOpenEditor,
    setCurrentFile: state.setCurrentFile,
    setIsHideScreen: state.setIsHideScreen,
    setFetchUrl: state.setFetchUrl,
    setFetchParams: state.setFetchParams,
    setInitEditor: state.setInitEditor,
    setIsDataManager: state.setIsDataManager,
    setPrevPointer: state.setPrevPointer,
    setNextPointer: state.setNextPointer,
    isHideScreen: state.isHideScreen,
    setLastFileId: state.setLastFileId,
  }));

  const [dataList, setDataList] = useState([]);

  const assignedMembersWorkflowListItem = useMemo(() => {
    const labeling = assignedMembersWorkflow?.Labeling?.map((step) =>
      getItem(step.name, step.id, stepIcons.labeling, [], "labeling")
    );
    const inreview = assignedMembersWorkflow?.Inreview?.map((step) =>
      getItem(step.name, step.id, stepIcons.reviewing, [], "reviewing")
    );

    const aiPrediction = assignedMembersWorkflow?.AIPrediction?.map((step) =>
      getItem(step.name, step.id, stepIcons.aiPrediction, [], "AIPrediction")
    );

    const labelingFinal =
      labeling?.length > 1
        ? getItem(
            "Labeling",
            "labeling",
            stepIcons.labeling,
            labeling,
            "labeling"
          )
        : labeling?.[0];

    const inreviewFinal =
      inreview?.length > 1
        ? getItem(
            "Reviewing",
            "reviewing",
            stepIcons.reviewing,
            inreview,
            "reviewing"
          )
        : inreview?.[0];

    const aiPredictionFinal =
      aiPrediction?.length > 1
        ? getItem(
            "AI Prediction",
            "AIPrediction",
            stepIcons.aiPrediction,
            aiPrediction,
            "AIPrediction"
          )
        : aiPrediction?.[0];

    const finalListItem = [
      getItem(
        "New",
        "00000000-0000-0000-0000-000000000000",
        stepIcons.new,
        [],
        "new"
      ),
      aiPredictionFinal,
      labelingFinal,
      inreviewFinal,
      getItem(
        "Completed",
        assignedMembersWorkflow?.Completed?.[0]?.id,
        stepIcons.completed,
        [],
        "completed"
      ),
    ].filter((item) => item);

    return finalListItem;
  }, [assignedMembersWorkflow]);

  let columns = [
    {
      title: "Preview",
      dataIndex: "img",
      key: "img",
      width: "130px",
      render: (text, record, i) => {
        if (record?.File) {
          const isAIPredictionStep =
            record.state.split("|")[0] === "AIPrediction";
          const isFailed = record.stepStatus === "Failed";

          const cropInfo = record?.File?.cropInfo
            ? JSON.parse(JSON.parse(record?.File?.cropInfo))
            : null;
          const isCrop = record?.File?.isCrop ?? false;

          const handleClick = () => {
            setCurrentFile(record.File.fileId);
            setLastFileId(undefined);
            setIsHideScreen(false);
            setFetchUrl("dataManagersSecurity");
            setFetchParams(getRequestParams());
            setPrevPointer(getRequestParams().page);
            setNextPointer(getRequestParams().page);
            setInitEditor(true);
            setIsDataManager(true);
            setOpenEditor(true);
          };

          const onClickImage = () => {
            if (isAnnotationProject) {
              if (isActiveProject) {
                if (project.annotationWorkFlow) {
                  if (isHideScreen) {
                    ModalConfirmBig.warning({
                      title: "Open new file?",
                      content:
                        "Editor has another file open, you will lost your current work if continue.",
                      okText: "Continue",
                      onOk: handleClick,
                    });
                  } else {
                    handleClick();
                  }
                } else {
                  error({
                    title: "You haven't created workflow for this project",
                  });
                }
              } else {
                error({
                  title:
                    "Cannot perform this action because your project is inactive now",
                });
              }
            }
          };

          const renderImageError = () => {
            return (
              <div
                className="no-image-preview w-[100px] h-[70px] text-[11px]"
                onClick={onClickImage}
              >
                Error preview
              </div>
            );
          };

          const renderAIPredictionStatus = () => {
            return (
              <div
                className="absolute top-0 bottom-0 left-0 right-0 bg-[#0000004D] flex justify-center items-center rounded-[5px]"
                onClick={onClickImage}
              >
                <IconAIPrediction color={isFailed ? "#CC1414" : "#33BFFF"} />
              </div>
            );
          };

          const getIconFileType = () => {
            if (validateZipType(record.FileName)) {
              return (
                <div className="flex h-[85px] justify-center items-center">
                  <IconPreviewZip />
                </div>
              );
            } else if (validateImageType(record.FileName)) {
              return (
                <LoadImage
                  onClickImage={onClickImage}
                  style={{
                    height: "80px",
                    width: "100px",
                    objectFit: "cover",
                    borderRadius: 5,
                    ...getImageStyleAfterEdit(cropInfo, isCrop),
                  }}
                  src={`/api/dm/image/?module=${params.moduleId}&project=${params.projectId}&file_id=${record.File.fileId}`}
                  alt=""
                  rederImageError={renderImageError}
                />
              );
            }

            return (
              <div className="flex h-[85px] justify-center items-center">
                <IconPreviewFile />
              </div>
            );
          };

          return (
            <div className="relative max-h-[100px] w-[100px] rounded-[5px] cursor-pointer overflow-hidden">
              {isAIPredictionStep &&
              record.stepStatus &&
              !["Completed", "New", "Pending"].includes(record.stepStatus)
                ? renderAIPredictionStatus()
                : null}
              {getIconFileType()}
            </div>
          );
        } else {
          return (
            <FolderImage
              id={
                viewFolderPane === "folder-path"
                  ? record.folder.concat(record.key, "/")
                  : record.key
              }
              setPathFolder={setPathFolder}
            />
          );
        }
      },
    },
  ];

  for (let i = 0; i < viewColumns.length; i++) {
    if (viewColumns[i] === "File ID") {
      columns.push({
        title: "File ID",
        dataIndex: "key",
        key: "key",
        width: "200px",
      });
    } else if (viewColumns[i] === "File name") {
      columns.push({
        title: "File Name",
        dataIndex: "filename",
        key: "filename",
        width: "200px",
        sorter: (a, b) => a.FileName.localeCompare(b.FileName),
        render: (text, record) =>
          searchedColumn ? (
            <Highlighter
              highlightStyle={{
                backgroundColor: "#ffc069",
                padding: 0,
              }}
              searchWords={[searchText]}
              autoEscape
              textToHighlight={
                record.FileName ? record.FileName.toString() : ""
              }
            />
          ) : (
            <Tooltip title={record.FileName}>
              <p
                style={{
                  display: "-webkit-box",
                  "-webkit-line-clamp": "3",
                  "-webkit-box-orient": "vertical",
                  overflow: "hidden",
                }}
              >
                {record.FileName}
              </p>
            </Tooltip>
          ),
      });
    } else if (viewColumns[i] === "Type") {
      columns.push({
        title: "Type",
        dataIndex: "dataType",
        key: "dataType",
        width: "100px",
      });
    } else if (viewColumns[i] === "Size") {
      columns.push({
        title: "Size",
        dataIndex: "fileSize",
        key: "fileSize",
        width: "100px",
      });
    } else if (viewColumns[i] === "Path folder") {
      columns.push({
        title: "Path folder",
        dataIndex: "folder",
        key: "folder",
        width: "200px",
      });
    } else if (viewColumns[i] === "Step" && isAnnotationProject) {
      columns.push({
        title: (
          <StepColumn
            items={assignedMembersWorkflowListItem}
            value={valueStep}
            onSelect={(value) => setValueStep(value)}
          />
        ),

        dataIndex: "step",
        key: "step",
        width: "200px",
        render: (text, record) => {
          if (record?.state) {
            let color = "";
            let icon = <></>;

            if (record.state.split("|")[0] === "Completed") {
              color = "rgb(41, 204, 57)";
              icon = (
                <FaRegCheckCircle
                  style={{ display: "inline-block", fontSize: 18 }}
                />
              );
            }
            if (record.state.split("|")[0] === "New") {
              color = "rgb(46, 230, 202)";
              icon = (
                <MdWidgets size={18} style={{ display: "inline-block" }} />
              );
            }
            if (record.state.split("|")[0] === "Labeling") {
              color = "rgb(51, 97, 255)";
              icon = (
                <RiEdit2Fill size={18} style={{ display: "inline-block" }} />
              );
            }
            if (record.state.split("|")[0] === "Inreview") {
              color = "rgb(255, 203, 51)";
              icon = (
                <MdContentPaste size={18} style={{ display: "inline-block" }} />
              );
            }
            if (record.state.split("|")[0] === "Archive") {
              return (
                <div className="flex items-center gap-[5px]">
                  <IconAllInbox />
                  <span className="text-[#CC1414]">Archived</span>
                </div>
              );
            }
            if (record.state.split("|")[0] === "AIPrediction") {
              const isFailed = record.stepStatus === "Failed";
              const { AIPrediction } = assignedMembersWorkflow;

              const { currentStep } = record;

              const currentNode = workflow?.find(
                (item) => item.id === currentStep?.stepId
              );

              const isManual = currentNode?.settings?.manualPrediction;

              const listMemberHavePermission = AIPrediction?.find(
                (item) => item.id === currentStep?.stepId
              )?.members_info;

              const hasPermissionAIPrediction =
                listMemberHavePermission?.findIndex(
                  (item) => item.userId === user.id
                ) !== -1;

              return (
                <div className="flex items-center gap-[5px]">
                  <div className="min-w-[20px]">
                    <IconAIPredictionWithoutRounded
                      color={isFailed ? "#CC1414" : "#33BFFF"}
                    />
                  </div>
                  <span
                    className={classNames("", {
                      "text-[#CC1414]": isFailed,
                      "text-[#33BFFF]": !isFailed,
                    })}
                  >
                    {`${record.state.split("|")[1]} ${
                      record.stepStatus &&
                      !["Completed", "New"].includes(record.stepStatus)
                        ? "| " + record.stepStatus
                        : ""
                    }`}
                  </span>
                  {isFailed && hasPermissionAIPrediction && isManual ? (
                    <div
                      className="cursor-pointer"
                      onClick={() => retryAIPrediction(record?.File)}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="11"
                        height="12"
                        viewBox="0 0 11 12"
                        fill="none"
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M9.38272 2.11147C8.26095 0.989704 6.67121 0.342794 4.92317 0.521726C2.39747 0.776361 0.319095 2.82721 0.0369316 5.35291C-0.34158 8.69069 2.23918 11.5054 5.49438 11.5054C7.68975 11.5054 9.57542 10.2185 10.4563 8.36724C10.6765 7.90614 10.3462 7.37623 9.83694 7.37623C9.5823 7.37623 9.34143 7.51387 9.23132 7.74097C8.45365 9.41331 6.58862 10.4731 4.55154 10.0189C3.02373 9.6817 1.79185 8.43606 1.46839 6.90825C0.890303 4.23802 2.9205 1.8706 5.49438 1.8706C6.6368 1.8706 7.65534 2.34546 8.39859 3.0956L7.35941 4.13479C6.92584 4.56836 7.22865 5.31162 7.84115 5.31162H10.3118C10.6903 5.31162 11 5.00193 11 4.62341V2.15277C11 1.54027 10.2567 1.23058 9.82317 1.66414L9.38272 2.11147Z"
                          fill="#CC1414"
                        />
                      </svg>
                    </div>
                  ) : null}
                </div>
              );
            }

            return (
              <div
                style={{ display: "inline-flex", alignItems: "center", color }}
              >
                {icon}
                <span style={{ marginLeft: 5, fontWeight: 500, size: 12 }}>
                  {record.state.split("|")[1]}
                </span>
              </div>
            );
          } else {
            return <div>--</div>;
          }
        },
      });
    } else if (viewColumns[i] === "Priority" && isAnnotationProject) {
      columns.push({
        title: "Priority",
        dataIndex: "priority",
        key: "priority",
        width: "100px",
        sorter: (a, b) => parseInt(a.priority) - parseInt(b.priority),
        render: (text, record) => {
          if (record.priority) {
            let backgroundColor = "";
            let color = "#FFF";
            let borderColor = "#FFF";

            switch (record.priority) {
              case "0":
                backgroundColor = "#E62E2E";
                break;
              case "1":
                backgroundColor = "#E95F5F";
                break;
              case "2":
                backgroundColor = "#FFA3A3";
                break;
              case "3":
                backgroundColor = "#FFC8C8";
                break;
              case "4":
                color = "#ADB8CC";
                borderColor = "#DFE3EB";
                backgroundColor = "#F0F0F0";
                break;
            }

            return (
              <Tag
                color={backgroundColor}
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  width: 32,
                  border: `solid 2px ${borderColor}`,
                  borderRadius: 5,
                  padding: "4px 10px",
                  boxShadow: "0px 2px 10px 0px rgba(38, 51, 77, 0.10)",
                }}
              >
                <span style={{ fontWeight: 900, color }}>
                  {parseInt(record.priority) + 1}
                </span>
              </Tag>
            );
          } else {
            return <div>--</div>;
          }
        },
      });
    } else if (viewColumns[i] === "Date Added") {
      columns.push({
        title: "Date Added",
        dataIndex: "dateadded",
        key: "dateadded",
        width: "200px",
        sorter: (a, b) => new Date(a.dateadded) - new Date(b.dateadded),
        render: (text, record) =>
          searchedColumn ? (
            <Highlighter
              highlightStyle={{
                backgroundColor: "#ffc069",
                padding: 0,
              }}
              searchWords={[searchText]}
              autoEscape
              textToHighlight={
                record.dateadded ? record.dateadded.toString() : ""
              }
            />
          ) : (
            record.dateadded
          ),
      });
    } else if (viewColumns[i] === "Date Modified") {
      columns.push({
        title: "Date Modified",
        dataIndex: "datemodified",
        key: "datemodified",
        width: "200px",
        sorter: (a, b) => new Date(a.datemodified) - new Date(b.datemodified),
        render: (text, record) =>
          searchedColumn ? (
            <Highlighter
              highlightStyle={{
                backgroundColor: "#ffc069",
                padding: 0,
              }}
              searchWords={[searchText]}
              autoEscape
              textToHighlight={
                record.datemodified ? record.datemodified.toString() : ""
              }
            />
          ) : (
            record.datemodified
          ),
      });
    } else if (viewColumns[i] === "Tags" && isAnnotationProject) {
      columns.push({
        title: (
          <TagsColumn
            tagsProject={tagsProject}
            value={valueTag}
            onSelect={(value) => setValueTag(value)}
            relation={relation}
            setRelation={setRelation}
          />
        ),
        dataIndex: "tags",
        key: "tags",
        width: "230px",
        render: (text, record) => {
          const splittedTags = record?.tags?.split(",")?.filter((tag) => tag);

          if (splittedTags?.length && tagsProject) {
            return (
              <div className="flex w-full gap-[4px] flex-shrink-0 self-stretch flex-wrap max-h-[79px] overflow-y-auto">
                {splittedTags.map((tag) => {
                  if (
                    tag.trim() &&
                    tagsProject?.find((n) => n.id === tag.trim())?.tagName
                  )
                    return (
                      <div
                        key={tag}
                        className="rounded-full px-[6px] py-[2px] bg-gray-blue-98 justify-center items-center w-fit max-w-full overflow-hidden overflow-ellipsis whitespace-nowrap"
                      >
                        {tagsProject?.find((n) => n.id === tag.trim())?.tagName}
                      </div>
                    );
                })}
              </div>
            );
          } else {
            return <div>--</div>;
          }
        },
      });
    } else if (
      viewColumns[i] === "Annotator" &&
      (assignmentConfig?.enableAutoAssign ||
        assignmentConfig?.enableManualAssign)
    ) {
      columns.push({
        title: (
          <AssigneeColumn
            title="Annotator"
            value={valueAnnotator}
            onSelect={(item) => {
              setValueAnnotator(item);
            }}
            users={
              membersWorkflow?.Annotator?.map((member) => ({
                ...member,
                avatar: !member.avatar || member.avatar_url,
              })) ?? []
            }
          />
        ),
        dataIndex: "shortTaskAssignments",
        key: "annotator",
        width: "230px",
        render: (text, record) => {
          if (record?.shortTaskAssignments) {
            const assignees =
              membersWorkflow?.Annotator?.map((member) => ({
                ...member,
                avatar: !member.avatar || member.avatar_url,
              })) ?? [];

            const annotators = record.shortTaskAssignments.filter(
              (workFlowStep) =>
                workFlowStep.assignmentType === ASSIGNMENT_TYPE.labeling ||
                workFlowStep.assignmentType === 0
            );
            let finalAnnotators = assignedMembersWorkflow?.Labeling?.map(
              (finalStep) => {
                const info = {
                  fileId: record?.File?.fileId,
                  fileName: record?.File?.fileName,
                  projectId: record?.File?.projectId,
                  stepId: finalStep.id,
                  stepName: finalStep.name,
                  members_info: finalStep.members_info,
                };

                const userAssigned = annotators.find(
                  (step) => step.workFlowStepId === finalStep.id
                );

                if (userAssigned) {
                  const userAssignedInfo = assignees?.find(
                    (memberInfo) =>
                      memberInfo.userId === userAssigned.userAssigned
                  );

                  if (userAssignedInfo) {
                    info.members_info = info.members_info.filter(
                      (member) => !(member.userId === userAssignedInfo.userId)
                    );
                    return { ...userAssigned, ...userAssignedInfo, ...info };
                  }

                  return { ...userAssigned, username: "Admin", ...info };
                }

                return { ...finalStep, ...info };
              }
            );

            if (
              record?.currentStep?.task?.assignmentType ===
              ASSIGNMENT_TYPE.labeling
            ) {
              const currentStepId = record.currentStep.stepId;

              finalAnnotators = finalAnnotators?.map((step) =>
                step.id === currentStepId ||
                step.workFlowStepId === currentStepId
                  ? { ...step, isCurrentStep: true }
                  : step
              );
            }

            return (
              <UserpicsV2
                assignmentConfig={assignmentConfig}
                onRefetchData={() => {
                  setReFreshData(true);
                }}
                assignees={assignees}
                users={finalAnnotators ?? []}
                backgroundColor="#fff"
                useRandomBackground
                showUsername
                workFlowType="annotator"
                handleTaskDoneTotalTask={handleTaskDoneTotalTask}
              />
            );
          } else {
            return <div>--</div>;
          }
        },
      });
    } else if (
      viewColumns[i] === "Reviewer" &&
      (assignmentConfig?.enableAutoAssign ||
        assignmentConfig?.enableManualAssign)
    ) {
      columns.push({
        title: (
          <AssigneeColumn
            title="Reviewer"
            value={valueReviewer}
            onSelect={(item) => {
              setValueReviewer(item);
            }}
            users={
              membersWorkflow?.Reviewer?.map((member) => ({
                ...member,
                avatar: !member.avatar || member.avatar_url,
              })) ?? []
            }
          />
        ),
        dataIndex: "reviewer",
        key: "reviewer",
        width: "230px",
        render: (text, record) => {
          if (record?.shortTaskAssignments) {
            const assignees =
              membersWorkflow?.Reviewer?.map((member) => ({
                ...member,
                avatar: !member.avatar || member.avatar_url,
              })) ?? [];

            const reviewers = record.shortTaskAssignments.filter(
              (workFlowStep) =>
                workFlowStep.assignmentType === ASSIGNMENT_TYPE.inreview ||
                workFlowStep.assignmentType === 1
            );
            let finalReviewers = assignedMembersWorkflow?.Inreview?.map(
              (finalStep) => {
                const info = {
                  fileId: record?.File?.fileId,
                  fileName: record?.File?.fileName,
                  projectId: record?.File?.projectId,
                  stepId: finalStep.id,
                  stepName: finalStep.name,
                  members_info: finalStep.members_info,
                };

                const userAssigned = reviewers.find(
                  (step) => step.workFlowStepId === finalStep.id
                );

                if (userAssigned) {
                  const userAssignedInfo = assignees?.find(
                    (memberInfo) =>
                      memberInfo.userId === userAssigned.userAssigned
                  );

                  if (userAssignedInfo) {
                    info.members_info = info.members_info.filter(
                      (member) => !(member.userId === userAssignedInfo.userId)
                    );
                    return { ...userAssigned, ...userAssignedInfo, ...info };
                  }

                  return { ...userAssigned, username: "Admin", ...info };
                }

                return { ...finalStep, ...info };
              }
            );

            if (
              record?.currentStep?.task?.assignmentType ===
              ASSIGNMENT_TYPE.inreview
            ) {
              const currentStepId = record.currentStep.stepId;

              finalReviewers = finalReviewers?.map((step) =>
                step.id === currentStepId ||
                step.workFlowStepId === currentStepId
                  ? { ...step, isCurrentStep: true }
                  : step
              );
            }

            return (
              <UserpicsV2
                assignmentConfig={assignmentConfig}
                onRefetchData={() => {
                  setReFreshData(true);
                }}
                assignees={assignees}
                users={finalReviewers ?? []}
                backgroundColor="#fff"
                useRandomBackground
                showUsername
                workFlowType="review"
                handleTaskDoneTotalTask={handleTaskDoneTotalTask}
              />
            );
          } else {
            return <div>--</div>;
          }
        },
      });
    }

    for (let k = 0; k < listAttribute?.length; k++) {
      if (viewColumns[i] === listAttribute[k].displayName) {
        columns.push({
          title: viewColumns[i],
          dataIndex: viewColumns[i],
          key: viewColumns[i],
          width: "200px",
        });
      }
    }
  }

  useEffect(() => {
    let data = [];

    if (dataSets) {
      if (
        viewFolderPane === "folder-path" ||
        pathFolder === "/" ||
        (!openFolderPane && viewFolderPane !== "folder-path")
      ) {
        for (let i = 0; i < dataSets.length; i++) {
          let ind_folder = 0;
          let ind_priority = 3;
          let ind_state = 1;
          let ind_step = 6;
          let ind_archive;
          let isFolder = false;

          if (!dataSets[i]?.fileId) isFolder = true;

          if (!isFolder) {
            if (dataSets?.[i]?.attributes) {
              for (let j = 0; j < dataSets[i].attributes.length; j++) {
                if (dataSets[i].attributes[j].code === "view_folder") {
                  ind_folder = j;
                }
                if (dataSets[i].attributes[j].code === "view_priority") {
                  ind_priority = j;
                }
                if (dataSets[i].attributes[j].code === "view_state") {
                  ind_state = j;
                }
                if (dataSets[i].attributes[j].code === "view_workflow_step") {
                  ind_step = j;
                }
                if (dataSets[i].attributes[j].code === "view_archive") {
                  if (dataSets[i].attributes[j].value === "true") {
                    ind_archive = j;
                  }
                }
              }

              let state_step = ind_archive
                ? "Archive"
                : dataSets[i]?.attributes[ind_state]
                  ? dataSets[i]?.attributes[ind_state]?.value.concat(
                      "|",
                      dataSets[i]?.attributes[ind_step]?.value
                    )
                  : dataSets?.[i]?.viewState.concat(
                      "|",
                      dataSets?.[i]?.viewWorkFlowStep
                    );

              let data_columns = {
                key: dataSets[i].fileId,
                dataType: dataSets[i].dataType,
                File: dataSets[i],
                folder:
                  dataSets[i]?.attributes[ind_folder]?.value ??
                  dataSets[i]?.pathFolder,
                state: state_step,
                FileName: dataSets[i].fileName,
                dateadded: dataSets[i]?.dateCreated
                  ? convertUTCToLocalTime(
                      dataSets[i]?.dateCreated,
                      DATE_TIME_FORMAT.DDMMYYYY_HHMM
                    )
                  : convertUTCToLocalTime(
                      dataSets[i]?.createdAt,
                      DATE_TIME_FORMAT.DDMMYYYY_HHMM
                    ),
                datemodified: dataSets[i]?.dateModified
                  ? convertUTCToLocalTime(
                      dataSets[i]?.dateModified,
                      DATE_TIME_FORMAT.DDMMYYYY_HHMM
                    )
                  : convertUTCToLocalTime(
                      dataSets[i]?.updatedAt,
                      DATE_TIME_FORMAT.DDMMYYYY_HHMM
                    ),
                fileSize: dataSets[i]?.fileSize,
                priority:
                  dataSets[i]?.attributes[ind_priority]?.value ??
                  dataSets[i].priority,
                shortTaskAssignments: dataSets[i]?.shortTaskAssignments,
                currentStep: dataSets[i]?.currentStep,
                stepStatus: dataSets[i]?.stepStatus,
                tags: dataSets[i].projectTags,
              };

              for (let k = 0; k < dataSets[i].attributes.length; k++) {
                if (dataSets[i].attributes[k].code.split("_")[0] === "meta") {
                  data_columns[dataSets[i].attributes[k].attributeName] =
                    dataSets[i].attributes[k].value;
                }
              }
              data.push(data_columns);

              if (
                valueAnnotator.length ||
                valueReviewer.length ||
                valueStep.length ||
                valueTag.length
              ) {
                data = data?.filter((item) => {
                  let step = true;
                  let annotator = true;
                  let reviewer = true;
                  let tag = true;

                  if (valueStep.length) {
                    step = valueStep.includes(item?.currentStep?.stepId);
                  }

                  if (valueAnnotator.length) {
                    annotator = !!item?.shortTaskAssignments?.find(
                      (task) =>
                        valueAnnotator.find(
                          (assignee) => assignee?.userId === task.userAssigned
                        ) && task.assignmentType === "Labeling"
                    );
                  }

                  if (valueReviewer.length) {
                    reviewer = !!item?.shortTaskAssignments?.find(
                      (task) =>
                        valueReviewer.find(
                          (assignee) => assignee?.userId === task.userAssigned
                        ) && task.assignmentType === "Inreview"
                    );
                  }

                  if (valueTag.length) {
                    if (relation === "or") {
                      tag = valueTag.some(
                        (n) =>
                          item?.tags?.split(", ")?.includes(n) ||
                          item?.tags?.split(",")?.includes(n)
                      );
                    } else {
                      tag = valueTag.every(
                        (n) =>
                          item?.tags?.split(", ")?.includes(n) ||
                          item?.tags?.split(",")?.includes(n)
                      );
                    }
                  }

                  return step && annotator && reviewer && tag;
                });
              }
            }
          } else {
            let data_columns = {
              key: dataSets[i].name,
              FileName: dataSets[i].name,
              detail: dataSets[i],
              dateadded: dataSets[i]?.dateCreated
                ? convertUTCToLocalTime(
                    dataSets[i]?.dateCreated,
                    DATE_TIME_FORMAT.DDMMYYYY_HHMM
                  )
                : "--",
              datemodified: dataSets[i]?.dateModified
                ? convertUTCToLocalTime(
                    dataSets[i]?.dateModified,
                    DATE_TIME_FORMAT.DDMMYYYY_HHMM
                  )
                : "--",
              folder: dataSets[i].filterPath,
              tags: dataSets[i].projectTags,
            };

            data.push(data_columns);
          }
        }
      } else {
        for (let i = 0; i < dataSets.length; i++) {
          if (dataSets[i]?.createdAt) {
            let data_columns = {
              key: dataSets[i].fileId,
              dataType: dataSets[i].dataType,
              File: dataSets[i],
              folder: dataSets[i]?.pathFolder,
              state: dataSets[i]?.viewState?.concat(
                "|",
                dataSets[i]?.viewWorkFlowStep
              ),
              FileName: dataSets[i].fileName,
              dateadded: dataSets[i]?.createdAt
                ? convertUTCToLocalTime(
                    dataSets[i]?.createdAt,
                    DATE_TIME_FORMAT.DDMMYYYY_HHMM
                  )
                : "--",
              datemodified: dataSets[i]?.updatedAt
                ? convertUTCToLocalTime(
                    dataSets[i]?.updatedAt,
                    DATE_TIME_FORMAT.DDMMYYYY_HHMM
                  )
                : "--",
              fileSize: dataSets[i]?.fileSize,
              priority: dataSets[i]?.priority.toString(),
              currentStep: dataSets[i]?.viewWorkFlowStep,
              shortTaskAssignments: dataSets[i]?.shortTaskAssignments,
              tags: dataSets[i].projectTags,
            };

            data.push(data_columns);
            if (
              valueAnnotator.length ||
              valueReviewer.length ||
              valueStep.length ||
              valueTag.length
            ) {
              data = data?.filter((item) => {
                let step = true;
                let annotator = true;
                let reviewer = true;
                let tag = true;

                if (valueStep.length) {
                  step = valueStep.includes(item?.currentStep?.stepId);
                }

                if (valueAnnotator.length) {
                  annotator = !!item?.shortTaskAssignments?.find(
                    (task) =>
                      valueAnnotator.find(
                        (assignee) => assignee?.userId === task.userAssigned
                      ) && task.assignmentType === "Labeling"
                  );
                }

                if (valueReviewer.length) {
                  reviewer = !!item?.shortTaskAssignments?.find(
                    (task) =>
                      valueReviewer.find(
                        (assignee) => assignee?.userId === task.userAssigned
                      ) && task.assignmentType === "Inreview"
                  );
                }

                if (valueTag.length) {
                  if (relation === "or") {
                    tag = valueTag.some(
                      (n) =>
                        item?.tags?.split(", ")?.includes(n) ||
                        item?.tags?.split(",")?.includes(n)
                    );
                  } else {
                    tag = valueTag.every(
                      (n) =>
                        item?.tags?.split(", ")?.includes(n) ||
                        item?.tags?.split(",")?.includes(n)
                    );
                  }
                }

                return step && annotator && reviewer && tag;
              });
            }
          } else {
            let data_columns = {
              key: dataSets[i].fileId,
              FileName: dataSets[i].fileId,
              dateadded: dataSets[i]?.dateCreated
                ? convertUTCToLocalTime(
                    dataSets[i]?.dateCreated,
                    DATE_TIME_FORMAT.DDMMYYYY_HHMM
                  )
                : "--",
              datemodified: dataSets[i]?.dateModified
                ? convertUTCToLocalTime(
                    dataSets[i]?.dateModified,
                    DATE_TIME_FORMAT.DDMMYYYY_HHMM
                  )
                : "--",
              folder: dataSets[i].fileId,
              tags: dataSets[i].projectTags,
            };

            data.push(data_columns);
          }
        }
      }
    }

    setDataList(data);
  }, [
    dataSets,
    openFolderPane,
    pathFolder,
    valueAnnotator,
    valueReviewer,
    valueStep,
    viewFolderPane,
    valueTag,
    relation,
  ]);

  const onSelectChange = useCallback(
    (record, selected, selectedRows) => {
      if (selected) {
        setListSelect([...listSelect, record.key]);
      } else {
        setListSelect(listSelect.filter((item) => item !== record.key));
      }
    },
    [listSelect]
  );

  const onSelectChangeAll = useCallback(
    (selected, selectedRows, changeRows) => {
      if (selected) {
        setListSelect(
          uniq([...listSelect, ...changeRows.map((item) => item.key)])
        );
      } else {
        setListSelect(
          difference(
            listSelect,
            changeRows.map((item) => item.key)
          )
        );
      }
    },
    [listSelect]
  );

  return (
    <Table
      loading={isLoading}
      className="dm-table-view table-dm"
      rowSelection={{
        selectedRowKeys: listSelect,
        onSelect: onSelectChange,
        onSelectAll: onSelectChangeAll,
        columnWidth: 50,
        // getCheckboxProps: () => ({
        //   disabled: !isActiveProject
        // })
      }}
      size="small"
      columns={columns}
      dataSource={dataList}
      pagination={false}
      // rowClassName={(record) => (task?.fileId && record.File.fileId===task.fileId) ? 'selected' :  'table-cell'}
      // scroll={{
      //   y: "calc(80vh - var(--header-height))",
      // }}

      components={{
        body: {
          row: DraggableBodyRow,
        },
      }}
      scroll={{ y: "calc(100vh - 268px)" }}
    />
  );
};

const DraggableBodyRow = memo((props) => {
  const { isDragDrop, dataSetsList, listSelect } = useDMStore((state) => ({
    isDragDrop: state.isDragDrop,
    dataSetsList: state.dataSetsList,
    listSelect: state.listSelect,
  }));

  const [{ isDragging }, drag, preview] = useDrag(
    () => ({
      type: "image",
      item: dataSetsList.find(
        (n) =>
          n?.fileId === props["data-row-key"] ||
          n?.name === props["data-row-key"]
      ),
      canDrag: listSelect?.includes(props["data-row-key"]) && isDragDrop,

      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    [listSelect, isDragDrop, dataSetsList]
  );

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
  }, []);
  return <tr ref={drag} {...props} />;
});
