.e-filemanager .e-search-wrap {
  display: none;
}

/* .selected {
  background-color: #faebd7;
} */
.table-cell {
  background-color: #ffffff;
}

@import "../../../../../../node_modules/@syncfusion/ej2-base/styles/material.css";
@import "../../../../../../node_modules/@syncfusion/ej2-icons/styles/material.css";
@import "../../../../../../node_modules/@syncfusion/ej2-inputs/styles/material.css";
@import "../../../../../../node_modules/@syncfusion/ej2-popups/styles/material.css";
@import "../../../../../../node_modules/@syncfusion/ej2-buttons/styles/material.css";
@import "../../../../../../node_modules/@syncfusion/ej2-splitbuttons/styles/material.css";
@import "../../../../../../node_modules/@syncfusion/ej2-navigations/styles/material.css";
@import "../../../../../../node_modules/@syncfusion/ej2-layouts/styles/material.css";
@import "../../../../../../node_modules/@syncfusion/ej2-grids/styles/material.css";
@import "../../../../../../node_modules/@syncfusion/ej2-react-filemanager/styles/material.css";

.ant-modal-header {
  padding: 0;
}

.overlay-class-popover-annotaoroption {
  .ant-popover-arrow {
    display: none;
  }
}

#id-annotaoroption-listLabeling {
  margin-bottom: 0px !important;
  padding-bottom: 0px !important;
}

.list-project {
  overflow: auto;
  white-space: nowrap;
  position: absolute;
  z-index: 1;
  bottom: 121px;
  border: 1px solid #cecece;
  background-color: #ffffff;
}

.list-project-new {
  position: absolute;
  z-index: 1;
  bottom: 500px;
  border: 1px solid #cecece;
  background-color: #ffffff;
}

.list-user-labling {
  max-height: 300px;
  overflow-x: hidden;
  overflow-y: auto;

  position: absolute;
  z-index: 1;
  /* bottom: 181px; */
  border: 1px solid #cecece;
  background-color: #ffffff;
  display: block;
}
