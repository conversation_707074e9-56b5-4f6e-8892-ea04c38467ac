import IconFolder from "@/ai_platform_v2/assets/Icons/IconFolder";
import useEditorModalStore from "@/ai_platform_v2/component/EditorModal/store";
import Tooltip from "@/ai_platform_v2/component/Tooltip/Tooltip";
import useDMStore from "@/ai_platform_v2/stores/dataManagerStore";
import { IMemberWorkflow } from "@/ai_platform_v2/types/memberWorkflow/memberWorkflow.type";
import {
  convertUTCToLocalTime,
  DATE_TIME_FORMAT,
} from "@/ai_platform_v2/utils/dateTime";
import { Button, Checkbox, ModalConfirmBig } from "@taureau/ui";
import classNames from "classnames";
import { useCallback, useEffect, useMemo } from "react";
import { useDrag } from "react-dnd";
import { getEmptyImage } from "react-dnd-html5-backend";
import {
  getCurrentModule,
  getCurrentProject,
} from "../../../../pages/DataSet/Const";
import { IFile } from "../../../types/file/file.types";
import { WorkflowNodeType } from "../../CreateProject/WorkflowConfig/types";
import UserAvatar from "../../SettingsAccount/component/SettingProfile/UserAvatar";
import { getPriorityColor } from "../../TaskManagers/helper";
import { getImageUrl } from "../helper";
import IconComplete from "./icons/IconComplete";
import IconLabeling from "./icons/IconLabeling";
import IconNews from "./icons/IconNews";
import IconReviewing from "./icons/IconReviewing";

import LoadingImage from "@/components/image/LoadImage";
import { useCurrentUser } from "@/providers/CurrentUser";
import { useProject } from "@/providers/ProjectProvider";
import {
  getImageStyleAfterEdit,
  validateImageType,
  validateZipType,
} from "@/utils/helpers";
import { IconAIPrediction } from "@v2/pages/DataManager/TableView/DataSetNew/DataSetsListNew";
import { ProjectType } from "../../Home/utils/const";
import IconPreviewZip from "@/ai_platform_v2/assets/Icons/IconPreviewZip";
import IconPreviewFile from "@/ai_platform_v2/assets/Icons/IconPreviewFile";

interface Props {
  file: IFile;
  checked?: boolean;
  sizeGrid: number;
  memberWorkflow: IMemberWorkflow | undefined;
  onChangeCheckbox?: (checked: boolean, fileId: string) => void;
  getRequestParams: any;
  retryAIPrediction: any;
  assignedMembersWorkflow: any;
}

const GridItem = ({
  file,
  onChangeCheckbox,
  checked,
  sizeGrid,
  memberWorkflow,
  getRequestParams,
  retryAIPrediction,
  assignedMembersWorkflow,
}: Props) => {
  const isAIPredictionStep = file.viewState === WorkflowNodeType.AIPrediction;
  const isFailed = file?.stepStatus === "Failed";
  const { projectDetail } = useProject();

  const moduleID = getCurrentModule();
  const projectID = getCurrentProject();

  const isShortVersion = useMemo(() => sizeGrid < 190, [sizeGrid]);

  const cropInfo = file?.cropInfo
    ? JSON.parse(JSON.parse(file?.cropInfo))
    : null;
  const isCrop = file?.isCrop ?? false;

  const { listSelect, isDragDrop } = useDMStore((state) => ({
    listSelect: state.listSelect,
    isDragDrop: state.isDragDrop,
  }));

  const [, drag, preview] = useDrag(
    () => ({
      type: "image",
      item: file,
      canDrag:
        listSelect?.includes((file?.fileId || file?.name) as any) && isDragDrop,
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    [listSelect]
  );

  const { user } = useCurrentUser();

  const { workflow } = useDMStore((state) => ({
    workflow: state.workflow,
  }));

  useEffect(() => {
    preview(getEmptyImage(), { captureDraggingState: true });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const {
    setOpenEditor,
    setCurrentFile,
    setIsHideScreen,
    setFetchUrl,
    setFetchParams,
    setInitEditor,
    setIsDataManager,
    setPrevPointer,
    setNextPointer,
    isHideScreen,
    setLastFileId,
  } = useEditorModalStore((state) => ({
    setOpenEditor: state.setOpenEditor,
    setCurrentFile: state.setCurrentFile,
    setIsHideScreen: state.setIsHideScreen,
    setFetchUrl: state.setFetchUrl,
    setFetchParams: state.setFetchParams,
    setInitEditor: state.setInitEditor,
    setIsDataManager: state.setIsDataManager,
    setPrevPointer: state.setPrevPointer,
    setNextPointer: state.setNextPointer,
    isHideScreen: state.isHideScreen,
    setLastFileId: state.setLastFileId,
  }));

  const [viewColumns] = useDMStore((state) => [state.viewColumns]);

  // const { setPathFolder } = useDMStore((state) => ({
  //   setPathFolder: state.setPathFolder,
  // }));

  const onChange = (e: any) => {
    onChangeCheckbox &&
      onChangeCheckbox(e.target.checked, (file?.fileId || file?.name) as any);
  };

  const openEditor = useCallback(() => {
    setOpenEditor(true);
    setCurrentFile(file.fileId);
    setLastFileId(undefined);
    setIsHideScreen(false);
    setFetchUrl("dataManagersSecurity");
    setFetchParams(getRequestParams());
    setPrevPointer(getRequestParams().page);
    setNextPointer(getRequestParams().page);
    setInitEditor(true);
    setIsDataManager(true);
  }, [file.fileId, getRequestParams]);

  const renderButtonOpenEditor = useCallback(() => {
    return (
      <div className="absolute top-0 bottom-0 right-0 left-0 bg-black-30 rounded-lg flex justify-center items-center opacity-0 group-hover:opacity-100">
        <Button
          theme="Primary"
          size={isShortVersion ? "xs" : "sm"}
          corner="Rounded"
          onClick={() => {
            if (isHideScreen) {
              ModalConfirmBig.warning({
                title: "Open new file?",
                content:
                  "Editor has another file open, you will lost your current work if continue.",
                okText: "Continue",
                onOk: openEditor,
              });
            } else {
              openEditor();
            }
          }}
        >
          Open
        </Button>
      </div>
    );
  }, [isHideScreen, isShortVersion, openEditor]);

  const renderAIPredictionStatus = useCallback(() => {
    return (
      <div className="absolute top-0 bottom-0 right-0 left-0 bg-black-30 rounded-lg flex justify-center items-center">
        <div
          className="cursor-pointer"
          onClick={() => {
            if (isHideScreen) {
              ModalConfirmBig.warning({
                title: "Open new file?",
                content:
                  "Editor has another file open, you will lost your current work if continue.",
                okText: "Continue",
                onOk: openEditor,
              });
            } else {
              openEditor();
            }
          }}
        >
          <IconAIPrediction
            color={isFailed ? "#CC1414" : "#33BFFF"}
            size={isShortVersion ? 30 : 36}
          />
        </div>
      </div>
    );
  }, [isFailed, isHideScreen, isShortVersion, openEditor]);

  const renderIconStep = useCallback(
    (step: string, file: any) => {
      const currentStep = file?.viewWorkFlowStepId;

      const listMemberHavePermission =
        assignedMembersWorkflow?.AIPrediction?.find(
          (item: any) => item.id === currentStep
        )?.members_info;

      const hasPermissionAIPrediction =
        listMemberHavePermission?.findIndex(
          (item: any) => item.userId === user.id
        ) !== -1;

      const currentNode = workflow?.find(
        (item: any) => item.id === currentStep
      );

      const isManual = currentNode?.settings?.manualPrediction;

      switch (step) {
        case WorkflowNodeType.New:
          return <IconNews className="min-w-[25px]" />;
        case WorkflowNodeType.Inreview:
        case WorkflowNodeType.Reviewing:
          return <IconReviewing className="min-w-[25px]" />;
        case WorkflowNodeType.Labeling:
          return <IconLabeling className="min-w-[25px]" />;
        case WorkflowNodeType.Completed:
          return <IconComplete className="min-w-[25px]" />;
        case WorkflowNodeType.AIPrediction:
          return (
            <div className="min-w-[25px] w-[25px] h-[25px]">
              {isFailed ? (
                hasPermissionAIPrediction && isManual ? (
                  <Tooltip title="Click to retry">
                    <div
                      className="cursor-pointer"
                      onClick={() => retryAIPrediction(file)}
                    >
                      <IconAIPrediction size={25} color="#CC1414" />
                    </div>
                  </Tooltip>
                ) : (
                  <div className="cursor-pointer">
                    <IconAIPrediction size={25} color="#CC1414" />
                  </div>
                )
              ) : (
                <IconAIPrediction size={25} color="#33BFFF" />
              )}
            </div>
          );
        default:
          break;
      }
    },
    [
      assignedMembersWorkflow?.AIPrediction,
      isFailed,
      retryAIPrediction,
      user.id,
      workflow,
    ]
  );

  const assignedUserInCurrentStep = useMemo(() => {
    if (!file?.viewWorkFlowStep || !memberWorkflow) return;

    const currentStep = file.currentStep;

    if (!currentStep) return;

    const currentAssignedTask = currentStep.task;

    if (!currentAssignedTask) return;

    if (file.viewState === WorkflowNodeType.Labeling) {
      const userAssigned = memberWorkflow.Annotator?.find(
        (ano) => ano.userId === currentAssignedTask.userAssigned
      );

      return userAssigned;
    } else if (
      file.viewState === WorkflowNodeType.Reviewing ||
      file.viewState === WorkflowNodeType.Inreview
    ) {
      const userAssigned = memberWorkflow.Reviewer?.find(
        (ano) => ano.userId === currentAssignedTask.userAssigned
      );

      return userAssigned;
    }

    return;
  }, [
    file.currentStep,
    file.viewState,
    file?.viewWorkFlowStep,
    memberWorkflow,
  ]);

  const renderAnnotatorAndReviewer = useCallback(() => {
    if (file.viewState)
      return (
        <>
          {file.viewState === WorkflowNodeType.Labeling &&
            viewColumns.includes("Annotator") &&
            assignedUserInCurrentStep && (
              <UserAvatar
                src={assignedUserInCurrentStep.avatar_url}
                user={assignedUserInCurrentStep}
                className={classNames(
                  "w-[27px] h-[27px] border-2 border-solid border-[#3361FF]"
                )}
                textClassName="text-[14px]"
              />
            )}

          {(file.viewState === WorkflowNodeType.Reviewing ||
            file.viewState === WorkflowNodeType.Inreview) &&
            assignedUserInCurrentStep &&
            viewColumns.includes("Reviewer") && (
              <UserAvatar
                src={assignedUserInCurrentStep.avatar_url}
                user={assignedUserInCurrentStep}
                className={classNames(
                  "w-[27px] h-[27px] border-2 border-solid border-[#FFCB33]"
                )}
                textClassName="text-[14px]"
              />
            )}
        </>
      );
  }, [assignedUserInCurrentStep, file, viewColumns]);

  const renderPriority = useCallback(() => {
    if (file.priority)
      return (
        <Button
          corner="Circle"
          className={classNames("w-[25px] h-[25px] text-white cursor-default", {
            "!text-[#ADB8CC]": file.priority === "4",
          })}
          style={{
            backgroundColor: getPriorityColor(file.priority),
          }}
        >
          {Number(file.priority) + 1}
        </Button>
      );
  }, [file.priority]);

  const getIconFileType = () => {
    if (validateZipType(file.fileName)) {
      return (
        <div className="flex h-[342px] justify-center items-center">
          <IconPreviewZip size={342} />
        </div>
      );
    } else if (validateImageType(file.fileName)) {
      return (
        <LoadingImage
          src={getImageUrl(moduleID, projectID, file.fileId, "Original")}
          alt=""
          className="w-full h-full object-cover rounded-[10px]"
          rederImageError={() => {
            return (
              <div className="no-image-preview w-full h-full text-[13px]">
                Error preview
              </div>
            );
          }}
          style={getImageStyleAfterEdit(cropInfo, isCrop)}
        />
      );
    }

    return (
      <div className="flex h-[342px] justify-center items-center">
        <IconPreviewFile height="342" width="100%" />
      </div>
    );
  };

  return (
    <>
      <div
        className="grid-card"
        style={{
          width: sizeGrid,
          aspectRatio: "1 / 1",
        }}
        ref={drag}
      >
        <div className="w-full flex flex-row justify-between">
          <Checkbox
            onChange={onChange}
            checked={Boolean(checked)}
            size={isShortVersion ? "xs" : "sm"}
          />

          {!isShortVersion && (
            <div className="flex justify-center items-center gap-[4px]">
              {viewColumns.includes("Step") &&
                file.viewState &&
                projectDetail?.projectType !== ProjectType.NonAnnotation &&
                renderIconStep(file.viewState, file)}
              {renderAnnotatorAndReviewer()}
              {viewColumns.includes("Priority") &&
                projectDetail?.projectType !== ProjectType.NonAnnotation &&
                renderPriority()}
            </div>
          )}
        </div>

        <div className="w-full h-full min-h-[80px] overflow-hidden rounded-[10px] mt-[10px]">
          {file?.fileId && file?.viewState ? (
            <div className="w-full h-full group relative overflow-hidden">
              {getIconFileType()}
              {isAIPredictionStep &&
              file.stepStatus &&
              !["Completed", "New", "Pending"].includes(file.stepStatus)
                ? renderAIPredictionStatus()
                : projectDetail?.projectType !== ProjectType.NonAnnotation &&
                  renderButtonOpenEditor()}
            </div>
          ) : (
            <div
              className="w-full h-full"
              onClick={() => {
                // setPathFolder(file?.filterPath?.concat(file?.name, "/"));
              }}
            >
              <IconFolder className="w-full h-full" color="#7D8FB3" />
            </div>
          )}
        </div>

        {!isShortVersion && (
          <>
            {["File name", "Size"].some((item) =>
              viewColumns.includes(item)
            ) && (
              <div className="flex flex-row items-center justify-between w-full mt-[5px]">
                <div className="whitespace-nowrap text-ellipsis overflow-hidden">
                  {viewColumns.includes("File name") && (
                    <Tooltip title={file.fileName}>
                      <span className=" font-medium text-[14px] text-gray-blue-grey-blue-40 select-text">
                        {file.fileName || file.name || file?.fileId}
                      </span>
                    </Tooltip>
                  )}
                </div>

                {viewColumns.includes("Size") && file.fileSize && (
                  <div className=" font-medium text-[12px] text-gray-blue-grey-blue-70 text-right whitespace-nowrap">
                    {file.fileSize} KB
                  </div>
                )}
              </div>
            )}

            <div className="flex flex-row items-center justify-between w-full">
              <div>
                {file.createdAt && viewColumns.includes("Date Added") && (
                  <Tooltip title={convertUTCToLocalTime(file.createdAt)}>
                    <div className=" font-normal text-[12px] text-gray-blue-grey-blue-60 whitespace-nowrap text-ellipsis overflow-hidden">
                      Create at{" "}
                      {convertUTCToLocalTime(
                        file.createdAt,
                        DATE_TIME_FORMAT.DDMMYYYY
                      )}
                    </div>
                  </Tooltip>
                )}

                {file.updatedAt && viewColumns.includes("Date Modified") && (
                  <Tooltip title={convertUTCToLocalTime(file.updatedAt)}>
                    <div className=" font-normal text-[12px] text-gray-blue-grey-blue-60 whitespace-nowrap text-ellipsis overflow-hidden">
                      Update at{" "}
                      {convertUTCToLocalTime(
                        file.updatedAt,
                        DATE_TIME_FORMAT.DDMMYYYY
                      )}
                    </div>
                  </Tooltip>
                )}
              </div>

              <span className=" font-medium text-[12px] text-[#E62E7B] text-right">
                {viewColumns.includes("Type") && file?.dataType?.toUpperCase()}
              </span>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default GridItem;
