import IconCompletedFile from "@/ai_platform_v2/assets/Icons/IconCompletedFile";
import IconLabelingFile from "@/ai_platform_v2/assets/Icons/IconLabelingFile";
import IconMoreHoriz from "@/ai_platform_v2/assets/Icons/IconMoreHoriz";
import IconNewFile from "@/ai_platform_v2/assets/Icons/IconNewFile";
import IconReviewFile from "@/ai_platform_v2/assets/Icons/IconReviewFile";
import IconTotalTask from "@/ai_platform_v2/assets/Icons/IconTotalTask";
import Message from "@/ai_platform_v2/component/Message/Message";
import useModulesStore from "@/ai_platform_v2/stores/modulesStore";
import { hex2rgb } from "@/ai_platform_v2/utils/colors";
import LoadImage from "@/components/image/LoadImage";
import { ABILITY_NEW, ABILITY_SCOPE_PROJECT } from "@/config/PermissionsConfig";
import { useAPI } from "@/providers/ApiProvider";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { ModalConfirmBig } from "@taureau/ui";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import QuickView from "./QuickView";

import IconAIPredictFile from "@/ai_platform_v2/assets/Icons/IconAIPredictFile";
import { useModule } from "@/ai_platform_v2/providers/ModuleProviderNew";
import { convertNumberToShortFormat } from "@/ai_platform_v2/utils/helpers";
import classNames from "classnames";
import { useHistory } from "react-router";
import { getImageUrl } from "../../DataManager/helper";
import "./ProjectCard.scss";

const useOutsideClick = (callback: any) => {
  const ref = React.useRef();

  React.useEffect(() => {
    const handleClick = (event: any) => {
      if (ref.current && !ref.current.contains(event.target)) {
        callback();
      }
    };

    document.addEventListener("click", handleClick);

    return () => {
      document.removeEventListener("click", handleClick);
    };
  }, [ref]);

  return ref;
};

interface Props {
  // nameProject: string;
  // nameModule: string;
  fetchProject: () => void;
  project: any;
  updateColorProjectCb: (projectId: string, color: string) => void;
  moreProject: any;
  setMoreProject: any;
}

interface IProjectColor {
  r: number;
  g: number;
  b: number;
}
const getListImage = () => {
  const list = [];

  for (let i = 0; i < 3; i++) {
    list.push(
      `/static/images/project_avatar_sample/${
        Math.floor(Math.random() * 30) + 1
      }.jpg`
    );
  }
  return list;
};

export const ProjectCard = (props: Props) => {
  const api = useAPI();

  const history = useHistory();
  const {
    project,
    fetchProject,
    updateColorProjectCb,
    moreProject,
    setMoreProject,
  } = props;

  const [randomNum] = useState(Math.floor(Math.random() * 10));

  const [imageList] = useState(getListImage());

  const isActiveProject = useMemo(
    () => project?.status === "Active",
    [project?.status]
  );

  const projectColor: IProjectColor = useMemo(() => {
    if (!isActiveProject && !project?.isDelete) {
      return {
        r: 0,
        g: 0,
        b: 0,
      };
    }

    return hex2rgb(project?.color);
  }, [isActiveProject, project?.color, project?.isDelete]);

  const { moduleFilter, isShowArchived, module, setModuleType } =
    useModulesStore((state) => ({
      moduleFilter: state.moduleParams.moduleFilter,
      isShowArchived: state.moduleParams.isShowArchived,
      module: state.currentModule,
      setModuleType: state.setModuleType,
    }));

  const { hasPermission, hasPermissionAllScope } = useCheckPermission();

  const canRevertProjects = useMemo(
    () => hasPermission(ABILITY_NEW.can_revert_projects) && project.isDelete,
    [hasPermission, project.isDelete]
  );

  const canPermanentDelete =
    hasPermission(ABILITY_SCOPE_PROJECT.can_update_status_projects) &&
    hasPermission(ABILITY_SCOPE_PROJECT.can_remove_projects);

  const { invalidateCache } = useModule();

  const handleDeleteProject = useCallback(async () => {
    const res = await api.callApi("deleteTemporaryProject", {
      params: {
        pk: project.id,
      },
    });

    if (res?.success) {
      Message.success({
        title: "Success",
        content: `${project?.name} deleted successfully!`,
      });
      fetchProject();
    }
  }, [api, fetchProject, project]);

  const handleClickDelete = useCallback(() => {
    const status = project?.status;

    if (status === "Active") {
      Message.error({
        title: "Error",
        content: "Cannot delete active project!",
      });
    } else if (status === "Inactive") {
      ModalConfirmBig.warn({
        title: "Are you sure to delete this project?",
        content:
          "If you continue, all data and member added into this project will be remove permanently.",
        okText: "Delete",
        cancelText: "Cancel",
        onOk: handleDeleteProject,
      });
    }
  }, [handleDeleteProject, project?.status]);

  const handleRevertProject = useCallback(async () => {
    if (!canRevertProjects || module?.status === "Inactive" || isShowArchived) {
      Message.error({
        title: "Error",
        content: `Cannot revert ${project?.name} in inactive Module`,
      });
    } else {
      const res = await api.callApi("revertProject", {
        params: {
          pk: project.id,
        },
      });

      if (res?.success) {
        Message.success({
          title: "Success",
          content: `${project?.name} revert successfully!`,
        });
        fetchProject();
      } else {
        Message.error({
          title: "Error",
          content: `${res?.message}`,
        });
      }
    }
  }, [
    api,
    canRevertProjects,
    fetchProject,
    isShowArchived,
    module,
    project.id,
    project?.name,
  ]);

  const handleHardDeleteProject = useCallback(async () => {
    if (
      module?.status === "Inactive" ||
      isShowArchived ||
      !canPermanentDelete
    ) {
      Message.error({
        title: "Error",
        content: `Cannot permanently delete ${project?.name}!`,
      });
    } else {
      const res = await api.callApi("updateProject", {
        params: {
          pk: project.id,
        },
        body: {
          deletePermanentStatus: "Ready",
        },
      });

      if (res?.success) {
        Message.success({
          title: "Success",
          content: `${project?.name} has been deleted permanently!`,
        });
        fetchProject();
      } else {
        Message.error({
          title: "Error",
          content: `${res?.message}`,
        });
      }
    }
  }, [isShowArchived, module, project.id, project?.name]);

  const handleClickRevert = useCallback(
    (e) => {
      e.stopPropagation();

      ModalConfirmBig.confirm({
        header: "Confirm",
        title: `You want to revert ${project?.name}?`,
        content:
          "If continue, all data items and member will be recovered immediately.",
        okText: "Revert",
        cancelText: "Cancel",
        onOk: handleRevertProject,
      });
    },
    [handleRevertProject, project]
  );

  const handleClickHardDelete = useCallback(
    (e) => {
      e.stopPropagation();

      ModalConfirmBig.warn({
        header: "Warning",
        title: `Permanently delete?`,
        content:
          "If continue, this project will be removed permanently. Be careful, this action cannot be undone .",
        okText: "Continue",
        cancelText: "Cancel",
        onOk: handleHardDeleteProject,
      });
    },
    [handleHardDeleteProject, project]
  );

  const [projectDetail, setProjectDetail] = useState();
  const [projectProgress, setProjectProgress] = useState();
  const [waitingProgress, setWaitingProgress] = useState(false);

  const [openQuickView, setOpenQuickView] = useState(false);

  const totalNewFile = project.stateStatistic.New;
  const totalLabelingFile = project.stateStatistic.Labeling;
  const totalReviewFile = project.stateStatistic.Inreview;
  const totalCompletedFile = project.stateStatistic.Completed;
  const totalAIPredictionFile = project.stateStatistic?.AIPrediction || 0;

  const totalFile =
    totalNewFile +
    totalLabelingFile +
    totalReviewFile +
    totalCompletedFile +
    totalAIPredictionFile;

  // const [hoverActive, setHoverActive] = useState(false);

  const getPercent = (totalFile: number, totalCompletedFile: number) => {
    if (totalFile === 0) return 0;

    return Math.floor((totalCompletedFile / totalFile) * 100);
  };

  const handleClickOutside = () => {
    setOpenQuickView(false);
  };
  const ref = useOutsideClick(handleClickOutside);

  const handleClickMoreBtn = useCallback(
    (e) => {
      e.stopPropagation();

      setOpenQuickView(!openQuickView);

      if (moreProject === project.id) {
        setMoreProject(null);
      } else {
        setMoreProject(project.id);
      }
    },
    [moreProject, openQuickView, project.id, setMoreProject]
  );

  const fetchProjectDetail = useCallback(async () => {
    const res = await api.callApi("project", {
      params: { pk: project.id },
    });

    if (res?.success) {
      setProjectDetail(res.data);
    }
  }, [project]);

  const fetchProjectProgress = useCallback(async () => {
    setWaitingProgress(true);

    const res = await api.callApi("projectStatistic", {
      params: { project: project.id, static_filter: "Status" },
    });

    setWaitingProgress(false);

    if (res?.success) {
      setProjectProgress(res.data);
    }
  }, [project]);

  useEffect(() => {
    if (openQuickView) {
      fetchProjectDetail();
      fetchProjectProgress();
    } else {
      invalidateCache();
    }
  }, [openQuickView]);

  const goToProjectDashboard = useCallback(
    (e) => {
      if (!project.isDelete && !isActiveProject) {
        Message.error({
          title: "Error",
          content: `This project is inactive now.`,
        });
        e.stopPropagation();
      } else if (project.isDelete) {
        e.preventDefault();
      } else {
        const moduleId = project?.moduleId;
        const projectId = project?.id;

        e.stopPropagation();

        if (
          hasPermissionAllScope(
            ABILITY_NEW.can_view_dataset,
            moduleId,
            projectId
          )
        ) {
          history.push(
            `/modules/${moduleId}/projects/${projectId}/dashboard-v2?type=Project`
          );
          setModuleType("");
        } else if (
          hasPermissionAllScope(
            ABILITY_NEW.can_update_projects,
            moduleId,
            projectId
          )
        ) {
          history.push(
            `/modules/${moduleId}/projects/${projectId}/settings-project`
          );
          setModuleType("");
        }
      }
    },
    [history, project, isActiveProject, hasPermissionAllScope]
  );

  const handleSettingAvatarProject = useCallback(
    (e) => {
      if (project.isDelete) {
        e.preventDefault();
      } else {
        const moduleId = project?.moduleId;
        const projectId = project?.id;

        e.stopPropagation();
        history.push(
          `/modules/${moduleId}/projects/${projectId}/settings-project?tab=infomation`
        );
      }
    },
    [history, project]
  );
  const renderHeaderCard = useCallback(() => {
    return (
      <div className="flex justify-between items-center">
        <div className="flex flex-col w-full max-w-[200px]">
          <span
            className={` text-[14px] font-medium leading-[30px] text-ellipsis overflow-hidden whitespace-nowrap w-full text-start ${
              isActiveProject ? "text-gray-blue-40" : "text-[#000]/[0.6]"
            }`}
          >
            {project.name}
          </span>

          <span className=" text-[13px] font-medium leading-[25px] text-black-30 text-ellipsis overflow-hidden whitespace-nowrap w-full text-start">
            {project.module}
          </span>
        </div>
      </div>
    );
  }, [isActiveProject, project.module, project.name]);

  const renderProjectAvatar = useCallback(() => {
    return (
      <div className="wrap-img-project w-full rounded-[10px] border-[2px] border-white overflow-hidden pointer-events-none flex bg-white gap-[2px]">
        {project.projectAvatar ? (
          <>
            {project.projectAvatar.split(",").map((id: any, i: any) => {
              if (id)
                return (
                  <div className="w-full h-full">
                    <LoadImage
                      className="object-cover w-full h-full"
                      src={getImageUrl(
                        project.moduleId,
                        project.id,
                        id,
                        "Original"
                      )}
                      alt=""
                      rederImageError={() => {
                        return (
                          <div className="no-image-preview h-full w-full text-[14px] !rounded-none">
                            Error preview
                          </div>
                        );
                      }}
                    />
                  </div>
                );

              return (
                <div
                  className="no-image-preview h-full w-full text-[14px] !rounded-none"
                  key={`${project.id}-${i}`}
                >
                  No image preview
                </div>
              );
            })}
          </>
        ) : (
          <div className="no-image-preview h-full w-full text-[14px]">
            No image preview
          </div>
        )}
      </div>
    );
  }, [project.id, project.moduleId, project.projectAvatar]);

  const renderContentCard = useCallback(() => {
    return (
      <div className="w-full flex flex-col justify-start items-center gap-[10px] overflow-hidden">
        <div
          style={{
            backgroundColor: `rgba(${projectColor.r}, ${projectColor.g}, ${projectColor.b}, 0.05)`,
          }}
          className="w-full h-full bg-blue-5 rounded-[5px] flex justify-start items-center"
        >
          <div className="flex w-full h-full justify-center items-center">
            <div className="flex w-full h-full items-center justify-center p-[10px] pr-0">
              <div className="w-full h-full flex flex-col items-center gap-[6px] justify-center">
                <div className="w-full flex justify-center items-center gap-[20px]">
                  <div
                    className={`w-1/6 min-w-1/6 h-fit icon-total-task${
                      isActiveProject ? "" : "-inactive"
                    }`}
                  >
                    <IconTotalTask size={25} />
                  </div>
                  <div className="text-process w-5/6  font-medium text-start text-[14px] leading-[25px] text-black-30">
                    {convertNumberToShortFormat(totalFile)}
                  </div>
                </div>
                <div className="w-full flex justify-center items-center gap-[20px]">
                  <div
                    className={`w-1/6 min-w-1/6 h-fit icon-new-file${
                      isActiveProject ? "" : "-inactive"
                    }`}
                  >
                    <IconNewFile size={25} />
                  </div>
                  <div className="text-process w-5/6  font-medium text-start text-[14px] leading-[25px] text-black-30">
                    {convertNumberToShortFormat(totalNewFile)}
                  </div>
                </div>
                <div className="w-full flex justify-center items-center gap-[20px]">
                  <div
                    className={`w-1/6 min-w-1/6 h-fit icon-labeling-file${
                      isActiveProject ? "" : "-inactive"
                    }`}
                  >
                    <IconLabelingFile size={25} />
                  </div>
                  <div className="text-process w-5/6  font-medium text-start text-[14px] leading-[25px] text-black-30">
                    {convertNumberToShortFormat(totalLabelingFile)}
                  </div>
                </div>
              </div>

              <div className="w-full h-full flex flex-col items-center gap-[6px] justify-center">
                <div className="w-full flex justify-center items-center gap-[20px]">
                  <div
                    className={`w-1/6 min-w-1/6 h-fit icon-process-task${
                      isActiveProject ? "" : "-inactive"
                    }`}
                  >
                    <div className="w-[25px]">
                      <IconAIPredictFile size={25} />
                    </div>
                  </div>
                  <div className="text-process w-5/6 font-roboto font-medium text-start text-[14px] leading-[25px] text-black-30">
                    {convertNumberToShortFormat(totalAIPredictionFile)}
                  </div>
                </div>
                <div className="w-full flex justify-center items-center gap-[20px]">
                  <div
                    className={`w-1/6 min-w-1/6 h-fit icon-review-file${
                      isActiveProject ? "" : "-inactive"
                    }`}
                  >
                    <IconReviewFile size={25} />
                  </div>
                  <div className="text-process w-5/6  font-medium text-start text-[14px] leading-[25px] text-black-30">
                    {convertNumberToShortFormat(totalReviewFile)}
                  </div>
                </div>
                <div className="w-full flex justify-center items-center gap-[20px]">
                  <div
                    className={`w-1/6 min-w-1/6 h-fit icon-completed-file${
                      isActiveProject ? "" : "-inactive"
                    }`}
                  >
                    <IconCompletedFile size={25} />
                  </div>
                  <div className="text-process w-5/6  font-medium text-start text-[14px] leading-[25px] text-black-30">
                    {convertNumberToShortFormat(totalCompletedFile)}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex w-fit h-full pr-[10px] justify-center items-center">
            <div className="w-[70px] h-[7a0px]">
              <div
                className="pie-project animate"
                style={{
                  "--p": getPercent(totalFile, totalCompletedFile),
                  "--c": isActiveProject
                    ? "#1d77f2"
                    : project?.isDelete
                      ? "#E40000"
                      : "#3B3B3B",

                  "--ct": isActiveProject
                    ? "#346"
                    : project?.isDelete
                      ? "#E40000"
                      : "#3B3B3B",
                }}
              >
                {!isActiveProject
                  ? project?.isDelete
                    ? "Deleted"
                    : "Inactive"
                  : `${getPercent(totalFile, totalCompletedFile)}%`}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }, [totalCompletedFile, totalAIPredictionFile, totalFile, projectColor]);

  const renderSeeMoreBtn = useCallback(() => {
    if (project?.isDelete) return null;

    return (
      <div
        className={classNames("absolute top-2.5 right-2.5", {
          "hover-content": !openQuickView,
        })}
      >
        <QuickView
          // open={openQuickView && !project?.isDelete}
          project={projectDetail ?? project}
          projectMembers={projectDetail?.members}
          projectProgress={projectProgress}
          waitingProgress={waitingProgress}
          fetchProject={fetchProject}
          onOpenChange={(value: any) => setOpenQuickView(value)}
          handleDeleteProject={handleClickDelete}
          updateColorProjectCb={updateColorProjectCb}
          isActiveProject={isActiveProject}
        >
          <div
            onClick={handleClickMoreBtn}
            className={classNames(
              "rounded-[5px] w-[25px] h-[25px] cursor-pointer flex items-center justify-center rotate-90 shadow-Shadows/Gray-Blue-30/3%/5b",
              {
                "bg-[#3361FF1A]": openQuickView,
                "bg-[#FFFFFF80] hover:bg-[#FFFFFFB3]": !openQuickView,
              }
            )}
          >
            <IconMoreHoriz
              size={25}
              color={openQuickView ? "#3361FF" : "#C3CAD9"}
            />
          </div>
        </QuickView>
      </div>
    );
  }, [
    fetchProject,
    handleClickDelete,
    handleClickMoreBtn,
    isActiveProject,
    openQuickView,
    project,
    projectDetail,
    projectProgress,
    updateColorProjectCb,
    waitingProgress,
  ]);

  const renderRevertWrapper = useCallback(() => {
    if (!canRevertProjects) return null;

    return (
      <div className="hover-content absolute top-0 bottom-0 right-0 left-0 bg-black-10 rounded-lg flex justify-center items-center gap-4">
        <div
          className="w-[62px] aspect-square bg-white rounded-full flex flex-col gap-1 justify-center items-center hover:bg-gray-blue-97 icon-revert"
          onClick={handleClickRevert}
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M6.30843 6.39994H8.52143L8.53333 6.4H16L16.0119 6.39994H19.6252L18.5861 17.1371C18.5301 17.7157 18.4863 18.1646 18.439 18.5306L6.30843 6.39994ZM4.17509 4.2666H7.49597C7.7613 1.86666 9.79599 0 12.2667 0C14.7374 0 16.7721 1.86666 17.0373 4.2666H20.8H22.9333C23.5225 4.2666 24 4.74416 24 5.33327C24 5.92237 23.5225 6.39994 22.9333 6.39994H21.7684L20.7095 17.3425L20.7055 17.384C20.6299 18.1658 20.5671 18.8135 20.4742 19.3406C20.414 19.6818 20.3363 20.0043 20.2195 20.311L22.0876 22.1791C22.5041 22.5956 22.5041 23.271 22.0876 23.6876C21.671 24.1041 20.9956 24.1041 20.5791 23.6876L0.312419 3.42092C-0.10414 3.00435 -0.10414 2.32898 0.312419 1.91242C0.728981 1.49586 1.40435 1.49586 1.82092 1.91242L4.17509 4.2666ZM12.2667 2.13333C13.5568 2.13333 14.6329 3.04944 14.88 4.2666H9.65336C9.90046 3.04944 10.9765 2.13333 12.2667 2.13333ZM17.0602 22.8353L15.0244 20.7996C14.877 20.7999 14.7198 20.7999 14.5516 20.7999H9.98181C9.14425 20.7999 8.57729 20.7991 8.13683 20.7647C7.70951 20.7312 7.48606 20.6706 7.32555 20.5947C6.94378 20.4139 6.62427 20.1237 6.40753 19.7612C6.31641 19.6087 6.23457 19.3921 6.16012 18.97C6.08337 18.5349 6.02801 17.9707 5.94733 17.1371L5.36719 11.1423L2.99425 8.76937L3.82391 17.3425L3.82793 17.3839C3.90357 18.1658 3.96625 18.8135 4.05922 19.3406C4.15612 19.8899 4.29839 20.3906 4.5764 20.8558C5.00988 21.5809 5.6489 22.1611 6.41245 22.5227C6.90214 22.7546 7.41423 22.848 7.97034 22.8915C8.50388 22.9333 9.15464 22.9333 9.94011 22.9332H9.98181H14.5516H14.5932C15.3787 22.9333 16.0295 22.9333 16.5631 22.8915C16.7327 22.8783 16.8981 22.8604 17.0602 22.8353Z"
              fill="#334466"
            />
          </svg>

          <span className="text-[10px] font-semibold leading-[11.5px]">
            Revert
          </span>
        </div>

        <div
          className="w-[62px] aspect-square bg-white rounded-full flex flex-col gap-1 justify-center items-center hover:bg-[#FDEBEB] icon-delete"
          onClick={handleClickHardDelete}
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M16.8791 4.46507H22.9091C23.5116 4.46507 24 4.96484 24 5.58135C24 6.19785 23.5116 6.69764 22.9091 6.69764H21.7177L20.6306 18.1926C20.5532 19.0108 20.4891 19.6887 20.394 20.2402C20.2949 20.8151 20.1494 21.3391 19.8651 21.8259C19.4218 22.5848 18.7682 23.1919 17.9873 23.5703C17.4865 23.813 16.9628 23.9108 16.3941 23.9563C15.8483 24.0001 15.1828 24.0001 14.3795 24H9.62064C8.81733 24.0001 8.15165 24.0001 7.60598 23.9563C7.03724 23.9108 6.51351 23.813 6.01269 23.5703C5.23177 23.1919 4.57824 22.5848 4.13491 21.8259C3.85058 21.3391 3.70508 20.8151 3.60598 20.2402C3.51089 19.6887 3.44679 19.0108 3.36942 18.1925L2.28229 6.69764H1.09091C0.488422 6.69764 0 6.19785 0 5.58135C0 4.96484 0.488422 4.46507 1.09091 4.46507H7.12088C7.39224 1.95349 9.47317 0 12 0C14.5269 0 16.6078 1.95349 16.8791 4.46507ZM8.16964 6.69764H4.47431L5.537 17.9342C5.6195 18.8066 5.67613 19.3971 5.75462 19.8524C5.83077 20.2942 5.91445 20.5209 6.00765 20.6804C6.22932 21.0598 6.55608 21.3634 6.94654 21.5526C7.1107 21.6321 7.33923 21.6955 7.77625 21.7306C8.22672 21.7666 8.80656 21.7674 9.66317 21.7674H14.3368C15.1934 21.7674 15.7732 21.7666 16.2238 21.7306C16.6608 21.6955 16.8893 21.6321 17.0534 21.5526C17.444 21.3634 17.7707 21.0598 17.9924 20.6804C18.0855 20.5209 18.1692 20.2942 18.2453 19.8524C18.3239 19.3971 18.3805 18.8066 18.463 17.9342L19.5257 6.69764H15.8304C15.8263 6.69768 15.8222 6.6977 15.8182 6.6977H8.18182C8.17775 6.6977 8.17369 6.69768 8.16964 6.69764ZM14.6727 4.46507C14.42 3.19129 13.3195 2.23257 12 2.23257C10.6805 2.23257 9.58002 3.19129 9.3273 4.46507H14.6727Z"
              fill="#334466"
            />
          </svg>

          <span className="text-[10px] font-semibold leading-[11.5px]">
            Delete
          </span>
        </div>
      </div>
    );
  }, [canRevertProjects, handleClickRevert, handleClickHardDelete]);

  return (
    <div
      onClick={goToProjectDashboard}
      className={classNames("relative card-list-project", {
        "card-list-project--active": openQuickView,
      })}
      style={{
        background: `linear-gradient(
            180deg,
            rgba(${projectColor.r}, ${projectColor.g}, ${projectColor.b}, 0.3) 0%,
            rgba(${projectColor.r}, ${projectColor.g}, ${projectColor.b}, 0) 100%
            )`,
      }}
    >
      <div className="wrap-card">
        {renderHeaderCard()}
        {renderProjectAvatar()}
        {renderContentCard()}
        {renderSeeMoreBtn()}
        {renderRevertWrapper()}
      </div>
    </div>
  );
};
