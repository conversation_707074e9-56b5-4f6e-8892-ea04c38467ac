.slider-container {
  height: 100% !important;
}
.slider-frame {
  height: 100% !important;
}
.slider-list {
  height: 100% !important;
}
.slide {
  height: 100% !important;
}
.slider-control-bottomcenter {
  ul {
    top: 0px !important;
  }

  .paging-item {
    button {
      padding: 7px 5px !important;
      fill: rgba(255, 255, 255, 0.5) !important;
    }
  }

  .paging-item-active {
    button {
      padding: 7px 5px !important;
      fill: #3361ff !important;
    }
  }
}
.slider-control-centerleft {
  visibility: hidden;
}
.slider-control-centerright {
  visibility: hidden;
}
.card-list-project:hover {
  .slider-control-centerright {
    visibility: visible;
  }
  .slider-control-centerleft {
    visibility: visible;
  }
}
.wrap-progessbar-project {
  border-radius: 99px;
  background: var(
    --gradient-lin,
    linear-gradient(
      180deg,
      rgba(81, 120, 255, 0.4) -128.44%,
      rgba(223, 230, 255, 0.1) 91.71%
    )
  );
}
.progessbar-project {
  border-radius: 99px;
  background: var(
    --gradient-gradient-5,
    linear-gradient(180deg, #1abffb 0%, #1d77f2 100%)
  );
}
.progessbar-project-inactive {
  border-radius: 99px;
  background: linear-gradient(180deg, #26334d 0%, #7d8fb3 100%);
}
.progessbar-project-deleted {
  border-radius: 99px;
  background: linear-gradient(
    180deg,
    rgba(255, 102, 51, 0.9) 0%,
    rgba(230, 46, 46, 0.9) 100%
  );
}
.label-percent-card-deleted {
  background: linear-gradient(180deg, #26334d 0%, #7d8fb3 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.label-percent-card-deleted {
  background: linear-gradient(
    180deg,
    rgba(255, 102, 51, 0.9) 0%,
    rgba(230, 46, 46, 0.9) 100%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.label-percent-card {
  background: var(
    --gradient-gradient-5,
    linear-gradient(180deg, #1abffb 0%, #1d77f2 100%)
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.pie-project {
  --p: 50;
  --b: 6px;
  --c: #1d77f2;
  --w: 70px;
  --ct: #346;

  width: var(--w);
  aspect-ratio: 1;
  position: relative;
  display: inline-grid;
  place-content: center;
  /* Medium/Medium 16 */

  font-size: 13px;
  font-style: normal;
  font-weight: 500;
  line-height: 30px; /* 187.5% */
  color: var(--ct);
}

.pie-project:before,
.pie-project:after {
  content: "";
  position: absolute;
  border-radius: 50%;
}
.pie-project:before {
  inset: 0;
  background:
    radial-gradient(farthest-side, var(--c) 98%, #0000) top/var(--b) var(--b)
      no-repeat,
    conic-gradient(var(--c) calc(var(--p) * 1%), rgba(51, 97, 255, 0.05) 0);
  -webkit-mask: radial-gradient(
    farthest-side,
    #0000 calc(99% - var(--b)),
    #000 calc(100% - var(--b))
  );
  mask: radial-gradient(
    farthest-side,
    #0000 calc(99% - var(--b)),
    #000 calc(100% - var(--b))
  );
}
.pie-project:after {
  inset: calc(50% - var(--b) / 2);
  background: var(--c);
  transform: rotate(calc(var(--p) * 3.6deg))
    translateY(calc(50% - var(--w) / 2));
}

.icon-revert {
  color: #346;
}

.icon-delete {
  color: #346;
  &:hover {
    path {
      fill: #cc1414;
    }

    color: #cc1414;
  }
}
