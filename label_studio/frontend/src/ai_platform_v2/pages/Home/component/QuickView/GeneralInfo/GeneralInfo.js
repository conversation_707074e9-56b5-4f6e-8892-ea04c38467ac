import useModulesStore from "@/ai_platform_v2/stores/modulesStore";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { Button, ModalConfirmBig } from "@taureau/ui";
import { Tooltip, Typography } from "antd";
import classNames from "classnames";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useHistory } from "react-router-dom";
import { IconCopy, IconSetting, IconTrash } from "../../../../../assets";
import { Block, Elem } from "../../../../../utils/bem";
import { CloneProject } from "../../CloneProject/CloneProject";

import { useModule } from "@v2/providers/ModuleProviderNew";
import { useAPI } from "../../../../../../providers/ApiProvider";
import { useProject } from "../../../../../../providers/ProjectProvider";
import Message from "../../../../../component/Message/Message";

import { useAppStore } from "../../../../../../providers/AppStoreProvider";
import "./GeneralInfo.styl";

const { Paragraph } = Typography;

export const GeneralInfo = ({ project, handleDeleteProject, fetchProject }) => {
  const [openCloneProject, setOpenCloneProject] = useState(false);

  const api = useAPI();

  const { isPublicVersion } = useAppStore();

  const { invalidateCache } = useProject();

  const { currentModule, setModuleType } = useModulesStore((state) => ({
    module: state.currentModule,
    setModuleType: state.setModuleType,
  }));

  const { module, fetchModule } = useModule();

  useEffect(() => {
    if (!currentModule) {
      fetchModule(project?.moduleId);
    }
  }, [currentModule, fetchModule, project?.moduleId]);

  const isInactiveModule = useMemo(
    () => currentModule?.status === "Inactive" || module?.status === "Inactive",
    [currentModule?.status, module?.status]
  );

  const isActiveProject = useMemo(
    () => project.status === "Active",
    [project.status]
  );

  const handleOkUpdateStatus = useCallback(async () => {
    const response = await api.callApi("updateProject", {
      params: {
        pk: project.id,
      },
      body: {
        status: isActiveProject ? "Inactive" : "Active",
        projectAvatar: project?.projectAvatar,
      },
    });

    if (response?.success) {
      fetchProject();
      invalidateCache();
      Message.success({
        title: "Success",
        content: `${project?.name} is ${
          isActiveProject ? "inactivated" : "activated"
        }.`,
      });
    } else {
      Message.error({
        title: "Error",
        content: response?.message,
      });
    }
  }, [api, fetchProject, isActiveProject, project, invalidateCache]);

  const handleUpdateStatus = useCallback(() => {
    if (isInactiveModule) {
      Message.error({
        title: "Error",
        content:
          "Cannot perform this action because your module is inactive now",
      });
    } else {
      ModalConfirmBig.confirm({
        title: `${isActiveProject ? "Inactivate" : "Activate"} ${
          project?.name
        } ?`,
        content: isActiveProject
          ? "If continue, your project will be inactivated. All member cannot access to this project anymore."
          : `If continue, your project will be activated. All member can continue working  on it.`,
        okText: "Update",
        cancelText: "Cancel",
        onOk: () => {
          handleOkUpdateStatus();
        },
      });
    }
  }, [isInactiveModule, project, isActiveProject, handleOkUpdateStatus]);

  const { hasPermissionAllScope } = useCheckPermission();

  const canViewDataset = hasPermissionAllScope(
    ABILITY_NEW.can_view_dataset,
    project.moduleId,
    project.id
  );

  const canUpdateProjects = hasPermissionAllScope(
    ABILITY_NEW.can_update_projects,
    project.moduleId,
    project.id
  );

  const canCreateProjects = hasPermissionAllScope(
    ABILITY_NEW.can_create_projects,
    project.moduleId
  );

  const canUpdateStatusProjects = hasPermissionAllScope(
    ABILITY_NEW.can_update_status_projects,
    project.moduleId,
    project.id
  );

  const history = useHistory();

  const canRemoveProjects = useMemo(
    () =>
      hasPermissionAllScope(
        ABILITY_NEW.can_remove_projects,
        project.moduleId,
        project.id
      ) && !project.isDelete,
    [hasPermissionAllScope, project.id, project.isDelete, project.moduleId]
  );

  const redirectToSettingProject = (e) => {
    e.stopPropagation();
    setModuleType("");
    history.push(
      `/modules/${project.moduleId}/projects/${project.id}/settings-project`
    );
  };

  return (
    <>
      <Block name="general-info">
        <Elem name="title">
          <Elem name="name">
            <Paragraph
              className="text-name"
              style={{ maxWidth: 303 }}
              ellipsis={{ tooltip: project.name }}
            >
              {project.name}
            </Paragraph>
          </Elem>
          <Elem name="action">
            {canUpdateProjects && isActiveProject ? (
              <Tooltip title="Project settings">
                <Button
                  corner="Circle"
                  className="bg-[#EDEFF2] border-none hover:!bg-[#F5F6F7] duration-300 btn-quick-view-project-settings"
                  size="xs"
                  icon={<IconSetting color="#334466" />}
                  onClick={redirectToSettingProject}
                />
              </Tooltip>
            ) : null}

            {!isPublicVersion &&
            (canViewDataset || canUpdateProjects) &&
            canCreateProjects &&
            isActiveProject ? (
              <Tooltip title="Clone project">
                <Button
                  corner="Circle"
                  className="bg-[#8833FF1A] border-none hover:!bg-[rgba(136,51,255,0.20)] duration-300"
                  size="xs"
                  icon={<IconCopy color="#8833FF" />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setOpenCloneProject(true);
                  }}
                />
              </Tooltip>
            ) : null}

            {canUpdateProjects && canUpdateStatusProjects ? (
              <Tooltip
                title={
                  isActiveProject ? "Inactivate project" : "Activate project"
                }
              >
                <Button
                  corner="Circle"
                  className={classNames("border-none duration-300", {
                    "bg-[#3361FF1A] hover:!bg-[rgba(51,97,255,0.10)]":
                      !isActiveProject,
                    "bg-[#FFCB331A] hover:!bg-[rgba(255,203,51,0.20)]":
                      isActiveProject,
                  })}
                  size="xs"
                  icon={
                    isActiveProject ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM9.99951 15.9998C9.44951 15.9998 8.99951 15.5498 8.99951 14.9998V8.99976C8.99951 8.44976 9.44951 7.99976 9.99951 7.99976C10.5495 7.99976 10.9995 8.44976 10.9995 8.99976V14.9998C10.9995 15.5498 10.5495 15.9998 9.99951 15.9998ZM14.001 15.9998C13.451 15.9998 13.001 15.5498 13.001 14.9998V8.99976C13.001 8.44976 13.451 7.99976 14.001 7.99976C14.551 7.99976 15.001 8.44976 15.001 8.99976V14.9998C15.001 15.5498 14.551 15.9998 14.001 15.9998Z"
                          fill="#FFCB33"
                        />
                      </svg>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M12 3C7.032 3 3 7.032 3 12C3 16.968 7.032 21 12 21C16.968 21 21 16.968 21 12C21 7.032 16.968 3 12 3ZM10.2 15.1498V8.84984C10.2 8.48084 10.623 8.26484 10.92 8.48984L15.123 11.6398C15.366 11.8198 15.366 12.1798 15.123 12.3598L10.92 15.5098C10.623 15.7348 10.2 15.5188 10.2 15.1498Z"
                          fill="#3361FF"
                        />
                      </svg>
                    )
                  }
                  onClick={(e) => {
                    e.stopPropagation();
                    handleUpdateStatus();
                  }}
                />
              </Tooltip>
            ) : null}

            {canRemoveProjects && (
              <Tooltip title="Delete this project">
                <Button
                  corner="Circle"
                  className="bg-[#E62E2E1A] border-none hover:!bg-[rgba(230,46,46,0.20)] duration-300"
                  size="xs"
                  icon={<IconTrash color="#E62E2E" />}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteProject();
                  }}
                />
              </Tooltip>
            )}
          </Elem>
        </Elem>

        {project.description && (
          <Elem name="description">
            <Paragraph
              className="text-description"
              ellipsis={{ rows: 2, tooltip: project.description }}
            >
              {project.description}
            </Paragraph>
          </Elem>
        )}
      </Block>

      {openCloneProject && (
        <CloneProject
          project={project}
          open={openCloneProject}
          reFetchProject={fetchProject}
          onClose={() => setOpenCloneProject(false)}
        />
      )}
    </>
  );
};
