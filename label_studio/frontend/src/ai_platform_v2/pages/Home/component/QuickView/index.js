import { Block, Elem } from "../../../../utils/bem";
import { GeneralInfo } from "./GeneralInfo/GeneralInfo";
import { TooltipListItem } from "../../../../../components_lse/TooltipListItem/TooltipListItem";
import { format } from "date-fns";
import "./QuickView.styl";
import { ProjectColor } from "./ProjectColor/ProjectColor";
import { Progress } from "./Progress/Progress";
import { ProjectWorkflow } from "./ProjectWorkflow/ProjectWorkflow";
import { ProjectMember } from "./ProjectMember/ProjectMember";
import { ModalConfirmBig } from "@taureau/ui";
import { memo, useCallback } from "react";
import { useAPI } from "@/providers/ApiProvider";

import { ColorPicker } from "@v2/component/ColorPicker/ColorPicker";
import { PROJECT_COLORS } from "@v2/utils/colors";

const QuickView = ({
  open,
  fetchProject,
  project = {},
  projectMembers = [],
  projectProgress = {},
  waitingProgress,
  onOpenChange,
  handleDeleteProject,
  updateColorProjectCb,
  isActiveProject,
  children,
}) => {
  const api = useAPI();
  const { New, Labeling, Inreview, Completed, AIPrediction } = projectProgress;

  const updateProjectColor = useCallback(
    async (color) => {
      const res = await api.callApi("updateProject", {
        params: { pk: project.id },
        body: { color },
      });

      if (res?.success) {
        updateColorProjectCb(project.id, color);
      }
    },
    [api, project, updateColorProjectCb]
  );

  const confirm = useCallback(
    (color) => {
      ModalConfirmBig.confirm({
        // bodyClassName: "confirm-update-color",
        title: "Confirmation",
        content: `You want to update color for ${
          project?.name ? project?.name : "the project"
        }?`,
        onOk: () => {
          updateProjectColor(color);
        },
        //   onCancel: () => {
        //     onOpenChange(true);
        //   },
      });
    },
    [project, updateProjectColor]
  );

  const content = (
    <Block
      name="project-quick-view"
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      <Elem name="general-info">
        <GeneralInfo
          project={project}
          fetchProject={fetchProject}
          handleDeleteProject={handleDeleteProject}
        />
      </Elem>

      <Elem name="progress">
        <Progress
          loading={waitingProgress}
          chartData={{
            total: New + Labeling + Inreview + Completed + (AIPrediction || 0),
            completed: Completed,
          }}
          legendData={{
            new: New,
            labeling: Labeling,
            inreview: Inreview,
            completed: Completed,
            aiPrediction: AIPrediction || 0,
          }}
        />
      </Elem>

      <Elem name="workflow">
        <ProjectWorkflow
          workflow={project?.annotationWorkFlow}
          preAnnotationWorkflow={project?.preProccessConfig}
        />
      </Elem>

      <Elem name="member">
        <ProjectMember projectMembers={projectMembers} />
      </Elem>

      <Elem name="color">
        <div className="text-[14px]  text-gray-blue-grey-blue-40 font-semibold mb-2.5 leading-[30px]">
          COLOR
        </div>
        <ColorPicker
          onChange={confirm}
          value={project?.color}
          listColors={PROJECT_COLORS}
          className="!gap-2.5 !justify-center !p-0"
          isDisabled={!isActiveProject}
        />
      </Elem>

      <Elem name="date">
        <Elem name="created-date">
          {/* {`Created at ${format(new Date(project.createdAt + "Z"), "dd MMM ’yy, HH:mm")}`} */}
          {`Created at ${format(
            new Date(project.createdAt),
            "dd MMM yy, HH:mm"
          )}`}
        </Elem>
        <Elem name="updated-date">
          {/* {`Updated at ${format(new Date(project.updatedAt + "Z"), "dd MMM ’yy, HH:mm")}`} */}
          {`Updated at ${format(
            new Date(project.updatedAt),
            "dd MMM yy, HH:mm"
          )}`}
        </Elem>
      </Elem>
    </Block>
  );

  return (
    <TooltipListItem
      // open={open}
      trigger="click"
      color="white"
      placement="rightTop"
      overlayClassName="quick-view-tooltip"
      maxHeight="max-content"
      overlayList={content}
      onOpenChange={onOpenChange}
      parentNodeId="tooltipParent"
    >
      {children}
    </TooltipListItem>
  );
};

export default memo(QuickView);
