import IconPlayArrow from "@v2/assets/Icons/IconPlayArrow";
import IconPreProcessingCompleted from "@v2/assets/Icons/IconPreProcessingCompleted";
import IconPreProcessingHuman from "@v2/assets/Icons/IconPreProcessingHuman";
import IconPreProcessingNew from "@v2/assets/Icons/IconPreProcessingNew";
import IconWorkflowAIPrediction from "@v2/assets/Icons/IconWorkflowAIPrediction";
import IconWorkflowCompleted from "@v2/assets/Icons/IconWorkflowCompleted";
import IconWorkflowLabeling from "@v2/assets/Icons/IconWorkflowLabeling";
import IconWorkflowNew from "@v2/assets/Icons/IconWorkflowNew";
import IconWorkflowReviewing from "@v2/assets/Icons/IconWorkflowReviewing";

import {
  orderPreProcessingWorkflow,
  orderWorkflow,
} from "@v2/pages/TaskManagers/helper";
import { useMemo, useRef, useState } from "react";
import { Block, Elem } from "../../../../../utils/bem";
// import "./Project.css";
import { concat } from "lodash";
import "./ProjectWorkflow.styl";

const WorkflowNode = ({ type }) => {
  switch (type) {
    case "New":
      return (
        <div>
          <div className="bg-[#2EE6CA] border-solid border-gray-blue-97 w-[30px] aspect-square rounded-full flex items-center justify-center">
            <IconWorkflowNew color="white" size={24} />
          </div>
        </div>
      );
    case "Labeling":
      return (
        <div>
          <div
            className={`bg-[#3361FF] w-[30px] aspect-square rounded-full flex items-center justify-center`}
          >
            <IconWorkflowLabeling color="white" size={24} />
          </div>
        </div>
      );
    case "Inreview":
      return (
        <div>
          <div
            className={`bg-[#FFCB33] w-[30px] aspect-square rounded-full flex items-center justify-center`}
          >
            <IconWorkflowReviewing color="white" size={24} />
          </div>
        </div>
      );
    case "Completed":
      return (
        <div>
          <div className="bg-[#29CC39] border-solid border-gray-blue-97 w-[30px] aspect-square rounded-full flex items-center justify-center">
            <IconWorkflowCompleted color="white" size={24} />
          </div>
        </div>
      );
    case "AIPrediction":
      return (
        <div>
          <div className="bg-white border-solid border-gray-blue-97 w-[30px] aspect-square rounded-full flex items-center justify-center">
            <IconWorkflowAIPrediction />
          </div>
        </div>
      );
    case "PreProcessingNew":
      return (
        <div>
          <div className="bg-white border-solid border-gray-blue-97 w-[30px] aspect-square rounded-full flex items-center justify-center">
            <IconPreProcessingNew />
          </div>
        </div>
      );
    case "HumanApproval":
      return (
        <div>
          <div className="bg-white border-solid border-gray-blue-97 w-[30px] aspect-square rounded-full flex items-center justify-center">
            <IconPreProcessingHuman />
          </div>
        </div>
      );
    case "PreProcessingCompleted":
      return (
        <div>
          <div className="bg-white border-solid border-gray-blue-97 w-[30px] aspect-square rounded-full flex items-center justify-center">
            <IconPreProcessingCompleted />
          </div>
        </div>
      );
    default:
      return;
  }
};

export const ProjectWorkflow = ({
  label = "WORKFLOW",
  workflow,
  preAnnotationWorkflow,
}) => {
  // Parse workflow from string to object
  const parsedWorkflow = useMemo(() => {
    if (!workflow) {
      return null;
    }

    try {
      return JSON.parse(workflow);
    } catch (e) {
      return null;
    }
  }, [workflow]);

  const parsedPreWorkflow = useMemo(() => {
    if (!preAnnotationWorkflow) {
      return null;
    }

    try {
      return JSON.parse(preAnnotationWorkflow);
    } catch (e) {
      return null;
    }
  }, [preAnnotationWorkflow]);

  const orderedWorkflow = useMemo(() => {
    return orderWorkflow(parsedWorkflow);
  }, [parsedWorkflow]);

  const orderedPreWorkflow = useMemo(() => {
    return orderPreProcessingWorkflow(parsedPreWorkflow);
  }, [parsedPreWorkflow]);

  // Add Play button inbetween nodes to orderedWorkflow
  const paddedWorkflow = useMemo(() => {
    if (!orderedWorkflow?.length) {
      return null;
    }

    const result = concat(orderedPreWorkflow, orderedWorkflow).reduce(
      (acc, node, index) => {
        if (index === 0) {
          return [node];
        }

        return [...acc, { type: "_padding_" }, node];
      },
      []
    );

    return result;
  }, [orderedWorkflow, orderedPreWorkflow]);

  const containerRef = useRef(null);
  const [scrollInterval, setScrollInterval] = useState(null);
  const stopScroll = () => {
    clearInterval(scrollInterval);
  };

  const handleMouseOver = (e) => {
    if (!containerRef.current) {
      return;
    }
    const container = containerRef.current;
    const containerWidth = container.offsetWidth;
    const { left } = container.getBoundingClientRect();

    const isRightEnd = e.pageX > left + containerWidth - 20;
    const isLeftEnd = e.pageX < left + 20;

    let scrollSpeed = 0;

    if (isRightEnd) {
      scrollSpeed = 5;
    } else if (isLeftEnd) {
      scrollSpeed = -5;
    }

    clearInterval(scrollInterval);

    setScrollInterval(
      setInterval(() => {
        container.scrollLeft += scrollSpeed;
      }, 10)
    );

    container.addEventListener("mouseleave", stopScroll);
    container.addEventListener("mouseup", stopScroll);
  };

  const field = (
    <Block
      name="project-workflow"
      ref={containerRef}
      onMouseOver={handleMouseOver}
    >
      {paddedWorkflow?.map((node) => {
        if (node.type === "_padding_") {
          return (
            <Elem name="arrow">
              <IconPlayArrow color="#C3CAD9" size={20} />
            </Elem>
          );
        }
        return (
          <WorkflowNode key={node.name} name={node.name} type={node.type} />
        );
      })}
    </Block>
  );

  return label ? (
    <>
      <div className="text-[14px]  text-gray-blue-grey-blue-40 font-semibold mb-2.5 leading-[30px]">
        {label}
      </div>
      <div className="flex w-full justify-center">{field}</div>
    </>
  ) : (
    field
  );
};
export default ProjectWorkflow;
