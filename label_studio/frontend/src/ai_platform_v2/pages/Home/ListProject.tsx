import IconCreateProject from "@/ai_platform_v2/assets/Icons/IconCreateProject";
import { Pagination } from "@/ai_platform_v2/component/Pagination/Pagination";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { memo, useCallback, useMemo, useState } from "react";
import { CreateProject } from "../CreateProject/CreateProject";
import { ProjectCard } from "./component/ProjectCard";
import { ProjectCardLoading } from "./component/ProjectCardLoading";
import { getCurrentModule } from "./Home";

import { LimitWrapper } from "@/ai_platform_v2/component/Limit/Limit";
import SearchNodata from "@/ai_platform_v2/component/SearchNodata";
import { useModule } from "@/ai_platform_v2/providers/ModuleProviderNew";
import useModulesStore from "@/ai_platform_v2/stores/modulesStore";
import { useCurrentUser } from "@/providers/CurrentUser";
import { Modal } from "antd";
import { IProject } from "../../types/project/project.types";
import Nodata from "./component/Nodata";
import "./ListProject.css";

const GRID_GAP = 20;

interface Props {
  listProject: any;
  currentPage: number;
  currentPageSize: number;
  totalItems: number;
  setCurrentPage: any;
  setCurrentPageSize: any;
  fetchProject: () => void;
  loadData: boolean;
  filterStatus: any;
  isDelete: boolean;
  updateColorProjectCb: (projectId: string, color: string) => void;
  searchName?: string;

  width: number;
  height: number;
}

const ListProject = (props: Props) => {
  const {
    fetchProject,
    isDelete,
    updateColorProjectCb,
    listProject,
    searchName,
    filterStatus,

    width,
    height,
  } = props;
  const { module } = useModule();
  const { canCreateProjectLimit } = useCurrentUser();

  const currentModuleId = getCurrentModule() ?? module?.id;
  const isNotCurrentModule = !currentModuleId || currentModuleId === "all";
  const modulesSize = useModulesStore(
    (state) => state.modulesList?.length ?? 0
  );

  const [openCreateProject, setOpenCreateProject] = useState(false);

  const onOpenCreateProject = useCallback(() => {
    if (canCreateProjectLimit) {
      setOpenCreateProject(true);
    } else {
      Modal.info({
        title: "",
        content: (
          <LimitWrapper content="Sorry about that, but you’ve exceeded the 5 free projects for your plan" />
        ),
        width: 700,
        className: "reached-resource-limit",
        icon: <></>,
        maskClosable: true,
        centered: true,
        closable: true,
        okText: null,
        okButtonProps: { style: { display: "none" } },
      });
    }
  }, [canCreateProjectLimit]);

  const [moreProject, setMoreProject] = useState(null);

  const loadNextPage = useCallback(
    async (page: number, pageSize: number) => {
      props.setCurrentPage(page);
      props.setCurrentPageSize(pageSize);
      localStorage.setItem("pages:projects-list", pageSize.toString());
    },
    [props]
  );

  const { hasPermissionAllScope, hasModulePermissionCreateProject } =
    useCheckPermission();

  let canCreateProjects = hasModulePermissionCreateProject();

  if (!isNotCurrentModule) {
    canCreateProjects =
      hasPermissionAllScope(ABILITY_NEW.can_create_projects, currentModuleId) ??
      false;
  }

  const canCreateProjectsFinal = useMemo(() => {
    const canShow =
      canCreateProjects &&
      (props.filterStatus === null || props.filterStatus === "Active") &&
      !isDelete;

    if (currentModuleId === "all") {
      return canShow && modulesSize > 0;
    }

    return canShow && module?.status === "Active" && !module?.isDelete;
  }, [
    canCreateProjects,
    props.filterStatus,
    isDelete,
    module,
    currentModuleId,
    modulesSize,
  ]);

  const itemNo = useMemo(() => {
    if (width < 620) return 1;
    if (width < 900) return 2;
    if (width < 1200) return 3;
    if (width < 1500) return 4;
    if (width < 1800) return 5;
    if (width < 2100) return 6;
    if (width < 2400) return 7;
    if (width < 2700) return 8;
    if (width < 3000) return 9;
    if (width < 3300) return 10;
    if (width < 3600) return 11;
    if (width < 3900) return 12;
    if (width < 4200) return 13;
    if (width < 4500) return 14;

    return 15;
  }, [width]);

  const renderContent = useCallback(() => {
    if (!listProject.length) {
      if (!searchName) {
        if (filterStatus === "Active" || (filterStatus === null && !isDelete)) {
          return (
            <Nodata
              openCreateProject={() => {
                onOpenCreateProject();
              }}
              canCreateProject={canCreateProjectsFinal}
              buttonContent="Create Annotation Project"
            />
          );
        }
        return <SearchNodata />;
      }
      return <SearchNodata />;
    }
    return (
      <div
        className="container-list-project h-fit w-full overflow-visible p-5 relative"
        id="tooltipParent"
        style={{
          gridTemplateColumns:
            itemNo === 1
              ? "repeat(1, minmax(auto, 1fr)"
              : `repeat(${itemNo}, minmax(auto, calc((100% - ${
                  (itemNo - 1) * GRID_GAP
                }px) / ${itemNo})))`,
        }}
      >
        {canCreateProjectsFinal ? (
          <div
            className="create-project flex items-center justify-center flex-col gap-[20px]"
            onClick={() => {
              onOpenCreateProject();
            }}
          >
            <IconCreateProject />
            <div className="text-create-project">Create Annotation Project</div>
          </div>
        ) : null}
        {listProject.map((item: IProject) => {
          return (
            <ProjectCard
              key={item.id}
              project={item}
              fetchProject={fetchProject}
              updateColorProjectCb={updateColorProjectCb}
              moreProject={moreProject}
              setMoreProject={setMoreProject}
            />
          );
        })}
      </div>
    );
  }, [
    canCreateProjectsFinal,
    fetchProject,
    listProject,
    searchName,
    updateColorProjectCb,
    itemNo,
  ]);

  return (
    <div
      className="flex flex-col bg-white overflow-y-hidden shadow-Shadows/Gray-Blue-30/5%/5b rounded-[10px] scrollbar-v-sm"
      style={{ width, height }}
    >
      <div className="h-full w-full overflow-y-scroll">
        {props.loadData ? (
          // Render loading ui
          <div
            className="container-list-project w-full overflow-y-hidden p-5"
            style={{
              gridTemplateColumns:
                itemNo === 1
                  ? "repeat(1, minmax(auto, 1fr)"
                  : `repeat(${itemNo}, minmax(auto, calc((100% - ${
                      (itemNo - 1) * GRID_GAP
                    }px) / ${itemNo})))`,
            }}
          >
            {[...Array(8).keys()].map((i) => (
              <ProjectCardLoading key={i} />
            ))}
          </div>
        ) : (
          renderContent()
        )}
      </div>
      <div className="w-full flex items-center justify-end pt-[5px] pb-2.5">
        <Pagination
          size="large"
          name="dataset-list"
          page={props.currentPage}
          totalItems={props.totalItems}
          deafultPageSize={props.currentPageSize}
          pageSizeOptions={[5, 10, 30, 50, 100]}
          pageSize={props.currentPageSize}
          onInit={(page, pageSize) => {
            props.setCurrentPage(page);
            props.setCurrentPageSize(pageSize);
            localStorage.setItem("pages:projects-list", pageSize.toString());
          }}
          onChange={(page, pageSize) => {
            props.setCurrentPage(page);
            props.setCurrentPageSize(pageSize);
            localStorage.setItem("pages:projects-list", pageSize.toString());
          }}
          onPageLoad={(page, pageSize) => loadNextPage(page, pageSize)}
        />
      </div>

      {canCreateProjects && (
        <CreateProject
          open={openCreateProject}
          currentModule={isNotCurrentModule ? undefined : currentModuleId}
          onClose={() => {
            props.fetchProject();
            setOpenCreateProject(false);
          }}
        />
      )}
    </div>
  );
};

export default memo(ListProject);
