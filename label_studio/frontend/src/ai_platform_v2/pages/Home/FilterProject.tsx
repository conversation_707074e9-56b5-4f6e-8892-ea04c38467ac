import IconSearch from "@/ai_platform_v2/assets/Icons/IconSearch";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { useAppStore } from "@/providers/AppStoreProvider";
import { useCheckPermission, usePermissions } from "@/providers/PermissionProvider";
import { Button, Input } from "@taureau/ui";
import classNames from "classnames";
import debounce from "lodash.debounce";
import React, { memo, useCallback, useMemo, useState } from "react";
import { CloneProject } from "./component/CloneProject/CloneProject";
import { SortOut } from "./component/SortOut";
import { ModuleType } from "./utils/const";
import { IModule } from "@/ai_platform_v2/types/module/module.types";

interface Props {
  module?: IModule;
  moduleType?: ModuleType;
  fetchProject?: () => void;
  setSearchName: any;
  filterStatus: any;
  onFilterStatus: (value: any) => void;
  sortByColumn: any;
  orderBy: any;
  onSelectSortByColumn: (value: any) => void;
  onOrderBy: (value: any) => void;

  // FILTER DELETE PROJECT
  isDelete: boolean;
  setDelete: (value: boolean) => void;
}

const FilterProject = (props: Props) => {
  const {
    module,
    moduleType,
    fetchProject,
    filterStatus,
    onFilterStatus,
    sortByColumn,
    orderBy,
    onSelectSortByColumn,
    onOrderBy,
    isDelete,
    setDelete,
  } = props;
  const [searchValue, setSearchValue] = useState("");
  const [openCloneProject, setOpenCloneProject] = useState(false);
  const { loading } = usePermissions();

  const isHideCloneAction = useMemo(() => {
    if (
      loading ||
      module?.status === "Inactive" ||
      moduleType === ModuleType.ML
    ) {
      return false;
    }

    return true;
  }, [loading, module, moduleType]);

  const { isPublicVersion } = useAppStore();

  const { hasPermissionAllScope, hasModulePermissionCreateProject } =
    useCheckPermission();

  const canCreateProjects = module?.id
    ? hasPermissionAllScope(ABILITY_NEW.can_create_projects, module?.id)
    : hasModulePermissionCreateProject();

  const canRevertProjects = useMemo(
    () => hasPermissionAllScope(ABILITY_NEW.can_revert_projects),
    [hasPermissionAllScope]
  );

  const debounceSearch = useCallback(
    debounce((value: string) => {
      props.setSearchName(value);
    }, 300),
    []
  );

  const onChangeSearchValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    setSearchValue(value);
    debounceSearch(value.trim());
  };

  const onPressEnter = useCallback(
    (e: { key: string }) => {
      if (e.key === "Enter") {
        props.setSearchName(searchValue);
      }
    },
    [props, searchValue]
  );

  return (
    <div className="flex flex-col w-full gap-[10px] py-2">
      <div className="flex flex-row w-full h-[40px] gap-[15px]">
        <Input
          placeholder="Search project"
          className="bg-white w-full h-full rounded-[10px] px-[15px]"
          inputClassName="placeholder:text-[#C3CAD9] h-full w-full text-[14px]"
          iconLeft={<IconSearch />}
          iconLeftClassName="m-0 mr-2.5"
          size="sm"
          value={searchValue}
          onChange={onChangeSearchValue}
          onKeyPress={onPressEnter}
        />
        <SortOut
          value={sortByColumn}
          valueOrder={orderBy}
          onSelect={onSelectSortByColumn}
          onOrder={onOrderBy}
        />

        {isHideCloneAction && canCreateProjects && !isPublicVersion ? (
          <Button
            theme="Primary"
            className="min-w-[129px] h-full"
            size="sm"
            onClick={() => setOpenCloneProject(true)}
          >
            Clone Project
          </Button>
        ) : null}
      </div>
      <div className="flex flex-row w-full gap-[7px] h-[20px]">
        <Button
          theme={!filterStatus && !isDelete ? "Primary" : "Light"}
          className="h-[20px] rounded-[15px]  text-[14px] font-medium !py-0 leading-[21px]"
          size="sm"
          onClick={() => {
            if (isDelete) {
              setDelete(false);
            } else {
              onFilterStatus?.(filterStatus ? null : "Active");
            }
          }}
        >
          All
        </Button>
        <Button
          theme={filterStatus === "Active" ? "Primary" : "Light"}
          className="h-[20px] rounded-[15px]  text-[14px] font-medium !py-0 leading-[21px]"
          size="sm"
          onClick={() => {
            onFilterStatus?.("Active");
            isDelete && setDelete(false);
          }}
        >
          Active
        </Button>
        <Button
          theme={filterStatus === "Inactive" ? "Primary" : "Light"}
          className="h-[20px] rounded-[15px]  text-[14px] font-medium !py-0 leading-[21px]"
          size="sm"
          onClick={() => {
            onFilterStatus?.(
              filterStatus !== "Inactive" ? "Inactive" : "Active"
            );
            isDelete && setDelete(false);
          }}
        >
          Inactive
        </Button>

        {canRevertProjects && (
          <Button
            className={classNames(
              "h-[20px] rounded-[15px]  text-[14px] font-medium !py-0 leading-[21px]",
              {
                "bg-[#CC1414] border-2 border-[#CC1414] shadow-Shadows/Red/30%/5b text-white hover:bg-[#CC1414]":
                  isDelete,
              }
            )}
            size="sm"
            onClick={() => {
              if (isDelete) {
                onFilterStatus?.("Active");
                setDelete(false);
              } else {
                onFilterStatus?.(null);
                setDelete(true);
              }
            }}
          >
            Deleted
          </Button>
        )}
      </div>

      {isHideCloneAction && canCreateProjects && openCloneProject && (
        <CloneProject
          open={openCloneProject}
          reFetchProject={fetchProject}
          moduleId={module?.id}
          onClose={() => setOpenCloneProject(false)}
        />
      )}
    </div>
  );
};

export default memo(FilterProject);
