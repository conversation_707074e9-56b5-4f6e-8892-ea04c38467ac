import React, { memo, useMemo } from "react";
import InformationModule from "./InformationModule";
import FilterProject from "./FilterProject";
import ListProject from "./ListProject";
import ListNonProject from "./NonAnnotationProject/ListNonAnnotationProject/ListProject";
import ListMLModulesProject from "./MLModulesProject/ListMLModuleProject/ListProject";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { PermissionMessage } from "@/components/PermissionMessage/PermissionMessage";
import useModulesStore from "@/ai_platform_v2/stores/modulesStore";

import AutoSizer from "react-virtualized-auto-sizer";
import { ModuleType, ProjectType, projectTypeList } from "./utils/const";
import ProjectTab from "./component/ProjectTab";

interface Props {
  listProject: any;
  setSearchName: any;
  currentPage: number;
  currentPageSize: number;
  totalItems: number;
  setCurrentPage: any;
  setCurrentPageSize: any;
  projectType: any;
  setProjectType: any;
  fetchProject: () => void;
  loadData: boolean;
  filterStatus: any;
  onFilterStatus: (value: any) => void;
  sortByColumn: any;
  orderBy: any;
  onSelectSortByColumn: (value: any) => void;
  onOrderBy: (value: any) => void;

  updateColorProjectCb: (projectId: string, color: string) => void;

  searchName?: string;

  // FILTER DELETE PROJECT
  isDelete: boolean;
  setDelete: (value: boolean) => void;

  menuBarRef: any;
}

const ContentHome = (props: Props) => {
  const {
    projectType,
    setProjectType,
    listProject,
    filterStatus,
    onFilterStatus,
    sortByColumn,
    orderBy,
    onSelectSortByColumn,
    onOrderBy,
    fetchProject,
    isDelete,
    setDelete,
    updateColorProjectCb,
    searchName,
    menuBarRef,
  } = props;

  const { module, moduleType } = useModulesStore((state) => ({
    module: state.currentModule,
    moduleType: state.moduleType,
  }));

  const { hasPermissionAllScope } = useCheckPermission();
  const canUpdateModules = hasPermissionAllScope(
    ABILITY_NEW.can_update_modules,
    module?.id
  );
  const canUpdateStatusModules = hasPermissionAllScope(
    ABILITY_NEW.can_update_status_modules,
    module?.id
  );

  const isInactiveModule = useMemo(
    () => module?.status === "Inactive",
    [module]
  );

  const isHidden = useMemo(() => {
    return isInactiveModule && (!canUpdateModules || !canUpdateStatusModules);
  }, [canUpdateModules, canUpdateStatusModules, isInactiveModule]);

  return (
    <div className="w-full h-full flex flex-col">
      {!isHidden ? (
        <>
          <div className="flex flex-col pl-[5px] mb-1">
            <InformationModule />
            {moduleType === ModuleType.Data && (
              <ProjectTab
                activeKey={projectType}
                options={projectTypeList}
                onChange={(value) => setProjectType(value)}
              />
            )}
          </div>
          <FilterProject
            module={module}
            moduleType={moduleType as ModuleType}
            setSearchName={props.setSearchName}
            filterStatus={filterStatus}
            onFilterStatus={onFilterStatus}
            sortByColumn={sortByColumn}
            orderBy={orderBy}
            fetchProject={() => {
              menuBarRef?.current?.countAllProject();
              fetchProject();
            }}
            onSelectSortByColumn={onSelectSortByColumn}
            onOrderBy={onOrderBy}
            isDelete={isDelete}
            setDelete={setDelete}
          />

          <div className="w-full h-full">
            <AutoSizer>
              {({ width, height }) => {
                if (
                  moduleType === ModuleType.Data &&
                  projectType === ProjectType.Annotation
                ) {
                  return (
                    <ListProject
                      listProject={listProject}
                      currentPage={props.currentPage}
                      currentPageSize={props.currentPageSize}
                      totalItems={props.totalItems}
                      setCurrentPage={props.setCurrentPage}
                      setCurrentPageSize={props.setCurrentPageSize}
                      fetchProject={() => {
                        menuBarRef?.current?.countAllProject();
                        fetchProject();
                      }}
                      loadData={props.loadData}
                      filterStatus={filterStatus}
                      isDelete={isDelete}
                      updateColorProjectCb={updateColorProjectCb}
                      searchName={searchName}
                      width={width}
                      height={height}
                    />
                  );
                } else if (
                  moduleType === ModuleType.Data &&
                  projectType === ProjectType.NonAnnotation
                ) {
                  return (
                    <ListNonProject
                      listProject={listProject}
                      currentPage={props.currentPage}
                      currentPageSize={props.currentPageSize}
                      totalItems={props.totalItems}
                      setCurrentPage={props.setCurrentPage}
                      setCurrentPageSize={props.setCurrentPageSize}
                      fetchProject={() => {
                        menuBarRef?.current?.countAllProject();
                        fetchProject();
                      }}
                      loadData={props.loadData}
                      filterStatus={filterStatus}
                      isDelete={isDelete}
                      updateColorProjectCb={updateColorProjectCb}
                      searchName={searchName}
                      width={width}
                      height={height}
                    />
                  );
                } else {
                  return (
                    <ListMLModulesProject
                      listProject={listProject}
                      currentPage={props.currentPage}
                      currentPageSize={props.currentPageSize}
                      totalItems={props.totalItems}
                      setCurrentPage={props.setCurrentPage}
                      setCurrentPageSize={props.setCurrentPageSize}
                      fetchProject={() => {
                        menuBarRef?.current?.countAllProject();
                        fetchProject();
                      }}
                      loadData={props.loadData}
                      filterStatus={filterStatus}
                      isDelete={isDelete}
                      updateColorProjectCb={updateColorProjectCb}
                      searchName={searchName}
                      width={width}
                      height={height}
                    />
                  );
                }
              }}
            </AutoSizer>
          </div>
        </>
      ) : (
        <PermissionMessage />
      )}
    </div>
  );
};

export default memo(ContentHome);
