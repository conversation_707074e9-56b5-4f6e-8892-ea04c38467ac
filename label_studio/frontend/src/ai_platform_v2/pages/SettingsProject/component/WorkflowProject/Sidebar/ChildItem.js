import classNames from "classnames";
import React, { memo, useState } from "react";

const ChildItem = ({ item, onDrag }) => {
  const { icon, description, title, value, color, hover } = item;

  const [isHovered, setHovered] = useState(false);

  return (
    <div
      className={classNames(
        "w-[157px] h-[82px] flex flex-col items-center justify-center px-1 pt-[2px] pb-2 rounded-[12px] cursor-pointer"
      )}
      draggable
      onDragStart={(e) => onDrag(e, value)}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      style={{
        background: isHovered ? hover : "#fff",
      }}
    >
      <div className="min-w-6">{icon}</div>
      <span
        className={classNames(`text-[12px] leading-[18px] font-semibold`)}
        style={{ color }}
      >
        {title}
      </span>
      <span
        className={classNames(
          `text-[10px] leading-[12px] text-[#346] text-center font-medium`
        )}
        style={{ wordBreak: "break-word" }}
      >
        {description}
      </span>
    </div>
  );
};

export default memo(ChildItem);
