import classNames from "classnames";
import React, { memo } from "react";

const TabItem = ({ isActive, tab, onDrag, onClick }) => {
  const { icon, iconActive, label, value } = tab;

  return (
    <div
      className={classNames(
        "flex gap-1 items-center justify-center px-1 py-[2px] rounded-[8px] cursor-pointer hover:bg-[#3361FF1A]"
      )}
      draggable
      onDragStart={(e) => onDrag(e, value)}
      onClick={onClick}
    >
      <div className="min-w-6">{isActive ? iconActive : icon}</div>
      <span
        className={classNames("text-[12px] leading-[18px] font-medium", {
          "text-[#3361FF]": isActive,
          "text-[#346]": !isActive,
        })}
      >
        {label}
      </span>
    </div>
  );
};

export default memo(TabItem);
