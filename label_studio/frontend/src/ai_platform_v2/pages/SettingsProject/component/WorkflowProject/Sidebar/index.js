import React, { memo, useCallback, useState } from "react";
import IconLabelingFile from "@/ai_platform_v2/assets/Icons/IconLabelingFile";
import IconReviewFile from "@/ai_platform_v2/assets/Icons/IconReviewFile";
import IconNewFile from "@/ai_platform_v2/assets/Icons/IconNewFile";
import IconCompletedFile from "@/ai_platform_v2/assets/Icons/IconCompletedFile";
import IconAIPrediction from "../Icon/AIPrediction";

import {
  AnnotationBlockIcon,
  HumanApprovalIcon,
  PreProcessingCompletedIcon,
  PreProcessingIcon,
  PreProcessingNewIcon,
} from "@/ai_platform_v2/assets/Icons/WorkflowIcons";

import TabItem from "./TabItem";
import classNames from "classnames";
import ChildItem from "./ChildItem";

const TABS = [
  {
    label: "Pre-processing block",
    value: "PreProcessingBlock",
    icon: <PreProcessingIcon />,
    iconActive: <PreProcessingIcon color="#3361FF" />,
    childItems: [
      {
        icon: <PreProcessingNewIcon />,
        title: "New",
        description: "Data is ready to be proceeded in Temporary Storage",
        value: "PreProcessingNew",
        color: "#818CF8",
        hover: "#F3F4FF",
      },
      {
        icon: <HumanApprovalIcon />,
        title: "Human approval",
        description: "Approve or reject data after evaluation",
        value: "HumanApproval",
        color: "#EDB7ED",
        hover: "#FEF8FE",
      },
      {
        icon: <PreProcessingCompletedIcon />,
        title: "Completed",
        description: "Final check before transfer data to main storage",
        value: "PreProcessingCompleted",
        color: "#15803D",
        hover: "#E8F3EC",
      },
    ],
  },
  {
    label: "Annotation block",
    value: "AnnotationBlock",
    icon: <AnnotationBlockIcon />,
    iconActive: <AnnotationBlockIcon color="#3361FF" />,
    childItems: [
      {
        icon: <IconNewFile active />,
        title: "New",
        description: "Data is ready to be annotated in Data Manager",
        value: "New",
        color: "#2EE6CA",
        hover:
          "linear-gradient(0deg, rgba(46, 230, 202, 0.10) 0%, rgba(46, 230, 202, 0.10) 100%), rgba(255, 255, 255, 0.20)",
      },
      {
        icon: <IconAIPrediction />,
        title: "AI Prediction",
        description: "Run AI Model to get pre-annotated data",
        value: "AIPrediction",
        color: "#33BFFF",
        hover: "#EBF9FF",
      },
      {
        icon: <IconLabelingFile active />,
        title: "Labeling",
        description: "Annotate data by single or multiple labeling interface",
        value: "Labeling",
        color: "#3361FF",
        hover: "#EBF0FF",
      },
      {
        icon: <IconReviewFile active />,
        title: "Reviewing",
        description: "Review annotation results then approve or reject",
        value: "Inreview",
        color: "#FFC20F",
        hover: "#FFFAEB",
      },
      {
        icon: <IconCompletedFile active />,
        title: "Completed",
        description: "Final check before exporting for training purpose",
        value: "Completed",
        color: "#29CC39",
        hover:
          "linear-gradient(0deg, rgba(41, 204, 57, 0.10) 0%, rgba(41, 204, 57, 0.10) 100%)",
      },
    ],
  },
];

const Sidebar = ({ setIsChange, hasAIPredictionNode = true }) => {
  const onDragStart = (event, nodeType) => {
    setIsChange?.();
    event.dataTransfer.setData("application/reactflow", nodeType);
    event.dataTransfer.effectAllowed = "move";
  };

  const [activeTab, setActiveTab] = useState();

  const handleClickTab = useCallback(
    (tab) => {
      if (activeTab?.value === tab.value) {
        setActiveTab();
      } else {
        setActiveTab(tab);
      }
    },
    [activeTab?.value]
  );

  return (
    <>
      <div
        className={classNames("sidebar-workflow", {
          "sidebar-workflow--active": activeTab,
        })}
      >
        {TABS.map((tab, index) => {
          const isActive = tab.value === activeTab?.value;

          return (
            <>
              <TabItem
                key={tab.value}
                isActive={isActive}
                tab={tab}
                onDrag={onDragStart}
                onClick={() => handleClickTab(tab)}
              />
              {index < TABS?.length - 1 && (
                <div className="h-[24px] w-[2px] bg-[#000000] opacity-10" />
              )}
            </>
          );
        })}
      </div>
      {activeTab?.childItems && (
        <div className="sidebar-options">
          {activeTab?.childItems?.map((item) => {
            if (!hasAIPredictionNode && item.value === "AIPrediction")
              return null;
            return (
              <ChildItem item={item} onDrag={onDragStart} key={item.value} />
            );
          })}
        </div>
      )}
    </>
  );
};

export default memo(Sidebar);
