import { Space, Typography } from "antd";
import React, { useContext, useEffect, useMemo, useState } from "react";
import { <PERSON>le, Position } from "reactflow";

import { PreProcessingNewIcon } from "@/ai_platform_v2/assets/Icons/WorkflowIcons";

import IconNewCircle from "@/ai_platform_v2/assets/Icons/NodeIcons/pre-processing-new-circle.svg?url";
import classNames from "classnames";
import { useAPI } from "../../../../../../../providers/ApiProvider";
import {
  getCurrentProject,
  WorkflowContext,
} from "../../../../SettingsProject";
import Icon3DCubeScan from "../../../ModelConfiguration/icons/Icon3DCubeScan";

const { Text } = Typography;
const color = "#818CF8";
const embeddingModelId = "da3bb369-a07a-4b25-b3fa-1df69c12ea35";

export function PreProcessingNewNode({ data, selected }) {
  const currentProject = getCurrentProject();

  const stepId = data?.id;
  const nodeSettings = data?.settings;
  const api = useAPI();

  const {
    needRefreshModel,
    isSelectedModel,
    imageDeduplication,
    setImageDeduplication,
    setIsSelectedModel,
    setIsChangedPreNew,
  } = useContext(WorkflowContext);

  const [loading, setLoading] = useState(false);
  const [systemModelName, setSystemModelName] = useState("");

  const canShowCurrentModel = useMemo(
    () => nodeSettings?.isCopilot && nodeSettings?.isSimilarityCheck,
    [nodeSettings]
  );

  const isCancelPublic = useMemo(() => {
    if (!isSelectedModel) {
      if (
        imageDeduplication?.status === "Cancel" ||
        imageDeduplication?.status === "UnPublish"
      ) {
        return true;
      } else {
        return false;
      }
    }

    return (
      nodeSettings?.systemModelDetailId === imageDeduplication?.id &&
      (imageDeduplication?.status === "Cancel" ||
        imageDeduplication?.status === "UnPublish")
    );
  }, [nodeSettings, imageDeduplication, isSelectedModel]);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(async () => {
    try {
      if (currentProject && stepId) {
        setLoading(true);

        const resApply = await api.callApi("applyToWorkflow", {
          params: {
            pk: currentProject,
            stepId,
          },
        });

        if (resApply.success) {
          const resModel = await api.callApi("categoryModels", {
            params: {
              pk: embeddingModelId,
              page: 1,
              pageSize: 99999,
            },
          });

          if (resModel.success) {
            const findModel = resModel.items?.find(
              (model) => model.id === resApply.data?.systemModelDetailId
            );

            setImageDeduplication({
              applyId: resApply.data?.id,
              ...findModel,
            });

            setIsSelectedModel(false);
            if (!resApply.data?.id) {
              setIsChangedPreNew(true);
            }
          }
        }

        setLoading(false);
      }
    } catch (error) {
      console.log("error: ", error?.message);
    } finally {
      //
    }
  }, [needRefreshModel, currentProject, stepId]); // eslint-disable-next-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (!isSelectedModel) {
      if (!loading) {
        setSystemModelName(imageDeduplication?.modelName ?? "");
      }
    } else {
      setSystemModelName(nodeSettings?.systemModelName ?? "");
    }
  }, [
    imageDeduplication?.modelName,
    nodeSettings?.systemModelName,
    isSelectedModel,
    loading,
  ]);

  return (
    <Space
      className={`workflow-node ${selected ? "pre-processing-new-active" : ""}`}
      direction={"vertical"}
    >
      <div>
        <Handle
          type="source"
          position={Position.Right}
          id="preProcessingNew"
          className={"handle-node"}
          style={{
            backgroundImage: `url(${IconNewCircle})`,
          }}
        />
        <div
          className={classNames("flow-node", {
            "!border-0": !(
              canShowCurrentModel && nodeSettings?.systemModelDetailId
            ),
          })}
        >
          <Space size={"small"}>
            <PreProcessingNewIcon />
            <Text style={{ color }}>{data?.name}</Text>
          </Space>
        </div>
      </div>
      {canShowCurrentModel && nodeSettings?.systemModelDetailId && (
        <Space direction={"vertical"} className="list-user" size={"small"}>
          <div
            className={classNames(
              "flex items-center gap-[8px] px-[8px] text-[#334466] text-[14px] font-medium leading-[15px]",
              {
                "has-current-model": isCancelPublic,
              }
            )}
          >
            <div className="flex">
              <Icon3DCubeScan />
            </div>
            {`${systemModelName} ${isCancelPublic ? " (error)" : ""}`}
          </div>
        </Space>
      )}
    </Space>
  );
}
