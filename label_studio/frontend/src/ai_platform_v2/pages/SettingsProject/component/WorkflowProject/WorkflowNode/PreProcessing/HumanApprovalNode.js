import React, { useContext } from "react";
import { <PERSON><PERSON>, Position } from "reactflow";
import { Avatar, Space, Tooltip, Typography } from "antd";
import CardUser from "../../CardUser";
import { UserContext } from "../../WorkflowSpace";
import Anyone from "../../Icon/Anyone";
import AvatarUser from "../AvatarUser";

import { HumanApprovalIcon } from "@/ai_platform_v2/assets/Icons/WorkflowIcons";

import IconHumanApprovalCircle from "@/ai_platform_v2/assets/Icons/NodeIcons/human-approval-circle.svg?url";

const { Text } = Typography;
const color = "#EDB7ED";

export function HumanApprovalNode({ data, selected }) {
  const { memberPreProcessing } = useContext(UserContext);
  const [users, setUsers] = React.useState([]);

  React.useEffect(() => {
    setUsers(
      memberPreProcessing.filter((user) => {
        return data?.members.find((mem) => mem === user.userId);
      })
    );
  }, [data?.members, memberPreProcessing]);

  return (
    <Space
      className={`workflow-node ${selected ? "human-approval-active" : ""}`}
      direction={"vertical"}
    >
      <div>
        <Handle
          type="target"
          position={Position.Left}
          id="humanApproval-handle-target"
          className={"handle-node"}
          style={{
            top: 23,
            backgroundImage: `url(${IconHumanApprovalCircle})`,
          }}
        />
        <div className="flow-node">
          <Space size={"small"}>
            <HumanApprovalIcon />
            <Text style={{ color }}>{data?.name}</Text>
          </Space>
        </div>
        <Handle
          type="source"
          position={Position.Right}
          id="humanApproval"
          className={"handle-node"}
          style={{
            top: 23,
            backgroundImage: `url(${IconHumanApprovalCircle})`,
          }}
        />
      </div>
      <Space direction={"vertical"} className="list-user" size={"small"}>
        {!users || users.length === 0 ? (
          <CardUser
            icon={
              <div className="icon-anyone">
                <Anyone />
              </div>
            }
            user={{
              id: "all",
              lastName: "Anyone",
              firstName: "",
            }}
            selected={[]}
          />
        ) : (
          <Avatar.Group
            maxCount={8}
            maxStyle={{ color: "#f56a00", backgroundColor: "#fde3cf" }}
          >
            {users.map((user, i) => (
              <Tooltip
                key={user.userId}
                title={user?.lastName + " " + user?.firstName}
                placement="top"
              >
                <AvatarUser user={user} />
              </Tooltip>
            ))}
          </Avatar.Group>
        )}
      </Space>
    </Space>
  );
}
