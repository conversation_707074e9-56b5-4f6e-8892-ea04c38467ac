import { INodeSettings } from "./types";

export const DEFAULT_NODE_SETTINGS: INodeSettings = {
  isAutoNextStep: false,
  isCancel: false,
  isArchive: false,
  isRequireRejectReason: false,
  isRedo: false,
  isRequireRedoReason: false,
  isNoLabel: false,
  isCorrectResult: false,
};

export const DEFAULT_PRE_PROCESSING_NEW_SETTINGS: any = {
  isCopilot: false,
  isSimilarityCheck: true,
  systemModelName: "",
  systemModelDetailId: "",
  apiEndpoint: "",
  threshold: 0.95,
};

export const DEFAULT_NODE_AI_PREDICTION_SETTINGS: any = Object.freeze({
  aiModelId: null,
  // percentageConfidence: 50,
  predictionTime: 1,
  manualPrediction: false,
});

export const DEFAULT_HUMAN_APPROVAL_SETTINGS: any = {
  isCancel: true,
  isRequireRejectReason: true,
};

export const DEFAULT_PRE_PROCESSING_COMPLETED_SETTINGS: any = {
  isManualConfirmation: false,
  isRedo: true,
  isRequireRedoReason: true,
};
