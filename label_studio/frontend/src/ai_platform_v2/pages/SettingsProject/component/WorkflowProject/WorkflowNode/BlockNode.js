import { isEmpty, maxBy } from "lodash";
import React from "react";
import { getNodesBounds, NodeResizer, useNodes } from "reactflow";

export function BlockNode({ data, selected }) {
  const nodes = useNodes();

  const childNodes = nodes?.filter((n) => n.parentNode === data?.id);

  const rect = getNodesBounds(childNodes);
  const { width, height } = rect;
  const nodeMaxY = maxBy(childNodes, (node) => node.position.y + node.height);
  const nodeMaxX = maxBy(childNodes, (node) => node.position.x + node.width);

  let minHeight = height;
  let minWidth = width;

  if (!isEmpty(nodeMaxY)) {
    minHeight = nodeMaxY?.position?.y + nodeMaxY?.height;
  }
  if (!isEmpty(nodeMaxX)) {
    minWidth = nodeMaxX?.position?.x + nodeMaxX?.width;
  }

  return (
    <div className="w-full h-full min-w-[240px] min-h-[240px] rounded-[15px] bg-[#FAFAFA66] border border-solid border-[#0000001A]">
      <NodeResizer
        isVisible={selected}
        minWidth={childNodes?.length ? (minWidth > 240 ? minWidth : 240) : 240}
        minHeight={
          childNodes?.length ? (minHeight > 240 ? minHeight : 240) : 240
        }
        handleStyle={{
          width: 10,
          height: 10,
          borderRadius: "50%",
          borderColor: "#FFABE1",
          backgroundColor: "#FFABE1",
        }}
        lineStyle={{
          borderColor: "#FFABE1",
          borderWidth: 1.5,
        }}
      />

      <div className="px-2 py-1 flex justify-center items-center rounded-[7px] bg-[#E4E4E499] text-[12px] leading-[18px] text-[#000000CC] absolute top-2 left-2">
        {data?.label}
      </div>
    </div>
  );
}
