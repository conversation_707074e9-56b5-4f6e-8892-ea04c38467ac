const IconUnion = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.99992 0.5C4.47716 0.5 0 4.97708 0 10.4998C0 16.0229 4.47716 20.5 9.99992 20.5C15.5229 20.5 20 16.0229 20 10.4998C20 4.97708 15.5229 0.5 9.99992 0.5ZM17.6562 14.4062C17.6562 15.0967 17.0966 15.6562 16.4062 15.6562C16.0841 15.6562 15.7915 15.5334 15.5698 15.3334C15.4834 15.3395 15.3947 15.3438 15.3018 15.3438C14.5189 15.3438 13.5175 15.1294 12.402 14.7284C11.9081 17.0651 11.0173 18.625 10 18.625C8.98276 18.625 8.09189 17.0649 7.59803 14.7284C6.48247 15.1295 5.48119 15.3438 4.69819 15.3438C4.60517 15.3438 4.51667 15.3395 4.43016 15.3334C4.20853 15.5334 3.91586 15.6562 3.59375 15.6562C2.90344 15.6562 2.34375 15.0967 2.34375 14.4062C2.34375 13.8601 2.69455 13.397 3.18261 13.2269C3.53355 12.4028 4.30251 11.421 5.3775 10.4216C3.51608 8.69077 2.57011 7.01291 3.12813 6.11584C3.38775 5.69822 3.9415 5.5 4.69833 5.5C5.48881 5.5 6.50162 5.71805 7.63016 6.12667C7.90794 4.87378 8.30131 3.85541 8.76717 3.19806C8.75686 3.13336 8.75 3.06759 8.75 3C8.75 2.30955 9.30969 1.75 10 1.75C10.6903 1.75 11.25 2.30955 11.25 3C11.25 3.06759 11.2431 3.13336 11.2329 3.19806C11.6988 3.85541 12.0921 4.87378 12.3698 6.12667C13.4984 5.7182 14.5111 5.5 15.3017 5.5H15.3021L15.3019 6.125C14.5407 6.125 13.5676 6.34641 12.4943 6.74389C12.6155 7.4133 12.7053 8.13703 12.7581 8.89997C13.26 9.26281 13.7278 9.63314 14.1561 10.0036C14.6397 9.557 15.066 9.10841 15.4203 8.6688C16.3801 7.47816 16.5164 6.72727 16.3415 6.44589C16.2147 6.24188 15.8358 6.125 15.3022 6.125L15.3021 5.5C16.0586 5.50007 16.6123 5.69829 16.872 6.11584C17.43 7.01291 16.4841 8.69077 14.6227 10.4216C15.6976 11.421 16.4665 12.4028 16.8175 13.2269C17.3055 13.397 17.6562 13.8601 17.6562 14.4062ZM10.625 3C10.625 3.34518 10.3452 3.625 10 3.625C9.65482 3.625 9.375 3.34518 9.375 3C9.375 2.65482 9.65482 2.375 10 2.375C10.3452 2.375 10.625 2.65482 10.625 3ZM12.0934 8.43728C11.8935 8.30331 11.6897 8.17056 11.4813 8.03994C11.2061 7.86752 10.9316 7.70502 10.6582 7.54877C11.0803 7.3362 11.4961 7.14547 11.9015 6.97656C11.9822 7.44256 12.0465 7.9313 12.0934 8.43728ZM11.7802 6.35125C11.6724 5.85138 11.545 5.38308 11.3978 4.95767C11.2391 4.49914 11.0738 4.13553 10.9115 3.85156C10.6834 4.09539 10.3597 4.24859 9.99945 4.24859C9.63919 4.24859 9.31555 4.09539 9.08744 3.85156C8.92516 4.13553 8.75991 4.49914 8.60122 4.95767C8.45397 5.38308 8.32649 5.85138 8.21875 6.35125C8.79264 6.58456 9.39017 6.86319 9.99953 7.18469C10.6087 6.86319 11.2063 6.58456 11.7802 6.35125ZM8.0982 6.97656C8.50362 7.14533 8.91966 7.3362 9.34164 7.54877C9.0682 7.70502 8.7937 7.86752 8.51866 8.03994C8.31006 8.17072 8.10614 8.30331 7.90625 8.43744C7.95317 7.93145 8.01748 7.44256 8.0982 6.97656ZM4.21875 14.4062C4.21875 14.7514 3.93893 15.0312 3.59375 15.0312C3.24857 15.0312 2.96875 14.7514 2.96875 14.4062C2.96875 14.0611 3.24857 13.7812 3.59375 13.7812C3.93893 13.7812 4.21875 14.0611 4.21875 14.4062ZM8.51838 12.8026C8.30437 12.6684 8.09548 12.5322 7.89062 12.3945C7.93397 12.9071 7.99492 13.4031 8.07267 13.8766C8.48603 13.7053 8.91061 13.5106 9.34128 13.2936C9.06792 13.1375 8.79342 12.9748 8.51838 12.8026ZM4.58001 8.6688C3.62015 7.47816 3.4839 6.72727 3.65892 6.44573C3.78571 6.24188 4.16451 6.125 4.69834 6.125C5.45953 6.125 6.43259 6.34625 7.50596 6.74389C7.38489 7.4133 7.29493 8.13717 7.24221 8.90011C6.74036 9.26297 6.27251 9.63314 5.84428 10.0036C5.36073 9.557 4.93432 9.10825 4.58001 8.6688ZM6.31641 10.4252C6.59556 10.6643 6.88991 10.9019 7.19814 11.136C7.19256 10.9269 7.18883 10.7165 7.18883 10.5033C7.18883 10.2358 7.19386 9.97186 7.20256 9.71094C6.89274 9.94623 6.59686 10.1849 6.31641 10.4252ZM4.57944 12.1789C4.93383 11.7393 5.36031 11.2904 5.84402 10.8438C6.2695 11.2118 6.73352 11.5794 7.23164 11.9401C7.27903 12.7072 7.36333 13.4368 7.47959 14.1131C6.46428 14.4871 5.54197 14.7004 4.8032 14.7189C4.82839 14.62 4.84319 14.517 4.84319 14.4102C4.84319 13.8218 4.43608 13.3297 3.88867 13.1968C4.04416 12.9043 4.26831 12.5648 4.57944 12.1789ZM16.4062 15.0312C16.7514 15.0312 17.0312 14.7514 17.0312 14.4062C17.0312 14.0611 16.7514 13.7812 16.4062 13.7812C16.0611 13.7812 15.7812 14.0611 15.7812 14.4062C15.7812 14.7514 16.0611 15.0312 16.4062 15.0312ZM14.1553 10.8398C14.639 11.2866 15.0655 11.7355 15.4198 12.1751C15.7307 12.561 15.9549 12.9005 16.1105 13.1931C15.563 13.326 15.1559 13.8181 15.1559 14.4064C15.1559 14.5133 15.1707 14.6163 15.1959 14.7151C14.4572 14.6967 13.5348 14.4833 12.5195 14.1093C12.6358 13.4331 12.7202 12.7034 12.7675 11.9363C13.2657 11.5758 13.7297 11.208 14.1553 10.8398ZM13.6851 10.4252C13.406 10.6643 13.1115 10.9019 12.8032 11.136C12.8089 10.9269 12.8126 10.7165 12.8126 10.5033C12.8126 10.2358 12.8075 9.97172 12.7988 9.71094C13.1087 9.94608 13.4046 10.1849 13.6851 10.4252ZM10.6584 13.2936C11.0891 13.5106 11.5136 13.7053 11.927 13.8766C12.0048 13.4033 12.0658 12.9072 12.1092 12.3945C11.9044 12.5322 11.6955 12.6684 11.4816 12.8024C11.2065 12.9748 10.932 13.1375 10.6584 13.2936ZM10.0001 7.89453C10.3824 8.10405 10.7666 8.32758 11.1494 8.56745C11.4945 8.78367 11.8292 9.00614 12.1524 9.23319C12.1753 9.64716 12.1875 10.0694 12.1875 10.4977C12.1875 10.87 12.1783 11.2379 12.161 11.5998C11.835 11.8289 11.4975 12.0535 11.1494 12.2715C10.7666 12.5114 10.3822 12.7351 10 12.9446C9.61776 12.7351 9.23355 12.5115 8.85078 12.2717C8.50266 12.0535 8.16498 11.8289 7.83905 11.5998C7.82173 11.2379 7.8125 10.87 7.8125 10.4977C7.8125 10.0694 7.8247 9.64731 7.84751 9.23334C8.17086 9.0063 8.5057 8.78367 8.85078 8.56745C9.23362 8.32758 9.61784 8.10405 10.0001 7.89453ZM8.75 10.4977C8.75 11.1881 9.30969 11.7477 10 11.7477C10.6903 11.7477 11.25 11.1881 11.25 10.4977C11.25 9.80723 10.6903 9.24769 10 9.24769C9.30969 9.24769 8.75 9.80723 8.75 10.4977ZM8.1875 14.5027C8.30127 15.0572 8.43942 15.5735 8.6008 16.0395C9.10128 17.4856 9.66677 17.9987 9.99895 17.9987C10.3311 17.9987 10.8966 17.4856 11.3971 16.0397C11.5584 15.5735 11.6966 15.0572 11.8105 14.5027C11.2273 14.2669 10.6191 13.9835 9.99895 13.6562C9.37884 13.9835 8.7707 14.2669 8.1875 14.5027ZM10.625 10.5C10.625 10.8452 10.3452 11.125 10 11.125C9.65482 11.125 9.375 10.8452 9.375 10.5C9.375 10.1548 9.65482 9.875 10 9.875C10.3452 9.875 10.625 10.1548 10.625 10.5Z"
      fill="url(#paint0_linear_16692_273263)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_16692_273263"
        x1="10.4167"
        y1="-2"
        x2="11.084"
        y2="28.7851"
        gradientUnits="userSpaceOnUse"
      >
        <stop offset="0.05497" stopColor="#5FF9FF" />
        <stop offset="0.26971" stopColor="#4FF1E2" />
        <stop offset="0.340131" stopColor="#4AEED8" />
        <stop offset="0.452434" stopColor="#40E9C5" />
        <stop offset="0.577428" stopColor="#34E3AF" />
        <stop offset="0.64938" stopColor="#29DEA8" />
        <stop offset="0.727114" stopColor="#1BD9A0" />
        <stop offset="0.912465" stopColor="#00CE90" />
      </linearGradient>
    </defs>
  </svg>
);

export default IconUnion;
