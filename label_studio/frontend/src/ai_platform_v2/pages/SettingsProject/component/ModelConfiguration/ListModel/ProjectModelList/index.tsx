import MiniPagination from "@/ai_platform_v2/pages/Home/component/MiniPagination";
import { memo, useCallback, useEffect, useState } from "react";
import IconSearch from "@/ai_platform_v2/assets/Icons/IconSearch";
import { Input } from "@taureau/ui";
import classNames from "classnames";
import { debounce } from "lodash";
import { AIModelCard } from "../AIModelCard/AIModelCard";
import { AIModelFilter } from "../AIModelFilter/AIModelFilter";
import { AIModelEmpty } from "../AIModelEmpty/AIModelEmpty";
import { AIModelNotFound } from "../AIModelNotFound/AIModelNotFound";
import { AIModelLoading } from "../AIModelLoading/AIModelLoading";
import { useAPI } from "@/providers/ApiProvider";
import { useProject } from "@/providers/ProjectProvider";
import Message from "@/ai_platform_v2/component/Message/Message";
import IconPlus from "@/ai_platform_v2/assets/Icons/IconPlus";
import { getCurrentProject } from "@/pages/DataSet/Const";
import "./ProjectModelList.scss";

const DEFAULT_PAGE_SIZE = 14;

const INIT_AI_MODEL_PARAMS = {
  currentPage: 1,
  searchValue: undefined,
  toolsSelected: [],
};

interface Props {
  [key: string]: any;
}

export const ListModel = (props: Props) => {
  const { goToModelDetail, setSelectedModelId } = props;

  const api = useAPI();

  const projectId = getCurrentProject();

  const [openFilter, setOpenFilter] = useState(false);

  const [loading, setLoading] = useState(true);
  const [modelParams, setModelParams] = useState(INIT_AI_MODEL_PARAMS);
  const [valueSearchName, setValueSearchName] = useState();
  const [total, setTotal] = useState(0);
  const [modelList, setModelList] = useState([]);

  const [isFocusSearch, setFocusSearch] = useState(false);

  const [loadingTool, setLoadingTool] = useState(true);
  const [toolList, setToolList] = useState([]);

  const fetchConfigTools = useCallback(async () => {
    setLoadingTool(true);
    const res = await api.callApi("fetchAllTools", {
      params: { id: projectId },
    });

    setLoadingTool(false);

    if (res.success) {
      setToolList(
        res?.data?.map((tool: any) => ({ label: tool, value: tool })) || []
      );
    }
  }, [projectId]);

  const fetchModelList = useCallback(async () => {
    const params: any = {
      pk: projectId,
      page: modelParams.currentPage,
      pageSize: DEFAULT_PAGE_SIZE,
    };

    if (modelParams.searchValue) {
      params.modelName = modelParams.searchValue;
    }

    if (modelParams.toolsSelected?.length) {
      params.labelingInterfaces = modelParams.toolsSelected;
    }

    setLoading(true);

    const result: any = await api.callApi("aiModels", {
      params,
    });

    setLoading(false);

    if (result?.success) {
      setModelList(result.items);
      setTotal(result.total);
    }
  }, [projectId, modelParams]);

  const debounceUpdateSearchValueStore = useCallback(
    debounce((value: string | any) => {
      setModelParams({ ...modelParams, currentPage: 1, searchValue: value });
    }, 300),
    [modelParams]
  );

  const onChangeSearchValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value: any = e.target.value;

    setValueSearchName(value);

    debounceUpdateSearchValueStore(value.trim());
  };

  const onChangeFilter = useCallback(
    (value: any) => {
      setModelParams({ ...modelParams, currentPage: 1, toolsSelected: value });
    },
    [modelParams]
  );

  const handleChangePage = useCallback(
    (page, pageSize) => {
      setModelParams({ ...modelParams, currentPage: page });
    },
    [modelParams]
  );

  const handleUpdateModelStatus = useCallback(
    async (model, modelStatus) => {
      const res = await api.callApi("updateAIModel", {
        params: { id: projectId },
        body: { id: model.id, status: modelStatus },
      });

      if (res.success) {
        Message.success({
          title: "Success",
          content: `${
            modelStatus === "Publish" ? "Published" : "Cancelled publishing of"
          } ${model.modelName} model`,
        });
        fetchModelList();
      } else {
        Message.error({
          title: "Error",
          content: `Cannot ${
            modelStatus === "Publish" ? "publish" : "cancel publishing of"
          } ${model.modelName} model`,
        });
      }
    },
    [projectId, fetchModelList]
  );

  useEffect(() => {
    if (projectId) {
      fetchModelList();
    }
  }, [projectId, modelParams]);

  useEffect(() => {
    if (projectId) {
      fetchConfigTools();
    }
  }, [projectId]);

  return (
    <>
      <div
        className="w-full flex flex-col justify-between relative"
        style={{ height: "calc(100% - 70px)" }}
      >
        {loading ||
        loadingTool ||
        modelList?.length ||
        modelParams.searchValue ||
        openFilter ||
        modelParams.toolsSelected.length ? (
          <>
            <div className="h-full flex flex-col items-start gap-[12px]">
              <div className="flex items-start justify-end gap-[9px] w-full">
                <Input
                  placeholder="Search by model name"
                  className={classNames(
                    "bg-white min-w-[177px] h-[36px] rounded-[10px] p-[8px] ai-model-search",
                    {
                      "ai-model-search-focus": isFocusSearch || valueSearchName,
                    }
                  )}
                  inputClassName={classNames(
                    "h-full w-full text-[12px] leading-[18px]",
                    {
                      "text-[#3361FF]": !isFocusSearch,
                    }
                  )}
                  iconLeft={
                    <IconSearch
                      size={20}
                      color={
                        isFocusSearch || valueSearchName ? "#3361FF" : "#C3CAD9"
                      }
                    />
                  }
                  iconLeftClassName="flex m-0 mr-[8px]"
                  size="sm"
                  value={valueSearchName}
                  onChange={onChangeSearchValue}
                  maxLength={100}
                  autoFocus={isFocusSearch}
                  onFocus={() => setFocusSearch(true)}
                  onBlur={() => setFocusSearch(false)}
                />
                <AIModelFilter
                  placeholder="Labeling interface"
                  value={modelParams.toolsSelected}
                  options={toolList}
                  onChange={onChangeFilter}
                  onOpen={(value: boolean) => setOpenFilter(value)}
                />
              </div>

              {loading || loadingTool ? (
                <div className="grid grid-cols-[repeat(auto-fill,_220px)] place-content-evenly p-[8px] items-start gap-x-[20px] gap-y-[16px] w-full">
                  <AIModelLoading />
                </div>
              ) : modelList?.length ? (
                <div
                  className="grid grid-cols-[repeat(auto-fill,_220px)] p-[8px] items-start gap-x-[20px] gap-y-[16px] w-full overflow-auto project-ai-model"
                  style={{
                    height: "calc(100vh - 370px)",
                    placeContent: "start space-evenly",
                  }}
                >
                  <div
                    className="ai-model-card-add-new"
                    onClick={() => {
                      goToModelDetail();
                      setSelectedModelId();
                    }}
                  >
                    <IconPlus size={30} color="#D9D9D9" />
                    New AI model
                  </div>
                  {modelList.map((model: any) => (
                    <AIModelCard
                      toolConfig={toolList}
                      key={model.id}
                      data={model}
                      onUpdateStatus={handleUpdateModelStatus}
                      onSelectModel={() => {
                        setSelectedModelId(model.id);
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <AIModelNotFound />
                </div>
              )}
            </div>
            <div className="flex justify-end items-center absolute bottom-0 right-0">
              <div className="flex w-fit">
                <MiniPagination
                  currentPage={modelParams.currentPage}
                  pageSize={DEFAULT_PAGE_SIZE}
                  totalItems={total}
                  loadMoreData={handleChangePage}
                  isLoading={loading}
                />
              </div>
            </div>
          </>
        ) : (
          <AIModelEmpty
            goToModelDetail={() => {
              goToModelDetail();
              setSelectedModelId();
            }}
          />
        )}
      </div>
      <div className="flex w-full items-center justify-between px-[22px] bg-gray-blue-97 rounded-b-[10px] h-[50px] absolute bottom-0 left-0" />
    </>
  );
};

export default memo(ListModel);
