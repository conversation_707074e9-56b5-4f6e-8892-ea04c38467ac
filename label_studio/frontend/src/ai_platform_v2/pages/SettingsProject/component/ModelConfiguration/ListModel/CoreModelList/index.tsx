import MiniPagination from "@/ai_platform_v2/pages/Home/component/MiniPagination";
import { memo, useCallback, useEffect, useMemo, useState } from "react";
import { CoreModelCard } from "../CoreModelCard/CoreModelCard";
import { AIModelFilter } from "../AIModelFilter/AIModelFilter";
import { AIModelEmpty } from "../AIModelEmpty/AIModelEmpty";
import { AIModelNotFound } from "../AIModelNotFound/AIModelNotFound";
import CoreModelLoading from "../CoreModelLoading";
import { useAPI } from "@/providers/ApiProvider";
import "./CoreModelList.scss";

const DEFAULT_PAGE_SIZE = 15;

const INIT_CORE_MODEL_PARAMS = {
  currentPage: 1,
  toolsSelected: [],
};

const ListCoreModel = () => {
  const api = useAPI();

  const [openFilter, setOpenFilter] = useState(false);

  const [loading, setLoading] = useState(true);
  const [modelDetailParams, setModelDetailParams] = useState(
    INIT_CORE_MODEL_PARAMS
  );
  const [total, setTotal] = useState(0);
  const [modelDetailList, setModelDetailList] = useState([]);

  const [loadingModelCategories, setLoadingModelCategories] = useState(true);
  const [modelCategories, setModelCategories] = useState<any>([]);

  const embedding = useMemo(
    () => (modelCategories?.length ? modelCategories[0] : undefined),
    [modelCategories]
  );

  const fetchModelCategories = useCallback(async () => {
    setLoadingModelCategories(true);
    const res: any = await api.callApi("allModelCategories");

    setLoadingModelCategories(false);

    if (res?.length) {
      setModelCategories(
        res.map((model: any) => ({ label: model.name, value: model.id }))
      );
    }
  }, []);

  const fetchModelDetailList = useCallback(async () => {
    const params: any = {
      pk: embedding.value,
      status: "Publish",
      page: modelDetailParams.currentPage,
      pageSize: DEFAULT_PAGE_SIZE,
    };

    if (modelDetailParams.toolsSelected?.length) {
      params.labelingInterfaces = modelDetailParams.toolsSelected;
    }

    setLoading(true);

    const result: any = await api.callApi("categoryModels", {
      params,
    });

    setLoading(false);

    if (result?.success) {
      setModelDetailList(result.items);
      setTotal(result.total);
    }
  }, [embedding, modelDetailParams]);

  const onChangeFilter = useCallback(
    (value: any) => {
      setModelDetailParams({
        ...modelDetailParams,
        currentPage: 1,
        toolsSelected: value,
      });
    },
    [modelDetailParams]
  );

  const handleChangePage = useCallback(
    (page, pageSize) => {
      setModelDetailParams({ ...modelDetailParams, currentPage: page });
    },
    [modelDetailParams]
  );

  useEffect(() => {
    if (embedding?.value) {
      fetchModelDetailList();
    }
  }, [embedding, modelDetailParams]);

  useEffect(() => {
    fetchModelCategories();
  }, []);

  return (
    <>
      <div
        className="w-full flex flex-col justify-between relative"
        style={{ height: "calc(100% - 70px)" }}
      >
        {loading ||
        loadingModelCategories ||
        modelDetailList?.length ||
        openFilter ||
        modelDetailParams.toolsSelected.length ? (
          <>
            <div className="h-full flex flex-col items-start gap-[12px]">
              <div className="flex items-start justify-end gap-[9px] w-full px-[16px]">
                <AIModelFilter
                  placeholder="Category"
                  value={modelDetailParams.toolsSelected}
                  options={modelCategories}
                  onChange={onChangeFilter}
                  onOpen={(value: boolean) => setOpenFilter(value)}
                />
              </div>

              {loading || loadingModelCategories ? (
                <div className="grid grid-cols-[repeat(auto-fill,_220px)] place-content-evenly p-[8px] items-start gap-x-[20px] gap-y-[16px] w-full">
                  <CoreModelLoading />
                </div>
              ) : modelDetailList?.length ? (
                <div
                  className="grid grid-cols-[repeat(auto-fill,_220px)] p-[8px] items-start gap-x-[20px] gap-y-[16px] w-full overflow-auto project-ai-model"
                  style={{
                    height: "calc(100vh - 370px)",
                    placeContent: "start space-evenly",
                  }}
                >
                  {modelDetailList.map((model: any) => (
                    <CoreModelCard
                      key={model.id}
                      modelCategory={embedding.label}
                      data={model}
                    />
                  ))}
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <AIModelNotFound />
                </div>
              )}
            </div>
            <div className="flex justify-end items-center absolute bottom-0 right-0">
              <div className="flex w-fit">
                <MiniPagination
                  currentPage={modelDetailParams.currentPage}
                  pageSize={DEFAULT_PAGE_SIZE}
                  totalItems={total}
                  loadMoreData={handleChangePage}
                  isLoading={loading}
                />
              </div>
            </div>
          </>
        ) : (
          <AIModelEmpty showCreateNewModel={false} />
        )}
      </div>
      <div className="flex w-full items-center justify-between px-[22px] bg-gray-blue-97 rounded-b-[10px] h-[50px] absolute bottom-0 left-0" />
    </>
  );
};

export default memo(ListCoreModel);
