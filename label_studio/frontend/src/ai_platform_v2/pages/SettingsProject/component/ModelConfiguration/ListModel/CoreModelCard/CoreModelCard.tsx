import { useMemo } from "react";
import { Typography } from "antd";
import Icon3DCubeScan from "../../icons/Icon3DCubeScan";
import "./CoreModelCard.scss";
import IconUnion from "../../icons/IconUnion";

const { Text } = Typography;

export interface CoreModelCardProps {
  modelCategory?: any;
  data?: any;
  onSelectModel?: any;
}

export const CoreModelCard = (props: CoreModelCardProps) => {
  const { modelCategory, data, onSelectModel } = props;

  const { id, modelName } = data;

  return (
    <div
      className="flex flex-col justify-between items-center w-[220px] h-[120px] px-[12px] py-[8px] rounded-[15px] relative core-model-card"
      onClick={onSelectModel}
    >
      <div className="flex absolute top-[6px] right-[6px]">
        <IconUnion />
      </div>
      <div className="flex flex-col justify-center items-start gap-[4px] w-full">
        <Icon3DCubeScan />
        <div className="flex flex-col justify-center items-start">
          <div className="text-[#346] text-[12px] font-normal leading-[18px]">
            <Text
              className="content-select-button"
              style={{
                width: 200,
                color: "#346",
                lineHeight: "18px",
              }}
              ellipsis={{ tooltip: modelName }}
            >
              {modelName}
            </Text>
          </div>
          <div className="text-[#7C8AA6] text-[11px] font-normal leading-[18px]">
            {modelCategory ? (
              modelCategory
            ) : (
              <span className="text-[#CC1414]">undefined</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
