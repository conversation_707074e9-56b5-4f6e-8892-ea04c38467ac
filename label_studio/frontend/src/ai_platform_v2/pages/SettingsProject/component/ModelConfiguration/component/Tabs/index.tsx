import classNames from "classnames";
import { useMemo } from "react";
import "./Tabs.scss";

interface TabsProps {
  activeKey?: string;
  items?: any;
  onChange?: (activeKey: string) => void;
}

const Tabs = (props: TabsProps) => {
  const { activeKey, items, onChange } = props;

  const activeContent = useMemo(() => {
    const activeItem = items?.find((item: any) => item.key === activeKey);

    if (activeItem) {
      return activeItem.children;
    }

    return "";
  }, [activeKey, items]);

  return (
    <div className="w-full flex flex-col items-start gap-[16px]">
      <div
        className="w-full flex items-start gap-[24px]"
        style={{ borderBottom: "2px solid rgba(0, 0, 0, 0.05)" }}
      >
        {items?.map((item: any) => (
          <div
            key={item?.key}
            className={classNames(
              "flex justify-center items-center px-[4px] pb-[4px] text-[#6B7A99] text-[14px] font-medium leading-[21px] cursor-pointer relative ai-model-tabs-item",
              {
                "!text-[#3361FF]": item?.key === activeKey,
              }
            )}
            onClick={() => onChange?.(item?.key)}
          >
            {item?.label}
            <div
              className={classNames(
                "w-full h-[2px] bg-[#3361FF] opacity-0 bottom-[-2px] absolute ai-model-tabs-item-selected-border",
                { "opacity-100": item?.key === activeKey }
              )}
            />
          </div>
        ))}
      </div>
      {activeContent}
    </div>
  );
};

export default Tabs;
