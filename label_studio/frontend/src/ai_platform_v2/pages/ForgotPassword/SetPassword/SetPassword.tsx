import {
  EmailInput,
  PasswordInput,
} from "@/ai_platform_v2/component/Input/Input";
import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import { Button } from "@taureau/ui";
import { useCallback, useMemo, useState } from "react";
import { useHistory, useLocation } from "react-router";
import "./SetPassword.styl";
import IconAccountCircle from "@/ai_platform_v2/assets/Icons/IconAccountCircle";
import IconArrowForward from "@/ai_platform_v2/assets/Icons/IconArrowForward";
import IconArrowBackNew from "@/ai_platform_v2/assets/Icons/IconArrowBackNew";
import IconLook from "@/ai_platform_v2/assets/Icons/IconLook";
import { useAPI } from "@/providers/ApiProvider";
import { PassReady } from "../PassReady/PassReady";
import IconSpin from "@/ai_platform_v2/assets/Icons/IconSpin";

export const SetPassword = () => {
  const location = useLocation();
  const search = new URLSearchParams(location.search);
  const token = search.get("token");

  const api = useAPI();
  const history = useHistory();
  const [loading, setLoading] = useState(false);

  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const [isValidatePassword, setIsValidatePassword] = useState(false);
  const [isValidateConfirmPassword, setIsValidateConfirmPassword] =
    useState(false);

  const isValidateForm = useMemo(
    () => isValidatePassword || isValidateConfirmPassword,
    [isValidatePassword, isValidateConfirmPassword]
  );

  const [isSubmit, setIsSubmit] = useState<boolean>(false);

  const [isReady, setIsReady] = useState(false);

  const isAlreadyMess = useMemo(
    () => !!password && !!confirmPassword && password !== confirmPassword,
    [password, confirmPassword]
  );

  const isResetPass = useMemo(
    () =>
      !isValidateForm &&
      !!password &&
      !!confirmPassword &&
      password === confirmPassword,
    [isValidateForm, password, confirmPassword]
  );

  const handleChangePassword = useCallback((value) => {
    setPassword(value);
    setIsSubmit(false);
  }, []);

  const handleChangeConfirmPassword = useCallback((value) => {
    setConfirmPassword(value);
    setIsSubmit(false);
  }, []);

  const handleResetPassword = useCallback(async () => {
    if (loading) {
      return;
    }

    setIsSubmit(true);
    if (!isResetPass) {
      return;
    }

    setLoading(true);

    const res = await api.callApi("resetPassword", {
      body: {
        newPassword: confirmPassword,
        token,
      },
    });

    setLoading(false);

    if (res?.success) {
      // const token = res.data.token;
      // Cookies.set("token", token, { expires: 7 });
      // location.replace(absoluteURL("/home"));
      setIsReady(true);
    }
  }, [token, confirmPassword, isResetPass, loading]);

  const handleKeyDown = useCallback(
    (event) => {
      if (event.key === "Enter") {
        handleResetPassword();
      }
    },
    [handleResetPassword]
  );

  return (
    <>
      {isReady ? (
        <PassReady />
      ) : (
        <Block name="taureau-forgot-password-set-pass">
          <Elem name="title-content">Reset Password</Elem>

          <Elem name="content">
            <Elem name="title">
              <Elem name="sub-title-content">Reset New Password</Elem>
              <Elem
                name="title-description"
                style={isValidateForm ? { color: "#CC1414" } : {}}
              >
                <div>Must be from 8 characters which includes at least</div>
                <div>1 uppercase, 1 lowercase, 1 number and 1 symbol.</div>
              </Elem>
            </Elem>

            <Elem name="body">
              <PasswordInput
                alreadyExists={isAlreadyMess}
                value={password}
                validate={true}
                status={isSubmit && !password ? "error" : null}
                prefix={<IconLook />}
                placeholder="Enter password"
                onChange={handleChangePassword}
                onKeyDown={handleKeyDown}
                showErrorMessage={false}
                setValidate={setIsValidatePassword}
              />

              <PasswordInput
                alreadyExists={isAlreadyMess}
                validate={true}
                alreadyMessage="Password doesn't match."
                errorMessStyle={{ lineHeight: "30px" }}
                value={confirmPassword}
                status={isSubmit && !confirmPassword ? "error" : null}
                prefix={<IconLook />}
                placeholder="Confirm password"
                onChange={handleChangeConfirmPassword}
                onKeyDown={handleKeyDown}
                showErrorMessage={false}
                setValidate={setIsValidateConfirmPassword}
              />
            </Elem>

            <Elem name="footer">
              <Button
                className="w-full text-[14px] leading-[30px]"
                theme="Primary"
                size="sm"
                onClick={handleResetPassword}
              >
                {loading ? <IconSpin className="taureau-loader" /> : "Reset"}
              </Button>

              <Button
                className="w-full text-[14px] leading-[30px]"
                size="sm"
                iconLeft={<IconArrowBackNew />}
                onClick={() => {
                  history.replace("/login");
                }}
              >
                Back to log in
              </Button>
            </Elem>
          </Elem>
        </Block>
      )}
    </>
  );
};

SetPassword.path = "/forgot-password/set-password";
SetPassword.exact = true;
