import React, { useCallback, useEffect, useState } from "react";
import IconSuccess from "./IconSuccess";
import { Block, Elem } from "@/ai_platform_v2/utils/bem";

import IconArrowBackNew from "@/ai_platform_v2/assets/Icons/IconArrowBackNew";

import { useAPI } from "@/providers/ApiProvider";
import Message from "@/ai_platform_v2/component/Message/Message";
import { Button } from "@taureau/ui";
import { useHistory } from "react-router";
import classNames from "classnames";

const SuccessViewPublicVer = ({ email }: any) => {
  const api = useAPI();

  const [remainingTime, setRemainingTime] = useState(0);

  const [isLoading, setLoading] = useState(false);

  const history = useHistory();

  const goToLogin = () => {
    history.replace("/login");
  };

  const handleResendEmail = useCallback(async () => {
    try {
      if (remainingTime || isLoading) return;

      setLoading(true);

      const res = await api.callApi("resendEmail", {
        body: {
          email,
        },
      });

      if (res?.success) {
        setRemainingTime(60);

        Message.success({
          content: "Verification link has been sent to your email",
        });
      } else {
        Message.error({
          content: "Cannot send email. Please retry!",
        });
      }
    } catch (error: any) {
      console.log("error", error?.message);
    } finally {
      setLoading(false);
    }
  }, [remainingTime, isLoading, email]);

  useEffect(() => {
    let intervalCountdown: any = null;

    if (remainingTime) {
      intervalCountdown = setInterval(() => {
        const time = remainingTime - 1;

        if (!time) clearInterval(intervalCountdown);
        setRemainingTime(time);
      }, 1000);
    }
    return () => clearInterval(intervalCountdown);
  }, [remainingTime]);

  return (
    <Block
      name="confirm-active"
      style={{
        minHeight: "728px",
      }}
    >
      <Elem name="content">
        <Elem name="title">
          <Elem name="title-content">Activate Your Account</Elem>
        </Elem>

        <Elem name="body">
          <div className="flex flex-col items-center w-[423px] pt-[30px]">
            <IconSuccess />

            <div className="flex flex-col items-center mt-[20px]">
              <div className="flex flex-col items-center justify-center gap-[10px] mb-[36px]">
                <span className=" font-medium text-[24px] text-[#346] leading-[36px] mb-[16px]">
                  Your account is created!
                </span>
                <div className="flex flex-col justify-center items-center">
                  <span className=" font-normal text-[14px] text-[#346] leading-[21px] mb-3 text-center">
                    You’re almost there! We sent a verification link to your
                    email
                  </span>
                  <span className=" font-normal text-[14px] text-[#346] leading-[21px] mb-3">
                    Just click on the link in that email to complete your
                    signup. If you don’t see it, you may need to check your{" "}
                    <span className="font-semibold">spam</span> folder
                  </span>
                  <span className=" font-normal text-[14px] text-[#346] leading-[21px]">
                    Still can’t find the email? Click below
                  </span>
                </div>
              </div>

              <Button
                theme="Primary"
                className={classNames("w-[281px] h-[32px] mb-5", {
                  "border-none": remainingTime,
                })}
                onClick={handleResendEmail}
                disabled={!!remainingTime}
                loading={isLoading}
              >
                {remainingTime ? `Resend after ${remainingTime}s` : "Resend"}
              </Button>

              <Button
                theme="Light"
                className="w-[281px] h-[32px]"
                onClick={goToLogin}
                iconLeft={<IconArrowBackNew />}
              >
                Back to log in
              </Button>
            </div>
          </div>
        </Elem>
      </Elem>
    </Block>
  );
};

export default SuccessViewPublicVer;
