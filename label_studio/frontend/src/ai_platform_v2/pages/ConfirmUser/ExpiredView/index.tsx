import React from "react";
import IconClock from "./IconClock";
import { Button } from "@taureau/ui";
import { useAPI } from "@/providers/ApiProvider";
import { useAbortController } from "@/hooks/useAbortController";
import debounce from "lodash/debounce";

const ExpiredView = ({
  token,
  tokenInfo,
  sendLinkSuccess,
  sendLinkFailure,
}: {
  token: string;
  tokenInfo: any;
  sendLinkSuccess: () => void;
  sendLinkFailure: () => void;
}) => {
  const api = useAPI();
  const abortController = useAbortController();

  const handleSendNewLink = async () => {
    try {
      const res = await api.callApi("resendInvitation", {
        //@ts-ignore
        signal: abortController.controller.current.signal,
        errorFilter: () => true,
        body: {
          token,
        },
      });

      if (res && res.success) {
        sendLinkSuccess();
      } else {
        sendLinkFailure();
      }
    } catch (error) {
      //
    }
  };

  const sendNewLinkDebounce = debounce(handleSendNewLink, 1000, {
    leading: true,
    trailing: false,
  });

  return (
    <div className="flex flex-col items-center py-[30px] mt-[24px] w-[394px]">
      <IconClock />

      <div className="h-[169px] flex flex-col justify-between items-center">
        <div className="flex flex-col items-center justify-center h-[111px] gap-[4px]">
          <span className=" font-medium text-[24px] text-[#346] leading-[36px]">
            Expired link!
          </span>
          <span className="text-center  font-normal text-[14px] text-[#346] leading-[21px]">
            {`This activation link is expired or has already been used. \nPlease
          try with a valid link.`}
          </span>
        </div>

        {["WaitingForConfirmation", "NotActivated"].includes(
          tokenInfo?.userStatus
        ) && (
          <Button
            theme="Primary"
            className="w-[281px] h-[32px]"
            size="sm"
            onClick={sendNewLinkDebounce}
          >
            Send new link
          </Button>
        )}
      </div>
    </div>
  );
};

export default ExpiredView;
