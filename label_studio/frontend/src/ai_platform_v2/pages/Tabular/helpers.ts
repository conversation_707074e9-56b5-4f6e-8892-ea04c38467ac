import { ITabularCategoryDetail } from "./types";

type GroupedData = {
  [key: string]: string[];
};

function groupByKey(data: ITabularCategoryDetail[]): GroupedData {
  return data.reduce((acc: GroupedData, item: ITabularCategoryDetail) => {
    const { key, value } = item;

    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(value);
    return acc;
  }, {});
}

export { groupByKey };
