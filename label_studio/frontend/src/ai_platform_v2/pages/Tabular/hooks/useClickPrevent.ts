import { useEffect, useRef } from "react";
import requestTimeout from "./requestTimeout";

const noop = () => {};

const useCancelableScheduledWork = () => {
  const cancelCallback = useRef(noop);
  const registerCancel = (fn: any) => (cancelCallback.current = fn);
  const cancelScheduledWork = () => cancelCallback.current();

  // Cancels the current sheduled work before the "unmount"
  useEffect(() => {
    return cancelScheduledWork;
  }, []);

  return [registerCancel, cancelScheduledWork];
};

const useClickPrevention = ({ onClick, onDoubleClick, delay = 300 }: any) => {
  const [registerCancel, cancelScheduledRaf] = useCancelableScheduledWork();

  const handleClick = (...args: any) => {
    cancelScheduledRaf();
    requestTimeout(() => onClick(...args), delay, registerCancel);
  };

  const handleDoubleClick = (...args: any) => {
    cancelScheduledRaf();
    onDoubleClick(...args);
  };

  return [handleClick, handleDoubleClick];
};

export default useClickPrevention;
