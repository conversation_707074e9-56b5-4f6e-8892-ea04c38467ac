import { DMBlankData } from "@/ai_platform_v2/assets/Images/NoData";
import Message from "@/ai_platform_v2/component/Message/Message";
import useSettingProjectStore from "@/ai_platform_v2/stores/settingProjectStore";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { PAGE_SIZE } from "@/consts/page";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { useProject } from "@/providers/ProjectProvider";
import { Button, ModalConfirmBig } from "@taureau/ui";
import { Spin } from "antd";
import classNames from "classnames";
import { differenceWith, isEmpty, isEqual, isNil } from "lodash";
import { useCallback, useEffect, useRef, useState } from "react";
import { PermissionMessage } from "../../../components/PermissionMessage/PermissionMessage";
import {
  getCurrentModule,
  getCurrentProject,
} from "../../../pages/DataSet/Const";
import { useAPI } from "../../../providers/ApiProvider";
import { ProjectType } from "../Home/utils/const";
import { Project } from "../Project/Project";
import DataTable from "./components/DataTable";
import ExportButton from "./components/ExportButton";
import GeneratedDescription from "./components/GeneratedDescription";
import Level from "./components/Level";
import StatusDropdown from "./components/StatusDropdown";
import TabularCategoryDropdown from "./components/TabularCategoryDropdown";
import VersionHistory from "./components/VersionHistory";
import { groupByKey } from "./helpers";
import useTabularDictionaryStore from "./tabularDictionaryStore";
import useTabularHistoryStore from "./tabularHistoryStore";
import { IData, ITabularCategory, ITabularCategoryDetail } from "./types";

const Tabular = () => {
  const api = useAPI();
  const moduleID = getCurrentModule();
  const projectId = getCurrentProject();
  const { project, loading } = useProject();

  const [isLoading, setLoading] = useState(false);

  const [isDetailLoading, setDetailLoading] = useState(false);

  const [isSaving, toggleSaving] = useState(false);

  const [currentTabularCategory, setCurrentTabularCategory] =
    useState<ITabularCategory>();

  const [currentTabularCategoryDetail, setCurrentTabularCategoryDetail] =
    useState<ITabularCategoryDetail[]>();

  const { isChange, setIsChange } = useSettingProjectStore((state) => ({
    isChange: state.isChange,
    setIsChange: state.setIsChange,
  }));

  const prevDataRef = useRef<IData | undefined>();

  const { hasPermissionAllScopeInProject } = useCheckPermission();

  const canViewDataset = hasPermissionAllScopeInProject(
    ABILITY_NEW.can_view_dataset,
    moduleID,
    projectId
  );

  const canExportVersion = hasPermissionAllScopeInProject(
    ABILITY_NEW.can_export_version,
    moduleID,
    projectId
  );

  const { setDictionaryData } = useTabularDictionaryStore((state) => ({
    setDictionaryData: state.setDictionaryData,
  }));

  const { loadingSelectedHistory, selectedHistory, setSelectedHistory } =
    useTabularHistoryStore((state) => ({
      loadingSelectedHistory: state.loading,
      selectedHistory: state.selectedHistory,
      setSelectedHistory: state.setSelectedHistory,
    }));

  const fetchCurrentTabularCategory = async () => {
    try {
      if (isLoading) return;
      setLoading(true);

      const res: any = await api.callApi("tabularCategories", {
        params: {
          projectId,
          page: 1,
          pageSize: 1,
        },
      });

      if (res.success) {
        setCurrentTabularCategory(res.items?.[0]);
        prevDataRef.current = {
          ...prevDataRef.current,
          status: res.items?.[0]?.status,
          description: res.items?.[0]?.description,
        };
      } else {
        Message.error({ content: "Something went wrong" });
      }
    } catch (error) {
      Message.error({ content: "Something went wrong" });
    } finally {
      setLoading(false);
    }
  };

  const fetchTabularCategoryDetail = async () => {
    try {
      if (isDetailLoading) return;
      setDetailLoading(true);

      const res = await api.callApi("tabularCategoriesDetail", {
        params: {
          projectId,
          tabularCategoryId: currentTabularCategory?.tabularCategoryId,
        },
      });

      if (res.success) {
        setCurrentTabularCategoryDetail(res.data);
        prevDataRef.current = {
          ...prevDataRef.current,
          tabularCategoryDetail: res.data,
        };
      } else {
        Message.error({ content: "Something went wrong" });
      }
    } catch (error) {
      Message.error({ content: "Something went wrong" });
    } finally {
      setDetailLoading(false);
    }
  };

  const fetchDictionary = async () => {
    try {
      // if (isLoading) return;
      // setLoading(true);

      const res: any = await api.callApi("tabularCategoriesDictionary", {
        params: {
          projectId,
          page: 1,
          pageSize: PAGE_SIZE.FULL,
        },
      });

      if (res.success) {
        const dictionaryData = groupByKey(res.items);

        setDictionaryData(dictionaryData);
      } else {
        Message.error({ content: "Something went wrong" });
      }
    } catch (error) {
      Message.error({ content: "Something went wrong" });
    } finally {
      // setLoading(false);
    }
  };

  const handleGenerateDescription = useCallback(async () => {
    try {
      const res = await api.callApi("generateTabularGeneratedDescription", {
        params: {
          projectId,
        },
        body: {
          disease_name: currentTabularCategory?.categoryName,
          provoke: currentTabularCategoryDetail
            ?.filter((currentTabularCategory) => currentTabularCategory?.value)
            .map((currentTabularCategory) => ({
              [currentTabularCategory.key]: JSON.stringify(
                currentTabularCategory.value
              ),
            })),
        },
      });

      if (res.success) {
        const tabularGeneratedDescription = res.data;

        handleChangeDescription(tabularGeneratedDescription);
      } else {
        Message.error({ content: "Something went wrong" });
      }
    } catch (error) {
      Message.error({ content: "Something went wrong" });
    } finally {
      // setLoading(false);
    }
  }, [projectId, currentTabularCategory, currentTabularCategoryDetail]);

  const handleChangeTabularCategory = useCallback(
    (value) => {
      if (isChange) {
        ModalConfirmBig.warning({
          title: "Wanna leave?",
          content: "If you continue, changes you made may not be saved",
          okText: "Leave",
          cancelText: "Cancel",
          onOk: () => {
            setCurrentTabularCategory(value);
            prevDataRef.current = {
              status: value.status,
              description: value.description,
            };
            setIsChange(false);
          },
        });
      } else {
        setCurrentTabularCategory(value);
        prevDataRef.current = {
          status: value.status,
          description: value.description,
        };
      }
    },
    [isChange]
  );

  const handleChangeStatus = useCallback((value) => {
    setIsChange(true);
    setCurrentTabularCategory((prev) => {
      if (!prev) return undefined;
      return {
        ...prev,
        status: value,
      };
    });
  }, []);

  const handleChangeDescription = useCallback((value) => {
    setIsChange(true);
    setCurrentTabularCategory((prev) => {
      if (!prev) return undefined;
      return {
        ...prev,
        description: value,
      };
    });
  }, []);

  const handleChangeDataTable = useCallback((key, value) => {
    setIsChange(true);

    setCurrentTabularCategoryDetail((prev) => {
      if (!prev) return undefined;
      return prev.map((item) => {
        if (item.key === key) {
          return {
            ...item,
            value_Quoted: value,
          };
        }
        return item;
      });
    });
  }, []);

  const handleChangeLevelDataTable = useCallback((columnKey, label, value) => {
    setIsChange(true);

    setCurrentTabularCategoryDetail((prev) => {
      if (!prev) return undefined;
      return prev.map((item) => {
        if (item.key === columnKey) {
          return {
            ...item,
            value_Quoted_Importance: JSON.stringify({
              ...JSON.parse(item.value_Quoted_Importance!),
              [label]: value,
            }),
          };
        }
        return item;
      });
    });
  }, []);

  const handleUpdateTabularCategory = useCallback(async () => {
    try {
      if (isSaving) return;
      toggleSaving(true);

      const body: any = {};

      if (prevDataRef.current?.status !== currentTabularCategory?.status) {
        body.status = currentTabularCategory?.status;
      }
      if (
        prevDataRef.current?.description !== currentTabularCategory?.description
      ) {
        body.description = currentTabularCategory?.description;
      }

      const changedElements = differenceWith(
        currentTabularCategoryDetail,
        prevDataRef.current?.tabularCategoryDetail ?? [],
        isEqual
      );

      if (changedElements.length) {
        body.tabularCategoryDetails = changedElements?.map((item) => ({
          tabularCategoryDetailId: item.tabularCategoryDetailId,
          categoryKey: item.categoryKey,
          key: item.key,
          value_Quoted: item.value_Quoted,
          value_Quoted_Importance: item.value_Quoted_Importance,
        }));
      }

      const res = await api.callApi("tabularCategoriesUpdate", {
        params: {
          projectId,
          tabularCategoryId: currentTabularCategory?.tabularCategoryId,
        },
        body,
      });

      if (res.success) {
        prevDataRef.current = {
          status: currentTabularCategory?.status,
          description: currentTabularCategory?.description,
          tabularCategoryDetail: currentTabularCategoryDetail,
        };
        setIsChange(false);
        Message.success({ content: "Object updated successfully" });
      } else {
        Message.error({ content: "Something went wrong" });
      }
    } catch (error) {
      Message.error({ content: "Something went wrong" });
    } finally {
      toggleSaving(false);
    }
  }, [
    currentTabularCategory,
    currentTabularCategoryDetail,
    projectId,
    isSaving,
  ]);

  const handleRefetchDataAfterRestored = useCallback(
    (history) => {
      // fetchCurrentTabularCategory();
      setIsChange(false);
      setCurrentTabularCategory((prev?: ITabularCategory) => {
        if (!prev) {
          return undefined;
        }

        return {
          ...prev,
          status: history.status,
          description: history.desciption,
        };
      });

      fetchTabularCategoryDetail();
    },
    [fetchTabularCategoryDetail]
  );

  useEffect(() => {
    if (canViewDataset && project?.projectType === ProjectType.NonAnnotation) {
      fetchDictionary();
    }
  }, [projectId, canViewDataset, project?.projectType]);

  useEffect(() => {
    if (canViewDataset && project?.projectType === ProjectType.NonAnnotation) {
      fetchCurrentTabularCategory();
      // reset selected version history
      setSelectedHistory(undefined);
    }
  }, [canViewDataset, project?.projectType, projectId]);

  useEffect(() => {
    if (
      currentTabularCategory?.tabularCategoryId &&
      canViewDataset &&
      project?.projectType === ProjectType.NonAnnotation
    ) {
      fetchTabularCategoryDetail();
    }
  }, [
    currentTabularCategory?.tabularCategoryId,
    projectId,
    canViewDataset,
    project?.projectType,
  ]);

  const renderContent = useCallback(() => {
    if (isLoading) {
      return (
        <div className="w-full h-full flex flex-col items-center justify-center">
          <Spin size="large" />
        </div>
      );
    }

    if (!isLoading && isEmpty(currentTabularCategory)) {
      return (
        <>
          <div className="w-full h-full flex flex-col justify-center items-center">
            <img
              src={DMBlankData}
              alt="search-no-data"
              className="mb-[8px]"
              width={200}
            />
            <span className=" text-[24px] font-semibold text-gray-blue-40 leading-[36px] mb-[4px]">
              Empty!
            </span>
            <span className="text-[16px] text-[#6B7A99] leading-[24px]">
              No object has been created yet
            </span>
          </div>
        </>
      );
    }

    const isShowHistory = !isEmpty(selectedHistory);

    return (
      <div className="flex flex-col overflow-hidden w-full h-full">
        <div className="flex items-center w-full gap-[20px] mb-[12px] px-[20px] pt-[12px]">
          <TabularCategoryDropdown
            currentTabularCategory={currentTabularCategory}
            setCurrentTabularCategory={handleChangeTabularCategory}
            disabled={isShowHistory}
          />

          {!isShowHistory && (
            <Button
              size="xs"
              corner="Rounded"
              theme="Primary"
              className={classNames("", {
                "border-none": !isChange,
              })}
              onClick={handleUpdateTabularCategory}
              loading={isSaving}
              disabled={!isChange}
            >
              Save
            </Button>
          )}
        </div>
        <div className="flex items-center gap-[20px] w-full mb-[8px] px-[20px] relative">
          <div
            className={classNames("px-[8px] py-[4px] flex w-full", {
              "flex-1": isShowHistory,
            })}
          >
            <StatusDropdown
              currentStatus={currentTabularCategory?.status}
              onChangeStatus={handleChangeStatus}
              disabled={isShowHistory}
            />
          </div>
          {isShowHistory && (
            <div
              className={classNames("px-[8px] py-[4px] flex w-full", {
                "flex-1": isShowHistory,
              })}
            >
              <StatusDropdown
                currentStatus={selectedHistory?.status}
                onChangeStatus={handleChangeStatus}
                disabled={isShowHistory}
              />
            </div>
          )}
          <div className="absolute right-[20px] flex items-center gap-[12px]">
            {canExportVersion && !isShowHistory ? <ExportButton /> : null}

            <VersionHistory
              currentTabularCategory={currentTabularCategory}
              reFetchData={handleRefetchDataAfterRestored}
            />
          </div>
        </div>

        {!isShowHistory && (
          <Level handleChangeLevelDataTable={handleChangeLevelDataTable} />
        )}

        {isShowHistory ? (
          <div className="w-full flex gap-[20px] px-[20px] h-full overflow-auto mb-[16px]">
            <div className="flex w-full flex-col gap-[16px] flex-1">
              <DataTable
                data={currentTabularCategoryDetail!}
                handleChangeDataTable={handleChangeDataTable}
                disabled={isShowHistory}
              />
              <GeneratedDescription
                description={currentTabularCategory?.description}
                onChangeDescription={handleChangeDescription}
                onGenerateDescription={handleGenerateDescription}
                disabled={isShowHistory}
              />
            </div>

            {loadingSelectedHistory && (
              <div className="flex h-full w-full justify-center items-center flex-1">
                <Spin size="large" />
              </div>
            )}
            {!loadingSelectedHistory && (
              <div className="flex w-full flex-col gap-[16px] flex-1">
                <DataTable
                  data={selectedHistory?.tabularCategoryHistoryDetailItems}
                  handleChangeDataTable={handleChangeDataTable}
                  disabled={isShowHistory}
                />
                <GeneratedDescription
                  description={selectedHistory?.desciption}
                  disabled={isShowHistory}
                />
              </div>
            )}
          </div>
        ) : (
          <div className="flex h-full w-full flex-col gap-[16px] px-[20px] overflow-auto">
            <DataTable
              data={currentTabularCategoryDetail!}
              handleChangeDataTable={handleChangeDataTable}
              disabled={isShowHistory}
              handleChangeLevelDataTable={handleChangeLevelDataTable}
            />
            <GeneratedDescription
              description={currentTabularCategory?.description}
              onChangeDescription={handleChangeDescription}
              onGenerateDescription={handleGenerateDescription}
              disabled={isShowHistory}
              hasDescription={!!prevDataRef?.current?.description}
            />
          </div>
        )}
      </div>
    );
  }, [
    canExportVersion,
    loadingSelectedHistory,
    currentTabularCategory,
    isLoading,
    currentTabularCategoryDetail,
    isSaving,
    selectedHistory,
    handleChangeTabularCategory,
    handleGenerateDescription,
    handleUpdateTabularCategory,
    handleRefetchDataAfterRestored,
  ]);

  if (loading || isNil(canViewDataset))
    return (
      <div className="w-full h-full flex flex-col items-center justify-center">
        <Spin size="large" />
      </div>
    );

  if (
    (!canViewDataset && !loading) ||
    project?.projectType !== ProjectType.NonAnnotation
  ) {
    return (
      <Project router="/tabular">
        <PermissionMessage />
      </Project>
    );
  }

  return (
    <Project router="/tabular">
      {/* <RouteLeaving when={isChange} /> */}
      <div className="h-full w-full relative">
        {renderContent()}
        {isSaving || isDetailLoading ? (
          <div className="w-full h-full flex flex-col items-center justify-center absolute top-0 left-0 z-10 bg-white/50">
            <Spin size="large" />
          </div>
        ) : null}
      </div>
    </Project>
  );
};

export default Tabular;

Tabular.path = "/tabular";
Tabular.exact = true;
