interface ITabularCategory {
  tabularCategoryId: string;
  categoryName: string;
  status: string;
  description?: string;
  isCommon?: boolean;
}

interface ITabularCategoryDetail {
  tabularCategoryDetailId: string;
  tabularCategoryId: string;
  categoryKey: number;
  key: string;
  value: string;
  dataType: string;
  value_Quoted?: string;
  rawData?: string;
  value_Quoted_Importance?: string;
}

interface DictionaryData {
  [key: string]: string[];
}

interface IData {
  status?: string;
  description?: string;
  tabularCategoryDetail?: ITabularCategoryDetail[];
}

interface IVersionHistoryData {
  historyId: string;
  avatar: string;
  actionType: VersionHistoryType;
  fullName: string;
  createdAt: string;
  isCurrent?: boolean;
  actionBy?: string;
  actionByHistoryId?: string;
}

enum VersionHistoryType {
  Draft = "Draft",
  Restored = "Restored",
  Changed = "Changed",
  Created = "Created",
}

interface ICitationItem {
  content: string;
  idx: string;
  chunk_page: number;
  chunk_content: string;
  chunk_file_name: string;
  chunk_file_url: string;
}

interface ILevel {
  label: string;
  color: string;
  bgColor: string;
  value: number;
  id: string;
}

export {
  DictionaryData,
  ICitationItem,
  IData,
  ILevel,
  ITabularCategory,
  ITabularCategoryDetail,
  IVersionHistoryData,
  VersionHistoryType,
};
