import { create } from "zustand";
import { DictionaryData } from "./types";

export interface IStore {
  dictionaryData: DictionaryData;
  setDictionaryData: (dictionaryData: DictionaryData) => void;
}

const useTabularDictionaryStore = create<
  IStore,
  [["zustand/persist", unknown]]
>((set) => ({
  dictionaryData: {},
  setDictionaryData: (dictionaryData: DictionaryData) =>
    set({ dictionaryData }),
}));

export default useTabularDictionaryStore;
