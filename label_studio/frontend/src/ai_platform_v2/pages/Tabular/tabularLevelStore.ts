import { create } from "zustand";

interface ISelectedItem {
  value: string;
  columnKey: string;
}

export interface IStore {
  selectedItems: ISelectedItem[];
  setSelectedItems: (selectedItems: ISelectedItem[]) => void;
  resetSelectedItems: () => void;
}

const useTabularLevelStore = create<IStore, [["zustand/persist", unknown]]>(
  (set) => ({
    selectedItems: [],
    setSelectedItems: (selectedItems: ISelectedItem[]) =>
      set({ selectedItems }),
    resetSelectedItems: () => set({ selectedItems: [] }),
  })
);

export default useTabularLevelStore;
