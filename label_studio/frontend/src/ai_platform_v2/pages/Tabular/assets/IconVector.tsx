import { memo } from "react";

const IconVector = ({
  size = 12,
  color = "#000",
  opacity = "0.3",
}: {
  size?: number;
  color?: string;
  opacity?: string;
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 12 12"
      fill="none"
    >
      <path
        className="duration-300"
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6 0C4.81331 0 3.65328 0.351894 2.66658 1.01118C1.67989 1.67047 0.910851 2.60754 0.456725 3.7039C0.00259969 4.80025 -0.11622 6.00665 0.115291 7.17054C0.346802 8.33443 0.918247 9.40352 1.75736 10.2426C2.59648 11.0818 3.66557 11.6532 4.82946 11.8847C5.99335 12.1162 7.19975 11.9974 8.2961 11.5433C9.39246 11.0891 10.3295 10.3201 10.9888 9.33342C11.6481 8.34672 12 7.18669 12 6C12 5.21207 11.8448 4.43185 11.5433 3.7039C11.2417 2.97594 10.7998 2.31451 10.2426 1.75736C9.68549 1.20021 9.02405 0.758251 8.2961 0.456723C7.56815 0.155195 6.78793 0 6 0ZM6.6 5.652L7.86 6.378H7.848C7.96359 6.4435 8.05417 6.5455 8.10553 6.66803C8.1569 6.79055 8.16615 6.92665 8.13183 7.055C8.09752 7.18334 8.02157 7.29667 7.91591 7.3772C7.81025 7.45774 7.68085 7.50093 7.548 7.5C7.4429 7.50072 7.33944 7.47383 7.248 7.422L5.688 6.522L5.634 6.468L5.538 6.39C5.51454 6.3603 5.49442 6.3281 5.478 6.294C5.45845 6.26179 5.44236 6.2276 5.43 6.192C5.41368 6.15385 5.40355 6.11334 5.4 6.072V6V3C5.4 2.84087 5.46322 2.68826 5.57574 2.57574C5.68826 2.46321 5.84087 2.4 6 2.4C6.15913 2.4 6.31174 2.46321 6.42426 2.57574C6.53679 2.68826 6.6 2.84087 6.6 3V5.652Z"
        fill={color}
        fillOpacity={opacity}
      />
    </svg>
  );
};

export default memo(IconVector);
