import { memo } from "react";

const IconStars04 = ({
  size = 16,
  className = "",
}: {
  size?: number;
  className?: string;
  color?: string;
  opacity?: string;
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 16 17"
      fill="none"
      className={className}
    >
      <g clipPath="url(#clip0_18719_103545)">
        <path
          d="M13.6621 3.03525L13.6619 3.03505C13.5772 2.94847 13.5014 2.84358 13.4338 2.73236C13.3662 2.84345 13.2905 2.94815 13.206 3.0346M13.6621 3.03525L14.0746 3.97895M13.6621 3.03525C13.779 3.15475 13.931 3.25909 14.0889 3.3489M13.6621 3.03525L15.3359 3.33954C15.1089 3.26141 14.8671 3.17011 14.6481 3.07107C14.5611 3.10828 14.4752 3.14704 14.3924 3.18703C14.293 3.23506 14.1891 3.28902 14.0889 3.3489M13.206 3.0346C13.0891 3.15467 12.9369 3.25934 12.7788 3.3493C12.9153 3.43077 13.0453 3.52332 13.148 3.62701C13.255 3.73494 13.35 3.87265 13.4333 4.01689C13.5167 3.8726 13.6121 3.73481 13.7197 3.62671M13.206 3.0346C13.2062 3.03446 13.2063 3.03431 13.2064 3.03416L12.8481 2.68547L13.2056 3.03505C13.2057 3.0349 13.2059 3.03475 13.206 3.0346ZM13.7197 3.62671C13.72 3.62644 13.7202 3.62617 13.7205 3.62591L14.0746 3.97895M13.7197 3.62671C13.7196 3.62681 13.7195 3.62691 13.7194 3.62701L14.0746 3.97895M13.7197 3.62671C13.8224 3.52313 13.9523 3.43051 14.0889 3.3489M14.0746 3.97895C14.1965 3.85594 14.408 3.7321 14.6521 3.61709C14.5546 3.57698 14.4581 3.53491 14.3653 3.4912C14.275 3.44862 14.1809 3.40122 14.0889 3.3489M2.54613 14.6443C2.55911 14.631 2.57177 14.6172 2.58412 14.6031C2.59687 14.6176 2.60994 14.6317 2.62333 14.6454C2.66151 14.6845 2.70335 14.7212 2.74734 14.7557C2.71908 14.7793 2.69196 14.804 2.66647 14.8298C2.63751 14.859 2.60986 14.8903 2.5835 14.9231C2.55714 14.8903 2.52949 14.859 2.50053 14.8298C2.47518 14.8042 2.44823 14.7796 2.42014 14.7561C2.46416 14.7218 2.50602 14.6852 2.54425 14.6463L2.54613 14.6443ZM6.67046 7.53887L6.67091 7.53841C6.91435 7.289 7.13385 6.93583 7.32855 6.55837C7.52703 6.17359 7.71624 5.73064 7.89029 5.2773C8.01987 4.93982 8.14262 4.59244 8.25596 4.25319C8.36932 4.59261 8.49211 4.94018 8.62172 5.27782C8.79578 5.73125 8.985 6.17428 9.1835 6.55903C9.37819 6.93641 9.59778 7.28957 9.84144 7.53874C10.0964 7.79969 10.4636 8.03739 10.8574 8.24927C11.259 8.46532 11.7224 8.67229 12.1956 8.86284C12.599 9.02531 13.0151 9.17803 13.4141 9.31624C13.0388 9.45198 12.6492 9.60061 12.2711 9.75756C11.8135 9.94753 11.3655 10.1526 10.9767 10.365C10.595 10.5734 10.2394 10.8052 9.99053 11.0564C9.74048 11.3087 9.50989 11.6697 9.30265 12.057C9.09144 12.4517 8.88749 12.9065 8.69871 13.3709C8.54098 13.7588 8.39183 14.1585 8.25601 14.5426C8.1202 14.1585 7.97105 13.7588 7.81332 13.3709C7.62454 12.9065 7.42058 12.4517 7.20937 12.057C7.00214 11.6697 6.77154 11.3087 6.52149 11.0564C6.27284 10.8051 5.91739 10.5733 5.53601 10.365C5.14719 10.1526 4.69913 9.94751 4.24143 9.75755C3.86309 9.60052 3.47322 9.45183 3.0976 9.31604C3.49636 9.17788 3.91215 9.02523 4.31538 8.86284C4.78852 8.6723 5.25195 8.46534 5.65369 8.24932C6.04782 8.03739 6.41521 7.7997 6.67046 7.53887ZM13.4337 5.26948C13.5135 5.04226 13.6062 4.80007 13.7059 4.58202L13.1605 4.58216C13.2603 4.80017 13.3533 5.04231 13.4337 5.26948ZM13.4337 1.4668C13.3547 1.70607 13.262 1.95688 13.1616 2.17539L13.4337 1.4668Z"
          fill="#2B7CFF"
          stroke="#2B7CFF"
        />
      </g>
      <defs>
        <clipPath id="clip0_18719_103545">
          <rect
            width={size}
            height={size}
            fill="white"
            transform="translate(0 0.799805)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default memo(IconStars04);
