import { memo } from "react";

const IconExport = ({
  size = 16,
  color = "#334466",
}: {
  size?: number;
  color?: string;
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 16 17"
      fill="none"
    >
      <path
        d="M5.80927 5.63977L7.33594 4.10643V10.4998C7.33594 10.6766 7.40618 10.8461 7.5312 10.9712C7.65622 11.0962 7.82579 11.1664 8.0026 11.1664C8.17942 11.1664 8.34898 11.0962 8.47401 10.9712C8.59903 10.8461 8.66927 10.6766 8.66927 10.4998V4.10643L10.1959 5.63977C10.2579 5.70225 10.3316 5.75185 10.4129 5.7857C10.4941 5.81954 10.5813 5.83697 10.6693 5.83697C10.7573 5.83697 10.8444 5.81954 10.9257 5.7857C11.0069 5.75185 11.0806 5.70225 11.1426 5.63977C11.2051 5.57779 11.2547 5.50406 11.2885 5.42282C11.3224 5.34158 11.3398 5.25444 11.3398 5.16644C11.3398 5.07843 11.3224 4.99129 11.2885 4.91005C11.2547 4.82881 11.2051 4.75508 11.1426 4.6931L8.47594 2.02643C8.41254 1.96574 8.33777 1.91816 8.25594 1.88643C8.09363 1.81976 7.91158 1.81976 7.74927 1.88643C7.66744 1.91816 7.59267 1.96574 7.52927 2.02643L4.8626 4.6931C4.80045 4.75526 4.75114 4.82905 4.7175 4.91027C4.68386 4.99148 4.66654 5.07853 4.66654 5.16644C4.66654 5.25434 4.68386 5.34139 4.7175 5.4226C4.75114 5.50382 4.80045 5.57761 4.8626 5.63977C4.92476 5.70193 4.99856 5.75123 5.07977 5.78487C5.16099 5.81851 5.24803 5.83583 5.33594 5.83583C5.42384 5.83583 5.51089 5.81851 5.5921 5.78487C5.67332 5.75123 5.74711 5.70193 5.80927 5.63977ZM14.0026 9.8331C13.8258 9.8331 13.6562 9.90334 13.5312 10.0284C13.4062 10.1534 13.3359 10.323 13.3359 10.4998V13.1664C13.3359 13.3432 13.2657 13.5128 13.1407 13.6378C13.0157 13.7629 12.8461 13.8331 12.6693 13.8331H3.33594C3.15913 13.8331 2.98956 13.7629 2.86453 13.6378C2.73951 13.5128 2.66927 13.3432 2.66927 13.1664V10.4998C2.66927 10.323 2.59903 10.1534 2.47401 10.0284C2.34898 9.90334 2.17942 9.8331 2.0026 9.8331C1.82579 9.8331 1.65622 9.90334 1.5312 10.0284C1.40618 10.1534 1.33594 10.323 1.33594 10.4998V13.1664C1.33594 13.6969 1.54665 14.2056 1.92172 14.5806C2.2968 14.9557 2.8055 15.1664 3.33594 15.1664H12.6693C13.1997 15.1664 13.7084 14.9557 14.0835 14.5806C14.4586 14.2056 14.6693 13.6969 14.6693 13.1664V10.4998C14.6693 10.323 14.599 10.1534 14.474 10.0284C14.349 9.90334 14.1794 9.8331 14.0026 9.8331Z"
        fill={color}
      />
    </svg>
  );
};

export default memo(IconExport);
