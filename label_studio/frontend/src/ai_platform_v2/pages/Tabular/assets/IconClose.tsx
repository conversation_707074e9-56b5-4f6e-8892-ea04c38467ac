import { memo } from "react";

const IconClose = ({
  size = 16,
  color = "#334466",
}: {
  size?: number;
  color?: string;
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 16 17"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.64645 4.14645C3.84171 3.95118 4.15829 3.95118 4.35355 4.14645L8 7.79289L11.6464 4.14645C11.8417 3.95118 12.1583 3.95118 12.3536 4.14645C12.5488 4.34171 12.5488 4.65829 12.3536 4.85355L8.70711 8.5L12.3536 12.1464C12.5488 12.3417 12.5488 12.6583 12.3536 12.8536C12.1583 13.0488 11.8417 13.0488 11.6464 12.8536L8 9.20711L4.35355 12.8536C4.15829 13.0488 3.84171 13.0488 3.64645 12.8536C3.45118 12.6583 3.45118 12.3417 3.64645 12.1464L7.29289 8.5L3.64645 4.85355C3.45118 4.65829 3.45118 4.34171 3.64645 4.14645Z"
        fill={color}
      />
    </svg>
  );
};

export default memo(IconClose);
