import { useMemo } from "react";
import classNames from "classnames";
import IconVector from "../../assets/IconVector";
import Avatar from "@/ai_platform_v2/component/Avatar/Avatar";
import { Typography } from "antd";
import {
  convertTimeFromNow,
  DATE_TIME_FORMAT,
} from "@/ai_platform_v2/utils/dateTime";
import { IVersionHistoryData, VersionHistoryType } from "../../types";

const { Text } = Typography;

const VersionHistoryItem = ({
  data,
  active,
  onClick,
  onRestore,
}: {
  data: IVersionHistoryData;
  active?: boolean;
  onClick?: () => void;
  onRestore?: () => void;
}) => {
  const {
    historyId,
    fullName = "",
    avatar = "",
    actionType,
    createdAt = "",
    isCurrent,
    actionByHistoryId,
  } = data;

  const isDraftVersion = useMemo(
    () => actionType === VersionHistoryType.Draft,
    [actionType]
  );

  const actionTypeContent = useMemo(() => {
    if (actionType === VersionHistoryType.Restored) {
      return `${actionType} ${actionByHistoryId ?? ""}`;
    }

    return actionType;
  }, [actionType, actionByHistoryId]);

  return (
    <div
      className={classNames(
        "flex h-[62px] cursor-pointer p-1 gap-[3px] justify-between rounded-lg hover:bg-[#F5F6F7] duration-300 border border-transparent",
        active && "!bg-[#F9FBFF] !border-[#2B7CFF]"
      )}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();

        onClick?.();
      }}
    >
      <div className="flex gap-2 items-center">
        <div className="flex flex-col items-center">
          <div className="flex p-[3px]">
            <IconVector
              color={isDraftVersion ? "#3361FF" : "#000"}
              opacity={isDraftVersion ? "1" : "0.3"}
            />
          </div>
          <div
            className={classNames(
              "flex h-[36px] w-[1px] bg-[rgba(0,0,0,0.05)] duration-300",
              isDraftVersion && "!bg-[#3361FF]"
            )}
          />
        </div>
        <div className="flex gap-2 items-start">
          <Avatar
            className="flex justify-center items-center rounded-full border border-[rgba(0,0,0,0.03)]"
            url={avatar}
            size="30px"
            fontSize="12px"
            name={fullName}
          />
          <div className="flex flex-col items-start max-w-[141px]">
            <div className="flex text-[12px] text-[#6B7A99] font-normal leading-[18px]">
              {historyId}
            </div>
            <Text
              className="flex text-[12px] !text-[#346] font-medium leading-[18px]"
              ellipsis={{
                tooltip: fullName,
              }}
            >
              {fullName}
            </Text>
            <div className="flex text-[12px] text-[#346] font-normal leading-[18px]">
              {actionTypeContent}
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col h-full justify-between items-end">
        <div
          className="flex text-[12px] text-[#3361FF] font-normal leading-[18px] p-[2px] rounded hover:bg-[#ECF1FA] duration-300"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (!isDraftVersion && !isCurrent) {
              onRestore?.();
            }
          }}
        >
          {!isDraftVersion && !isCurrent ? "Restore" : ""}
        </div>
        <div className="flex text-[12px] text-[#6B7A99] font-normal leading-[18px]">
          {convertTimeFromNow(createdAt, true, DATE_TIME_FORMAT.MMMDD_HHMM)}
        </div>
      </div>
    </div>
  );
};

export default VersionHistoryItem;
