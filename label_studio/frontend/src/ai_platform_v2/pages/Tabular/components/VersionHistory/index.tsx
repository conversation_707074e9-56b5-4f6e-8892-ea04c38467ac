import Message from "@/ai_platform_v2/component/Message/Message";
import { getCurrentProject } from "@/pages/DataSet/Const";
import { useAPI } from "@/providers/ApiProvider";
import { useCurrentUser } from "@/providers/CurrentUser";
import { ModalConfirmBig } from "@taureau/ui";
import { Spin } from "antd";
import classNames from "classnames";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import IconClose from "../../assets/IconClose";
import IconHistory from "../../assets/IconHistory";
import useTabularHistoryStore from "../../tabularHistoryStore";
import { VersionHistoryType } from "../../types";
import "./index.scss";
import VersionHistoryItem from "./VersionHistoryItem";

const VersionHistory = ({
  currentTabularCategory,
  reFetchData,
}: {
  currentTabularCategory: any;
  reFetchData: (history: any) => void;
}) => {
  const api = useAPI();
  const { user } = useCurrentUser();
  const projectId = getCurrentProject();

  const wrapperRef = useRef<HTMLDivElement>(null);
  const closeButtonRef = useRef<HTMLDivElement>(null);
  const versionButtonRef = useRef<HTMLDivElement>(null);

  const [loading, setLoading] = useState(false);
  const [openHistoryList, setOpenHistoryList] = useState(false);
  const [versionHistoryList, setVersionHistoryList] = useState<any>([]);

  const {
    selectedHistory,
    setSelectedHistory,
    loadingDetail,
    setLoadingDetail,
  } = useTabularHistoryStore((state) => ({
    loadingDetail: state.loading,
    setLoadingDetail: state.setLoading,
    selectedHistory: state.selectedHistory,
    setSelectedHistory: state.setSelectedHistory,
  }));

  const buttonContent = useMemo(() => {
    if (selectedHistory) {
      return `Version ${selectedHistory?.historyId} displayed`;
    }

    return "Version history";
  }, [selectedHistory]);

  const fetchVersionHistoryDetail = useCallback(
    async (history) => {
      if (!history?.id) {
        return;
      }

      // temporary select version history
      setSelectedHistory(history);

      setLoadingDetail(true);

      const res = await api.callApi("tabularCategoryHistoryDetail", {
        params: {
          projectId,
          versionHistoryId: history.id,
        },
      });

      setLoadingDetail(false);

      if (res.success) {
        setSelectedHistory({
          ...res.data,
          historyId: history.historyId,
          status: history.status,
        });
      }
    },
    [projectId]
  );

  const handleSelectVersionHistory = useCallback(
    (history) => {
      if (loadingDetail) {
        return;
      }

      if (
        history?.actionType === VersionHistoryType.Draft ||
        (selectedHistory?.historyId === history?.historyId &&
          selectedHistory?.historyId &&
          history?.historyId)
      ) {
        setSelectedHistory(undefined);
        return;
      }

      fetchVersionHistoryDetail(history);
    },
    [loadingDetail, selectedHistory, fetchVersionHistoryDetail]
  );

  const fetchVersionHistories = useCallback(async () => {
    if (!currentTabularCategory?.tabularCategoryId) {
      return;
    }

    setLoading(true);

    const res = await api.callApi("tabularCategoryHistories", {
      params: {
        projectId,
        tabularCategoryId: currentTabularCategory?.tabularCategoryId,
      },
    });

    setLoading(false);

    if (res.success) {
      const totalVersionHistory = res?.total;
      const versionHistoryListData =
        res?.items?.map((history: any, index: number) => ({
          ...history,
          historyId: `#${totalVersionHistory - index}`,
          isCurrent: index === 0,
        })) ?? [];

      setVersionHistoryList([
        {
          historyId: `#${totalVersionHistory + 1}`,
          avatar: user.avatar,
          actionType: "Draft",
          fullName: user.fullName,
          createdAt: Date.now(),
        },
        ...versionHistoryListData.map((history: any) => {
          let actionByHistoryId = "";

          if (history.actionType === VersionHistoryType.Restored) {
            actionByHistoryId =
              versionHistoryListData.find(
                (findHistory: any) =>
                  history.historyRestoredVersionId === findHistory.id
              )?.historyId ?? "";
          }

          return {
            ...history,
            actionByHistoryId,
          };
        }),
      ]);
    }
  }, [projectId, user, currentTabularCategory?.tabularCategoryId]);

  const handleRestoreVersionHistory = useCallback(
    (history) => {
      ModalConfirmBig.warning({
        header: "Warning",
        title: "Wanna Restore this version history?",
        content: `If you continue, changes you made may not be saved.`,
        onOk: async () => {
          try {
            const res = await api.callApi("restoreTabularCategoryHistory", {
              params: {
                projectId,
                tabularCategoryId: history.tabularCategoryId,
                versionHistoryId: history.id,
              },
            });

            if (res.success) {
              Message.success({
                title: "Success",
                content: "Version restored successfully",
              });

              // Reset version history and closing ver history sidebar
              setOpenHistoryList(false);
              setSelectedHistory(undefined);
              reFetchData?.(history);
            } else {
              Message.error({
                title: "Error",
                content: res.message || "Something went wrong",
              });
            }
          } catch (error) {
            Message.error({
              content: "Something went wrong",
            });
          }
        },
        okText: "Continue",
        cancelText: "Cancel",
      });
    },
    [projectId, reFetchData]
  );

  useEffect(() => {
    function handleClickOutside(event: any) {
      if (
        wrapperRef.current &&
        !wrapperRef.current.contains(event.target) &&
        !closeButtonRef.current?.contains(event.target) &&
        !versionButtonRef.current?.contains(event.target)
      ) {
        setOpenHistoryList(false);
      }
    }
    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (openHistoryList) {
      fetchVersionHistories();
    }
  }, [openHistoryList, currentTabularCategory?.tabularCategoryId]);

  return (
    <div className="flex relative">
      <div
        className={classNames(
          "flex items-center gap-2 duration-300",
          !selectedHistory && "!gap-0"
        )}
      >
        <div
          ref={versionButtonRef}
          className={classNames(
            "flex px-2 py-1 gap-[6px] items-center rounded-lg cursor-pointer hover:bg-[#EDEFF2] duration-300",
            openHistoryList && "!bg-[#ECF1FA]"
          )}
          onClick={() => setOpenHistoryList(!openHistoryList)}
        >
          <IconHistory color={openHistoryList ? "#3361FF" : "#334466"} />
          <div
            className={classNames(
              "flex text-[14px] text-[#346] font-medium leading-[21px] duration-300",
              openHistoryList && "!text-[#3361FF]"
            )}
          >
            {buttonContent}
          </div>
        </div>

        <div
          ref={closeButtonRef}
          className={classNames(
            "flex gap-[2px] w-[52px] opacity-100 items-center cursor-pointer overflow-hidden duration-300 ease-in-out",
            !selectedHistory ? "!w-0 !opacity-0" : "ml-2"
          )}
          onClick={() => setSelectedHistory(undefined)}
        >
          <IconClose />
          <div className="flex text-[12px] text-[#346] font-normal leading-[18px]">
            Close
          </div>
        </div>
      </div>

      <div
        ref={wrapperRef}
        className={classNames(
          "flex flex-col absolute right-0 top-[37px] z-30 min-h-[300px] h-[calc(100vh-200px)] w-[0px] opacity-0 rounded-[20px] bg-white border border-[#EAEAEA] shadow-md gap-1 overflow-hidden transition-[width,opacity] duration-700 ease-in-out",
          openHistoryList && "!w-[342px] !opacity-100"
        )}
      >
        <div className="flex p-3 items-center justify-between">
          <div className="flex text-[14px] text-[#346] font-medium leading-[21px]">
            Version history
          </div>
          <div
            className="flex cursor-pointer"
            onClick={() => setOpenHistoryList(false)}
          >
            <IconClose />
          </div>
        </div>
        <div className="version-history-list h-full flex flex-col px-3 pb-3 gap-1 overflow-x-hidden overflow-y-auto">
          {loading && (
            <div className="flex justify-center items-center h-full w-full">
              <Spin size="large" />
            </div>
          )}
          {!loading &&
            versionHistoryList.map((history: any) => (
              <VersionHistoryItem
                key={history.id}
                data={history}
                active={
                  selectedHistory?.historyId === history?.historyId &&
                  selectedHistory?.historyId &&
                  history?.historyId
                }
                onClick={() => handleSelectVersionHistory(history)}
                onRestore={() => handleRestoreVersionHistory(history)}
              />
            ))}
        </div>
      </div>
    </div>
  );
};

export default VersionHistory;
