import { <PERSON><PERSON>, <PERSON>, Typography } from "antd";
import classNames from "classnames";
import { memo, useCallback, useMemo, useState } from "react";

import Message from "@/ai_platform_v2/component/Message/Message";
import { getCurrentProject } from "@/pages/DataSet/Const";
import { useAPI } from "@/providers/ApiProvider";
import { SpecialZoomLevel, Viewer, Worker } from "@react-pdf-viewer/core";
import { ICitationItem } from "../../types";

import { fullScreenPlugin } from "@react-pdf-viewer/full-screen";
import { pageNavigationPlugin } from "@react-pdf-viewer/page-navigation";
import { searchPlugin } from "@react-pdf-viewer/search";
import { zoomPlugin } from "@react-pdf-viewer/zoom";

import "@react-pdf-viewer/core/lib/styles/index.css";
import "@react-pdf-viewer/default-layout/lib/styles/index.css";

import "@react-pdf-viewer/full-screen/lib/styles/index.css";
import "@react-pdf-viewer/page-navigation/lib/styles/index.css";
import "@react-pdf-viewer/search/lib/styles/index.css";
import "@react-pdf-viewer/zoom/lib/styles/index.css";

import { isEmpty } from "lodash";
import IconClose from "../../assets/IconClose";
import "./CitationItem.scss";

const { Paragraph, Text } = Typography;

const CitationItem = ({
  citationNumber,
  citationDataset,
}: {
  citationNumber: string;
  citationDataset: ICitationItem[];
}) => {
  const [isActive, setIsActive] = useState(false);
  const projectId = getCurrentProject();

  const fullScreenPluginInstance = fullScreenPlugin();
  const zoomPluginInstance = zoomPlugin();
  const pageNavigationPluginInstance = pageNavigationPlugin();
  const searchPluginInstance = searchPlugin();
  const { EnterFullScreenButton } = fullScreenPluginInstance;
  const { Zoom } = zoomPluginInstance;
  const {
    GoToNextPageButton,
    GoToPreviousPageButton,
    NumberOfPages,
    CurrentPageInput,
  } = pageNavigationPluginInstance;
  const { ShowSearchPopoverButton } = searchPluginInstance;

  const api = useAPI();

  const [isGeneratingFile, setGeneratingFile] = useState(false);
  const [pdfData, setPdfData] = useState<any>();

  const citationData = useMemo(() => {
    const data = citationDataset.find(
      (item) => item.idx === `idx_${citationNumber}`
    );

    return data;
  }, [citationDataset, citationNumber]);

  const isInvalidCitation = useMemo(() => {
    return isEmpty(citationData);
  }, [citationData]);

  const handleGenerateHighlight = useCallback(async () => {
    try {
      if (isGeneratingFile || pdfData) return;
      setGeneratingFile(true);

      const res: any = await api.callApi("highlightCitation", {
        params: {
          projectId,
        },
        body: {
          page: citationData?.chunk_page,
          quote: citationData?.content,
          chunk: citationData?.chunk_content,
          file_url: citationData?.chunk_file_name,
        },
      });

      if (res?.out_url) {
        setPdfData({
          url: res?.out_url,
          page: res?.main_page ?? 0,
        });
      }
    } catch (error) {
      Message.error({ content: "Something went wrong" });
    } finally {
      setGeneratingFile(false);
    }
  }, [projectId, citationData, pdfData, isGeneratingFile]);

  if (isActive)
    return (
      <Popover
        placement="right"
        showArrow={false}
        content={() => {
          if (isInvalidCitation || (isEmpty(pdfData) && !isGeneratingFile)) {
            return (
              <div className="relative flex flex-col w-[265px] bg-white rounded-[12px] h-[280px] p-[20px] items-center justify-center text-[12px] leading-[18px] text-[#346]">
                Cannot load citation, please retry!
                <div
                  className="absolute top-[10px] right-[10px] cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsActive(false);
                  }}
                >
                  <IconClose />
                </div>
              </div>
            );
          }
          return (
            <div
              className="flex flex-col w-[650px] h-[553px] playground-multiple-agents rounded-[12px] bg-white shadow-xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex w-full items-center justify-between gap-[4px] border-b border-[#E5E5E5] p-3 relative">
                <div className="flex items-center gap-[8px]">
                  <Text
                    ellipsis={{ tooltip: true }}
                    style={{
                      fontSize: "12px",
                      lineHeight: "18px",
                      color: "#346",
                      fontWeight: 500,
                    }}
                  >
                    {citationData?.chunk_file_name}
                  </Text>
                </div>
                <div
                  className="cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsActive(false);
                  }}
                >
                  <IconClose />
                </div>
              </div>

              <div className="flex w-full h-[calc(100%-43px)] flex-col overflow-x-auto overflow-y-hidden pb-5 pt-2">
                {isGeneratingFile ? (
                  <div className="flex h-[calc(100%-40px)] w-full items-center justify-center">
                    <Spin />
                  </div>
                ) : (
                  <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js">
                    <div className="flex items-center justify-between px-[4px] pb-[8px]">
                      <ShowSearchPopoverButton />

                      <div className="flex items-center gap-[4px]">
                        <GoToNextPageButton key={"next-page-view-pdf"} />
                        <CurrentPageInput />
                        /
                        <NumberOfPages />
                        <GoToPreviousPageButton />
                      </div>
                      <div className="flex items-center gap-[4px]">
                        <Zoom />
                        <EnterFullScreenButton />
                      </div>
                    </div>

                    <div className="flex h-[calc(100%-40px)] w-full">
                      <Viewer
                        renderLoader={() => <Spin />}
                        initialPage={pdfData?.page ?? 0}
                        fileUrl={pdfData?.url}
                        plugins={[
                          searchPluginInstance,
                          pageNavigationPluginInstance,
                          zoomPluginInstance,
                          fullScreenPluginInstance,
                        ]}
                        defaultScale={SpecialZoomLevel.PageWidth}
                      />
                    </div>
                  </Worker>
                )}
              </div>
            </div>
          );
        }}
        overlayInnerStyle={{
          padding: 0,
          borderRadius: "12px",
          boxShadow:
            "0px 10px 15px -3px rgba(0, 0, 0, 0.10), 0px 4px 6px -2px rgba(0, 0, 0, 0.05)",
          border: "1px solid rgba(0, 0, 0, 0.05)",
        }}
        overlayClassName="citation-item-popover"
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setIsActive(false);
          }
        }}
        open={isActive}
        trigger={"click"}
      >
        <div
          className={classNames(
            "inline-flex items-center justify-center min-w-[20px] ml-[4px] px-[6px] py-[1px] rounded-full text-[12px]  cursor-pointer leading-[18px] shadow-sm hover:bg-[#334466] hover:text-white",
            isActive ? "bg-[#334466] text-white" : "text-[#346] bg-[#C3CAD9]"
          )}
          onClick={(e) => {
            // setIsActive(!isActive);
            e.stopPropagation();
          }}
        >
          {citationNumber}
        </div>
      </Popover>
    );

  return (
    <Popover
      placement="bottomLeft"
      showArrow={false}
      content={() => {
        if (isInvalidCitation) {
          return (
            <div className="relative flex flex-col w-[265px] bg-white rounded-[12px] h-[280px] p-[20px] items-center justify-center text-[12px] leading-[18px] text-[#346]">
              Cannot load citation, please retry!
              <div className="absolute top-[10px] right-[10px] cursor-pointer">
                <IconClose />
              </div>
            </div>
          );
        }
        return (
          <div
            className="flex flex-col w-[265px] bg-white rounded-[12px]"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-[20px] rounded-t-[12px]">
              <Paragraph
                ellipsis={{ rows: 12, tooltip: true }}
                style={{ fontSize: "12px", lineHeight: "18px", color: "#346" }}
              >
                {citationData?.content}
              </Paragraph>
            </div>
            <div className="px-[12px] py-[8px] flex flex-col rounded-b-[12px] border-t border-[#E5E5E5]">
              {citationData?.chunk_page === 0 ? (
                <div className="text-[10px] leading-[15px] text-[#6B7A99]">
                  The information was generated by ChatGPT
                </div>
              ) : (
                <>
                  <Text
                    ellipsis={{ tooltip: true }}
                    style={{
                      fontSize: "12px",
                      lineHeight: "18px",
                      color: "#346",
                      fontWeight: 500,
                    }}
                  >
                    {citationData?.chunk_file_name}
                  </Text>
                  <div className="text-[10px] leading-[15px] text-[#6B7A99]">
                    {`Page ${citationData?.chunk_page}`}
                  </div>
                </>
              )}
            </div>
          </div>
        );
      }}
      overlayInnerStyle={{
        padding: 0,
        borderRadius: "12px",
        boxShadow:
          "0px 10px 15px -3px rgba(0, 0, 0, 0.10), 0px 4px 6px -2px rgba(0, 0, 0, 0.05)",
        border: "1px solid rgba(0, 0, 0, 0.05)",
      }}
      overlayClassName="citation-item-popover"
    >
      <div
        className={classNames(
          "inline-flex items-center justify-center min-w-[20px] ml-[4px] px-[6px] py-[1px] rounded-full text-[12px]  cursor-pointer leading-[18px] shadow-sm hover:bg-[#334466] hover:text-white",
          "text-[#346] bg-[#C3CAD9]"
        )}
        onClick={(e) => {
          e.stopPropagation();
          if (citationData?.chunk_page === 0) return;
          handleGenerateHighlight();
          setIsActive(!isActive);

          // onClick(citationNumber);
        }}
      >
        {citationNumber}
      </div>
    </Popover>
  );
};

export default memo(CitationItem);
