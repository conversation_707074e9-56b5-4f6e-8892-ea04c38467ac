import { memo, useEffect, useRef, useState } from "react";
import { ICitationItem } from "../../../types";
import CitationItem from "../CitationItem";

interface IProps {
  value?: string;
  onChange?: (value: string) => void;
  autoResize?: boolean;
  maxHeight?: number;
  onBlur?: () => void;
  onFocus?: () => void;
  isAutofocus?: boolean;
  disabled?: boolean;
  citationDataset?: ICitationItem[];
}

const DataText = ({
  value,
  onChange,
  autoResize = true,
  maxHeight = 200,
  onBlur,
  isAutofocus = false,
  disabled = false,
  citationDataset = [],
}: IProps) => {
  const [isFocus, setFocus] = useState(isAutofocus ?? false);

  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const adjustHeight = () => {
    if (!textAreaRef.current) return;
    textAreaRef.current!.style.height = "inherit";

    textAreaRef.current!.style.height = `${textAreaRef.current!.scrollHeight > maxHeight ? maxHeight : textAreaRef.current!.scrollHeight}px`;
  };

  const parseTextWithReferences = (text: string) => {
    const regex = /\[(\d+)\]/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(text)) !== null) {
      const before = text.slice(lastIndex, match.index);
      const number = match[1];

      if (before) parts.push(before);
      parts.push(
        <CitationItem
          key={match.index}
          citationNumber={number}
          citationDataset={citationDataset}
        />
      );
      lastIndex = regex.lastIndex;
    }

    // Push any remaining text after the last match
    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }

    return parts;
  };

  useEffect(() => {
    if (isFocus && !disabled) {
      // Set the cursor to the last character
      textAreaRef.current!.selectionStart = value?.length ?? 0;
      textAreaRef.current!.selectionEnd = value?.length ?? 0;
    }
  }, [isFocus, disabled]);

  useEffect(() => {
    if (autoResize && !disabled && isFocus) adjustHeight();
  }, [value, disabled, isFocus]);

  if (disabled || !isFocus)
    return (
      <div
        className="w-full min-h-[62px] text-[14px] text-[#346] px-[12px] py-[10px] leading-[21px] whitespace-pre-wrap hover:bg-[#f7f8fa]"
        // style={{
        //   display: "ruby",
        // }}
        onClick={() => {
          if (!disabled) setFocus(true);
        }}
      >
        {parseTextWithReferences(value ?? "")}
      </div>
    );

  return (
    <textarea
      ref={textAreaRef}
      value={value}
      onChange={(e) => {
        onChange?.(e.target.value);
      }}
      className={
        "w-full resize-none bg-transparent border-none outline-0 text-[14px] text-[#346] px-[12px] py-[10px] leading-[21px] hover:bg-[#f7f8fa]"
      }
      onBlur={() => {
        setFocus(false);
        onBlur?.();
      }}
      autoFocus
      onFocus={() => {
        setFocus(true);
      }}
    />
  );
};

export default memo(DataText);
