.tabular-data-select {
  position: relative;
  width: 100%;

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    min-height: 49px;
    cursor: pointer;

    &:hover {
      background-color: #f7f8fa;
    }

    &__selected {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      flex: 1;
    }

    &__item {
      display: flex;
      align-items: center;
      padding: 4px 12px;
      border-radius: 6px;
      background-color: #f2f3f5;
      color: #346;
      font-size: 14px;
      font-weight: 400;
      line-height: 21px;

      &:hover {
        background-color: #e1e5ed;
      }

      &--active {
        border: 1px solid #66cfff;
      }

      &--hallmark,
      &--very_common,
      &--common,
      &--normal,
      &--rare {
        padding-left: 8px;
      }

      &--hallmark {
        border-left: 12px solid #b20000;
      }

      &--very_common {
        border-left: 12px solid #e62e2e;
      }

      &--common {
        border-left: 12px solid #ff6666;
      }

      &--normal {
        border-left: 12px solid #ffa8a8;
      }

      &--rare {
        border-left: 12px solid #fad5d5;
      }
    }

    &__icon {
      color: #c3cad9;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 8px;
    }
  }

  &__dropdown {
    // position: absolute;
    // top: calc(100% + 8px);
    // left: 0;
    width: 100%;
    background-color: #ffffff;
    border-radius: 12px;

    padding: 8px;

    z-index: 10;
    overflow: hidden;

    &__search {
      padding: 6px 8px;
      background-color: #edeff2;
      display: flex;
      align-items: center;
      border-radius: 8px;
      margin-bottom: 8px;
      justify-content: space-between;

      input {
        width: 100%;
        border: none;
        background-color: transparent;
        color: #346;

        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        outline: none;

        &::placeholder {
          color: #6b7a99;
        }
      }

      &__icon {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 16px;
      }
    }

    &__list {
      max-height: 200px;
      overflow-y: auto;

      &__item {
        padding: 6px 4px;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
        cursor: pointer;
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        color: #334466;
        border-radius: 6px;

        &:hover {
          background-color: #f5f6f7;
        }

        &--selected {
          background-color: #ecf1fa !important;
          color: #3361ff;
        }
      }

      &__empty {
        padding: 12px 4px;
        text-align: center;
        font-size: 12px;
        font-weight: 400;
        line-height: 1.5em;
        color: #6b7a99;
      }
    }
  }
}

.tabular-data-select__popover {
  .ant-popover-inner {
    border-radius: 12px;
    padding: 0;
  }
  .ant-popover-inner-content {
    border-radius: 12px;
    padding: 0;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow:
      0px 10px 15px -3px rgba(0, 0, 0, 0.1),
      0px 4px 6px -4px rgba(0, 0, 0, 0.1);
  }
}
