import { Popover } from "antd";
import classNames from "classnames";
import { orderBy } from "lodash";
import React, { useCallback, useEffect, useRef, useState } from "react";
import DropdownIcon from "../../../assets/DropdownIcon";
import SearchIcon from "../../../assets/SearchIcon";
import { LEVELS } from "../../../constants";
import useClickPrevention from "../../../hooks/useClickPrevent";
import useTabularLevelStore from "../../../tabularLevelStore";
import { ICitationItem } from "../../../types";
import CitationItem from "../CitationItem";
import "./DataSelect.scss";

interface DataSelectOption {
  value: string;
  label: string;
}

interface DataSelectProps {
  options?: DataSelectOption[];
  value?: string | string[];
  placeholder?: string;
  multiple?: boolean;
  onChange?: (value: string | string[]) => void;
  className?: string;
  disabled?: boolean;
  citationDataset?: ICitationItem[];
  dataLevel?: {
    [key: string]: number;
  };
  columnKey?: string;
  handleChangeLevelDataTable?: (
    columnKey: string,
    label: string,
    value?: number
  ) => void;
}

const DataSelect: React.FC<DataSelectProps> = ({
  options = [],
  value = [],
  placeholder = "",
  multiple = true,
  onChange,
  className = "",
  disabled = false,
  citationDataset = [],
  dataLevel = {},
  columnKey = "",
  handleChangeLevelDataTable = () => {},
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const [selectedValues, setSelectedValues] = useState<string[]>([]);

  const { setSelectedItems, selectedItems, resetSelectedItems } =
    useTabularLevelStore((state) => ({
      setSelectedItems: state.setSelectedItems,
      selectedItems: state.selectedItems,
      resetSelectedItems: state.resetSelectedItems,
    }));

  const searchInputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
  };

  const handleSelectOption = (optionValue: string) => {
    let newSelectedValues: string[];

    if (multiple) {
      if (
        selectedValues
          ?.map((item) => item.replace(/\[\d+\]/g, ""))
          ?.includes(optionValue)
      ) {
        newSelectedValues = selectedValues.filter(
          (val) => val?.replace(/\[\d+\]/g, "") !== optionValue
        );
        // Remove the level if it exists
        handleChangeLevelDataTable(columnKey, optionValue, undefined);
      } else {
        newSelectedValues = [...selectedValues, optionValue];

        // Set the level to 0 if it doesn't exist
        handleChangeLevelDataTable(columnKey, optionValue, 0);
      }
    } else {
      newSelectedValues = [optionValue];
      setIsOpen(false);
      setSearchValue("");
    }

    setSelectedValues(newSelectedValues);
    onChange?.(multiple ? newSelectedValues : newSelectedValues[0] || "");
  };

  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchValue.toLowerCase())
  );

  const parseTextWithReferences = (text: string) => {
    const regex = /\[(\d+)\]/g;
    const parts = [];
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(text)) !== null) {
      const before = text.slice(lastIndex, match.index);
      const number = match[1];

      if (before) parts.push(before);
      parts.push(
        <CitationItem
          key={match.index}
          citationNumber={number}
          citationDataset={citationDataset}
        />
      );
      lastIndex = regex.lastIndex;
    }

    // Push any remaining text after the last match
    if (lastIndex < text.length) {
      parts.push(text.slice(lastIndex));
    }

    return parts;
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        event.target instanceof Element &&
        !event.target?.className?.includes("tabular-item")
      ) {
        resetSelectedItems();
      }
    };

    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setSelectedValues(Array.isArray(value) ? value : [value]);
  }, [value]);

  // Click item to select or deselect
  const handleClickItem = useCallback(
    (item: string, isActive: boolean) => {
      if (isActive) {
        const newSelectedItems = selectedItems.filter(
          (selectedItem) => selectedItem.value !== item?.replace(/\[\d+\]/g, "")
        );

        setSelectedItems(newSelectedItems);
      } else {
        setSelectedItems([
          ...selectedItems,
          { value: item?.replace(/\[\d+\]/g, ""), columnKey },
        ]);
      }
    },
    [selectedItems, columnKey]
  );

  // Double click item to change its level
  const handleDoubleClickItem = useCallback(
    (item: string) => {
      const currentLevel = dataLevel[item?.replace(/\[\d+\]/g, "")] ?? 0;

      handleChangeLevelDataTable(
        columnKey,
        item?.replace(/\[\d+\]/g, ""),
        (currentLevel + 1) % LEVELS.length
      );
    },
    [dataLevel, columnKey]
  );

  const [handleClick, handleDoubleClick] = useClickPrevention({
    onClick: handleClickItem,
    onDoubleClick: handleDoubleClickItem,
  });

  if (disabled) {
    return (
      <div className={`tabular-data-select ${className}`} ref={dropdownRef}>
        <div className="tabular-data-select__header">
          <div className="tabular-data-select__header__selected">
            {selectedValues.length > 0 ? (
              orderBy(
                selectedValues,
                (item) => dataLevel[item?.replace(/\[\d+\]/g, "")] ?? 0,
                "desc"
              ).map((label, index) => (
                <div
                  key={index}
                  className={classNames(
                    "tabular-data-select__header__item",
                    `tabular-data-select__header__item--${LEVELS?.find((level) => level.value === dataLevel[label?.replace(/\[\d+\]/g, "")])?.id}`
                  )}
                >
                  {parseTextWithReferences(label)}
                </div>
              ))
            ) : (
              <div>{placeholder}</div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <Popover
      placement="bottom"
      showArrow={false}
      trigger="click"
      onOpenChange={(visible: boolean) => {
        setIsOpen(visible);
        if (!visible) {
          setSearchValue("");
        }
      }}
      overlayClassName="tabular-data-select__popover"
      content={
        <div
          className="tabular-data-select__dropdown"
          style={{ width: dropdownRef.current?.offsetWidth }}
        >
          <div className="tabular-data-select__dropdown__search">
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Search value name"
              value={searchValue}
              onChange={handleSearchChange}
              onClick={(e) => e.stopPropagation()}
            />
            <div className="tabular-data-select__dropdown__search__icon">
              <SearchIcon />
            </div>
          </div>
          <div className="tabular-data-select__dropdown__list">
            {filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <div
                  key={option.value}
                  className={`tabular-data-select__dropdown__list__item ${
                    selectedValues
                      ?.map((item) => item.replace(/\[\d+\]/g, ""))
                      ?.includes(option.value)
                      ? "tabular-data-select__dropdown__list__item--selected"
                      : ""
                  }`}
                  onClick={() => handleSelectOption(option.value)}
                >
                  {option.label}
                </div>
              ))
            ) : (
              <div className="tabular-data-select__dropdown__list__empty">
                No results found
              </div>
            )}
          </div>
        </div>
      }
    >
      <div className={`tabular-data-select ${className}`} ref={dropdownRef}>
        <div
          className={classNames("tabular-data-select__header", {
            "bg-[#F7F8FA]": isOpen,
          })}
        >
          <div className="tabular-data-select__header__selected">
            {selectedValues.length > 0 ? (
              orderBy(
                selectedValues,
                (item) => dataLevel[item?.replace(/\[\d+\]/g, "")] ?? 0,
                "desc"
              ).map((label, index) => {
                const isActive =
                  selectedItems?.findIndex(
                    (item) =>
                      item.value === label?.replace(/\[\d+\]/g, "") &&
                      item.columnKey === columnKey
                  ) !== -1;

                return (
                  <div
                    key={index}
                    className={classNames(
                      "tabular-item tabular-data-select__header__item",
                      `tabular-data-select__header__item--${LEVELS?.find((level) => level.value === dataLevel[label?.replace(/\[\d+\]/g, "")])?.id}`,
                      isActive && "tabular-data-select__header__item--active"
                    )}
                    onClick={(e) => {
                      // handleClickItem(label, isActive);
                      handleClick(label, isActive);
                      e.stopPropagation();
                    }}
                    onDoubleClick={(e) => {
                      handleDoubleClick(label);
                      e.stopPropagation();
                    }}
                  >
                    {parseTextWithReferences(label)}
                  </div>
                );
              })
            ) : (
              <div>{placeholder}</div>
            )}
          </div>
          <div
            className={classNames("tabular-data-select__header__icon", {
              "rotate-180": isOpen,
            })}
          >
            <DropdownIcon />
          </div>
        </div>
      </div>
    </Popover>
  );
};

export default DataSelect;
