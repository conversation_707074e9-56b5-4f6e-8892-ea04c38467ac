.citation-item-popover {
  .ant-popover-inner-content {
    padding: 0 !important;
  }

  .ant-typography {
    margin-bottom: 0;
  }
}

.playground-multiple-agents {
  .viewer-layout-container {
    border: none;
    .viewer-layout-toolbar {
      background-color: rgb(249 249 249 / var(--tw-bg-opacity));

      .viewer-toolbar {
        .viewer-toolbar-left {
          flex-grow: 1;
        }
        .viewer-toolbar-right {
          .viewer-toolbar-item:nth-last-child(-n + 4) {
            display: none;
          }
        }
      }

      .viewer-button {
        padding: 6px;
        .viewer-icon {
          width: 12px;
          height: 12px;
        }
      }
      .viewer-toolbar-current-page-input {
        height: 24px;
        width: 40px;
      }
      .viewer-toolbar-item {
        font-size: 12px;
      }
    }

    .viewer-layout-main {
      overflow: auto;
      &::-webkit-scrollbar-track {
        box-shadow: 0;
      }

      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 8px;
        background-color: #e5e5e5;
        border: solid 2px transparent;
        background-clip: content-box;

        &:hover {
          background-clip: border-box;
        }
      }
    }
  }
}
