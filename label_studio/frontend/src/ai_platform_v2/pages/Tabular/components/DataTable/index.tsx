import { Typography } from "antd";
import React, { memo, useCallback } from "react";
import IconKeyTable from "../../assets/IconKeyTable";
import IconValueTable from "../../assets/IconValueTable";
import useTabularDictionaryStore from "../../tabularDictionaryStore";
import { ITabularCategoryDetail } from "../../types";
import DataSelect from "./DataSelect";
import DataText from "./DataText";
import "./styles.scss";

const { Text } = Typography;

interface IProps {
  data: ITabularCategoryDetail[];
  handleChangeDataTable: (key: string, value: string) => void;
  disabled?: boolean;
  handleChangeLevelDataTable?: (
    columnKey: string,
    label: string,
    value: number
  ) => void;
}

const DataTable = ({
  data,
  handleChangeDataTable,
  disabled = false,
  handleChangeLevelDataTable = () => {},
}: IProps) => {
  const { dictionaryData } = useTabularDictionaryStore((state) => ({
    dictionaryData: state.dictionaryData,
  }));

  const renderTableHeader = useCallback(() => {
    return (
      <>
        <div className="tabular-data-table__header border-r-[2px] border-[#0000000D]">
          <IconKeyTable />
          <div className="tabular-data-table__header__item">Key</div>
        </div>
        <div className="tabular-data-table__header">
          <IconValueTable />
          <div className="tabular-data-table__header__item">Values</div>
        </div>
      </>
    );
  }, []);

  const renderTableColumn = useCallback(
    (columnData: ITabularCategoryDetail) => {
      const value = columnData.value_Quoted ?? columnData.value;

      try {
        const citationDataset = columnData?.rawData
          ? JSON.parse(
              columnData.rawData
                .replace(/\n/g, "\\n")
                .replace(/\r/g, "\\r")
                .replace(/\t/g, "\\t")
            )
          : [];

        return (
          <React.Fragment key={columnData.tabularCategoryDetailId}>
            <div className="tabular-data-table__column border-t-[2px] border-r-[2px] border-[#0000000D] py-[10px] px-[12px]">
              <Text
                ellipsis={{ tooltip: true }}
                style={{ color: "#346", fontSize: "14px", lineHeight: "21px" }}
              >
                {columnData.key}
              </Text>
            </div>
            <div className="tabular-data-table__column  border-t-[2px] border-[#0000000D]">
              {columnData?.dataType === "DropDown" ? (
                <DataSelect
                  options={dictionaryData?.[columnData.key]?.map((item) => ({
                    value: item,
                    label: item,
                  }))}
                  value={value ? JSON.parse(value) : []}
                  onChange={(value) => {
                    handleChangeDataTable(
                      columnData.key,
                      JSON.stringify(value)
                    );
                  }}
                  disabled={disabled}
                  citationDataset={citationDataset}
                  columnKey={columnData.key}
                  dataLevel={
                    columnData?.value_Quoted_Importance
                      ? JSON.parse(columnData.value_Quoted_Importance)
                      : {}
                  }
                  handleChangeLevelDataTable={handleChangeLevelDataTable}
                />
              ) : (
                <DataText
                  value={value ? JSON.parse(value) : ""}
                  onChange={(value) => {
                    handleChangeDataTable(
                      columnData.key,
                      JSON.stringify(value)
                    );
                  }}
                  disabled={disabled}
                  citationDataset={citationDataset}
                />
              )}
            </div>
          </React.Fragment>
        );
      } catch (error) {
        console.log("columnData", columnData, error);
      }
    },
    [dictionaryData, disabled]
  );

  return (
    <div className="tabular-data-table">
      {renderTableHeader()}
      {data?.map((columnData) => renderTableColumn(columnData))}
    </div>
  );
};

export default memo(DataTable);
