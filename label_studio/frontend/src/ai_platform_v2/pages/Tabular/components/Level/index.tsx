import { memo, useCallback } from "react";
import { LEVELS } from "../../constants";
import useTabularLevelStore from "../../tabularLevelStore";

interface ILevel {
  handleChangeLevelDataTable: (
    columnKey: string,
    label: string,
    value: number
  ) => void;
}

const Level = ({ handleChangeLevelDataTable }: ILevel) => {
  const { selectedItems, resetSelectedItems } = useTabularLevelStore(
    (state) => ({
      selectedItems: state.selectedItems,
      resetSelectedItems: state.resetSelectedItems,
    })
  );

  const handleClick = useCallback(
    (value: number) => {
      if (selectedItems.length) {
        selectedItems.forEach((item) => {
          handleChangeLevelDataTable(item.columnKey, item.value, value);
        });
        resetSelectedItems();
      }
    },
    [selectedItems]
  );

  return (
    <div className="w-full mt-[8px] mb-[16px] flex flex-col items-center gap-[16px]">
      <div className="w-full flex items-center justify-center gap-[20px]">
        {LEVELS.map((level) => (
          <div
            key={level.value}
            className={`tabular-item flex px-3 py-1 rounded-full cursor-pointer shadow-sm text-[12px] leading-[18px] select-none hover:shadow-[#00000040]`}
            style={{ backgroundColor: level.bgColor, color: level.color }}
            onClick={() => handleClick(level.value)}
          >
            {level.label}
          </div>
        ))}
      </div>
      <span className="text-[12px] leading-[18px] text-[#334466]">
        Click on the values sequentially in order or choose level above to
        select decreasing levels of importance
      </span>
    </div>
  );
};

export default memo(Level);
