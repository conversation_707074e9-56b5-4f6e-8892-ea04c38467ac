import IconProcessingExport from "@/ai_platform_v2/assets/Icons/IconProcessingExport";
import Message from "@/ai_platform_v2/component/Message/Message";
import { getCurrentProject } from "@/pages/DataSet/Const";
import { useAPI } from "@/providers/ApiProvider";
import { memo, useState } from "react";
import IconExport from "../../assets/IconExport";

function getFileName(disposition: any) {
  let filename = "";

  if (disposition && disposition.indexOf("attachment") !== -1) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
    const matches = filenameRegex.exec(disposition);

    if (matches !== null && matches[1]) {
      filename = matches[1].replace(/['"]/g, "");
    }
  }
  return filename;
}

const downloadFile = (blob: any, filename: string) => {
  const link = document.createElement("a");

  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
};

const ExportButton = () => {
  const projectId = getCurrentProject();

  const [isLoading, setLoading] = useState(false);

  const api = useAPI();

  const handleExport = async () => {
    try {
      if (isLoading) return;
      setLoading(true);

      const res = await api.callApi("tabularCategoryExportRaw", {
        params: {
          projectId,
        },
      });

      if (res.ok) {
        const blob = await res.blob();

        const headers = new Map(Array.from(res.headers));
        const fileName = getFileName(headers.get("content-disposition"));

        downloadFile(blob, fileName);
      }
    } catch (error) {
      Message.error({ content: "Something went wrong" });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div
      className="flex gap-[6px] items-center px-[8px] py-[4px] rounded-[8px] cursor-pointer hover:bg-[#EDEFF2]"
      onClick={handleExport}
    >
      {isLoading ? <IconProcessingExport /> : <IconExport />}
      <span className="text-[14px] font-medium leading-[21px] text-[#346]">
        {`Export${isLoading ? "ing" : ""}`}
      </span>
    </div>
  );
};

export default memo(ExportButton);
