import classNames from "classnames";
import { memo } from "react";
import { LIST_STATUS } from "../../constants";

interface IProps {
  currentStatus?: string;
  onChangeStatus: (status: string) => void;
  disabled?: boolean;
}

const StatusDropdown = ({
  currentStatus = LIST_STATUS[0].value,
  onChangeStatus,
  disabled = false,
}: IProps) => {
  if (disabled) {
    return (
      <div
        className={classNames(
          "flex items-center rounded-full cursor-pointer px-[12px] py-[4px] transition-all min-w-[60px]"
        )}
        style={{
          background: LIST_STATUS?.find(
            (status) => status.value === currentStatus
          )?.style.backgroundColor,
        }}
      >
        <span
          className="text-[12px] leading-[18px]"
          style={{
            color: LIST_STATUS?.find((status) => status.value === currentStatus)
              ?.style.color,
          }}
        >
          {currentStatus}
        </span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-[8px]">
      {LIST_STATUS.map((status) => (
        <div
          key={status.value}
          className={classNames(
            "flex items-center rounded-full cursor-pointer px-[12px] py-[4px] transition-all border min-w-[60px]"
          )}
          style={{
            background: status.style.backgroundColor,
            borderColor:
              currentStatus === status.value
                ? status.style.color
                : "transparent",
          }}
          onClick={() => onChangeStatus(status.value)}
        >
          <span
            className="text-[12px] leading-[18px]"
            style={{
              color: status.style.color,
            }}
          >
            {status.label}
          </span>
        </div>
      ))}
    </div>
  );
};

export default memo(StatusDropdown);
