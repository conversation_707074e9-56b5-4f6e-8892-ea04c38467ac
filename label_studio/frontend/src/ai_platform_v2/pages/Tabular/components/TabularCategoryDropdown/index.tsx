import Message from "@/ai_platform_v2/component/Message/Message";
import { PAGE_SIZE } from "@/consts/page";
import { getCurrentProject } from "@/pages/DataSet/Const";
import { useAPI } from "@/providers/ApiProvider";
import classNames from "classnames";
import { debounce } from "lodash";
import {
  createRef,
  memo,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import OutsideClickHandler from "react-outside-click-handler";
import IconChevronUp from "../../assets/IconChevronUp";
import { ITabularCategory } from "../../types";
import FilterStatus from "./FilterStatus";

interface IProps {
  currentTabularCategory?: ITabularCategory;
  setCurrentTabularCategory: (tabularCategory: ITabularCategory) => void;
  disabled?: boolean;
}

const TabularCategoryDropdown = ({
  currentTabularCategory,
  setCurrentTabularCategory,
  disabled = false,
}: IProps) => {
  const api = useAPI();
  const projectId = getCurrentProject();

  const [isOpenDropdownOptions, toggleDropdownOptions] = useState(false);
  const [isOpenDropdownFilter, toggleDropdownFilter] = useState(false);

  const [searchTabularCategoryName, setSearchTabularCategoryName] =
    useState("");
  const [searchValue, setSearchValue] = useState("");

  const [isLoading, setLoading] = useState(false);

  const [tabularCategories, setTabularCategories] = useState<
    ITabularCategory[]
  >([]);

  const [currentFilterStatus, setCurrentFilterStatus] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);

  const [pageSize] = useState(PAGE_SIZE.XL);

  const isInit = useRef(true);

  const fetchTabularCategories = async (
    page = currentPage,
    keyword = searchTabularCategoryName,
    status = currentFilterStatus
  ) => {
    try {
      if (isLoading) return;
      setLoading(true);

      const res = await api.callApi("tabularCategories", {
        params: {
          projectId,
          page,
          pageSize,
          keyword,
          status,
        },
      });

      if (res.success) {
        if (isInit?.current) {
          setTabularCategories(res.items);

          setTotalPages(Math.ceil(res.total / pageSize));
        } else {
          setTabularCategories([...tabularCategories, ...res.items]);
        }

        setCurrentPage(page);
        setSearchTabularCategoryName(keyword);
        setCurrentFilterStatus(status);
      } else {
        Message.error({ content: "Something went wrong" });
      }
    } catch (error) {
      Message.error({ content: "Something went wrong" });
    } finally {
      setLoading(false);
      isInit.current = false;
    }
  };

  const debounceSearch = useCallback(
    debounce((value: string) => {
      isInit.current = true;

      setCurrentPage(1);
      setSearchTabularCategoryName(value);
    }, 300),
    []
  );

  const onChangeSearchValue = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    setSearchValue(value);
    debounceSearch(value.trim());
  };

  const inpRef = createRef<HTMLInputElement>();

  useEffect(() => {
    if (isOpenDropdownOptions) {
      fetchTabularCategories();
    }
  }, [
    isOpenDropdownOptions,
    searchTabularCategoryName,
    currentFilterStatus,
    projectId,
  ]);

  const handleEndReached = () => {
    fetchTabularCategories(currentPage + 1);
  };

  const debounceEndReached = useCallback(
    debounce(() => {
      handleEndReached?.();
    }, 600),
    [handleEndReached]
  );

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop;
    const scrollHeight = e.currentTarget.scrollHeight;
    const clientHeight = e.currentTarget.clientHeight;

    if (
      scrollTop + clientHeight >= scrollHeight - 20 &&
      currentPage < totalPages
    ) {
      debounceEndReached();
    }
  };

  const handleToggleDropdownFilter = useCallback((value) => {
    toggleDropdownFilter(value);
    // if (value) {
    //   toggleDropdownOptions(false);
    //   onChangeSearchValue({ target: { value: "" } });
    // }
  }, []);

  const handleChangeFilterStatus = (value: string) => {
    isInit.current = true;

    setCurrentPage(1);
    setCurrentFilterStatus(value);
  };

  return (
    <div className="object-dropdown-wrapper relative w-full">
      {disabled ? (
        <div
          className={classNames(
            "flex items-center px-[20px] py-[10px] rounded-full border bg-[#EDEFF2] gap-[8px] cursor-pointer border-[#E1E5ED]"
          )}
        >
          <div className="flex w-full items-center gap-2">
            <span className="text-[14px] leading-[21px] text-[#346]">
              {currentTabularCategory?.categoryName}
            </span>
            {currentTabularCategory?.isCommon && (
              <div className="flex items-center px-2 rounded-full text-[12px] text-[#3361FF] leading-[18px] bg-white">
                common
              </div>
            )}
          </div>
        </div>
      ) : (
        <OutsideClickHandler
          onOutsideClick={() => {
            toggleDropdownOptions(false);
            onChangeSearchValue({ target: { value: "" } });
          }}
        >
          <div
            className={classNames(
              "flex items-center px-[20px] py-[10px] rounded-full border bg-[#EDEFF2] gap-[8px] cursor-pointer",
              {
                "border-[#2B7CFF]": isOpenDropdownOptions,
                "border-[#E1E5ED]": !isOpenDropdownOptions,
              }
            )}
            onClick={() => {
              if (isOpenDropdownOptions) {
                onChangeSearchValue({ target: { value: "" } });
              }
              toggleDropdownOptions(!isOpenDropdownOptions);
            }}
          >
            {isOpenDropdownOptions ? (
              <input
                ref={inpRef}
                className="w-full text-[14px] leading-[21px] text-[#346] bg-transparent outline-none border-none"
                value={searchValue}
                onChange={onChangeSearchValue}
                autoFocus
                onClick={(e) => e.stopPropagation()}
              />
            ) : (
              <div className="flex w-full items-center gap-2">
                <span className="text-[14px] leading-[21px] text-[#346]">
                  {currentTabularCategory?.categoryName}
                </span>
                {currentTabularCategory?.isCommon && (
                  <div className="flex items-center px-2 rounded-full text-[12px] text-[#3361FF] leading-[18px] bg-white">
                    common
                  </div>
                )}
              </div>
            )}
            <div className="flex items-center">
              <FilterStatus
                currentFilterStatus={currentFilterStatus}
                onChangeFilterStatus={handleChangeFilterStatus}
                isOpenDropdownFilter={isOpenDropdownFilter}
                toggleDropdownFilter={handleToggleDropdownFilter}
                className="mr-[20px]"
              />
              <div
                className={classNames("flex items-center min-w-[20px]", {
                  "rotate-180": !isOpenDropdownOptions,
                })}
                onClick={() => {
                  if (isOpenDropdownOptions) {
                    onChangeSearchValue({ target: { value: "" } });
                  }
                  toggleDropdownOptions(!isOpenDropdownOptions);
                }}
              >
                <IconChevronUp
                  color={isOpenDropdownOptions ? "#2B7CFF" : "#667085"}
                />
              </div>
            </div>
          </div>
          {isOpenDropdownOptions && (
            <div
              onScroll={handleScroll}
              className="z-10 absolute top-[calc(100%+8px)] left-0 w-full bg-white border border-[#0000000D] rounded-[12px] overflow-auto shadow-lg max-h-[350px] p-[8px] flex flex-col gap-[4px]"
            >
              {tabularCategories?.length > 0 ? (
                tabularCategories.map((object) => {
                  const isActive =
                    currentTabularCategory?.tabularCategoryId ===
                    object.tabularCategoryId;

                  return (
                    <div
                      key={object.tabularCategoryId}
                      className={classNames(
                        "px-[4px] py-[6px] cursor-pointer hover:bg-[#F5F6F7] rounded-[6px] flex items-center text-[14px] leading-[21px] gap-2",
                        {
                          "bg-[#ECF1FA] text-[#3361FF]": isActive,
                          "text-[#334466]": !isActive,
                        }
                      )}
                      onClick={() => {
                        setCurrentTabularCategory(object);
                        toggleDropdownOptions(false);
                        onChangeSearchValue({ target: { value: "" } });
                      }}
                    >
                      {object.categoryName}
                      {object.isCommon && (
                        <div
                          className={classNames(
                            "flex items-center px-2 rounded-full text-[12px] text-[#3361FF] leading-[18px]",
                            isActive ? "bg-white" : "bg-[#ECF1FA]"
                          )}
                        >
                          common
                        </div>
                      )}
                    </div>
                  );
                })
              ) : (
                <div className="px-[4px] py-[6px] text-[12px] leading-[18px] text-[#6b7a99] text-center">
                  No results found
                </div>
              )}
            </div>
          )}
        </OutsideClickHandler>
      )}
    </div>
  );
};

export default memo(TabularCategoryDropdown);
