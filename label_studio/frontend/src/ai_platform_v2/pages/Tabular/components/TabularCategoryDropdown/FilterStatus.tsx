import classNames from "classnames";
import { memo } from "react";
import Outside<PERSON><PERSON><PERSON>and<PERSON> from "react-outside-click-handler";
import { LIST_STATUS } from "../../constants";

interface IProps {
  currentFilterStatus: string;
  onChangeFilterStatus: (status: string) => void;
  isOpenDropdownFilter: boolean;
  toggleDropdownFilter: (isOpen: boolean) => void;
  className?: string;
}

const FilterStatus = ({
  currentFilterStatus,
  onChangeFilterStatus,
  isOpenDropdownFilter,
  toggleDropdownFilter,
  className,
}: IProps) => {
  return (
    <OutsideClickHandler onOutsideClick={() => toggleDropdownFilter(false)}>
      <div className={classNames("flex items-center relative", className)}>
        <div
          className="flex items-center"
          onClick={(e) => {
            toggleDropdownFilter(!isOpenDropdownFilter);
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <div
            className={classNames("flex w-[1px] h-[13px] mr-[8px]", {
              "bg-[#3361FF]": isOpenDropdownFilter || currentFilterStatus,
              "bg-[#667085]": !isOpenDropdownFilter && !currentFilterStatus,
            })}
          />
          <span
            className={classNames("text-[12px] leading-[18px]", {
              "text-[#3361FF]": isOpenDropdownFilter || currentFilterStatus,
              "text-[#667085]": !isOpenDropdownFilter && !currentFilterStatus,
            })}
          >
            {currentFilterStatus ? currentFilterStatus : "Status"}
          </span>
        </div>
        {isOpenDropdownFilter && (
          <div className="z-20 absolute top-[calc(100%+8px)] right-0 w-[103px] bg-white border border-[#0000000D] rounded-[12px] shadow-lg p-[8px] flex flex-col gap-[4px]">
            {LIST_STATUS.map((status) => {
              const isActive = status.value === currentFilterStatus;

              return (
                <div
                  key={status.value}
                  className={classNames(
                    "px-[4px] py-[6px] cursor-pointer hover:bg-[#F5F6F7] rounded-[6px] flex items-center text-[12px] leading-[18px]",
                    {
                      "bg-[#ECF1FA] text-[#3361FF]": isActive,
                      "text-[#334466]": !isActive,
                    }
                  )}
                  onClick={(e) => {
                    onChangeFilterStatus(isActive ? "" : status.value);
                    toggleDropdownFilter(false);
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                >
                  {status.label}
                </div>
              );
            })}
          </div>
        )}
      </div>
    </OutsideClickHandler>
  );
};

export default memo(FilterStatus);
