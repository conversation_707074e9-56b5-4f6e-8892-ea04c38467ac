import { Spin } from "antd";
import classNames from "classnames";
import { useCallback, useEffect, useRef, useState } from "react";
import IconStars04 from "../../assets/IconStars04";
import "./styles.scss";

interface IProps {
  description?: string;
  onChangeDescription?: (value: string) => void;
  onGenerateDescription?: () => Promise<void>;
  disabled?: boolean;
  hasDescription?: boolean;
}

const MAX_HEIGHT = 400;

const GeneratedDescription = ({
  description,
  onChangeDescription,
  onGenerateDescription,
  disabled = false,
  hasDescription = false,
}: IProps) => {
  const [loading, setLoading] = useState(false);
  const [isFocused, setFocused] = useState(false);

  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  const adjustHeight = () => {
    textAreaRef.current!.style.height = "auto";
    textAreaRef.current!.style.height = `${textAreaRef.current!.scrollHeight > MAX_HEIGHT ? MAX_HEIGHT : textAreaRef.current!.scrollHeight}px`;
  };

  const handleChange = useCallback(
    (e) => {
      const value = e.target.value;

      onChangeDescription?.(value);
    },
    [onChangeDescription]
  );

  const handleGenerate = useCallback(async () => {
    if (loading) {
      return;
    }

    setLoading(true);
    await onGenerateDescription?.();
    setLoading(false);
  }, [loading, onGenerateDescription]);

  useEffect(() => {
    if (!textAreaRef.current) {
      return;
    }
    adjustHeight();
  }, [description]);

  if (disabled) {
    return description ? (
      <div className="flex w-full py-[12px] px-[14px] whitespace-pre-wrap rounded-2xl border border-[#EAEAEA] bg-[#F2F3F5] shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] text-[#346] text-[14px] font-normal leading-[21px]">
        {description}
      </div>
    ) : null;
  }

  return (
    <div className="flex flex-col gap-3">
      <div className="flex px-2">
        <div
          className={classNames(
            "flex cursor-pointer h-[30px] py-[3px] pl-[10px] pr-[5px] gap-[5px] items-center rounded-[99px] border border-[#2B7CFF] bg-white shadow-sm hover:bg-[#ECF1FA] duration-300",
            loading && "!bg-[#ECF1FA]"
          )}
          onClick={handleGenerate}
        >
          <div className="flex text-[12px] text-[#2B7CFF] font-medium leading-[18px]">
            Generate description
          </div>
          {loading ? <Spin size="small" /> : <IconStars04 />}
        </div>
      </div>

      {description || hasDescription || isFocused ? (
        <div className="relative flex h-full w-full">
          {loading && (
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-10 flex p-3 items-center rounded-2xl bg-[#ECF1FA]">
              <IconStars04 size={21} />
            </div>
          )}
          <textarea
            ref={textAreaRef}
            className={classNames(
              "flex min-h-[200px] max-h-[400px] w-full py-[12px] px-[14px] rounded-2xl border border-[#EAEAEA] bg-[#F2F3F5] shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] duration-300",
              "text-[#346] text-[14px] font-normal leading-[21px]",
              "resize-none outline-none focus:border-[#2B7CFF]",
              loading && "opacity-40"
            )}
            value={description}
            onChange={handleChange}
            maxLength={4096}
            onFocus={() => setFocused(true)}
            onBlur={() => setFocused(false)}
          />
        </div>
      ) : null}
    </div>
  );
};

export default GeneratedDescription;
