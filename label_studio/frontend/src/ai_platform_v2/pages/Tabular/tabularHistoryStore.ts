import { create } from "zustand";

export interface IStore {
  loading: boolean;
  setLoading: (loading: boolean) => void;
  selectedHistory: any;
  setSelectedHistory: (selectedHistory: any) => void;
}

const useTabularHistoryStore = create<IStore, [["zustand/persist", unknown]]>(
  (set) => ({
    loading: false,
    setLoading: (loading: boolean) => set({ loading }),
    selectedHistory: undefined,
    setSelectedHistory: (selectedHistory: any) => set({ selectedHistory }),
  })
);

export default useTabularHistoryStore;
