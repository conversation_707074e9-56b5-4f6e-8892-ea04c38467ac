import Message from "@/ai_platform_v2/component/Message/Message";
import { Modal } from "@/ai_platform_v2/component/Modal/Modal";
import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { getCurrentModule, getCurrentProject } from "@/pages/DataSet/Const";
import { useAPI } from "@/providers/ApiProvider";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { useProject } from "@/providers/ProjectProvider";
import { Button, ModalConfirmBig } from "@taureau/ui";
import {
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useRef,
  useState,
} from "react";
import { ProjectType } from "../Home/utils/const";
import useAdvancedConfigStore from "../SettingsProject/component/AdvancedAnnotation/store";
import { CompareVersion } from "./CompareVersion/CompareVersion";
import { CreateExport, CreateExportRef } from "./CreateExport/CreateExport";
import "./ExportData.scss";
import {
  ACTIONS,
  ExportDataReducer,
  INITIAL_PAGE_SIZE,
  initialState,
  MOD_SCREEN,
  ZIP_FILE_STATUS,
} from "./ExportDataReducer";
import { ExportEmpty } from "./ExportEmpty/ExportEmpty";
import { ExportList } from "./ExportList/ExportList";
import { clearMLToolValidator } from "./helpers";

const VALID_TOOLS_TO_SYNC_CLEAR_ML = [
  "RectangleLabels",
  "BrushLabels",
  "PolygonLabels",
];

function getFileName(disposition: any) {
  let filename = "";

  if (disposition && disposition.indexOf("attachment") !== -1) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
    const matches = filenameRegex.exec(disposition);

    if (matches !== null && matches[1]) {
      filename = matches[1].replace(/['"]/g, "");
    }
  }
  return filename;
}

const downloadFile = (blob: any, filename: string) => {
  const link = document.createElement("a");

  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
};

export const nameContent = (name: string) => {
  const indexOfFirst = name.indexOf("/");
  const indexOfSecond = name.indexOf("/", indexOfFirst + 1);

  return name.slice(indexOfSecond + 1);
};

interface ExportDataProps {
  open: boolean;
  onOpen?: () => void;
  onClose: () => void;
}

export const ExportData = (props: ExportDataProps) => {
  const { open, onClose } = props;
  const api = useAPI();

  const moduleID = getCurrentModule();
  const projectID = getCurrentProject();

  const { hasPermissionAllScope } = useCheckPermission();
  const canTrain = hasPermissionAllScope(
    ABILITY_NEW.can_train,
    moduleID,
    projectID
  );

  const { advancedConfig } = useAdvancedConfigStore((state) => {
    return {
      advancedConfig: state.advancedConfig,
    };
  });
  const { projectDetail } = useProject();

  const isActivatedTraining = useMemo(() => {
    if (projectDetail === undefined) return undefined;

    return projectDetail?.dataTrainingStatus === "Completed";
  }, [projectDetail]);

  const [exportState, dispatch] = useReducer(ExportDataReducer, initialState);

  const {
    downloadExportListWatching,
    toolDownload,

    currentPage,
    currentTotal,
    compareInfo,
    exportVersions,
    currentMod,
    exportListSelected,
    exportListDownload,
  } = exportState;

  const [loadingData, setLoadingData] = useState(false);

  const isSelected = useMemo(
    () => !!exportListSelected?.length,
    [exportListSelected]
  );

  const contentDownloadOrCreate = useMemo(
    () =>
      isSelected && projectDetail?.projectType !== ProjectType.NonAnnotation
        ? "Download"
        : projectDetail?.projectType === ProjectType.NonAnnotation
          ? "Create Dataset"
          : "Create Version",
    [isSelected, projectDetail]
  );

  const handleSelect = useCallback((value) => {
    dispatch({
      type: ACTIONS.SET_EXPORT_LIST_SELECTED,
      payload: value,
    });
  }, []);

  const currentProjectId = useMemo(
    () => getCurrentProject(),
    [getCurrentProject()]
  );

  const loadNextData = useCallback((page, pageSize) => {
    dispatch({
      type: ACTIONS.LOAD_NEXT_PAGE,
      payload: page,
    });
  }, []);

  const fetchConfigTools = useCallback(async () => {
    if (!currentProjectId) {
      return;
    }

    const res = await api.callApi("fetchAllTools", {
      params: { id: currentProjectId },
    });

    if (res?.success) {
      dispatch({
        type: ACTIONS.LOAD_TOOL_DOWNLOAD,
        payload: res.data,
      });
    }
  }, [currentProjectId]);

  const fetchExportData = useCallback(
    async (useLoading = true) => {
      useLoading && setLoadingData(true);
      const res = await api.callApi("exports", {
        params: {
          pk: currentProjectId,
          page: currentPage,
          pageSize: INITIAL_PAGE_SIZE,
          orderBy: "asc",
          sortBy: "date",
        },
        // body: { task_upload_id: taskUploadId },
      });

      useLoading && setLoadingData(false);

      if (res?.success) {
        dispatch({
          type: ACTIONS.LOAD_EXPORT_LIST,
          payload: { data: res.items, total: res.total },
        });
      } else {
        // Not found
        if (res?.errorCode === "ExportVersionControllerMS.NotFound") {
          dispatch({
            type: ACTIONS.LOAD_EXPORT_LIST,
            payload: { data: [], total: 0 },
          });
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [currentProjectId, currentPage]
  );

  const fetchCompareInfo = useCallback(async () => {
    const res = await api.callApi("compareInfo", {
      params: {
        pk: currentProjectId,
      },
    });

    if (res?.success) {
      dispatch({
        type: ACTIONS.LOAD_COMPARE_INFO,
        payload: res.data,
      });
    }
  }, [currentProjectId]);

  const handleDownload = useCallback(
    async (exportList) => {
      dispatch({
        type: ACTIONS.LOAD_EXPORT_DOWNLOAD_LIST,
        payload: exportList.map((exportItem: any) => exportItem.fileId),
      });

      await exportList?.forEach(async (exportItem: any) => {
        const revisionId = exportItem.revisionId;
        const fileId = exportItem.fileId;
        const dataType = exportItem.dataType;
        const convertData = exportItem?.convertData;

        if (convertData === "COCO") {
          window.open(
            `${window.APP_SETTINGS.hostname}/api/projects/${currentProjectId}/export_version/convert_to_coco?revisionId=${revisionId}`,
            "_blank",
            "noopener,noreferrer"
          );
        } else if (convertData === "YOLO") {
          window.open(
            `${window.APP_SETTINGS.hostname}/api/projects/${currentProjectId}/export_version/convert_to_yolo?revisionId=${revisionId}`,
            "_blank",
            "noopener,noreferrer"
          );
        } else if (convertData === "clearml") {
          window.open(
            "http://************:8080/datasets",
            "_blank",
            "noopener,noreferrer"
          );
        } else {
          const allTools = toolDownload?.filter(
            (tool: string) => tool !== "General"
          );

          // const response = await api.callApi("downloadExportRaw", {
          const response = await api.callApi("downloadExportConvertorRaw", {
            params: {
              pk: currentProjectId,
              fileId,
              exportType: dataType,
              convert_data: allTools.length > 0 ? allTools : "default",
              advancedConfigId: advancedConfig?.id,
            },
          });

          if (response?.ok) {
            const blob = await response.blob();

            const headers = new Map(Array.from(response.headers));
            const fileName = getFileName(headers.get("content-disposition"));

            downloadFile(blob, fileName);
          } else {
            if (response?.status === 500) {
              // api.handleError(response);
            } else {
              if (response) {
                const responseText = await response?.text();

                const responseJson = JSON.parse(responseText);

                if (!responseJson?.success) {
                  Message.error({ content: responseJson?.message });
                }
              } else {
                Message.error({
                  content:
                    'Please select "Include annotated data" while creating version!',
                });
              }
            }
          }
        }

        dispatch({
          type: ACTIONS.REMOVE_EXPORT_DOWNLOAD,
          payload: fileId,
        });
      });
    },
    [toolDownload, currentProjectId, exportListDownload, advancedConfig]
  );

  const handleDelete = useCallback(
    async (exportList) => {
      const countExportItem = exportList?.length;

      ModalConfirmBig.warning({
        title:
          countExportItem > 1
            ? `Delete ${countExportItem} selected versions?`
            : `Delete ${nameContent(exportList?.[0]?.name)}?`,
        content: `If continue, ${
          countExportItem > 1 ? "these versions" : "this version"
        } will be permanently removed from this project.`,
        okText: "Delete",
        cancelText: "Cancel",
        onOk: async () => {
          const res = await api.callApi("deleteExportMultiple", {
            params: {
              pk: currentProjectId,
              ids: exportList?.map((item: any) => item?.revisionId),
            },
          });

          if (res?.success) {
            // handle reload page
            const calculateTotalPage = Math.ceil(
              (currentTotal - countExportItem) / INITIAL_PAGE_SIZE
            );
            const totalPage = calculateTotalPage === 0 ? 1 : calculateTotalPage;

            const currentPageFinal =
              currentPage > totalPage ? totalPage : currentPage;

            currentPage === currentPageFinal
              ? fetchExportData()
              : dispatch({
                  type: ACTIONS.SET_CURRENT_PAGE,
                  payload: currentPageFinal,
                });

            dispatch({
              type: ACTIONS.SET_EXPORT_LIST_SELECTED,
              payload: exportListSelected?.filter(
                (exportItem: any) =>
                  !exportList?.find(
                    (item: any) => exportItem?.revisionId === item?.revisionId
                  )
              ),
            });

            Message.success({
              // title: "Invalid or unknown formats",
              content:
                countExportItem > 1
                  ? `${countExportItem} versions deleted.`
                  : `${nameContent(exportList?.[0]?.name)} deleted`,
            });
          }
        },
      });
    },
    [exportListSelected, currentPage, currentTotal, currentProjectId]
  );

  const handleConvertToCocoFormat = useCallback(
    async (exportVersion) => {
      const { dataType, fileId, revisionId, name } = exportVersion;

      const res = await api.callApi("convertToCoco", {
        params: {
          pk: currentProjectId,
        },
        body: { exportType: dataType, fileId, revisionId },
      });

      if (res?.success) {
        dispatch({
          type: ACTIONS.ADD_WATCHING_CONVERT_COCO,
          payload: revisionId,
        });
        fetchExportData(false);
      } else {
        Message.error({
          content: `${nameContent(name)} conversion error to COCO format`,
        });
      }
    },
    [currentProjectId, fetchExportData]
  );

  const handleConvertToYoloFormat = useCallback(
    async (exportVersion) => {
      const { dataType, fileId, revisionId, name } = exportVersion;

      const res = await api.callApi("convertToYolo", {
        params: {
          pk: currentProjectId,
        },
        body: { exportType: dataType, fileId, revisionId },
      });

      if (res?.success) {
        dispatch({
          type: ACTIONS.ADD_WATCHING_CONVERT_YOLO,
          payload: revisionId,
        });
        fetchExportData(false);
      } else {
        Message.error({
          content: `${nameContent(name)} conversion error to YOLO format`,
        });
      }
    },
    [currentProjectId, fetchExportData]
  );

  const handleSynchronize = useCallback(async () => {
    exportListSelected.forEach(async (exportVersion: any) => {
      const {
        revisionId,
        name,
        imageAmount,
        isDataTraining,
        dataTrainingStatus,
        toolLabeling,
      } = exportVersion;

      if (isDataTraining || dataTrainingStatus) {
        return;
      }

      if (toolLabeling) {
        if (!clearMLToolValidator(toolLabeling, VALID_TOOLS_TO_SYNC_CLEAR_ML)) {
          Message.error({ content: "Data type has not been supported" });
          return;
        }
      }

      if (imageAmount === 0) {
        Message.error({
          content: `${nameContent(
            name
          )} synchronization error to AI Experiment`,
        });
        return;
      }

      const res = await api.callApi("synchronizeToClearML", {
        params: {
          pk: currentProjectId,
        },
        body: { revisionId },
      });

      if (res?.success) {
        handleSelect([]);
        dispatch({
          type: ACTIONS.ADD_WATCHING_SYNCHRONIZE,
          payload: revisionId,
        });
        fetchExportData(false);
      } else {
        Message.error({
          content: `${nameContent(
            name
          )} synchronization error to AI Experiment`,
        });
      }
    });
  }, [exportListSelected, currentProjectId, fetchExportData]);

  const handleCompressZip = useCallback(
    async (revisionId) => {
      const res = await api.callApi("compressExportZip", {
        params: {
          pk: currentProjectId,
        },
        body: {
          revisionId,
        },
      });

      if (res?.success) {
        dispatch({
          type: ACTIONS.ADD_WATCHING_COMPRESS_ZIP,
          payload: revisionId,
        });
        await fetchExportData(false);
      } else {
        Message.error({
          content: "Compress data failed.",
        });
      }
    },
    [currentProjectId, fetchExportData]
  );

  const handleDownloadZip = useCallback(
    async (revisionId) => {
      window.open(
        `${window.APP_SETTINGS.hostname}/api/projects/${currentProjectId}/export_version/zip_file?revisionId=${revisionId}`,
        "_blank",
        "noopener,noreferrer"
      );

      // dispatch({
      //   type: ACTIONS.ADD_WATCHING_DOWNLOAD_ZIP,
      //   payload: revisionId,
      // });
      // const res = await api.callApi("downloadExportZipRaw", {
      //   params: {
      //     pk: currentProjectId,
      //     revisionId,
      //   },
      // });
      // dispatch({
      //   type: ACTIONS.REMOVE_WATCHING_DOWNLOAD_ZIP,
      //   payload: revisionId,
      // });
      // if (res?.ok) {
      //   const blob = await res.blob();
      //   const headers = new Map(Array.from(res.headers));
      //   const fileName = getFileName(headers.get("content-disposition"));
      //   downloadFile(blob, fileName);
      // } else {
      //   Message.error({
      //     content: "Download data failed.",
      //   });
      // }
    },
    [currentProjectId]
  );

  const handleDownloadOrCreate = useCallback(() => {
    if (
      isSelected &&
      projectDetail?.projectType !== ProjectType.NonAnnotation
    ) {
      handleDownload?.(exportListSelected);
    } else {
      // handle open create a new export version screen
      dispatch({ type: ACTIONS.UPDATE_MOD, payload: MOD_SCREEN.create });
    }
  }, [isSelected, exportListSelected, handleDownload]);

  const isEmpty = useMemo(
    () => !loadingData && !exportVersions?.length,
    [loadingData, exportVersions]
  );

  const loadingDownloadOrCreate = useMemo(() => {
    if (isSelected) {
      return !!exportListDownload?.length;
    }

    return false;
  }, [isSelected, exportListDownload]);

  const modalTitle = useMemo(() => {
    switch (currentMod) {
      case MOD_SCREEN.list:
        return projectDetail?.projectType !== ProjectType.NonAnnotation
          ? "Export Data"
          : "Export Dataset";
      case MOD_SCREEN.create:
        return projectDetail?.projectType !== ProjectType.NonAnnotation
          ? "Create Version"
          : "Create Dataset";
      case MOD_SCREEN.compare:
        return "Compare Version";
    }
  }, [currentMod, projectDetail?.projectType]);

  const createExportRef = useRef<CreateExportRef>(null);

  const handleClose = () => {
    const isExportChanged = createExportRef?.current?.isChanged;

    const _close = () => {
      dispatch({ type: ACTIONS.UPDATE_MOD, payload: MOD_SCREEN.list });
      dispatch({ type: ACTIONS.RESET });
      onClose();
    };

    if (isExportChanged) {
      ModalConfirmBig.warning({
        title: "Wanna leave?",
        content: "If you continue, changes you made may not be saved",
        okText: "Confirm",
        cancelText: "Cancel",
        onOk: () => {
          _close();
        },
      });
    } else {
      _close();
    }
  };

  let body = <></>;

  switch (currentMod) {
    case MOD_SCREEN.list: {
      body = isEmpty ? (
        <ExportEmpty
          onClickAction={() =>
            dispatch({ type: ACTIONS.UPDATE_MOD, payload: MOD_SCREEN.create })
          }
        />
      ) : (
        <>
          <Elem name="content">
            <ExportList
              toolList={toolDownload}
              loading={loadingData}
              items={exportVersions}
              itemsSelected={exportListSelected}
              itemsDownload={exportListDownload}
              currentPage={currentPage}
              pageSize={INITIAL_PAGE_SIZE}
              totalItems={currentTotal}
              loadNextData={loadNextData}
              onSelect={handleSelect}
              onDownload={handleDownload}
              onDelete={handleDelete}
              onSelectConvertData={(revisionId: string, convertData: any) =>
                dispatch({
                  type: ACTIONS.UPDATE_CONVERT_DATA,
                  payload: {
                    revisionId,
                    convertData,
                  },
                })
              }
              downloadingZip={downloadExportListWatching}
              onCompressZip={handleCompressZip}
              onDownloadZip={handleDownloadZip}
              onConvertToCOCO={handleConvertToCocoFormat}
              onConvertToYOLO={handleConvertToYoloFormat}
              onSynchronize={handleSynchronize}
            />
          </Elem>
          <Elem name="footer">
            <Button
              className={`text-[#CC1414] h-[30px] w-[130px] px-[15px] py-[10px] text-[14px] font-[500] leading-[30px] export-button ${
                !isSelected ? "button-export-disabled" : ""
              }`}
              size="sm"
              corner="Rounded"
              theme="Light"
              disabled={!isSelected}
              onClick={() => handleDelete(exportListSelected)}
            >
              Delete
            </Button>

            {projectDetail?.projectType !== ProjectType.NonAnnotation && (
              <Button
                className={`text-[#6B7A99] h-[30px] w-[130px] px-[15px] py-[10px] text-[14px] font-[500] leading-[30px] export-button ${
                  exportListSelected?.length !== 2
                    ? "button-export-disabled"
                    : ""
                }`}
                size="sm"
                corner="Rounded"
                theme="Light"
                disabled={exportListSelected?.length !== 2}
                onClick={() =>
                  dispatch({
                    type: ACTIONS.UPDATE_MOD,
                    payload: MOD_SCREEN.compare,
                  })
                }
              >
                Compare
              </Button>
            )}

            <Button
              // className={`${disabledUploadButton ? "button-disabled" : ""}`}
              className={`h-[30px] w-fit px-[15px] py-[5px] text-[14px] font-[500] leading-[30px]`}
              loading={loadingDownloadOrCreate}
              size="sm"
              corner="Rounded"
              theme="Primary"
              onClick={handleDownloadOrCreate}
            >
              {contentDownloadOrCreate}
            </Button>
          </Elem>
        </>
      );
      break;
    }

    case MOD_SCREEN.create: {
      body = (
        <CreateExport
          ref={createExportRef}
          onCreateVersionExportSuccess={async (id) => {
            // re-fetch compare info
            await fetchCompareInfo();
            //add id to watching list
            dispatch({
              type: ACTIONS.ADD_WATCHING_EXPORT,
              payload: id,
            });
          }}
          onCancel={() => {
            fetchExportData();
            dispatch({ type: ACTIONS.UPDATE_MOD, payload: MOD_SCREEN.list });
          }}
        />
      );
      break;
    }

    case MOD_SCREEN.compare: {
      body = (
        <CompareVersion
          ref={createExportRef}
          projectId={currentProjectId}
          firstVersion={exportListSelected?.[0]}
          secondVersion={exportListSelected?.[1]}
          attributes={compareInfo?.attributes}
          onCancel={() =>
            dispatch({ type: ACTIONS.UPDATE_MOD, payload: MOD_SCREEN.list })
          }
        />
      );
      break;
    }
  }

  useEffect(async () => {
    if (open) {
      await fetchCompareInfo();
      await fetchConfigTools();
    }
  }, [open]);

  useEffect(async () => {
    if (open) {
      await fetchExportData();
    }
  }, [currentPage, open]);

  useEffect(() => {
    let timer: any = null;

    if (
      open &&
      exportVersions?.filter(
        (item: any) =>
          item.status !== "Done" ||
          item.zipFileStatus === ZIP_FILE_STATUS.inprogress ||
          item.zipFileStatus === ZIP_FILE_STATUS.new ||
          (!item.urlConverter &&
            (item.converterStatus === "Processing" ||
              item.converterStatus === "Queued")) ||
          (!item.urlYoloConverter &&
            (item.yoloStatus === "Processing" ||
              item.yoloStatus === "Queued")) ||
          item.dataTrainingStatus === "Processing" ||
          item.dataTrainingStatus === "Queued" ||
          item.dataTrainingStatus === -1
      ).length !== 0
    ) {
      timer = setTimeout(() => fetchExportData(false), 5000);
    }
    return () => {
      clearTimeout(timer);
    };
  }, [exportVersions, open]);

  return (
    <Modal
      className="modal-export-data"
      title={modalTitle}
      centered
      open={open}
      onCancel={handleClose}
      footer={null}
      width={1200}
    >
      <Block name="export-data">{body}</Block>
    </Modal>
  );
};
