import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import "./ExportItem.styl";
import { Dropdown } from "../component/Dropdown/Dropdown";
import IconDeleteOutline from "@/ai_platform_v2/assets/Icons/IconDeleteOutline";
import { useCallback, useMemo } from "react";
import { nameContent } from "../ExportData";
import { DownloadExport } from "../component/DownloadExportVer2/DownloadExport";
import IconProcessingExport from "@/ai_platform_v2/assets/Icons/IconProcessingExport";
import { Typography } from "antd";
import { DownloadDataset } from "../component/DownloadDataset/DownloadDataset";
import {
  TOOLS_ACCESS_CONVERT_TO_YOLO,
  ZIP_FILE_STATUS,
} from "../ExportDataReducer";
import IconEllipsis from "@/ai_platform_v2/assets/Icons/IconEllipsis";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import {
  getCurrentModule,
  getCurrentProject,
} from "../../SettingsProject/SettingsProject";
import { TypingIcon } from "../assets/Icons/TypingIcon";
import CSVIcon from "../assets/Icons/CSVIcon";
import JsonIcon from "../assets/Icons/JsonIcon";
import CalendarTodayIcon from "../assets/Icons/CalendarTodayIcon";
import ImageIcon from "../assets/Icons/ImageIcon";
import {
  convertUTCToLocalTime,
  DATE_TIME_FORMAT,
} from "@/ai_platform_v2/utils/dateTime";
import { useProject } from "@/providers/ProjectProvider";
import { ProjectType } from "../../Home/utils/const";
import IconZip from "@/ai_platform_v2/assets/Icons/IconZip";
import FileIcon from "../assets/Icons/FileIcon";

const { Text } = Typography;

const ClearMLIcon = ({ size = 25 }: any) => (
  <svg
    width={size}
    height={size}
    viewBox="0 0 25 26"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.3126 16.4622C16.263 17.5183 14.8097 18.1674 13.2046 18.1674C11.5994 18.1674 10.1461 17.5183 9.09321 16.4654L10.0072 15.5514C9.19011 14.7344 8.68304 13.604 8.68304 12.3541C8.68304 9.8576 10.708 7.83262 13.2046 7.83262C14.4544 7.83262 15.5848 8.33966 16.4019 9.15675L17.3159 8.24277L18.2299 7.3288L19.1406 6.41804C17.6227 4.89687 15.5234 3.95703 13.2046 3.95703C8.56679 3.95703 4.80747 7.71634 4.80747 12.3541H3.51562C3.51562 17.7056 7.85303 22.043 13.2046 22.043V20.7512C15.5234 20.7512 17.6227 19.8113 19.1406 18.2902L18.2266 17.3762L17.3126 16.4622Z"
      fill="white"
    />
    <path
      d="M13.2045 20.7511C8.56678 20.7511 4.80748 16.9918 4.80748 12.354H3.51562C3.51562 17.7055 7.85303 22.0429 13.2045 22.0429V20.7511Z"
      fill="#82DD22"
    />
    <path
      d="M13.2047 19.4592C9.28067 19.4592 6.09947 16.278 6.09947 12.354H4.80762C4.80762 16.9918 8.56692 20.7511 13.2047 20.7511L13.2047 19.4592Z"
      fill="#AFE61E"
    />
    <path
      d="M19.1408 6.41802C17.6228 4.89686 15.5236 3.95703 13.2047 3.95703C8.56692 3.95703 4.80762 7.71633 4.80762 12.3541H6.09947C6.09947 8.43009 9.28067 5.24889 13.2047 5.24889C15.1683 5.24889 16.9446 6.04338 18.23 7.32878L19.1408 6.41802Z"
      fill="#14AA8C"
    />
    <path
      d="M9.09348 16.4656C8.04062 15.4127 7.39146 13.9594 7.39146 12.3542C7.39146 9.14397 9.99455 6.54088 13.2048 6.54088C14.8099 6.54088 16.2633 7.19004 17.3161 8.2429L18.2301 7.32891C16.9447 6.04352 15.1684 5.24902 13.2048 5.24902C9.2808 5.24902 6.09961 8.43022 6.09961 12.3542C6.09961 14.3179 6.8941 16.0942 8.1795 17.3796L9.09348 16.4656Z"
      fill="#50BEFF"
    />
    <path
      d="M10.0076 15.5512C9.19051 14.7341 8.68346 13.6038 8.68346 12.3539C8.68346 9.85737 10.7084 7.83238 13.205 7.83238C14.4548 7.83238 15.5852 8.33944 16.4023 9.15654L17.3163 8.24255C16.2634 7.18968 14.8101 6.54053 13.205 6.54053C9.99469 6.54053 7.3916 9.14362 7.3916 12.3539C7.3916 13.959 8.04076 15.4123 9.09362 16.4652L10.0076 15.5512Z"
      fill="#0096FF"
    />
    <path
      d="M19.1411 18.2904L18.2271 17.3764L17.3131 16.4624C16.2635 17.5185 14.8101 18.1677 13.205 18.1677C11.5999 18.1677 10.1465 17.5185 9.09368 16.4656L8.17969 17.3796C9.46509 18.665 11.2414 19.4595 13.205 19.4595V20.7514C15.5239 20.7514 17.6232 19.8115 19.1411 18.2904Z"
      fill="#14AA8C"
    />
    <path
      d="M10.876 12.8517V12.8387C10.876 11.4823 11.9191 10.4165 13.3305 10.4165C14.2832 10.4165 14.8937 10.817 15.307 11.3886L14.3349 12.1411C14.0701 11.8085 13.7633 11.5953 13.3176 11.5953C12.6652 11.5953 12.2066 12.1476 12.2066 12.8258V12.8387C12.2066 13.5363 12.6652 14.0821 13.3176 14.0821C13.802 14.0821 14.0895 13.8561 14.3704 13.517L15.3426 14.2081C14.9033 14.8153 14.3091 15.261 13.2788 15.261C11.9482 15.261 10.876 14.2436 10.876 12.8517Z"
      fill="#334466"
    />
  </svg>
);

const ClearMLTagIcon = () => (
  <svg
    width="8"
    height="10"
    viewBox="0 0 8 10"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.86571 6.72285C6.34338 7.24839 5.62017 7.57142 4.82143 7.57142C4.02268 7.57142 3.29946 7.24839 2.77553 6.72447L3.23035 6.26964C2.82375 5.86304 2.57142 5.30053 2.57142 4.67857C2.57142 3.43626 3.57911 2.42858 4.82143 2.42858C5.44339 2.42858 6.00588 2.6809 6.41249 3.0875L6.86732 2.63268L7.32213 2.17787L7.77535 1.72465C7.01999 0.967687 5.97535 0.5 4.82143 0.5C2.51357 0.5 0.642852 2.37072 0.642852 4.67857H0C0 7.34161 2.15839 9.5 4.82143 9.5V8.85714C5.97535 8.85714 7.01999 8.38946 7.77535 7.6325L7.32053 7.17768L6.86571 6.72285Z"
      fill="white"
    />
    <path
      d="M4.82142 8.85728C2.51357 8.85728 0.642856 6.98657 0.642856 4.67871H0C0 7.34174 2.15839 9.50013 4.82142 9.50013V8.85728Z"
      fill="#82DD22"
    />
    <path
      d="M4.82114 8.21442C2.86847 8.21442 1.28543 6.63139 1.28543 4.67871H0.642578C0.642578 6.98657 2.51329 8.85728 4.82115 8.85728L4.82114 8.21442Z"
      fill="#AFE61E"
    />
    <path
      d="M7.77507 1.72464C7.01971 0.967677 5.97507 0.5 4.82114 0.5C2.51329 0.5 0.642578 2.37071 0.642578 4.67857H1.28543C1.28543 2.72589 2.86847 1.14286 4.82114 1.14286C5.79828 1.14286 6.68221 1.53822 7.32185 2.17786L7.77507 1.72464Z"
      fill="#14AA8C"
    />
    <path
      d="M2.77595 6.72418C2.25202 6.20025 1.92899 5.47704 1.92899 4.67829C1.92899 3.08079 3.22434 1.78544 4.82184 1.78544C5.62059 1.78544 6.3438 2.10847 6.86773 2.6324L7.32255 2.17758C6.68291 1.53794 5.79898 1.14258 4.82184 1.14258C2.86917 1.14258 1.28613 2.72561 1.28613 4.67829C1.28613 5.65543 1.68149 6.53936 2.32113 7.179L2.77595 6.72418Z"
      fill="#50BEFF"
    />
    <path
      d="M3.2305 6.27006C2.82389 5.86345 2.57157 5.30095 2.57157 4.67899C2.57157 3.43667 3.57924 2.42899 4.82156 2.42899C5.44353 2.42899 6.00603 2.68131 6.41263 3.08792L6.86746 2.6331C6.34353 2.10917 5.62031 1.78613 4.82156 1.78613C3.22407 1.78613 1.92871 3.08149 1.92871 4.67899C1.92871 5.47773 2.25175 6.20095 2.77567 6.72488L3.2305 6.27006Z"
      fill="#0096FF"
    />
    <path
      d="M7.77495 7.6323L7.32013 7.17748L6.86531 6.72266C6.34299 7.24819 5.61977 7.57123 4.82102 7.57123C4.02228 7.57123 3.29906 7.24819 2.77513 6.72426L2.32031 7.17908C2.95996 7.81873 3.84388 8.21408 4.82102 8.21408V8.85694C5.97495 8.85694 7.01959 8.38926 7.77495 7.6323Z"
      fill="#14AA8C"
    />
    <path
      d="M3.66309 4.92565V4.91922C3.66309 4.24422 4.18219 3.71387 4.88451 3.71387C5.35862 3.71387 5.66237 3.91315 5.86808 4.19762L5.38433 4.57208C5.25255 4.40655 5.09987 4.30047 4.87809 4.30047C4.55344 4.30047 4.32523 4.5753 4.32523 4.91279V4.91922C4.32523 5.26637 4.55344 5.53797 4.87809 5.53797C5.11916 5.53797 5.26219 5.42547 5.40201 5.25672L5.88576 5.60065C5.66719 5.90279 5.37148 6.12458 4.8588 6.12458C4.19666 6.12458 3.66309 5.61833 3.66309 4.92565Z"
      fill="#334466"
    />
  </svg>
);

const initConvertFormatTypeItems = [
  {
    label: "Convert to",
    format: "COCO",
    value: "coco",
    disabled: false,
  },
  {
    label: "Convert to",
    format: "YOLO",
    value: "yolo",
    disabled: false,
  },
];

interface ExportDataProps {
  checked?: boolean;
  loadingDownloadZip?: boolean;
  loadingDownload?: boolean;
  item?: any;
  toolList?: any;
  onSelect?: (checked: boolean) => void;
  onDelete?: () => void;
  onDownload?: () => void;
  onSelectConvertData?: (revisionId: string, convertData: any) => void;
  onCompressZip?: (revisionId: string) => void;
  onDownloadZip?: (revisionId: string) => void;
  onConvertToCOCO?: (exportVersion: any) => void;
  onConvertToYOLO?: (exportVersion: any) => void;
  onSynchronize?: (exportVersion: any) => void;
}

export const ExportItem = (props: ExportDataProps) => {
  const {
    checked,
    loadingDownloadZip,
    loadingDownload,
    item,
    toolList = [],
    onSelect,
    onDelete,
    onDownload,
    onSelectConvertData,
    onCompressZip,
    onDownloadZip,
    onConvertToCOCO,
    onConvertToYOLO,
    onSynchronize,
  } = props;
  const {
    dataType,
    date,
    fileId,
    imageAmount,
    name,
    revisionId,
    status,
    convertData,
    zipFileId,
    zipFileName,
    zipFilePath,
    zipFileStatus,

    isConverter,
    urlConverter,
    converterStatus,

    isYoloConverter,
    urlYoloConverter,
    yoloStatus,

    isDataTraining,
    dataTrainingStatus,
  } = item;

  // const [value, setValue] = useState(["default"]);

  // const toolListFinal = useMemo(() => {
  //   const isGeneralValue = convertData?.includes("default");

  //   if (isGeneralValue) {
  //     return toolList?.map((tool: any) => ({
  //       label: tool,
  //       value: tool === "General" ? "default" : tool,
  //       // disabled: tool !== "General",
  //     }));
  //   } else {
  //     return toolList?.map((tool: any) => ({
  //       label: tool,
  //       value: tool === "General" ? "default" : tool,
  //       // disabled: tool === "General",
  //     }));
  //   }
  // }, [convertData, toolList]);

  const moduleID = getCurrentModule();
  const projectID = getCurrentProject();

  const { projectDetail } = useProject();

  const { hasPermissionAllScope } = useCheckPermission();
  const canTrain = hasPermissionAllScope(
    ABILITY_NEW.can_train,
    moduleID,
    projectID
  );

  const waitingConvert = useMemo(
    () =>
      !urlConverter &&
      (converterStatus === "Processing" || converterStatus === "Queued"),
    [converterStatus, urlConverter]
  );

  const waitingConvertYOLO = useMemo(
    () =>
      !urlYoloConverter &&
      (yoloStatus === "Processing" || yoloStatus === "Queued"),
    [yoloStatus, urlYoloConverter]
  );

  const formatListFinal = useMemo(() => {
    const formatList = [
      { label: `Standard ${dataType.toUpperCase()}`, value: dataType },
    ];

    if (isConverter) {
      formatList.push({ label: "COCO Format", value: "COCO" });
    }

    if (isYoloConverter) {
      formatList.push({ label: "YOLO Format", value: "YOLO" });
    }

    // if (isDataTraining) {
    //   formatList.push({ label: "ClearML", value: "clearml" });
    // }

    return formatList;
  }, [dataType, isConverter, isYoloConverter]);

  const imageAmountContent = useMemo(
    () => `${imageAmount} ${imageAmount > 1 ? "Files" : "File"}`,
    [imageAmount]
  );

  const iconDataType = useMemo(() => {
    const convertType = convertData ?? dataType;

    if (projectDetail?.projectType === ProjectType.NonAnnotation)
      return <IconZip size={35} color="#E64B17" />;
    if (convertType === 0) {
      return <div style={{ height: 72 }}></div>;
    }

    return convertType === "Csv" ? (
      <CSVIcon />
    ) : convertType === "Json" ? (
      <JsonIcon />
    ) : convertType === "COCO" ? (
      <div className="coco-format-icon">COCO</div>
    ) : convertType === "YOLO" ? (
      <div className="yolo-format-icon">YOLO</div>
    ) : (
      <div className="clearml-sync-icon">
        <ClearMLIcon size={35} />
      </div>
    );
  }, [dataType, convertData]);

  const exportDate = useMemo(() => {
    if (!date) {
      return "";
    }

    return convertUTCToLocalTime(date, DATE_TIME_FORMAT.MMMDD);
  }, [date]);

  const isProcessing = useMemo(() => {
    return status === "Queued" || status === "Processing";
  }, [status]);

  const actionStatusTag = useMemo(() => {
    return (
      <Elem name="tag-list">
        {(isYoloConverter || waitingConvertYOLO) && (
          <Elem name="tag-item" className="yolo-type">
            YOLO
            {waitingConvertYOLO && (
              <Elem name="tag-item-loading">
                converting
                <TypingIcon color="#008CCC" />
              </Elem>
            )}
          </Elem>
        )}
        {(isConverter || waitingConvert) && (
          <Elem name="tag-item" className="coco-type">
            COCO
            {waitingConvert && (
              <Elem name="tag-item-loading">
                converting
                <TypingIcon color="#CC4314" />
              </Elem>
            )}
          </Elem>
        )}
      </Elem>
    );
  }, [isConverter, isYoloConverter, waitingConvert, waitingConvertYOLO]);

  const handleDelete = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();

      onDelete?.();
    },
    [onDelete]
  );

  const handleDownload = useCallback(() => {
    !loadingDownload && onDownload?.();
  }, [loadingDownload, onDownload]);

  const handleSelect = useCallback(
    (e) => {
      e.preventDefault();
      e.stopPropagation();

      onSelect?.(!checked);
    },
    [checked, onSelect]
  );

  const handleSelectToolDownload = useCallback(
    (value) => {
      // setValue(value);
      onSelectConvertData?.(revisionId, value);
    },
    [revisionId, onSelectConvertData]
  );

  const zipFileCompressing = useMemo(() => {
    if (zipFileId) {
      if (
        zipFileStatus === ZIP_FILE_STATUS.completed ||
        zipFileStatus === ZIP_FILE_STATUS.deleted
      ) {
        return false;
      } else {
        return true;
      }
    }

    return false;
  }, [zipFileId, zipFileStatus]);

  const disabledConvertToYolo = useMemo(
    () =>
      toolList?.length > 2 ||
      !toolList?.find((tool: string) =>
        TOOLS_ACCESS_CONVERT_TO_YOLO.includes(tool)
      ),
    [toolList]
  );

  const convertFormatTypeItems = useMemo(
    () =>
      initConvertFormatTypeItems
        .filter((item) => {
          if (item.value === "coco") {
            return !isConverter;
          } else if (item.value === "yolo") {
            return !isYoloConverter;
          }

          return true;
        })
        .map((item) => {
          if (item.value === "yolo" && disabledConvertToYolo) {
            return { ...item, disabled: true };
          }

          return item;
        }),
    [isConverter, isYoloConverter, disabledConvertToYolo]
  );

  return (
    <Block
      name="export-data-item"
      className={`${checked ? "export-data-item-selected" : ""}`}
      onClick={handleSelect}
    >
      <Elem name="left">
        <Elem name="info-type">
          <Elem name="export-type">
            <Elem name="icon-export-type">{iconDataType}</Elem>
            <Elem name="export-name">
              <Text
                style={{ maxWidth: 350, padding: "0px 4px" }}
                ellipsis={{ tooltip: nameContent(name) }}
              >
                {nameContent(name)}
              </Text>
              <Elem name="info-date">
                <Elem name="created-date">
                  <CalendarTodayIcon />
                  {exportDate}
                </Elem>

                <div className="flex w-[2px] h-[2px] rounded-full bg-[#7C8AA6]" />

                <Elem name="export-quantity">
                  {!isProcessing && (
                    <>
                      {projectDetail?.projectType !==
                      ProjectType.NonAnnotation ? (
                        <ImageIcon />
                      ) : (
                        <FileIcon />
                      )}
                      {imageAmount}
                    </>
                  )}
                </Elem>
              </Elem>
            </Elem>
          </Elem>
        </Elem>
        {actionStatusTag}
      </Elem>

      <Elem name="right">
        {!isProcessing ? (
          <>
            <DownloadDataset
              style={{ marginRight: 5 }}
              downloading={loadingDownloadZip}
              compressing={zipFileCompressing}
              revisionId={revisionId}
              zipFileId={
                zipFileStatus === ZIP_FILE_STATUS.deleted
                  ? undefined
                  : zipFileId
              }
              onCompress={onCompressZip}
              onDownload={onDownloadZip}
            />
            {/* <DownloadExport
              value={convertData}
              items={toolListFinal}
              onSelect={handleSelectToolDownload}
              onDownload={handleDownload}
            /> */}
            {projectDetail?.projectType !== ProjectType.NonAnnotation && (
              <DownloadExport
                value={convertData}
                items={formatListFinal}
                onSelect={handleSelectToolDownload}
                onDownload={handleDownload}
              />
            )}

            <Elem name="delete-action" onClick={handleDelete}>
              <IconDeleteOutline size={20} color="#E62E2E" />
            </Elem>

            {(!isConverter || !isYoloConverter) &&
              projectDetail?.projectType !== ProjectType.NonAnnotation && (
                <Dropdown
                  placement="rightTop"
                  items={convertFormatTypeItems}
                  onSelect={(value) => {
                    if (value === "coco") {
                      !waitingConvert && onConvertToCOCO?.(item);
                    } else if (value === "yolo") {
                      // disabled convert to yolo
                      !waitingConvertYOLO &&
                        !disabledConvertToYolo &&
                        onConvertToYOLO?.(item);
                    }
                  }}
                >
                  <IconEllipsis />
                </Dropdown>
              )}
          </>
        ) : (
          <Elem name="processing">
            <Elem name="processing-icon">
              <IconProcessingExport />
            </Elem>
            <Elem name="processing-label">Processing</Elem>
          </Elem>
        )}
      </Elem>
    </Block>
  );
};
