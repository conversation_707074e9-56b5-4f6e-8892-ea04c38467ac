import IconArrowBack from "@/ai_platform_v2/assets/Icons/IconArrowBack";
import Message from "@/ai_platform_v2/component/Message/Message";
import useFetchProject from "@/ai_platform_v2/hooks/useFetchProject";
import {
  getWorkflowDeserialized,
  orderWorkflow,
} from "@/ai_platform_v2/pages/TaskManagers/helper";
import { useAPI } from "@/providers/ApiProvider";
import { Button, Input, ModalConfirmBig, Toggle } from "@taureau/ui";
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import type { Range } from "react-date-range";

import DatePickerRange from "@/ai_platform_v2/component/DatePickerRange";
import { Elem } from "@/ai_platform_v2/utils/bem";
import { DEFAULT_NEW_NODE_ID } from "@/ai_platform_v2/utils/consts";
import { Popover } from "antd";
import classNames from "classnames";
import { isEqual } from "lodash";
import moment from "moment";
import { twMerge } from "tailwind-merge";
import { WorkflowNodeType } from "../../CreateProject/WorkflowConfig/types";
import { getToolsFromToolConfig } from "../helpers";
import Chip from "./Chip";
import IconExpand from "./IconExpand";
import ImageSize from "./ImageSize";
import InputNumber from "./InputNumber";
import { useProject } from "@/providers/ProjectProvider";
import { ProjectType } from "../../Home/utils/const";

const FORMAT_OPTIONS = [
  { value: "Csv", label: "CSV" },
  { value: "Json", label: "JSON" },
];

const showRangeDate = (range: Range | undefined): string | undefined => {
  if (!range || !range.startDate) {
    return undefined;
  }
  const startDate = moment(range.startDate!).startOf("day");
  const endDate = moment(range.endDate!).startOf("day");

  if (startDate.isSame(endDate)) {
    return startDate.format("MMM DD, YYYY");
  }

  return `${startDate.format("MMM DD, YYYY")} to ${endDate.format("MMM DD, YYYY")}`;
};

interface Props {
  onCancel?: () => void;
  onCreateVersionExportSuccess?: (id: string) => void;
}

export interface CreateExportRef {
  isChanged: boolean;
}

export const CreateExport = forwardRef(
  (
    { onCancel, onCreateVersionExportSuccess }: Props,
    ref: React.ForwardedRef<CreateExportRef>
  ) => {
    const [format, setFormat] = useState(FORMAT_OPTIONS[0].value);
    const [versionName, setVersionName] = useState("");
    const [isCheckedAll, setCheckedAll] = useState(true);
    const [includeDiscussion, setIncludeDiscussion] = useState(true);
    const [previousIncludeDiscussion, setPreviousIncludeDiscussion] =
      useState(true);
    const [isArchived, setIsArchivedData] = useState(true);
    const [previousIsArchived, setPreviousIsArchivedData] = useState(true);

    const [quantity, setQuantity] = useState("");
    const [rangeDateFilter, setRangeDateFilter] = useState<Array<Range>>([
      { key: "selection" },
    ]);
    const [fromSize, setFromSize] = useState<any>("");
    const [toSize, setToSize] = useState<any>("");

    const [visibleCalendar, setVisibleCalendar] = React.useState(false);

    const [errorVersionName, setErrorVersionName] = useState(false);

    const [waiting, setWaitingStatus] = useState(false);

    const api = useAPI();

    const [annotatedChecked, setAnnotatedChecked] = useState<
      Record<string, boolean> | undefined
    >(undefined);

    const { data, isLoading } = useFetchProject(true);

    const { projectDetail } = useProject();

    const workflow = useMemo(
      () => getWorkflowDeserialized(data?.annotationWorkFlow),
      [data?.annotationWorkFlow]
    );

    const nodes = useMemo(() => {
      if (!workflow) return;

      return orderWorkflow(workflow);
    }, [workflow]);

    useEffect(() => {
      //init list checked
      if (Array.isArray(nodes)) {
        const listChecked: Record<string, boolean> = {};

        nodes.forEach((node) => {
          listChecked[node.id as any] = true;
        });

        setAnnotatedChecked(listChecked);
      }
    }, [nodes]);

    useEffect(() => {
      if (!annotatedChecked) {
        setCheckedAll(false);
        return;
      }

      const listChecked = Object.values(annotatedChecked);

      if (listChecked.every((item) => item)) {
        setCheckedAll(true);
      } else {
        setCheckedAll(false);
      }
    }, [annotatedChecked]);

    const onChangeValue = (e: React.ChangeEvent<HTMLInputElement>) => {
      let value = e.target.value;

      if (value === " ") {
        return;
      }

      if (value.endsWith("  ")) {
        value = value.trim();
      }

      setVersionName(value);
    };

    const onClickAll = () => {
      const value = !isCheckedAll;

      setCheckedAll(value);

      if (!value) {
        setAnnotatedChecked(() => {
          const listChecked: Record<string, boolean> = {};

          nodes?.forEach((node) => {
            listChecked[node.id as any] = false;
          });

          return listChecked;
        });
      } else {
        setAnnotatedChecked(() => {
          const listChecked: Record<string, boolean> = {};

          nodes?.forEach((node) => {
            listChecked[node.id as any] = true;
          });

          return listChecked;
        });
      }
    };

    const isChanged = useMemo(
      () =>
        format !== FORMAT_OPTIONS[0].value ||
        versionName !== "" ||
        (!isCheckedAll &&
          projectDetail?.projectType !== ProjectType.NonAnnotation) ||
        includeDiscussion !== previousIncludeDiscussion ||
        isArchived !== previousIsArchived ||
        !isEqual(rangeDateFilter, [{ key: "selection" }]) ||
        quantity ||
        fromSize ||
        toSize,
      [
        format,
        versionName,
        isCheckedAll,
        includeDiscussion,
        isArchived,
        rangeDateFilter,
        quantity,
        fromSize,
        toSize,
        previousIncludeDiscussion,
        previousIsArchived,
        projectDetail,
      ]
    );

    useImperativeHandle(ref, () => ({
      isChanged,
    }));

    const clearData = () => {
      setVersionName("");
      setCheckedAll(true);
      setIncludeDiscussion(false);
      setIsArchivedData(false);
      setAnnotatedChecked(undefined);
      setQuantity("");
      setRangeDateFilter([{ key: "selection" }]);
      setFromSize("");
      setToSize("");
    };

    const onCreateVersion = async () => {
      //validate version name
      if (!versionName) {
        setErrorVersionName(true);
        return;
      }

      if (fromSize && toSize && fromSize > toSize) {
        Message.error({ content: "Size range is invalid" });
        return;
      }

      //call api
      if (data?.id) {
        const workflowStepIds = Object.entries(annotatedChecked || {})
          .filter(([, value]) => value)
          .map(([key]) => key);

        const includeAnnotated = !!workflowStepIds?.length;

        const dateRange = rangeDateFilter?.[0];

        const fromDate =
          dateRange?.startDate instanceof Date
            ? moment(dateRange?.startDate).startOf("d")?.toISOString()
            : dateRange?.startDate;

        const toDate =
          dateRange?.endDate instanceof Date
            ? moment(dateRange?.endDate).endOf("d")?.toISOString()
            : dateRange?.endDate;

        const body: any = {
          versionName,
          exportType:
            projectDetail?.projectType !== ProjectType.NonAnnotation
              ? format
              : "Dataset",
          includeAnnotated,
          includeDiscussion,
          isArchived,
          fromDate,
          toDate,
          fromSize: !fromSize ? undefined : Math.round(fromSize),
          toSize: !toSize ? undefined : Math.round(toSize),
          quantity: !quantity ? undefined : quantity,
        };

        if (projectDetail?.projectType !== ProjectType.NonAnnotation) {
          body.toolLabeling = data?.enableAvancedAnnotation
            ? "OneK"
            : getToolsFromToolConfig(data?.labelConfig)?.join(",");
        }

        if (includeAnnotated) {
          const newNode = nodes?.find(
            (node) => node.type === WorkflowNodeType.New
          );

          body.workflowStepIds = workflowStepIds.join(",");

          //if exits newNode id, then replace id
          if (newNode?.id) {
            body.workflowStepIds = body.workflowStepIds.replace(
              newNode.id,
              DEFAULT_NEW_NODE_ID
            );
          }
        }

        if (projectDetail?.projectType === ProjectType.NonAnnotation)
          body.workflowStepIds = DEFAULT_NEW_NODE_ID;

        setWaitingStatus(true);

        const res = await api.callApi("createExport", {
          params: {
            pk: data.id,
          },
          body,
        });

        setWaitingStatus(false);

        if (res?.success) {
          clearData();
          onCreateVersionExportSuccess?.(res.data);
          onCancel?.();
        } else {
          if (res.errorCode === "ExportVersion.RevisionExists") {
            Message.error({
              content: "Version name exists.",
            });
          }
        }
      }
    };

    const handleBack = useCallback(() => {
      isChanged
        ? ModalConfirmBig.warning({
            title: "Wanna leave?",
            content: "If you continue, changes you made may not be saved",
            okText: "Confirm",
            cancelText: "Cancel",
            onOk: () => {
              clearData();
              onCancel?.();
            },
          })
        : onCancel?.();
    }, [isChanged, onCancel]);

    const onBlurVersionName = () => {
      if (!versionName) {
        setErrorVersionName(true);
      } else {
        setErrorVersionName(false);
      }
    };

    const renderCalendar = () => {
      const onDone = (ranges: Range[] | undefined) => {
        setVisibleCalendar(false);

        if (!ranges) {
          return;
        }

        setRangeDateFilter(ranges);
      };

      return (
        <DatePickerRange
          ranges={rangeDateFilter}
          onDone={onDone}
          onClickReset={() => {
            setRangeDateFilter([
              {
                key: "selection",
                startDate: undefined,
                endDate: undefined,
              },
            ]);
          }}
          enabledScroll
        />
      );
    };

    const renderDateContent = () => {
      const dateStr = showRangeDate(rangeDateFilter?.[0]);
      const active = visibleCalendar;

      return (
        <div className="flex justify-center items-center cursor-pointer gap-[4px] rounded-[5px] bg-[#E4E7F0] py-[4px] px-[8px]">
          <span className=" text-[14px] text-[#346] leading-[21px]">
            {dateStr || "Select date"}
          </span>
          <IconExpand isActive={active} />
        </div>
      );
    };

    const renderGeneral = () => {
      return (
        <div className="flex flex-col">
          <span className="text-[16px] font-semibold leading-[24px] text-[#346]">
            General
          </span>
          <span className="text-[12px] leading-[18px] text-[#6B7A99]">
            Choose a name and select format for your version
          </span>
          <div className="mt-[12px] flex flex-col gap-[8px] px-[8px]">
            <span className="text-[14px] font-medium leading-[21px] text-[#334466]">
              {projectDetail?.projectType !== ProjectType.NonAnnotation
                ? "Version name"
                : "Dataset name"}
            </span>
            <Input
              className={classNames("w-[582px] h-[45px]", {
                "border-2 border-[#CC1414]": errorVersionName,
              })}
              inputClassName={twMerge(
                classNames(
                  "w-full h-full text-[14px] text-gray-blue-grey-blue-40",
                  "placeholder:text-[#C3CAD9]",
                  {
                    "placeholder:text-[#CC1414]": errorVersionName,
                  }
                )
              )}
              placeholder="Enter name here..."
              onChange={onChangeValue}
              value={versionName}
              onBlur={onBlurVersionName}
            />
          </div>
          {projectDetail?.projectType !== ProjectType.NonAnnotation && (
            <div className="mt-[4px] p-[8px] flex flex-col gap-[8px]">
              <span className="text-[14px] font-medium leading-[21px] text-[#334466]">
                Format
              </span>

              <div className="flex gap-[12px] items-center">
                {FORMAT_OPTIONS.map((item) => {
                  const isSelected = item.value === format;

                  return (
                    <Chip
                      key={item.value}
                      label={item.label}
                      value={item.value}
                      isSelected={isSelected}
                      onClick={() => setFormat(item.value)}
                      className="min-w-[62px]"
                    />
                  );
                })}
              </div>
            </div>
          )}
        </div>
      );
    };

    const renderDataExport = () => {
      const isBlankWorkflow =
        !isLoading && (!Array.isArray(nodes) || !nodes.length);

      if (projectDetail?.projectType === ProjectType.NonAnnotation) {
        return (
          <div className="mt-[12px] flex flex-col">
            <span className="text-[16px] font-semibold leading-[24px] text-[#346]">
              Data Export
            </span>
            <span className="text-[12px] leading-[18px] text-[#6B7A99]">
              Select the following preferences to export your data
            </span>
            <div className="mt-[12px] flex flex-col gap-[8px]">
              {/* QUANTITY & ADD DATE */}
              <div className="flex items-center gap-[12px]">
                <div className="flex items-center gap-[12px] p-[8px]">
                  <span className="font-medium text-[14px] text-[#334466] leading-[21px]">
                    Quantity
                  </span>
                  <InputNumber
                    min={1}
                    value={quantity}
                    onChange={setQuantity}
                  />
                </div>

                <div className="flex items-center gap-[12px] p-[8px]">
                  <span className="font-medium text-[14px] text-[#334466] leading-[21px]">
                    Added date
                  </span>
                  <Popover
                    trigger="click"
                    placement="bottom"
                    showArrow={false}
                    open={visibleCalendar}
                    onOpenChange={setVisibleCalendar}
                    content={renderCalendar()}
                    destroyTooltipOnHide
                    overlayClassName="overflow-y-auto my-popup"
                  >
                    {renderDateContent()}
                  </Popover>
                </div>
              </div>

              {/* IMAGE SIZE */}
              <div className="flex items-center gap-[12px] p-[8px]">
                <span className="font-medium text-[14px] text-[#334466] leading-[21px]">
                  {projectDetail?.projectType !== ProjectType.NonAnnotation
                    ? "Image size"
                    : "File size"}
                </span>
                <ImageSize setFromSize={setFromSize} setToSize={setToSize} />
              </div>

              {/* ARCHIVED DATA */}
              <div className="flex items-center  font-medium text-[14px] text-[#334466] gap-[16px] p-[8px]">
                <div className="w-[268px]">Archived data</div>
                <Toggle
                  size="sm"
                  checked={isArchived}
                  onChange={(e) => {
                    setIsArchivedData(e.target.checked);
                  }}
                />
              </div>
            </div>
          </div>
        );
      }
      if (isBlankWorkflow) return null;

      return (
        <div className="mt-[12px] flex flex-col">
          <span className="text-[16px] font-semibold leading-[24px] text-[#346]">
            Data Export
          </span>
          <span className="text-[12px] leading-[18px] text-[#6B7A99]">
            Select the following preferences to export your data
          </span>
          <div className="mt-[12px] flex flex-col gap-[8px]">
            {/* QUANTITY & ADD DATE */}
            <div className="flex items-center gap-[12px]">
              <div className="flex items-center gap-[12px] p-[8px]">
                <span className="font-medium text-[14px] text-[#334466] leading-[21px]">
                  Quantity
                </span>
                <InputNumber min={1} value={quantity} onChange={setQuantity} />
              </div>

              <div className="flex items-center gap-[12px] p-[8px]">
                <span className="font-medium text-[14px] text-[#334466] leading-[21px]">
                  Added date
                </span>
                <Popover
                  trigger="click"
                  placement="bottom"
                  showArrow={false}
                  open={visibleCalendar}
                  onOpenChange={setVisibleCalendar}
                  content={renderCalendar()}
                  destroyTooltipOnHide
                  overlayClassName="overflow-y-auto my-popup"
                >
                  {renderDateContent()}
                </Popover>
              </div>
            </div>

            {/* IMAGE SIZE */}
            <div className="flex items-center gap-[12px] p-[8px]">
              <span className="font-medium text-[14px] text-[#334466] leading-[21px]">
                Image size
              </span>
              <ImageSize setFromSize={setFromSize} setToSize={setToSize} />
            </div>

            {/* DATA STATE */}
            <div className="p-[8px] flex flex-col gap-[8px]">
              <span className="text-[14px] font-medium leading-[21px] text-[#334466]">
                Data state
              </span>

              {isLoading ? (
                <div>...</div>
              ) : (
                <div className="flex gap-[12px] items-center">
                  <Chip
                    key={"all"}
                    label={"All"}
                    isSelected={isCheckedAll}
                    onClick={onClickAll}
                  />
                  {nodes?.map((node) => {
                    const checked = !!annotatedChecked?.[node.id as any];

                    return (
                      <Chip
                        key={node.id}
                        label={node.name}
                        isSelected={checked}
                        onClick={() => {
                          setAnnotatedChecked((prev) => ({
                            ...prev,
                            [node.id as any]: !checked,
                          }));
                        }}
                      />
                    );
                  })}
                </div>
              )}
            </div>

            {/* ARCHIVED DATA */}
            {!isBlankWorkflow && (
              <div className="flex items-center  font-medium text-[14px] text-[#334466] gap-[16px] p-[8px]">
                <div className="w-[268px]">Archived data</div>
                <Toggle
                  size="sm"
                  checked={isArchived}
                  onChange={(e) => {
                    setIsArchivedData(e.target.checked);
                    setPreviousIsArchivedData(e.target.checked);
                  }}
                />
              </div>
            )}

            {/* DISCUSSION HISTORY */}
            <div className="flex items-center font-medium text-[14px] text-[#334466] gap-[16px] p-[8px]">
              <div className="w-[268px] flex flex-col">
                <span>Discussion history</span>
                <span className="text-[10px] leading-[18px] font-normal">
                  Include comments for each file in your exported version
                </span>
              </div>
              <Toggle
                size="sm"
                checked={includeDiscussion}
                onChange={(e) => {
                  setIncludeDiscussion(e.target.checked);
                  setPreviousIncludeDiscussion(!e.target.checked);
                }}
              />
            </div>
          </div>
        </div>
      );
    };

    return (
      <div className="w-full h-full scrollbar-v-md">
        <div className="w-full h-[calc(100%-52px)] px-[32px] overflow-y-auto">
          {renderGeneral()}

          {renderDataExport()}
        </div>

        <Elem
          name="footer"
          // className="flex justify-between items-center bg-[#FAFBFC] border border-solid border-[#F7F8FA] px-[22px] py-[10px] h-fit w-full"
          style={{ justifyContent: "space-between" }}
        >
          <Button
            className="bg-transparent w-[30px] h-[30px] border-0"
            icon={<IconArrowBack />}
            onClick={handleBack}
          />

          <Button
            loading={waiting}
            className={`w-fit h-[30px] text-[14px] font-[500]`}
            size="sm"
            corner="Rounded"
            theme="Primary"
            onClick={onCreateVersion}
          >
            {projectDetail?.projectType === ProjectType.NonAnnotation
              ? "Create Dataset"
              : "Create Version"}
          </Button>
        </Elem>
      </div>
    );
  }
);
