import React, { memo } from "react";

import styles from "./styles.module.scss";
import IconSuccess from "../assets/IconSuccess";
import { Button } from "@taureau/ui";
import { useHistory } from "react-router";

const AccountVerified = () => {
  const history = useHistory();

  return (
    <div className={styles.card}>
      <span className={styles.title}>Account is verified!</span>
      <IconSuccess />
      <span className={styles.content}>
        You have successfully verified your account
        <br /> Now you’re all set to log into{" "}
        <span className="font-medium">allbyai.</span>
      </span>

      <Button
        theme="Primary"
        className="w-[281px] h-[32px]"
        onClick={() => history.push("/login")}
      >
        Login now
      </Button>
    </div>
  );
};

export default memo(AccountVerified);
