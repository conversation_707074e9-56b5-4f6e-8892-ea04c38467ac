import React, { memo } from "react";

import styles from "./styles.module.scss";
import { Button } from "@taureau/ui";
import { useHistory } from "react-router";
import IconLinkFail from "../assets/IconLinkFail";

const ErrorLink = () => {
  const history = useHistory();

  return (
    <div className={styles.card}>
      <span className={styles.title}>Error!</span>
      <IconLinkFail />
      <span className={styles.content}>
        Something went wrong.
        <br /> Please try logging in again.
      </span>

      <Button
        theme="Primary"
        className="w-[281px] h-[32px]"
        onClick={() => history.push("/login")}
      >
        Back to login
      </Button>
    </div>
  );
};

export default memo(ErrorLink);
