import React, { memo, useCallback, useEffect, useState } from "react";
import { But<PERSON> } from "@taureau/ui";
import { useHistory } from "react-router";
import IconArrowBackNew from "@/ai_platform_v2/assets/Icons/IconArrowBackNew";

import IconClock from "../assets/IconClock";
import styles from "./styles.module.scss";
import { useAPI } from "@/providers/ApiProvider";
import classNames from "classnames";
import Message from "@/ai_platform_v2/component/Message/Message";

interface Props {
  email?: string;
}

const ExpiredLink = (props: Props) => {
  const { email } = props;

  const history = useHistory();
  const api = useAPI();

  const goToLogin = () => history.push("/login");

  const [remainingTime, setRemainingTime] = useState(0);

  const [isLoading, setLoading] = useState(false);

  const handleResendEmail = useCallback(async () => {
    try {
      if (remainingTime || isLoading) return;

      setLoading(true);

      const res = await api.callApi("resendEmail", {
        body: {
          email,
        },
      });

      if (res?.success) {
        setRemainingTime(60);

        Message.success({
          content: "Verification link has been sent to your email",
        });
      } else {
        Message.error({
          content: "Cannot send email. Please retry!",
        });
      }
    } catch (error: any) {
      console.log("error", error?.message);
    } finally {
      setLoading(false);
    }
  }, [remainingTime, email]);

  useEffect(() => {
    let intervalCountdown: any = null;

    if (remainingTime) {
      intervalCountdown = setInterval(() => {
        const time = remainingTime - 1;

        if (!time) clearInterval(intervalCountdown);
        setRemainingTime(time);
      }, 1000);
    }
    return () => clearInterval(intervalCountdown);
  }, [remainingTime]);

  return (
    <div className={styles.card}>
      <span className={styles.title}>Expired link!</span>
      <IconClock />
      <span className={styles.content}>
        This activation link is expired or has already been used.
        <br />
        Click below to get a new valid link.
      </span>

      <Button
        theme="Primary"
        className={classNames("w-[281px] h-[32px] mb-5", {
          "border-none": remainingTime,
        })}
        onClick={handleResendEmail}
        disabled={!!remainingTime}
        loading={isLoading}
      >
        {remainingTime ? `Resend after ${remainingTime}s` : "Send new link"}
      </Button>

      <Button
        theme="Light"
        className="w-[281px] h-[32px]"
        onClick={goToLogin}
        iconLeft={<IconArrowBackNew />}
      >
        Back to log in
      </Button>
    </div>
  );
};

export default memo(ExpiredLink);
