import { useCallback, useEffect, useState } from "react";
import { useAPI } from "@/providers/ApiProvider";

import { useLocation } from "react-router";
import AccountVerified from "./components/AccountVerified";
import ExpiredLink from "./components/ExpiredLink";
import ErrorLink from "./components/ErrorLink";
import { Spin } from "antd";
import { LoadingOutlined } from "@ant-design/icons";

enum ActivateAccountView {
  LOADING = "loading",
  ACTIVATE_SUCCESS = "activate-success",
  TOKEN_EXPIRED = "token-expired",
  TOKEN_ERROR = "token-error",
}

export const ActiveAccount = () => {
  const api = useAPI();
  const location = useLocation();

  const search = new URLSearchParams(location.search);

  // Dirty: Not a standard ==> Need html encode special characters in url ex: +, @
  const email = location?.search?.split("&email=")?.[1] || "";

  const token = search.get("token");

  const [view, setView] = useState<ActivateAccountView>(
    ActivateAccountView.LOADING
  );

  const handleActiveAccount = useCallback(async () => {
    if (token) {
      try {
        const res = await api.callApi("activeAccount", {
          body: {
            token,
          },
        });

        if (res?.success) {
          setView(ActivateAccountView.ACTIVATE_SUCCESS);
        } else if (res?.data === "Expired") {
          setView(ActivateAccountView.TOKEN_EXPIRED);
        } else {
          setView(ActivateAccountView.TOKEN_ERROR);
        }
      } catch (error) {
        setView(ActivateAccountView.TOKEN_ERROR);
      }
    } else {
      setView(ActivateAccountView.TOKEN_ERROR);
    }
  }, [email, token]);

  useEffect(() => {
    handleActiveAccount();
  }, [handleActiveAccount]);

  const renderContent = useCallback(() => {
    switch (view) {
      case ActivateAccountView.LOADING:
        return (
          <div
            className="w-[350px] py-[16px] flex items-center justify-center bg-[#fff] rounded-[10px] gap-[10px]"
            style={{
              boxShadow: "0px 2px 10px 0px rgba(38, 51, 77, 0.30)",
            }}
          >
            <span className="text-[16px] text-[#346] font-medium leading-[24px]">
              Page loading
            </span>
            <Spin
              indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
            />
          </div>
        );

      case ActivateAccountView.ACTIVATE_SUCCESS:
        return <AccountVerified />;

      case ActivateAccountView.TOKEN_EXPIRED:
        return <ExpiredLink email={email} />;

      case ActivateAccountView.TOKEN_ERROR:
        return <ErrorLink />;

      default:
        break;
    }
  }, [email, view]);

  return <div className="z-10">{renderContent()}</div>;
};

ActiveAccount.path = "/active-account/public";
ActiveAccount.exact = true;
