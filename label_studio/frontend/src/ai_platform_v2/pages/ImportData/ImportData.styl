.import-data
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 30px;
    align-self: stretch;
    height: fit-content
    transition: all .6s ease-out

    &__content
        display: flex;
        padding: 0px 30px;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        align-self: stretch;
        height: fit-content
        // height: 72vh
        // min-height: 510px
        transition: all .6s ease-out

    &__header
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
        align-self: stretch;

        .toggle-items-new
            width 260px
            padding 5px
            border-radius: 10px
            &__item
                flex: 1 0 0
                border-radius: 10px
                color: var(--gray-blue-grey-blue-40, var(--Light-Grey-Blue, #7D8FB3));
                
                font-size: 14px;
                font-style: normal;
                font-weight: 300;
                line-height: 24px;
                transition: color .3s, background .3s ease-out

            &__item_active
                color: var(--gray-blue-grey-blue-70, #346);


    &__select-pre
        display: flex
        justify-content: center
        align-items: center
        height: 100%
        width: 100%

    &__body-out
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px
        width: 100%
        height: fit-content
        // height: 100%
        overflow: hidden

    &__body-advanced
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%
        height: fit-content
        // height: 100%
        overflow: hidden

    &__body
        display: flex;
        flex-direction: column;
        align-items: center;
        // gap: 10px;
        gap: 4px
        align-self: stretch;
        width: 100%
        height: fit-content
        // height: 100%
        overflow: hidden
        transition: all .6s ease-out
    
    .body-pre-processed
        align-items: flex-start
        flex-direction: row-reverse;
        gap: 12px

    &__body-detail
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
        align-self: stretch;
        width: 100%
        height: 0
        max-height: fit-content
        overflow: auto
        transition: all .6s ease-out

        &::-webkit-scrollbar {
            width: 7.5px;
            height: 7.5px;
        }

        &::-webkit-scrollbar-track {
            box-shadow: 0;
            /* border-radius: 10px; */
        }

        &::-webkit-scrollbar-thumb {
            background-color: rgb(98 112 140)
            border-radius: 10px;
        }

    .show-body-detail
        // height: 100%
        min-height: 200px
        height: 45vh
        padding: 8px 0px

    &__footer
        display: flex;
        padding: 12px 30px;
        justify-content: flex-end;
        align-items: center;
        gap: 16px;
        align-self: stretch;
        border-top: 2px solid var(--gray-blue-grey-blue-95, #EDEFF2);

    .footer-hide
        border-color: #fff
        opacity 0
        height: 0
        display: none

.taureau-pre-processed-detached
    .dropzone-description__icon-upload
        padding: 3px

.taureau-upload-detached
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    align-self: stretch;

    &__header
        display: flex;
        width: 100%
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;

        &-title, &-description
            color: var(--Gray-Blue-Grey-Blue-40, #346);
            
            font-style: normal;
            line-height: 20px;

        &-title
            font-size: 14px;
            font-weight: 500;
            
        &-description
            font-size: 13px;
            font-weight: 400;

    &__body
        display: flex
        width: 100%;
        height: 100%
        flex-direction: column
        align-items: center

    &__upload-zone
        display: flex
        width: 100%
        height: 100%
        // height: fit-content

        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 8px;

        &-description
            display: flex;
            align-items: flex-start;
            gap: 4px;

            &-icon
                display: flex

            &-content
                word-wrap: break-word
                color: var(--Gray-Blue-Grey-Blue-40, #346);
                
                font-size: 13px;
                font-style: normal;
                font-weight: 400;
                line-height: 21px;

        .dropzone
            width: 100%
            height: 100%
            min-height: 173px

            .dropzone-description__icon-upload
                padding 3px


:global(.modal-import-data)
    height: fit-content
    transition: width .6s ease-out

    :global(.ant-modal-header)
        padding 8px 10px 4px 20px
        margin-bottom: 0
        :global(.ant-modal-title)
            font-size: 18px !important
            font-weight: 500 !important

    :global(.ant-modal-close) > :global(.ant-modal-close-x)
        svg
            transition: all .3s
            fill #C3CAD9

    :global(.ant-modal-close):hover
        svg
            fill: rgba(0, 0, 0, 0.75) !important


:global(.import-tooltip)
    padding 0 !important
    :global(.ant-tooltip-arrow-content)
        background: var(--Light-Grey-Blue, #346);
        &:before
            background: var(--Light-Grey-Blue, #346);
    :global(.ant-tooltip-inner)
        color: #fff
        background: var(--Light-Grey-Blue, #346);

:global(#headlessui-portal-root)
    dialog
        z-index 9999 !important

:global(.button-disabled)
    opacity .6 !important
    cursor not-allowed !important
    color: var(--gray-blue-grey-blue-85, #C3CAD9) !important;
    border: 2px solid var(--gray-blue-grey-blue-95, #EDEFF2) !important;
    background-color: var(--gray-blue-grey-blue-95, #EDEFF2) !important;

:global(.tas-message-custom)
   :global(.alert-warning) 
        background-color: #ffeec6

:global(.taureau-minus-button)
    svg > path
        transition: all .3s

:global(.taureau-minus-button):hover
    svg > path
        fill: rgba(0, 0, 0, 0.75) !important

:global(.button-import-loading)
    width fit-content 
    pointer-events: none
    background-color: #fff !important
    border: none !important 
    animation button-loading 1.8s linear infinite
    background-image: repeating-linear-gradient(115deg, rgba(51,97,255,0.4) 24px, rgba(51,97,255,0.6) 0px, rgba(51,97,255,0.6) 36px, rgba(51,97,255,0.4) 28px, rgba(51,97,255,0.4) 46.9px) !important;

@keyframes button-loading
  0%
    background-position 0 0

  100%
    background-position 100px 0
