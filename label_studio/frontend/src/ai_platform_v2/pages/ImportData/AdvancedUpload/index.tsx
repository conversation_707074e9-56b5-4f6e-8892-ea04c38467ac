import IconChevronRightDouble from "@/ai_platform_v2/assets/Icons/IconChevronRightDouble";
import { useImportData } from "@/ai_platform_v2/providers/ImportDataProvider";
import { getCurrentProject } from "@/pages/DataSet/Const";
import { useAPI } from "@/providers/ApiProvider";
import { Select } from "antd";
import { memo, useCallback, useEffect, useRef, useState } from "react";
import { v4 as uuid } from "uuid";
import { DropzoneDes } from "../DropzoneDes/DropzoneDes";
import { ExportFailedList, FileList } from "../FileList/FileList";
import { Upload } from "../Upload/Upload";
import "./styles.scss";

interface IAdvancedUpload {
  labelFailedFiles: string;
  failedFiles: any[];
}

const AdvancedUpload = ({ failedFiles, labelFailedFiles }: IAdvancedUpload) => {
  const importData = useImportData();
  const projectId = getCurrentProject();
  const api = useAPI();

  const [listMLProject, setListMLProject] = useState<any[]>([]);
  const [selectedMLProject, setSelectedMLProject] = useState<any>(null);

  const isInit = useRef(true);

  const [hovered, setHovered] = useState<boolean>(false);

  const {
    folderTree,
    filesByTab,
    fileTree,
    importState,

    filesLength,
    filesLengthMess,
    filesSelectedLength,
    filesSelectedLengthMess,

    handleSaveAdvancedFiles,
    handleRemoveFiles,
    handleSelectFiles,
    handleRemainFolder,
    handleFolderPath,
    handleAddInvalidFiles,
    setFileVersionId,
    setMLProjectId,

    fileVersionId,
  } = importData;

  const handleChangeMLProject = useCallback(
    (value: any) => {
      setSelectedMLProject(value);
      setMLProjectId(value);

      // check has fileVersionId
      if (!fileVersionId?.[value]) {
        setFileVersionId((prev: any) => ({
          ...prev,
          [value]: uuid(),
        }));
      }
    },
    [fileVersionId]
  );

  const handleGetListMLProject = useCallback(async () => {
    try {
      const res = await api.callApi("mlDataAccessByUser", {
        params: {
          pk: projectId,
        },
      });

      if (res?.success && res?.data) {
        setListMLProject(
          res?.data?.map((item: any) => ({
            value: item.mlProjectId,
            label: item.mlProjectName,
          }))
        );

        const mlId = res?.data[0]?.mlProjectId;

        setSelectedMLProject(res?.data[0]?.mlProjectId);

        if (!fileVersionId?.[mlId]) {
          setFileVersionId((prev: any) => ({
            ...prev,
            [mlId]: uuid(),
          }));
        }

        setMLProjectId(res?.data[0]?.mlProjectId);
        isInit.current = false;
      } else {
        setMLProjectId("");
      }
    } catch (error: any) {
      console.log("error: ", error?.message);
      setMLProjectId("");
    }
  }, [projectId, fileVersionId]);

  useEffect(() => {
    if (!isInit.current) return;
    handleGetListMLProject();
  }, [fileVersionId]);

  const { currentProject, waiting } = importState;

  return (
    <div className="ls-import-data__body-advanced">
      <div className="text-[12px] text-[#6B7A99] leading-[20px] mb-[24px]">
        In advanced mode: Data is automatically added to Machine Learning
        project
      </div>
      <div className="flex items-center gap-[8px] mb-[16px]">
        <span className="text-[14px] leading-[21px] text-[#346]">
          Select Machine Learning project to add data
        </span>
        <IconChevronRightDouble color="#3361FF" />
        <Select
          value={selectedMLProject}
          options={listMLProject}
          onChange={handleChangeMLProject}
          placeholder="Select ML project"
          className="select-ml"
          popupClassName="select-ml-popup"
        />
      </div>
      <div className="ls-import-data__body">
        <Upload
          customClassName={`${filesByTab?.length ? "h-[20vh]" : "h-[34vh]"}`}
          isValidateDataTypes={false}
          multiple={true}
          loading={waiting}
          dataTypes={[]}
          onInvalid={(invalidFiles: any) =>
            handleAddInvalidFiles(
              invalidFiles.map((file: any) => ({
                fileName: file.file.name,
                imagePath: file.fullPath,
              }))
            )
          }
          onHover={(value) => setHovered(value)}
          sendFiles={async (value) => {
            handleSaveAdvancedFiles(value);
          }}
        >
          <DropzoneDes dataTypes={[]} hovered={hovered} />
        </Upload>
        <div
          className={`ls-import-data__body-detail ${filesLength ? "ls-show-body-detail" : ""}`}
        >
          <FileList
            folderTree={folderTree}
            importState={importState}
            onRemainFolder={handleRemainFolder}
            onSelectFolderPath={handleFolderPath}
            files={fileTree}
            filesLength={filesLength}
            filesLengthMess={filesLengthMess}
            filesSelectedLength={filesSelectedLength}
            filesSelectedLengthMess={filesSelectedLengthMess}
            onRemove={handleRemoveFiles}
            onSelect={handleSelectFiles}
          />
        </div>
      </div>
      {!!failedFiles.length && (
        <ExportFailedList
          label={labelFailedFiles}
          style={{ marginTop: filesByTab?.length ? 0 : 10 }}
          files={failedFiles}
          currentProject={currentProject}
        />
      )}
    </div>
  );
};

export default memo(AdvancedUpload);
