.select-ml {
  width: 120px;
  .ant-select-selector {
    border-radius: 6px !important;
    border: none !important;
    background-color: #edeff2 !important;
    box-shadow: none !important;

    .ant-select-selection-item {
      color: #62708c !important;
    }
  }
}

.select-ml-popup {
  border-radius: 12px !important;
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 8px !important;

  .ant-select-item {
    font-size: 12px !important;
    line-height: 18px !important;
    color: #346;
    border-radius: 6px !important;
    padding: 4px 6px !important;
    min-height: 26px !important;
  }
  .ant-select-item-option-content {
    align-self: center;
  }
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    color: #3361ff;
    font-weight: 400;
    background-color: #ecf1fa;
  }

  .rc-virtual-list-holder-inner {
    gap: 4px;
  }
}
