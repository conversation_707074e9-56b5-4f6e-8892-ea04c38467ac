// import { unique } from "../../../utils/helpers";

import Message from "@/ai_platform_v2/component/Message/Message";
import { isEqual } from "lodash";
import { checkFileTypeOtherZip } from "./const/validate";

export const tabList = [
  {
    label: "Raw",
    value: "raw",
  },
  {
    label: "Pre-processed",
    value: "preProcessed",
  },
  {
    label: "Advanced",
    value: "advanced",
  },
];

export const initialPause = {
  raw: false,
  preProcessed: false,
  advanced: false,
};

export const dataTypesPre = [".coco", ".csv", ".yolo"];

export const formatItem = [
  {
    key: "CSV",
    title: "CSV",
  },
  {
    key: "COCO",
    title: "COCO",
  },
  {
    key: "YOLO",
    title: "YOLO",
  },
];

export const fileStatusOptions = {
  save: "save",
  uploading: "uploading",
  paused: "paused",
  failed: "failed",
  completed: "completed",
};

export const ACTIONS = {
  LOAD_DATASET_TOTAL: "load_dataset_total",
  LOAD_CONFIG_TOOL_LIST: "load_config_tool_list",
  LOAD_META_DATA_LIST: "load_meta_data_list",

  LOAD_CURRENT_PROJECT: "load_current_project",
  LOAD_DATA_TYPES: "load_data_types",
  LOAD_FOLDERS: "load_folders",
  SET_FOLDER_PATH: "set_folder_path",
  SET_PROGRESSING: "set_progressing",
  SET_PROGRESSING_OF_TYPE: "set_progressing_of_type",

  ADD_UPLOAD_ZIP_TASKS: "add_upload_zip_task",
  REMOVE_UPLOAD_ZIP_TASKS: "remove_upload_zip_task",

  WAITING_STATUS: "waiting_status",

  SET_REMAIN: "set_remain",

  ADD_INVALID_FILES: "add_invalid_files",

  SET_PRE_PROCESSED: "set_pre_processed",

  SET_TAB: "set_tab",

  SAVE: "save",
  SAVE_PAUSED: "save_paused",
  SAVE_PAUSED_BY_TAB: "save_paused_by_tab",
  REMOVE: "remove",
  REMOVE_ALL: "remove_all",
  UPLOADING: "uploading",
  UPLOADING_ALL: "uploading_all",
  PAUSED: "paused",
  PAUSED_ALL: "paused_all",
  PAUSED_ALL_BY_TAB: "paused_all_by_tab",
  FAILED: "failed",
  COMPLETED: "completed",

  RETRY_ALL: "retry_all",

  UPDATE_PROGRESS_FILE: "update_progress_file",

  ADD_FILE_PRE_ATTACHED: "add_file_pre_attached",
  REMOVE_FILE_PRE_ATTACHED: "remove_file_pre_attached",
  SET_FILE_PRE_ATTACHED_STATUS: "set_file_pre_attached_status",
  SET_ALL_FILE_PRE_ATTACHED_STATUS: "set_all_file_pre_attached_status",
  UPDATE_ALL_FILE_PRE_ATTACHED_PROGRESS:
    "update_all_file_pre_attached_progress",
  REMOVE_ALL_FILE_PRE_ATTACHED_PROGRESS:
    "remove_all_file_pre_attached_progress",

  SELECT: "select",
  SELECT_ALL: "select_all",
  UNSELECT_ALL: "unselect_all",
  RESET: "reset",

  ADD_RAW_FILE_INPROGRESS: "add_raw_file_inprogress",
  REMOVE_RAW_FILE_INPROGRESS: "remove_raw_file_inprogress",

  CLOSE: "close",

  ACTION_WHEN_LIMIT_REQUEST: "action_when_limit_request",
  RESET_FILES: "reset_files",
};

export const initialState = {
  datasetTotal: 0,
  configToolList: [],
  metaDataList: [],

  currentProject: {},
  dataTypes: [],
  folders: [],
  folderPath: "/",
  progressing: { raw: false, preProcessed: false, advanced: false },

  uploadZipTasks: [],

  waiting: false,

  remain: false,

  invalidFiles: [],

  currentPreProcessed: null,
  currentTab: tabList[0].value,
  files: [],

  preAttached: { zipFile: undefined, formattedFile: undefined },

  rawFilesInProgress: [],

  close: false,
};

export const ImportDataReducer = (state = initialState, action: any) => {
  switch (action.type) {
    case ACTIONS.ACTION_WHEN_LIMIT_REQUEST:
      // set completed status for all files (raw and pre-processed|attached)
      // set completed status for raw
      state.files.forEach((file: any) => {
        // only raw tab
        if (file.tab === "raw") {
          if (!file?.fullPath || file.status !== fileStatusOptions.uploading) {
            // continue
          } else if (file.fullPath.toLowerCase().includes("zip")) {
            // completed for zip
            file.status = fileStatusOptions.completed;
          } else {
            file.status = fileStatusOptions.failed;
          }
        }
      });

      // set completed status for pre-processed|attached
      if (
        state.preAttached.zipFile &&
        state.preAttached.zipFile?.status === fileStatusOptions.uploading
      ) {
        state.preAttached.zipFile.status = fileStatusOptions.completed;
      }
      if (
        state.preAttached.formattedFile &&
        state.preAttached.formattedFile?.status === fileStatusOptions.uploading
      ) {
        state.preAttached.formattedFile.status = fileStatusOptions.completed;
      }

      // set progressing
      state.progressing.raw = false;
      state.progressing.preProcessed = false;

      return {
        ...state,
        // clear all zip tasks
        uploadZipTasks: [],
      };

    case ACTIONS.LOAD_DATASET_TOTAL:
      return {
        ...state,
        datasetTotal: action.payload,
      };

    case ACTIONS.LOAD_CONFIG_TOOL_LIST:
      return {
        ...state,
        configToolList: action.payload,
      };

    case ACTIONS.LOAD_META_DATA_LIST:
      return {
        ...state,
        metaDataList: action.payload,
      };

    case ACTIONS.LOAD_CURRENT_PROJECT:
      return {
        ...state,
        currentProject: action.payload,
      };

    case ACTIONS.LOAD_DATA_TYPES:
      return {
        ...state,
        dataTypes: action.payload,
      };

    case ACTIONS.LOAD_FOLDERS:
      return {
        ...state,
        folders: action.payload,
      };

    case ACTIONS.SET_FOLDER_PATH:
      return {
        ...state,
        folderPath: action.payload,
      };

    case ACTIONS.SET_PROGRESSING: {
      state.progressing[state.currentTab] = action.payload;

      return {
        ...state,
        // progressing: action.payload,
      };
    }

    case ACTIONS.SET_PROGRESSING_OF_TYPE: {
      if (action.payload.type === "raw") {
        state.progressing.raw = action.payload.value;
      } else if (action.payload.type === "preProcessed") {
        state.progressing.preProcessed = action.payload.value;
      } else {
        state.progressing.advanced = action.payload.value;
      }

      return {
        ...state,
        // progressing: action.payload,
      };
    }

    case ACTIONS.ADD_UPLOAD_ZIP_TASKS: {
      const isHasZipTask = state.uploadZipTasks.includes(action.payload);

      if (isHasZipTask) {
        return state;
      }

      return {
        ...state,
        uploadZipTasks: [...state.uploadZipTasks, action.payload],
      };
    }

    case ACTIONS.REMOVE_UPLOAD_ZIP_TASKS:
      return {
        ...state,
        uploadZipTasks: state.uploadZipTasks.filter(
          (task: any) => task !== action.payload
        ),
      };

    case ACTIONS.WAITING_STATUS:
      return {
        ...state,
        waiting: action.payload,
      };

    case ACTIONS.SET_REMAIN:
      return {
        ...state,
        remain: action.payload,
      };

    case ACTIONS.ADD_INVALID_FILES: {
      const finalInvalidFiles = action.payload.filter((file: any) => {
        const duplicateInvalidFile = state.invalidFiles.find(
          (fileCheck: any) => fileCheck.imagePath === file.imagePath
        );

        return !duplicateInvalidFile;
      });

      return {
        ...state,
        invalidFiles: [...state.invalidFiles, ...finalInvalidFiles],
      };
    }

    case ACTIONS.SET_PRE_PROCESSED:
      return {
        ...state,
        currentPreProcessed: action.payload,
      };

    case ACTIONS.SET_TAB:
      return {
        ...state,
        currentTab: action.payload,
      };

    case ACTIONS.SAVE: {
      const filesSave = action.payload.filter((file: any) => {
        const pathName = file?.fullPath ?? file.file.name;

        return !state.files.find((fileState: any) => {
          const pathNameState = fileState?.fullPath ?? fileState.file.name;

          return pathName === pathNameState;
        });
      });

      if (filesSave.length !== action.payload.length) {
        Message.warning({
          // title: "Invalid or unknown formats",
          content: "You have uploaded duplicated file. Please recheck!",
        });
      }
      return {
        ...state,
        files: [...filesSave, ...state.files],
      };
    }

    case ACTIONS.SAVE_PAUSED:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            file.status === fileStatusOptions.paused &&
            file.tab === state.currentTab &&
            checkFileTypeOtherZip(file)
          ) {
            file.status = fileStatusOptions.save;
          }
          return file;
        }),
      };

    case ACTIONS.SAVE_PAUSED_BY_TAB:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            file.status === fileStatusOptions.paused &&
            file.tab === action.payload &&
            checkFileTypeOtherZip(file)
          ) {
            file.status = fileStatusOptions.save;
          }
          return file;
        }),
      };

    case ACTIONS.REMOVE:
      return {
        ...state,
        files: state.files.filter((file: any) => {
          const findFile = !action.payload.includes(file.file);

          if (!findFile) {
            const fileType = file.file.type;

            if (fileType[fileType.search("image")]) {
              URL.revokeObjectURL(file.file);
            }
          }

          return findFile;
        }),
      };

    case ACTIONS.REMOVE_ALL:
      return {
        ...state,
        files: state.files.filter((file: any) => file.tab !== state.currentTab),
      };

    case ACTIONS.UPLOADING: {
      const payload = action.payload.map((file: any) => file.file);

      return {
        ...state,
        files: state.files.map((file: any) => {
          if (payload.includes(file.file)) {
            file.status = fileStatusOptions.uploading;
          }
          return file;
        }),
      };
    }

    case ACTIONS.UPLOADING_ALL:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            file.status === fileStatusOptions.save &&
            file.tab === state.currentTab
          ) {
            file.status = fileStatusOptions.uploading;
          }
          return file;
        }),
      };

    case ACTIONS.PAUSED:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (action.payload.includes(file)) {
            file.status = fileStatusOptions.paused;
          }
          return file;
        }),
      };

    case ACTIONS.PAUSED_ALL: {
      const rawFilesInProgress: any = state.rawFilesInProgress;

      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            !rawFilesInProgress.includes(file.fullPath) &&
            file.status === fileStatusOptions.uploading &&
            file.tab === state.currentTab &&
            checkFileTypeOtherZip(file)
          ) {
            file.status = fileStatusOptions.paused;
          }
          return file;
        }),
      };
    }

    case ACTIONS.PAUSED_ALL_BY_TAB: {
      const rawFilesInProgress: any = state.rawFilesInProgress;

      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            !rawFilesInProgress.includes(file.fullPath) &&
            file.status === fileStatusOptions.uploading &&
            file.tab === action.payload &&
            checkFileTypeOtherZip(file)
          ) {
            file.status = fileStatusOptions.paused;
          }
          return file;
        }),
      };
    }

    case ACTIONS.FAILED: {
      const filePayload = action.payload.map((file: any) => file.file);

      return {
        ...state,
        files: state.files.map((file: any) => {
          if (filePayload.includes(file.file)) {
            const findFile = action.payload.find((fileCheck: any) =>
              isEqual(fileCheck.file, file.file)
            );

            file.status = fileStatusOptions.failed;
            file.eventMLExportId = findFile.eventMLExportId;
            file.fileVersionId = findFile.fileVersionId;
          }
          return file;
        }),
      };
    }

    case ACTIONS.COMPLETED: {
      const filePayload = action.payload.map((file: any) => file.file);

      return {
        ...state,
        files: state.files.map((file: any) => {
          if (filePayload.includes(file.file)) {
            const findFile = action.payload.find((fileCheck: any) =>
              isEqual(fileCheck.file, file.file)
            );

            file.status = fileStatusOptions.completed;
            file.eventMLExportId = findFile.eventMLExportId;
            file.fileVersionId = findFile.fileVersionId;
          }
          return file;
        }),
      };
    }

    case ACTIONS.RETRY_ALL:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            file.status === fileStatusOptions.failed &&
            file.tab === state.currentTab
          ) {
            file.status = fileStatusOptions.uploading;
          }
          return file;
        }),
      };

    case ACTIONS.UPDATE_PROGRESS_FILE:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (action.payload.file.includes(file)) {
            file.progress = action.payload.progress;
          }
          return file;
        }),
      };

    case ACTIONS.ADD_FILE_PRE_ATTACHED: {
      const fileType = action.payload?.fileType;
      const file = action.payload?.file;

      if (!state.preAttached[fileType]) {
        state.preAttached[fileType] = file;
      }

      return {
        ...state,
        // preAttached,
      };
    }

    case ACTIONS.REMOVE_FILE_PRE_ATTACHED: {
      const fileType = action.payload;

      if (state.preAttached[fileType]) {
        state.preAttached[fileType] = undefined;
      }

      return {
        ...state,
        // preAttached,
      };
    }

    case ACTIONS.SET_FILE_PRE_ATTACHED_STATUS: {
      const fileType = action.payload?.fileType;
      const status = action.payload?.status;

      if (state.preAttached[fileType]) {
        state.preAttached[fileType].status = status;
      }

      return {
        ...state,
        // preAttached,
      };
    }

    case ACTIONS.SET_ALL_FILE_PRE_ATTACHED_STATUS: {
      const status = action.payload;

      if (state.preAttached.zipFile) {
        state.preAttached.zipFile.status = status;
      }

      if (state.preAttached.formattedFile) {
        state.preAttached.formattedFile.status = status;
      }

      return {
        ...state,
        // preAttached,
      };
    }

    case ACTIONS.UPDATE_ALL_FILE_PRE_ATTACHED_PROGRESS: {
      const progress = action.payload;

      if (state.preAttached.zipFile) {
        state.preAttached.zipFile.progress = progress;
      }

      if (state.preAttached.formattedFile) {
        state.preAttached.formattedFile.progress = progress;
      }

      return {
        ...state,
        // preAttached,
      };
    }

    case ACTIONS.REMOVE_ALL_FILE_PRE_ATTACHED_PROGRESS: {
      state.preAttached.zipFile = undefined;
      state.preAttached.formattedFile = undefined;

      return {
        ...state,
        // preAttached,
      };
    }

    case ACTIONS.SELECT: {
      const selectedFile = state.files.map((file: any) => {
        const findFile = action.payload.includes(file.file);

        if (findFile) {
          if (
            file.status === fileStatusOptions.save ||
            file.status === fileStatusOptions.paused ||
            file.status === fileStatusOptions.failed
          ) {
            file.checked = !file.checked;
          }
        }
        return file;
      });

      return {
        ...state,
        files: selectedFile,
      };
    }

    case ACTIONS.SELECT_ALL:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            (file.status === fileStatusOptions.save ||
              file.status === fileStatusOptions.paused ||
              file.status === fileStatusOptions.failed) &&
            file.tab === state.currentTab
          ) {
            file.checked = true;
          }
          return file;
        }),
      };

    case ACTIONS.UNSELECT_ALL:
      return {
        ...state,
        files: state.files.map((file: any) => {
          if (
            (file.status === fileStatusOptions.save ||
              file.status === fileStatusOptions.paused ||
              file.status === fileStatusOptions.failed) &&
            file.tab === state.currentTab
          ) {
            file.checked = false;
          }
          return file;
        }),
      };

    case ACTIONS.RESET: {
      state.files.forEach((file: any) => {
        const fileType = file.file.type;

        if (fileType[fileType.search("image")]) {
          URL.revokeObjectURL(file.file);
        }
      });

      state.progressing.raw = false;
      state.progressing.preProcessed = false;

      state.preAttached.zipFile = undefined;
      state.preAttached.formattedFile = undefined;

      return initialState;
    }

    case ACTIONS.ADD_RAW_FILE_INPROGRESS: {
      const fullPath = action.payload?.fullPath;

      const rawFilesInProgress: any = state.rawFilesInProgress;

      if (!fullPath || rawFilesInProgress.includes(fullPath)) {
        return { ...state };
      }

      return {
        ...state,
        rawFilesInProgress: [...rawFilesInProgress, fullPath],
      };
    }

    case ACTIONS.REMOVE_RAW_FILE_INPROGRESS: {
      const fullPath = action.payload?.fullPath;

      if (!fullPath) {
        return { ...state };
      }

      return {
        ...state,
        rawFilesInProgress: [
          ...state.rawFilesInProgress.filter((key: any) => key !== fullPath),
        ],
      };
    }

    case ACTIONS.CLOSE:
      return { ...initialState, close: true };

    case ACTIONS.RESET_FILES:
      return {
        ...state,
        files: [],
      };

    default:
      return state;
  }
};
