import { useCallback, useRef } from "react";
import { Tooltip } from "antd";
import { HomeNoData } from "@/ai_platform_v2/assets/Images/NoData";
import classNames from "classnames";
import IconChevronDown from "@/ai_platform_v2/assets/Icons/IconChevronDown";
import "./Select.styl";

interface PropsSelectListOption {
  value?: any;
  options?: any;
  onChange?: (value: any) => void;
}
export const SelectListOption = (props: PropsSelectListOption) => {
  const { value, options, onChange } = props;

  const handleChange = useCallback(
    (option: any) => {
      onChange?.(option);
    },
    [onChange]
  );

  // const checkedList = useMemo(
  //   () => options?.filter((option: any) => value?.includes(option.value)),
  //   [options, value]
  // );

  // const unCheckedList = useMemo(
  //   () => options?.filter((option: any) => !value?.includes(option.value)),
  //   [options, value]
  // );

  // const finalList = useMemo(
  //   () => [...checkedList, ...unCheckedList],
  //   [checkedList, unCheckedList]
  // );

  return (
    <div className="flex flex-col p-2 gap-1 rounded-xl">
      {options?.length ? (
        options?.map((option: any) => (
          <div
            key={option?.value}
            className={classNames(
              "text-[#346] text-[12px] font-normal leading-[18px] p-1 rounded-md hover:bg-[#F5F6F7]",
              value?.value === option?.value && "!bg-[#ECF1FA] !text-[#3361FF]"
            )}
            onClick={() => handleChange(option)}
          >
            {option?.label}
          </div>
        ))
      ) : (
        <div className="w-full h-full flex flex-col justify-center items-center bg-white p-5">
          <img
            src={HomeNoData}
            alt="search-no-data"
            width={150}
            className="mb-[20px]"
          />
          <span className="text-[17px] font-medium text-gray-blue-40 leading-[30px]">
            Empty!
          </span>
          <span className="text-[13px] font-light text-[#62708C] text-center leading-5 mb-[10px]">
            There’re no available meta data now.
          </span>
        </div>
      )}
    </div>
  );
};

interface PropsSelect {
  open?: boolean;
  overlayClassName?: string;
  placement?: any;
  placeholder?: string;
  value?: any;
  options?: any;
  onChange?: (value: any) => void;
}
export const Select = (props: PropsSelect) => {
  const {
    open = false,
    overlayClassName,
    placement = "bottom",
    value,
    options,
    onChange,
  } = props;
  const refSelect = useRef(null);

  const handleChange = useCallback(
    (value) => {
      onChange?.(value);
    },
    [onChange]
  );

  return (
    <Tooltip
      //   open={open}
      trigger="click"
      color="#fff"
      placement={placement}
      title={
        <SelectListOption
          value={value}
          options={options}
          onChange={handleChange}
        />
      }
      overlayClassName={`tooltip-hide-arrow tooltip-full-width rounded-xl border border-[rgba(0,0,0,0.05)] w-full min-w-[100px] !max-w-[200px] ${
        overlayClassName ? overlayClassName : ""
      }`}
      overlayInnerStyle={{
        overflow: "auto",
        padding: 0,
        borderRadius: "12px !important",
      }}
      getPopupContainer={(triggerNode: any) => triggerNode.parentNode}
      //   onOpenChange={(newOpen: any) => {
      //     setOpenFilter(newOpen);
      //   }}
    >
      <div
        className={classNames(
          "flex px-1 py-[2px] gap-[2px] items-center rounded-md bg-[#F5F6F7]",
          open && "taureau-import-select-expand !bg-[#EDEFF2]"
        )}
        ref={refSelect}
      >
        <div className="text-[#62708C] text-[12px] font-normal leading-5">
          {value?.label}
        </div>
        <div className="flex">
          <IconChevronDown size={14} color="#62708C" />
        </div>
      </div>
    </Tooltip>
  );
};
