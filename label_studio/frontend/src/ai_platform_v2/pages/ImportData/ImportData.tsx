import IconFormatted from "@/ai_platform_v2/assets/Icons/IconFormatted";
import IconInfo from "@/ai_platform_v2/assets/Icons/IconInfo";
import IconMinus from "@/ai_platform_v2/assets/Icons/IconMinus";
import Message from "@/ai_platform_v2/component/Message/Message";
import RouteLeavingProject from "@/ai_platform_v2/component/RouteLeavingProject";
import { ToggleItems } from "@/ai_platform_v2/component/ToggleItems/ToggleItems";
import { useImportData } from "@/ai_platform_v2/providers/ImportDataProvider";
import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { getCurrentModule, getCurrentProject } from "@/pages/DataSet/Const";
import { useAPI } from "@/providers/ApiProvider";
import { useCheckPermission } from "@/providers/PermissionProvider";
import { useProject } from "@/providers/ProjectProvider";
import { useWebSocket } from "@/providers/WebSocketProvider";
import { Button, ModalConfirmBig } from "@taureau/ui";
import { uniq } from "lodash";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useHistory } from "react-router";
import { Modal } from "../../component/Modal/Modal";
import { ProjectType } from "../Home/utils/const";
import AdvancedUpload from "./AdvancedUpload";
import {
  checkFileTypeOtherZip,
  csvParser,
  csvParserHeader,
  validateAttribute,
  validateRequiredColumnDetached,
} from "./const/validate";
import { DropzoneDes } from "./DropzoneDes/DropzoneDes";
import { FileItem } from "./FileItem/FileItem";
import { ExportFailedList, FileList } from "./FileList/FileList";
import "./ImportData.styl";
import { ACTIONS, dataTypesPre, fileStatusOptions } from "./ImportDataReducer";
import { PreProcessed } from "./PreProcessed/PreProcessed";
import { SelectPre, SelectPreButton } from "./SelectPre/SelectPre";
import { Upload } from "./Upload/Upload";
import { UploadAttached } from "./UploadAttached/UploadAttached";

interface Props {
  open?: boolean;
  onOpen?: () => void;
  onClose?: () => void;
  fetchDataSets?: () => void;
}

export const ImportData = (props: Props) => {
  const { open, onOpen, onClose, fetchDataSets } = props;
  const moduleId = getCurrentModule();
  const projectId = getCurrentProject();
  const history = useHistory();

  const [hovered, setHovered] = useState<boolean>(false);
  const { project, projectDetail, checkCanImportLimit } = useProject();

  const isConfigPreProcessing = useMemo(() => {
    if (projectDetail?.preProccessConfig === undefined) return undefined;
    if (!projectDetail?.preProccessConfig) return false;

    return !!JSON.parse(projectDetail.preProccessConfig)?.node?.length;
  }, [projectDetail.preProccessConfig]);

  const { hasPermissionAllScope } = useCheckPermission();

  const canExportVersion = hasPermissionAllScope(
    ABILITY_NEW.can_export_version,
    moduleId,
    projectId
  );

  const api = useAPI();

  const importData = useImportData();
  const { reConnectDM } = useWebSocket();

  const {
    isZipProgress,
    folderTree,
    filesByTab,
    fileTree,
    importState,
    filesSelected,
    filesCompletedLength,
    filesLength,
    filesLengthMess,
    filesSelectedLength,
    filesSelectedLengthMess,
    percentImportProgress,
    dataImportProgress,
    dispatch,
    handleUpdateImportData,
    handleSaveFiles,
    handleRemoveFiles,
    handleSelectFiles,
    handleRemainFolder,
    handleFolderPath,
    handleAddInvalidFiles,
    handleMinusImportModal,
    fetchProjectDataTypes,
    fetchFolders,
    fetchConfigTools,
    fetchMetaData,
    fetchDatasetTotal,
    onUploadFiles,
    pause,
    setPause,
    handlePause,
    handleClear: setClear,
    handleCancelUploading,
    handleCloseImport,
    handleSavePreFiles,
    handleRemovePreFiles,
    importZipCsv,

    isContinue,
    setIsContinue,

    setFuncCloseModal,

    isNonAnnotationProject,

    resetFiles,
    rejectRawUploadingProgress,

    eventMLExportId,
    handleResetAdvancedTab,
  } = importData;

  const {
    configToolList,
    metaDataList,

    currentPreProcessed,
    currentProject,
    dataTypes,
    currentTab,
    waiting,
    progressing,
    folders,
    folderPath,
    invalidFiles,
    files,
    uploadZipTasks,

    preAttached,
    rawFilesInProgress,
  } = importState;

  const [expand, setExpand] = useState(true);

  const [isHaveFiles, setIsHaveFiles] = useState(false);
  const [isRefetch, setIsRefetch] = useState(false);

  const [isUpdatingStatusExportVersion, setUpdatingStatusExportVersion] =
    useState(false);

  const hasFailedFiles = useMemo(() => {
    return filesByTab?.some(
      (file: any) => file.status === fileStatusOptions.failed
    );
  }, [filesByTab]);

  const isShowContinueBtn = useMemo(() => {
    // only show continue button when: currentTab is advanced and at least one file is upload success and all files are completed
    if (
      currentTab === "advanced" &&
      percentImportProgress === 100 &&
      filesCompletedLength &&
      !progressing[currentTab]
    ) {
      return true;
    }

    return false;
  }, [currentTab, percentImportProgress, filesCompletedLength, progressing]);

  const handleSelectTab = useCallback((value: any) => {
    dispatch({
      type: ACTIONS.SET_TAB,
      payload: value,
    });
  }, []);

  const handleUpload = useCallback(() => {
    if (pause[currentTab]) {
      return;
    }

    if (["raw", "advanced"]?.includes(currentTab)) {
      onUploadFiles();
    } else if (currentTab === "preProcessed") {
      if (currentPreProcessed === "attached") {
        importZipCsv();
      } else {
        onUploadFiles();
      }
    }
  }, [pause, currentTab, currentPreProcessed, importZipCsv, onUploadFiles]);

  const handleCloseModal = useCallback(
    (isShowConfirm = true, onRedirect = () => {}) => {
      if (
        files.filter((file: any) => {
          const fileNameSplit = file.file.name.split(".");
          const fileType =
            fileNameSplit?.[fileNameSplit?.length - 1].toLowerCase();

          return (
            (file.status === fileStatusOptions.uploading ||
              file.status === fileStatusOptions.paused) &&
            fileType === "zip"
          );
        }).length !== uploadZipTasks.length
      ) {
        return;
      }

      const zipFileAttached = preAttached.zipFile;
      const formattedFileAttached = preAttached.formattedFile;

      const zipFileAttachedCompleted =
        preAttached.zipFile?.status === fileStatusOptions.completed ||
        preAttached.zipFile?.status === fileStatusOptions.failed;

      const filesLengthAll = files?.length;
      const filesCompletedLengthAll = files?.filter(
        (file: any) =>
          file.status === fileStatusOptions.completed ||
          file.status === fileStatusOptions.failed
      ).length;

      // if (filesLengthAll > 0 || zipFileAttached || formattedFileAttached) {
      if (isHaveFiles) {
        if (
          filesLengthAll !== filesCompletedLengthAll ||
          (!zipFileAttachedCompleted && zipFileAttached !== undefined)
        ) {
          if (isShowConfirm) {
            ModalConfirmBig.warning({
              title: "Stop import progress?",
              content: `If you continue, the current import progress will be closed and stopped.`,
              onOk: () => {
                handleCancelUploading(true);
                handlePause?.(true, "raw");
                handlePause?.(true, "preProcessed");
                handlePause?.(true, "advanced");

                isRefetch && fetchDataSets?.();
                onClose?.();
                handleCloseImport();
                onRedirect?.();
                handleResetAdvancedTab();
              },
            });
          } else {
            handleCancelUploading(true);
            handlePause?.(true, "raw");
            handlePause?.(true, "preProcessed");
            handlePause?.(true, "advanced");

            isRefetch && fetchDataSets?.();
            onClose?.();
            handleCloseImport();
            onRedirect?.();
            handleResetAdvancedTab();
          }
        } else {
          isRefetch && fetchDataSets?.();
          onClose?.();
          handleCloseImport();
          onRedirect?.();
          handleResetAdvancedTab();
        }
      } else {
        onClose?.();
        handleCloseImport();
        onRedirect?.();
        handleResetAdvancedTab();
      }
    },
    [
      files,
      filesLength,
      filesCompletedLength,
      handleCloseImport,
      uploadZipTasks?.toString(),
      preAttached?.toString(),

      isRefetch,
      isHaveFiles,
    ]
  );

  const handleUpdateStatusEventMLExport = useCallback(async () => {
    try {
      const listFileUploaded = filesByTab?.filter(
        (file: any) => file.status === fileStatusOptions.completed
      );

      if (!listFileUploaded?.length || isUpdatingStatusExportVersion) return;

      setUpdatingStatusExportVersion(true);

      const listEventMLExportId = uniq(
        listFileUploaded?.map((file: any) => file.eventMLExportId)
      );

      const res = await Promise.all(
        listEventMLExportId.map((eventMLExportId: any) => {
          return api.callApi("updateStatusExport", {
            params: {
              projectId,
              eventMLExportId,
            },
          });
        })
      );

      // const res = await api.callApi("updateStatusExport", {
      //   params: {
      //     projectId,
      //     eventMLExportId,
      //   },
      // });

      if (res?.some((item) => item?.success)) {
        handleCloseModal();
        Message.success({
          content: "Export version created successfully",
        });
      }
    } catch (error) {
      Message.error({
        content: "Something went wrong!",
      });
    } finally {
      setUpdatingStatusExportVersion(false);
    }
  }, [projectId, filesByTab, handleCloseModal]);

  const onClickContinue = useCallback(() => {
    if (hasFailedFiles) {
      ModalConfirmBig.confirm({
        title: "Continue without retrying?",
        content: `We’ll ignore failed files then create new export version and add it to the target ML project.`,
        onOk: () => {
          handleUpdateStatusEventMLExport();
        },
      });
    } else {
      handleUpdateStatusEventMLExport();
    }
  }, [
    hasFailedFiles,
    projectId,
    eventMLExportId,
    filesByTab,
    handleUpdateStatusEventMLExport,
  ]);

  const handleRetry = useCallback(() => {
    onUploadFiles(true);
  }, [onUploadFiles]);

  const dataTypesFinal = useMemo(() => {
    return currentTab === "raw" ? dataTypes : dataTypesPre;
  }, [dataTypes, currentTab]);

  const uploadButtonContent = useMemo(() => {
    if (currentTab === "preProcessed" && currentPreProcessed === "attached") {
      const status = preAttached.zipFile?.status;

      if (status === fileStatusOptions.save) {
        return "Upload";
      } else if (status === fileStatusOptions.uploading) {
        const percent = preAttached.zipFile?.progress ?? 0;

        return `Uploading ${Math.ceil(percent)}%`;
      }
      return "Upload";
    }

    let countProgress = 0;
    let sumProgress = 0;
    const totalFiles = filesByTab?.length ?? 0;
    const findFilesUploadingLength = filesByTab?.filter((file: any) => {
      countProgress += file?.progress ? 1 : 0;
      sumProgress += file?.progress ?? 0;
      return (
        file.status === fileStatusOptions.save ||
        file.status === fileStatusOptions.uploading ||
        file.status === fileStatusOptions.paused
      );
    })?.length;

    const findFilesUploadingMess =
      findFilesUploadingLength > 1
        ? `${findFilesUploadingLength} files`
        : `${findFilesUploadingLength} file`;

    const percentZip =
      findFilesUploadingLength === 0
        ? 0
        : sumProgress / (countProgress === 0 ? 1 : countProgress);

    const percent =
      totalFiles === 0
        ? 0
        : Math.floor(
            ((totalFiles - findFilesUploadingLength) * 100 + percentZip) /
              totalFiles
          );

    return pause[currentTab]
      ? "Paused"
      : findFilesUploadingLength
        ? progressing[currentTab]
          ? `Uploading ${percent}%`
          : `Upload ${findFilesUploadingLength ? findFilesUploadingMess : ""}`
        : "Upload";
  }, [
    JSON.stringify(progressing),
    JSON.stringify(pause),
    filesByTab,
    currentTab,
    currentPreProcessed,
    preAttached.zipFile?.status,
    preAttached.zipFile?.progress,
  ]);

  const removeButtonContent = useMemo(() => {
    return `Delete ${
      filesSelectedLength > 1
        ? `${filesSelectedLength} files`
        : `${filesSelectedLength} file`
    }`;
  }, [filesSelectedLength]);

  const hideMinusModalButton = useMemo(() => {
    const uploadedFileLength = files?.filter(
      (file: any) =>
        file.status === fileStatusOptions.uploading ||
        file.status === fileStatusOptions.paused
    )?.length;

    const zipPreAttached = preAttached.zipFile;
    const formattedPreAttached = preAttached.formattedFile;

    const preAttachedStatus =
      zipPreAttached?.status === fileStatusOptions.uploading ||
      zipPreAttached?.status === fileStatusOptions.paused ||
      formattedPreAttached?.status === fileStatusOptions.uploading ||
      formattedPreAttached?.status === fileStatusOptions.paused;

    return !(preAttachedStatus || uploadedFileLength);
  }, [files, JSON.stringify(preAttached)]);

  const isLeaveProject = useMemo(() => {
    const uploadedFileLength = files?.filter(
      (file: any) => file.status !== fileStatusOptions.save
    )?.length;

    const zipPreAttached = preAttached.zipFile;
    const formattedPreAttached = preAttached.formattedFile;

    const preAttachedStatus =
      zipPreAttached?.status !== fileStatusOptions.save ||
      formattedPreAttached?.status !== fileStatusOptions.save;

    return !(preAttachedStatus || uploadedFileLength);
  }, [files, JSON.stringify(preAttached)]);

  const handleClickDelete = useCallback(() => {
    handleRemoveFiles(filesSelected.map((file: any) => file.file));
  }, [filesSelected]);

  const checkIsRefetch = useMemo(() => {
    const fileCompleted = files?.find(
      (file: any) => file.status === fileStatusOptions.completed
    );

    const attachedCompleted =
      preAttached.zipFile?.status === fileStatusOptions.completed;

    if ((fileCompleted || attachedCompleted) && !isRefetch) {
      setIsRefetch(true);
    }
  }, [isRefetch, files, preAttached.zipFile?.status]);

  const checkIsHaveFiles = useMemo(() => {
    if ((files?.length || preAttached.zipFile) && !isHaveFiles) {
      setIsHaveFiles(true);
    }
  }, [isHaveFiles, files, preAttached.zipFile?.toString()]);

  useEffect(() => {
    setFuncCloseModal(() => handleCloseModal);
  }, [
    files,
    filesLength,
    filesCompletedLength,
    handleCloseImport,
    uploadZipTasks?.toString(),
    preAttached?.toString(),

    isRefetch,
    isHaveFiles,
  ]);

  const failedFiles = useMemo(() => {
    const failedList = filesByTab
      .filter((file: any) => file.status === fileStatusOptions.failed)
      .map((file: any) => ({
        fileName: file.file.name,
        imagePath: file.fullPath,
        status: "Failed",
      }));

    if (["raw", "advanced"]?.includes(currentTab)) {
      const invalidList = invalidFiles.map((file: any) => ({
        ...file,
        status: "Invalid",
      }));

      return [...invalidList, ...failedList];
    }

    return failedList;
  }, [filesByTab, invalidFiles, currentTab]);

  const labelFailedFiles = useMemo(() => {
    const failedList = filesByTab
      .filter((file: any) => file.status === fileStatusOptions.failed)
      .map((file: any) => ({
        fileName: file.file.name,
        imagePath: file.fullPath,
        status: "Failed",
      }));

    return failedList.length ? "Export failed list" : "Export invalid list";
  }, [filesByTab, invalidFiles]);

  const showBody = useMemo(() => {
    if (currentTab === "advanced") {
      return false;
    }

    if (currentTab === "raw") {
      return true;
    }

    return currentPreProcessed && currentTab === "preProcessed";
  }, [currentTab, currentPreProcessed]);

  const disabledUploadButton = useMemo(() => {
    if (currentTab === "preProcessed" && currentPreProcessed === "attached") {
      if (
        preAttached.zipFile?.status === fileStatusOptions.completed ||
        preAttached.zipFile?.status === fileStatusOptions.failed
      ) {
        return true;
      }

      return !(preAttached.zipFile && preAttached.formattedFile);
    }
    return filesCompletedLength === filesLength && !pause[currentTab];
  }, [
    preAttached.zipFile?.status,
    preAttached.zipFile,
    preAttached.formattedFile,
    currentPreProcessed,
    currentTab,
    filesCompletedLength,
    filesLength,
    pause,
  ]);

  const showClearButton = useMemo(() => {
    if (currentTab === "preProcessed" && currentPreProcessed === "attached") {
      const preZipFile = preAttached.zipFile;
      const preFormattedFile = preAttached.formattedFile;

      return preZipFile || preFormattedFile
        ? preZipFile?.status !== fileStatusOptions.uploading
        : false;
    }

    return filesByTab?.length
      ? !progressing[currentTab] || pause[currentTab]
      : false;
    // return filesByTab?.length
    //   ? !progressing[currentTab] &&
    //       !filesByTab.some(
    //         (file: any) => file.status === fileStatusOptions.paused
    //       )
    //   : false;
  }, [
    JSON.stringify(preAttached),
    filesByTab,
    progressing[currentTab],
    currentTab,
    currentPreProcessed,
  ]);

  const isDisabledContinueBtn = useMemo(
    () => pause[currentTab] && rawFilesInProgress?.length,
    [pause, currentTab, rawFilesInProgress?.length]
  );

  const handleClear = useCallback(() => {
    if (currentTab === "preProcessed" && currentPreProcessed === "attached") {
      dispatch({
        type: ACTIONS.REMOVE_ALL_FILE_PRE_ATTACHED_PROGRESS,
      });
    } else {
      dispatch({
        type: ACTIONS.REMOVE_ALL,
      });
    }
    setClear(true);
    handlePause?.(false, currentPreProcessed);
  }, [currentTab, currentPreProcessed, handlePause]);

  useEffect(() => {
    if (isContinue) {
      onUploadFiles();
      setIsContinue(false);
    }
  }, [isContinue]);

  useEffect(async () => {
    if (currentProject?.id) {
      await fetchProjectDataTypes();
      await fetchFolders();
      await fetchConfigTools();
      await fetchMetaData();
      await fetchDatasetTotal();
    }
  }, [currentProject?.id, fetchProjectDataTypes]);

  useEffect(async () => {
    if (open && project && !currentProject?.id) {
      await dispatch({
        type: ACTIONS.LOAD_CURRENT_PROJECT,
        payload: project,
      });

      setIsHaveFiles(false);
      setIsRefetch(false);
    }
  }, [open, project, currentProject]);

  useEffect(() => {
    if (isConfigPreProcessing && open) {
      handleSelectTab("preProcessed");
      dispatch({
        type: ACTIONS.SET_PRE_PROCESSED,
        payload: "detached",
      });
    }
  }, [open, isConfigPreProcessing]);

  const uploadRaw = (
    <Upload
      className={`${
        filesByTab?.length || (currentTab === "preProcessed" && expand)
          ? "is-files"
          : ""
      }`}
      multiple={currentTab === "raw"}
      disabled={currentTab === "preProcessed" && !!filesByTab?.length}
      loading={waiting}
      dataTypes={dataTypesFinal}
      onInvalid={(invalidFiles: any) =>
        handleAddInvalidFiles(
          invalidFiles.map((file: any) => ({
            fileName: file.file.name,
            imagePath: file.fullPath,
          }))
        )
      }
      onHover={(value) => setHovered(value)}
      sendFiles={async (value) => {
        if (currentTab === "raw") {
          handleSaveFiles(value);
        } else {
          if (!filesByTab?.length) {
            const csvHeader = await csvParserHeader(value.file);
            const csvFile = await csvParser(value.file);

            const validateRequired = validateRequiredColumnDetached(
              csvHeader,
              csvFile
            );

            const validateViewNMeta = validateAttribute(
              csvHeader,
              configToolList?.map((tool: any) => tool.key)
            );

            if (validateRequired && validateViewNMeta) {
              handleSaveFiles([value]);
            } else {
              Message.error({
                // title: "Invalid or unknown formats",
                content: "Incorrect format.",
              });
            }
          }
        }
      }}
    >
      {currentTab === "preProcessed" ? (
        filesByTab?.length ? (
          <FileItem
            widthNameTooltip={139}
            data={fileTree[0]}
            onRemove={(file) => handleRemoveFiles(file)}
            onUpload={onUploadFiles}
          />
        ) : (
          <DropzoneDes
            className="taureau-pre-processed-detached"
            icon={<IconFormatted />}
            subDescription="formatted file"
            dataTypes={dataTypesFinal?.map((dataType: string) =>
              dataType.toUpperCase()
            )}
            hovered={hovered}
          />
        )
      ) : (
        <DropzoneDes
          dataTypes={isNonAnnotationProject ? [] : dataTypesFinal}
          hovered={hovered}
        />
      )}
    </Upload>
  );

  const uploadDetached = (
    <Block name="taureau-upload-detached">
      <Elem name="header">
        <Elem name="header-title">Drop file</Elem>
        <Elem name="header-description">Import your data here</Elem>
      </Elem>

      <Elem name="body">
        <Elem name="upload-zone">
          {uploadRaw}

          <Elem name="upload-zone-description">
            <Elem name="upload-zone-description-icon">
              <IconInfo size={20} color="#334466" />
            </Elem>
            <Elem name="upload-zone-description-content">
              The formatted file must include a “file_id” column to map
              annotations with corresponding imported raw data in your project.
              <br />
              You could download template in the Instruction tab to get file id
              generated by the system.
            </Elem>
          </Elem>
        </Elem>
      </Elem>
    </Block>
  );

  return (
    <>
      <RouteLeavingProject
        isShowConfirm={!hideMinusModalButton}
        when={!isLeaveProject}
        handleAfterUnload={() => handleCloseModal(false)}
        handleAfterSuccess={
          hideMinusModalButton ? () => handleCloseModal(false) : undefined
        }
      />
      <Modal
        className="modal-import-data"
        title={
          <span>
            {!isConfigPreProcessing
              ? "Import Data"
              : "Import Pre-processed Data"}
            {!hideMinusModalButton && (
              <button
                type="button"
                aria-label="Close"
                className="ant-modal-close taureau-minus-button"
                style={{ top: 2, right: 33 }}
                onClick={handleMinusImportModal}
              >
                <span className="ant-modal-close-x">
                  <span
                    role="img"
                    aria-label="close"
                    className="anticon anticon-close ant-modal-close-icon"
                  >
                    <IconMinus />
                  </span>
                </span>
              </button>
            )}
          </span>
        }
        centered
        open={open}
        // onOk={() => setOpen(false)}
        onCancel={() => handleCloseModal()}
        footer={null}
        // width={
        //   !currentPreProcessed && currentTab === "preProcessed"
        //     ? "997px"
        //     : "70%"
        // }
        width="997px"
      >
        <Block name="import-data">
          <Elem name="content">
            <Elem name="header">
              {project?.projectType !== ProjectType.NonAnnotation ? (
                <ToggleItems
                  items={{ raw: "Raw", preProcessed: "Pre-processed" }}
                  active={currentTab}
                  onSelect={handleSelectTab}
                />
              ) : canExportVersion ? (
                <ToggleItems
                  items={{ raw: "Raw", advanced: "Advanced" }}
                  active={currentTab}
                  onSelect={(value) => {
                    if (progressing?.raw || progressing?.advanced) return;
                    handleSelectTab(value);
                    resetFiles();
                  }}
                />
              ) : (
                <></>
              )}
            </Elem>

            {!currentPreProcessed && currentTab === "preProcessed" && (
              <Elem name="select-pre">
                <SelectPre
                  onSelect={(value) =>
                    dispatch({
                      type: ACTIONS.SET_PRE_PROCESSED,
                      payload: value,
                    })
                  }
                />
              </Elem>
            )}

            {showBody && (
              <Elem name="body-out">
                {currentTab === "preProcessed" && !isConfigPreProcessing && (
                  <SelectPreButton
                    value={currentPreProcessed}
                    onSelect={(value) =>
                      dispatch({
                        type: ACTIONS.SET_PRE_PROCESSED,
                        payload: value,
                      })
                    }
                  />
                )}
                <Elem
                  name="body"
                  className={
                    currentPreProcessed && currentTab === "preProcessed"
                      ? "body-pre-processed"
                      : ""
                  }
                  // style={{
                  //   gap:
                  //     filesByTab?.length || currentTab === "preProcessed"
                  //       ? 10
                  //       : 0,
                  // }}
                >
                  {currentTab === "preProcessed" &&
                  currentPreProcessed === "attached" ? (
                    <UploadAttached
                      zipFile={preAttached.zipFile}
                      formattedFile={preAttached.formattedFile}
                      onRemoveZipFile={(file) =>
                        handleRemovePreFiles("zipFile")
                      }
                      onRemoveFormattedFile={(file) =>
                        handleRemovePreFiles("formattedFile")
                      }
                      sendZipFile={(file) =>
                        handleSavePreFiles("zipFile", file)
                      }
                      sendFormattedFile={(file) =>
                        handleSavePreFiles("formattedFile", file)
                      }
                      onValidate={(csvHeaders: any) =>
                        validateAttribute(
                          csvHeaders,
                          configToolList?.map((tool: any) => tool.key)
                        )
                      }
                    />
                  ) : currentTab === "preProcessed" &&
                    currentPreProcessed === "detached" ? (
                    uploadDetached
                  ) : (
                    uploadRaw
                  )}

                  <Elem
                    name="body-detail"
                    style={
                      currentTab !== "raw"
                        ? { height: "fit-content", minHeight: 75 }
                        : {}
                    }
                    className={`${
                      filesLength || currentTab !== "raw"
                        ? "show-body-detail"
                        : ""
                    }`}
                  >
                    {currentTab === "preProcessed" && (
                      <PreProcessed
                        onRedirectMeta={() => {
                          if (!hideMinusModalButton) {
                            onClose?.();
                            history.push(
                              `/modules/${moduleId}/projects/${projectId}/settings-project?tab=metadata`
                            );
                          } else {
                            handleCloseModal(true, () => {
                              history.push(
                                `/modules/${moduleId}/projects/${projectId}/settings-project?tab=metadata`
                              );
                            });
                          }
                        }}
                        expand={expand}
                        setExpand={setExpand}
                        currentPreProcessed={currentPreProcessed}
                        currentProject={currentProject}
                        configToolList={configToolList}
                        metaDataList={metaDataList}
                      />
                    )}
                    {currentTab === "raw" && (
                      <FileList
                        folderTree={folderTree}
                        importState={importState}
                        onRemainFolder={handleRemainFolder}
                        onSelectFolderPath={handleFolderPath}
                        files={fileTree}
                        filesLength={filesLength}
                        filesLengthMess={filesLengthMess}
                        filesSelectedLength={filesSelectedLength}
                        filesSelectedLengthMess={filesSelectedLengthMess}
                        onRemove={handleRemoveFiles}
                        onSelect={handleSelectFiles}
                      />
                    )}
                  </Elem>
                </Elem>
                {!!failedFiles.length && (
                  <ExportFailedList
                    label={labelFailedFiles}
                    style={{ marginTop: filesByTab?.length ? 0 : 10 }}
                    files={failedFiles}
                    currentProject={currentProject}
                  />
                )}
              </Elem>
            )}

            {currentTab === "advanced" && (
              <AdvancedUpload
                failedFiles={failedFiles}
                labelFailedFiles={labelFailedFiles}
              />
            )}
          </Elem>

          <Elem
            name="footer"
            className={`${
              !currentPreProcessed && currentTab === "preProcessed"
                ? "footer-hide"
                : ""
            }`}
          >
            {!!filesSelectedLength && (
              <Button
                className="text-[#E62E2E]"
                size="sm"
                corner="Rounded"
                theme="Light"
                onClick={handleClickDelete}
                // disabled={!importState.files.length}
                // loading={watting}
              >
                {removeButtonContent}
              </Button>
            )}
            {hasFailedFiles && (
              <Button
                className="text-[#6B7A99]"
                size="sm"
                corner="Rounded"
                theme="Light"
                onClick={handleRetry}
                // disabled={!importState.files.length}
                // loading={watting}
              >
                Retry
              </Button>
            )}
            {/* {(progressing[currentTab] || pause[currentTab]) && ( */}
            {(progressing[currentTab] || pause[currentTab]) &&
              ["raw", "advanced"]?.includes(currentTab) &&
              files?.find(
                (file: any) =>
                  (currentTab === "raw" ? checkFileTypeOtherZip(file) : true) &&
                  file.status !== fileStatusOptions.save
              ) && (
                <Button
                  className="text-[#6B7A99]"
                  size="sm"
                  corner="Rounded"
                  theme="Light"
                  onClick={() => {
                    if (isZipProgress && !pause[currentTab]) {
                      Message.error({
                        content: "Cannot pause zip file upload process.",
                      });
                      return;
                    }

                    if (pause[currentTab]) {
                      setIsContinue(true);
                      dispatch({ type: ACTIONS.SAVE_PAUSED });
                    } else {
                      dispatch({ type: ACTIONS.PAUSED_ALL });
                      rejectRawUploadingProgress.current(
                        new Error("Upload Process was Cancelled form Outside")
                      );
                    }
                    handlePause?.(!pause[currentTab]);
                  }}
                  // disabled={isDisabledContinueBtn}
                  // loading={watting}
                >
                  {pause[currentTab] ? "Continue" : "Pause"}
                </Button>
              )}

            {showClearButton && (
              <Button
                className="text-[#6B7A99]"
                size="sm"
                corner="Rounded"
                theme="Light"
                onClick={handleClear}
              >
                Clear
              </Button>
            )}
            {isShowContinueBtn ? (
              <Button
                className={`${
                  isUpdatingStatusExportVersion ? "button-import-loading" : ""
                } min-w-[114px] min-h-[40px] px-[15px] py-[5px]`}
                size="sm"
                corner="Rounded"
                theme="Primary"
                onClick={onClickContinue}
                // loading={progressing[currentTab]}
              >
                {isUpdatingStatusExportVersion
                  ? "Adding to ML project"
                  : "Add to ML project"}
              </Button>
            ) : (
              <Button
                className={`${disabledUploadButton ? "button-disabled" : ""} ${
                  progressing[currentTab] && !pause[currentTab]
                    ? "button-import-loading"
                    : ""
                } ${
                  pause[currentTab] ? "!cursor-not-allowed" : ""
                } min-w-[114px] min-h-[40px] px-[15px] py-[5px]`}
                size="sm"
                corner="Rounded"
                theme="Primary"
                onClick={handleUpload}
                disabled={disabledUploadButton}
                // loading={progressing[currentTab]}
              >
                {uploadButtonContent}
              </Button>
            )}
          </Elem>
        </Block>
      </Modal>
    </>
  );
};
