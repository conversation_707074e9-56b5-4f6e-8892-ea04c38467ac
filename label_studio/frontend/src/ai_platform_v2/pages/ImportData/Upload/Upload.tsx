import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import { useCallback, useRef, useState, useEffect } from "react";
import "./Upload.styl";
import { Spin } from "antd";
import Message from "@/ai_platform_v2/component/Message/Message";

const flatten = (nested: any) => {
  return [].concat(...nested);
};

const traverseFileTree = (item: any, path: any) => {
  return new Promise((resolve) => {
    path = path || "";
    if (item.isFile) {
      // Avoid hidden files
      if (item.name[0] === ".") return resolve([]);

      resolve([item]);
    } else if (item.isDirectory) {
      // Get folder contents
      const dirReader = item.createReader();
      const dirPath = path + item.name + "/";

      dirReader.readEntries((entries: any) => {
        Promise.all(
          entries.map((entry: any) => traverseFileTree(entry, dirPath))
        )
          .then(flatten)
          .then(resolve);
      });
    }
  });
};

const getFiles = (files: any) => {
  // @todo this can be not a files, but text or any other draggable stuff
  return new Promise((resolve) => {
    if (!files.length) return resolve([]);
    if (!files[0].webkitGetAsEntry) return resolve(files);

    // Use DataTransferItemList interface to access the file(s)
    const entries = Array.from(files).map((file: any) =>
      file.webkitGetAsEntry()
    );

    Promise.all(entries.map(traverseFileTree))
      .then(flatten)
      .then((fileEntries) =>
        fileEntries.map((fileEntry: any) => {
          const file = new Promise((res) => fileEntry.file(res)).then(
            (file: any) => ({ file, fullPath: fileEntry.fullPath.substring(1) })
          );

          return file;
        })
      )
      .then((filePromises) => Promise.all(filePromises))
      .then(resolve);
  });
};

interface Props {
  id?: string;
  className?: any;
  customClassName?: any;
  multiple?: boolean;
  loading?: any;
  disabled?: boolean;
  dataTypes?: any;
  isValidateDataTypes?: boolean;
  children?: any;
  sendFiles?: (value: any) => void;
  onHover?: (value: any) => void;
  onInvalid?: (value: any) => void;
}

export const Upload = (props: Props) => {
  const {
    id = "file-input",
    className = "",
    customClassName = "",
    multiple = true,
    dataTypes = [],
    loading = false,
    disabled = false,
    isValidateDataTypes = true,
    children,
    sendFiles,
    onHover,
    onInvalid,
  } = props;

  const [hovered, setHovered] = useState(false);
  const handleHover = (e: any) => {
    e.preventDefault();
    setHovered(true);
  };
  const onLeave = setHovered.bind(null, false);
  // const dropzoneRef = useRef();

  const handleInvalid = useCallback(
    (files: any, finalFiles: any) => {
      const invalidFiles = files.filter(
        (file: any) =>
          !finalFiles.find(
            (fileCheck: any) => fileCheck.fullPath === file.fullPath
          )
      );

      onInvalid?.(invalidFiles);
    },
    [onInvalid]
  );

  const handleValidateFileType = useCallback(
    (file) => {
      const fileNameSplit = file.file.name.split(".");
      const fileType = fileNameSplit?.[fileNameSplit?.length - 1];

      return dataTypes
        .map((type: string) => type.toLowerCase())
        .includes(`.${fileType}`.toLowerCase());
    },
    [dataTypes]
  );

  const onDrop = useCallback(
    (e: any) => {
      e.preventDefault();
      onLeave();
      getFiles(e.dataTransfer.items).then((files: any) => {
        const finalFiles = isValidateDataTypes
          ? files.filter(handleValidateFileType)
          : files;

        if (finalFiles.length !== files.length) {
          handleInvalid(files, finalFiles);

          Message.error({
            // title: "Invalid or unknown formats",
            content: "Invalid or unknown formats",
          });
        }
        finalFiles?.length &&
          sendFiles?.(multiple ? finalFiles : finalFiles?.[0]);
      });
    },
    [
      isValidateDataTypes,
      multiple,
      onLeave,
      sendFiles,
      handleValidateFileType,
      handleInvalid,
    ]
  );

  const onUpload = useCallback(
    (e: any) => {
      e.preventDefault();

      const files = [...e.target.files].map((file: any) => ({
        fullPath: file.name,
        file,
      }));

      const finalFiles = isValidateDataTypes
        ? files.filter(handleValidateFileType)
        : files;

      if (finalFiles.length !== files.length) {
        handleInvalid(files, finalFiles);

        Message.error({
          // title: "Invalid or unknown formats",
          content: "Invalid or unknown formats",
        });
      }

      finalFiles?.length &&
        sendFiles?.(multiple ? finalFiles : finalFiles?.[0]);
      e.target.value = "";
    },
    [
      isValidateDataTypes,
      multiple,
      sendFiles,
      handleValidateFileType,
      handleInvalid,
    ]
  );

  useEffect(() => {
    onHover?.(hovered);
  }, [hovered]);

  return (
    <>
      <input
        id={id}
        type="file"
        name="file"
        multiple={multiple}
        accept={isValidateDataTypes ? dataTypes?.join(", ") : undefined}
        onChange={onUpload}
        style={{ display: "none" }}
        disabled={disabled || loading}
      />
      <Block
        name={`dropzone ${customClassName}`}
        className={`${hovered ? "hovered" : ""} ${className}`}
        // ref={dropzoneRef}
        onDragStart={handleHover}
        onDragOver={handleHover}
        onDragLeave={onLeave}
        onDrop={onDrop}
        onClick={() => document.getElementById(id)?.click()}
      >
        {loading ? <Spin /> : children}
      </Block>
    </>
  );
};
