.import-progress
    position absolute
    bottom: 0
    right 19px
    z-index 1

    display: flex
    flex-direction: column
    // gap: 7px
    width: 300px;
    border-radius: 6px 6px 0px 0px
    background-color: #fff
    border: solid 1px rgba(0,0,0,0.05)
    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.10);
    
    &__header
        display: flex;
        height: 40px;
        align-items: center;
        gap: 10px;
        flex-shrink: 0;

        border-radius: 6px 6px 0px 0px
        padding: 0 10px 0 15px
        transition: background .5s

    &__title
        width: 70%
        display: flex;
        align-items: center;
        gap: 10px;

    &__title-content
        display: flex

        :global(.content-select-button)
            color: var(--blue-blue, #3361FF);
            text-align: center;
            
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            transition: color .3s

    .paused-title-content
        margin-right: 10px

    &__title-progress-content
        // margin-left: auto
        margin-right: auto
        opacity 1
        transition: opacity .3s

    .hide-title-progress-content
        opacity 0

    .paused-title-progress-content
        margin-left: -9px

    &__title-progress-content, &__body-progress-content, &__item-content
        color: var(--gray-blue-grey-blue-40, var(--Light-Grey-Blue, #346));
        text-align: center;
        
        font-size: 14px;
        font-style: normal;
        font-weight: 300;
        line-height: normal;

    &__action
        width: fit-content
        display: flex;
        align-items: center
        justify-content: flex-end

    
    &__body
        display: flex;
        width: 100%;
        height: 0;
        opacity 0
        overflow: hidden
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 9px;
        flex-shrink: 0;
        transition: height 0.5s, opacity .5s

    .body-expand
        height: 81px;
        opacity 1

    &__body-detail
        display: flex;
        width: 100%;
        padding-left: 15px;
        justify-content: space-between;
        align-items: center;

    &__file-detail
        display: flex;
        width: 179px;
        align-items: flex-start;
        flex-direction: column
        gap: 7px;
        flex-shrink: 0;

    &__item
        display: flex
        align-items: center
        gap: 3px

    &__item-completed, &__item-failed
        
        font-size: 14px;
        font-style: normal;
        font-weight: 300;
        line-height: normal;

    &__item-completed
        color: var(--yellow-green-green-dark-1, #13BF24);

    &__item-failed
        color: var(--red-red-dark-1, #CC1414);

    &__body-progress
        display: flex;
        width: 90%;
        padding: 0px 10px 0 15px;
        justify-content: space-between;
        align-items: center;

    &__expand, &__full-screen, &__close
        cursor pointer

    &__expand
        transition: transform 0.5s
        
    .collapse
        transform: scaleY(-1);

    &__pause-action
        cursor pointer

.import-progress-batch
    .import-progress__item-completed
        color: var(--Fade-Green, #66CCA7);

    .import-progress__item-failed
        color: var(--Fade-Red, #DE5462);


.paused-normal
    :global(.ls-import-progress__title-content) :global(.content-select-button)
        color: var(--blue-blue, #E6B117);

.uploading-normal
    :global(.ls-import-progress__title-content) :global(.content-select-button)
        color: var(--blue-blue, #3361FF);

.completed-normal
    :global(.ls-import-progress__title-content) :global(.content-select-button)
        color: var(--blue-blue, #13BF24);


.paused-batch
    :global(.ls-import-progress__title-content) :global(.content-select-button)
        color: var(--blue-blue, #E6B117);

.uploading-batch
    :global(.ls-import-progress__title-content) :global(.content-select-button)
        color: var(--blue-blue, #7BD3EA);

.completed-batch
    :global(.ls-import-progress__title-content) :global(.content-select-button)
        color: var(--blue-blue, #66CCA7);