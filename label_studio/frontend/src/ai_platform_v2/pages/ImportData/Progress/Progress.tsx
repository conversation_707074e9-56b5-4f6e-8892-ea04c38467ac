import IconClose from "@/ai_platform_v2/assets/Icons/IconClose";
import IconExpandLess from "@/ai_platform_v2/assets/Icons/IconExpandLess";
import IconFullScreen from "@/ai_platform_v2/assets/Icons/IconFullScreen";
import { Block, Elem } from "@/ai_platform_v2/utils/bem";

import IconPauseCircleFilled from "@/ai_platform_v2/assets/Icons/IconPauseCircleFilled";
import IconPlayCircleFilled from "@/ai_platform_v2/assets/Icons/IconPlayCircleFilled";
import { ProgressBar } from "@/ai_platform_v2/component/ProgressBar/ProgressBar";
import { useImportData } from "@/ai_platform_v2/providers/ImportDataProvider";
import { Tooltip, Typography } from "antd";
import { useCallback, useMemo } from "react";
import { fileStatusOptions } from "../ImportDataReducer";
import "./Progress.styl";

const { Text } = Typography;

const INIT_PROGRESS_BACKGROUND_COLORS: any = {
  normal: {
    save: "#fff",
    uploading: "rgba(51, 97, 255, 0.05)",
    paused: "rgba(255, 203, 51, 0.1)",
    failed: "#fff",
    completed: "rgba(41, 204, 57, 0.1)",
  },
  batch: {
    save: "#fff",
    uploading: "rgba(123, 211, 234, 0.1)",
    paused: "rgba(255, 203, 51, 0.1)",
    failed: "#fff",
    completed: "rgba(102, 204, 167, 0.1)",
  },
};

const INIT_PROGRESS_COLORS: any = {
  normal: { uploading: "#3361FF", paused: "#E6B117", completed: "#13BF24" },
  batch: { uploading: "#7BD3EA", paused: "#E6B117", completed: "#66CCA7" },
};

interface Props {
  mode?: string; // normal | batch
  title?: any;
  open?: boolean;
  enabledPause?: boolean;
  paused?: boolean;
  percent?: number;
  data?: any;
  onFullScreen?: () => void;
  onPause?: () => void;
  onClose?: () => void;
}
export const Progress = (props: Props) => {
  const {
    mode = "normal",
    title = "Import Progress",
    open,
    enabledPause,
    paused,
    percent = 0,
    data = { completed: 0, failed: 0 },
    onFullScreen,
    onPause,
    onClose,
  } = props;
  const { completed = 0, failed = 0 } = data;

  const { expandMiniBar, setExpandMiniBar } = useImportData();

  const percentFinal = useMemo(() => Math.floor(percent), [percent]);

  const percentContent = useMemo(
    () => (paused ? "Paused" : `${percentFinal}%`),
    [paused, percentFinal]
  );

  const completedContent = useMemo(
    () => `${completed} ${completed > 1 ? "items" : "item"} `,
    [completed]
  );

  const failedContent = useMemo(
    () => `${failed} ${failed > 1 ? "items" : "item"} `,
    [failed]
  );

  const statusUpload = useMemo(() => {
    if (paused) {
      return fileStatusOptions.paused;
    }
    return percentFinal === 100
      ? fileStatusOptions.completed
      : fileStatusOptions.uploading;
  }, [percentFinal, paused]);

  const backgroundColorProgressBar = useMemo(() => {
    switch (statusUpload) {
      case fileStatusOptions.save:
        return INIT_PROGRESS_BACKGROUND_COLORS?.[mode].save;
      case fileStatusOptions.uploading:
        return INIT_PROGRESS_BACKGROUND_COLORS?.[mode].uploading;
      case fileStatusOptions.paused:
        return INIT_PROGRESS_BACKGROUND_COLORS?.[mode].paused;
      case fileStatusOptions.failed:
        return INIT_PROGRESS_BACKGROUND_COLORS?.[mode].failed;
      case fileStatusOptions.completed:
        return INIT_PROGRESS_BACKGROUND_COLORS?.[mode].completed;
    }
  }, [statusUpload, mode]);

  const colorByStatus = useMemo(() => {
    switch (statusUpload) {
      case fileStatusOptions.uploading:
        return INIT_PROGRESS_COLORS?.[mode].uploading;
      case fileStatusOptions.paused:
        return INIT_PROGRESS_COLORS?.[mode].paused;
      case fileStatusOptions.completed:
        return INIT_PROGRESS_COLORS?.[mode].completed;
    }
  }, [statusUpload, mode]);

  const handleClose = useCallback(() => {
    onClose?.();
  }, [onClose]);

  return (
    <>
      {open && (
        <Block
          name="import-progress"
          className={`${statusUpload}-${mode} ${
            mode === "batch" ? "import-progress-batch" : ""
          }`}
        >
          <Elem
            name="header"
            style={{
              backgroundImage: `linear-gradient(to right, ${backgroundColorProgressBar} ${
                expandMiniBar ? 100 : percentFinal
              }%, #fff 0)`,
            }}
          >
            <Elem name="title">
              <Elem
                name="title-content"
                className={`${
                  percentContent === "Paused" ? "paused-title-content" : ""
                }`}
              >
                <Text
                  className="content-select-button"
                  style={{
                    width: percentContent === "Paused" ? 138 : 150,
                  }}
                  ellipsis={{ tooltip: title }}
                >
                  {title}
                </Text>
              </Elem>
              <Elem
                name="title-progress-content"
                className={`${
                  expandMiniBar ? "hide-title-progress-content" : ""
                } ${
                  percentContent === "Paused"
                    ? "paused-title-progress-content"
                    : ""
                }`}
              >
                {percentContent}
              </Elem>
            </Elem>
            <Elem name="action">
              <Tooltip
                overlayClassName="import-tooltip"
                title={expandMiniBar ? "Minimize" : "Maximize"}
              >
                <Elem
                  name="expandMiniBar"
                  className={`${expandMiniBar ? "collapse" : ""}`}
                  onClick={() => setExpandMiniBar(!expandMiniBar)}
                >
                  <IconExpandLess size={27} />
                </Elem>
              </Tooltip>
              <Tooltip overlayClassName="import-tooltip" title="Full Screen">
                <Elem name="full-screen" onClick={onFullScreen}>
                  <IconFullScreen size={21} />
                </Elem>
              </Tooltip>
              <Tooltip
                overlayClassName="import-tooltip"
                title="Stop Importing"
                placement="topRight"
              >
                <Elem name="close" onClick={handleClose}>
                  <IconClose size={21} color="#CC1414" />
                </Elem>
              </Tooltip>
            </Elem>
          </Elem>

          <Elem name="body" className={`${expandMiniBar ? "body-expand" : ""}`}>
            <Elem name="body-detail">
              <Elem name="file-detail">
                <Elem name="item">
                  <Elem name="item-content">{completedContent}</Elem>
                  <Elem name="item-completed">Completed</Elem>
                </Elem>
                <Elem name="item">
                  <Elem name="item-content">{failedContent}</Elem>
                  <Elem name="item-failed">Failed</Elem>
                </Elem>
              </Elem>
              {enabledPause && percentFinal !== 100 && (
                <Tooltip
                  overlayClassName="import-tooltip"
                  title={paused ? "Continue" : "Pause"}
                >
                  <Elem name="pause-action" onClick={onPause}>
                    {paused ? (
                      <IconPlayCircleFilled size={36} />
                    ) : (
                      <IconPauseCircleFilled size={36} />
                    )}
                  </Elem>
                </Tooltip>
              )}
            </Elem>

            <Elem name="body-progress">
              <ProgressBar
                percent={percentFinal}
                strokeColor={colorByStatus}
                paused={paused}
              />
            </Elem>
          </Elem>
        </Block>
      )}
    </>
  );
};
