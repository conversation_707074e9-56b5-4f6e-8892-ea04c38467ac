import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import "./PreProcessed.styl";
import { TagSelect } from "@/ai_platform_v2/component/TagSelect/TagSelect";
import { useCallback, useEffect, useState } from "react";
import IconInfo from "@/ai_platform_v2/assets/Icons/IconInfo";
import { Tooltip } from "antd";
import { formatItem } from "../ImportDataReducer";
import { useAPI } from "@/providers/ApiProvider";
import { Instruction } from "../Instruction/Instruction";
import { MultiSelect } from "../component/MultiSelect/MultiSelect";

function getFileName(disposition: any) {
  let filename = "";

  if (disposition && disposition.indexOf("attachment") !== -1) {
    const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
    const matches = filenameRegex.exec(disposition);

    if (matches !== null && matches[1]) {
      filename = matches[1].replace(/['"]/g, "");
    }
  }
  return filename;
}

const downloadFile = (blob: any, filename: any) => {
  const link = document.createElement("a");

  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
};

interface Props {
  currentPreProcessed?: any;
  currentProject?: any;
  configToolList?: any;
  metaDataList?: any;
  onRedirectMeta?: any;
}
export const PreProcessed = (props: Props) => {
  const {
    currentPreProcessed,
    currentProject,
    configToolList,
    metaDataList,
    onRedirectMeta,
  } = props;

  const api = useAPI();
  const [waiting, setWaitingStatus] = useState(false);
  const [openInstruction, setOpenInstruction] = useState(false);

  const [csvInstruction, setCsvInstruction] = useState();

  const handleSetModal = useCallback((value) => {
    setOpenInstruction(value);
  }, []);

  const [configuredToolValue, setConfiguredToolValue] = useState([]);
  const [formatValue, setFormatValue] = useState("CSV");
  const [metaDataValue, setMetaDataValue] = useState([]);

  const handleChangeConfiguredToolValue = useCallback((values) => {
    setConfiguredToolValue(values);
  }, []);

  const handleChangeFormatValue = useCallback((values) => {
    setFormatValue(values);
  }, []);

  const handleChangeMetaDataValue = useCallback((values) => {
    setMetaDataValue(values);
  }, []);

  const downloadCsvSmaple = useCallback(async () => {
    const params = {
      pk: currentProject?.id,
      include_fileId: currentPreProcessed === "detached",
    };

    if (configuredToolValue?.length) {
      params.annotation_tools = configuredToolValue;
    }
    if (metaDataValue?.length) {
      params.meta_data = metaDataValue;
    }

    const response = await api.callApi("downloadSampleCsvRaw", {
      params,
    });

    if (response.ok) {
      const blob = await response.blob();

      const headers = new Map(Array.from(response.headers));
      const fileName = getFileName(headers.get("content-disposition"));

      downloadFile(blob, fileName);
    }
  }, [currentPreProcessed, currentProject, configuredToolValue, metaDataValue]);

  const fetchCsvSmapleInstruction = useCallback(async () => {
    const params = {
      pk: currentProject?.id,
      include_fileId: currentPreProcessed === "detached",
    };

    if (configuredToolValue?.length) {
      params.annotation_tools = configuredToolValue;
    }
    if (metaDataValue?.length) {
      params.meta_data = metaDataValue;
    }

    setWaitingStatus(true);

    const response = await api.callApi("sampleCsvInstruction", {
      params,
    });

    setWaitingStatus(false);

    if (response) {
      setCsvInstruction(response);
    }
  }, [currentPreProcessed, currentProject, configuredToolValue, metaDataValue]);

  const handleOpenInstruction = useCallback(async () => {
    handleSetModal(true);
    await fetchCsvSmapleInstruction();
  }, [fetchCsvSmapleInstruction]);

  useEffect(() => {
    setConfiguredToolValue(configToolList.map((tool: any) => tool.key));
  }, [configToolList]);

  return (
    <Block name="pre-processed-import">
      <Elem name="header">
        <Elem name="header-title">Instruction</Elem>
        <Elem name="header-description">
          Follow these to download the correct template of formatted file
        </Elem>
      </Elem>
      <Elem name="body">
        {!!configToolList?.length && (
          <Elem name="item">
            <Elem name="item-label">
              <Elem name="item-label-content">Configured Tool</Elem>
              <Elem name="item-label-desc">
                Select labeling tool that your pre-annotated data created from.
                You could choose more than one if annotations labeled by mixed
                tool
              </Elem>
            </Elem>
            <Elem name="item-content">
              <TagSelect
                type="multiple"
                items={configToolList}
                values={configuredToolValue}
                onChange={handleChangeConfiguredToolValue}
              />
            </Elem>
          </Elem>
        )}

        <Elem name="item">
          <Elem name="item-label">
            <Elem name="item-label-content">Format</Elem>
            <Elem name="item-label-desc">
              Choose your format of file contains pre-annotated data
            </Elem>
          </Elem>
          <Elem name="item-content">
            <TagSelect
              type="single"
              items={formatItem}
              values={formatValue}
              onChange={handleChangeFormatValue}
            />
          </Elem>
        </Elem>

        <Elem name="item">
          <Elem name="item-label">
            <Elem name="item-label-content">Meta data</Elem>
            <Elem name="item-label-desc">
              Choose meta data if you want to upload annotations along with
              supplementary information
            </Elem>
          </Elem>
          <Elem name="item-content" className="import-pre-meta-data">
            <MultiSelect
              placeholder="Choose meta data"
              overlayClassName="tooltip-custom-style"
              value={metaDataValue}
              options={metaDataList}
              onChange={handleChangeMetaDataValue}
              onRedirectMeta={onRedirectMeta}
            />
          </Elem>
        </Elem>

        <Elem name="item">
          <Elem name="item-label">
            <Elem name="item-label-content">Template</Elem>
            <Elem name="item-label-desc">
              Check and download the template generated from your selection
              above
            </Elem>
          </Elem>
          <Elem name="item-content">
            <Elem name="template">
              <Elem name="template-name" onClick={downloadCsvSmaple}>
                CSV Template
              </Elem>
              <Tooltip
                overlayClassName="import-tooltip"
                title="Template instruction"
              >
                <Elem
                  name="template-instruction"
                  onClick={handleOpenInstruction}
                >
                  <IconInfo />
                </Elem>
              </Tooltip>
            </Elem>
          </Elem>
        </Elem>
      </Elem>

      <Instruction
        open={openInstruction}
        loading={waiting}
        columns={csvInstruction?.columns ?? []}
        data={csvInstruction?.data ?? []}
        onClose={() => handleSetModal(false)}
      />
    </Block>
  );
};
