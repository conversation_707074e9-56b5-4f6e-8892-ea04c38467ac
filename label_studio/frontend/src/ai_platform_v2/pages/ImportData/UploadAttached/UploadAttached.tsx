import { Block, Elem } from "@/ai_platform_v2/utils/bem";
import { Upload } from "../Upload/Upload";
import { useCallback, useState } from "react";
import { DropzoneDes } from "../DropzoneDes/DropzoneDes";
import IconPlus from "@/ai_platform_v2/assets/Icons/IconPlus";
import "./UploadAttached.styl";
import IconZip from "@/ai_platform_v2/assets/Icons/IconZip";
import IconFormatted from "@/ai_platform_v2/assets/Icons/IconFormatted";
import { FileItem } from "../FileItem/FileItem";
import {
  csvParser,
  csvParserHeader,
  validateRequiredColumnAttached,
} from "../const/validate";
import Message from "@/ai_platform_v2/component/Message/Message";
import IconInfo from "@/ai_platform_v2/assets/Icons/IconInfo";

const dataTypes = { zip: [".zip"], format: [".coco", ".csv"] };

interface UploadAttachedProps {
  className?: any;
  loading?: any;
  zipFile?: any;
  formattedFile?: any;
  onRemoveZipFile?: (file: any) => void;
  onRemoveFormattedFile?: (file: any) => void;
  sendZipFile?: (file: any) => void;
  sendFormattedFile?: (file: any) => void;
  //   onHover?: (value: any) => void;
  //   onInvalid?: (value: any) => void;
  onValidate?: (csvHeaders: any) => boolean;
}
export const UploadAttached = (props: UploadAttachedProps) => {
  const {
    className,
    loading,
    zipFile,
    formattedFile,
    onRemoveZipFile,
    onRemoveFormattedFile,
    sendZipFile,
    sendFormattedFile,
    onValidate,
  } = props;
  const [hoveredZip, setHoveredZip] = useState(false);
  const [hoveredFormatted, setHoveredFormatted] = useState(false);

  const handleRemoveZipFile = useCallback(
    (files) => {
      onRemoveZipFile?.(files?.[0]);
    },
    [onRemoveZipFile]
  );

  const handleRemoveFormattedFile = useCallback(
    (files) => {
      onRemoveFormattedFile?.(files?.[0]);
    },
    [onRemoveFormattedFile]
  );

  const handleSendFormattedFile = useCallback(
    async (file) => {
      const csvHeader = await csvParserHeader(file.file);
      const csvFile = await csvParser(file.file);

      const validateRequired = validateRequiredColumnAttached(
        csvHeader,
        csvFile
      );

      const validateViewNMeta = onValidate?.(csvHeader);

      if (validateRequired && validateViewNMeta) {
        sendFormattedFile?.(file);
      } else {
        Message.error({
          // title: "Invalid or unknown formats",
          content: "Incorrect format.",
        });
      }
    },
    [sendFormattedFile, onValidate]
  );

  return (
    <Block name="taureau-upload-attached">
      <Elem name="header">
        <Elem name="header-title">Drop file</Elem>
        <Elem name="header-description">Import your data here</Elem>
      </Elem>

      <Elem name="body">
        <Elem name="upload-zone">
          <Upload
            className={className}
            id="zip-file-input"
            disabled={!!zipFile}
            loading={loading}
            multiple={false}
            dataTypes={dataTypes.zip}
            onHover={(value) => setHoveredZip(value)}
            sendFiles={sendZipFile}
          >
            {zipFile ? (
              <FileItem
                widthNameTooltip={139}
                data={zipFile}
                onRemove={handleRemoveZipFile}
              />
            ) : (
              <DropzoneDes
                icon={<IconZip />}
                subDescription="zip file"
                hovered={hoveredZip}
              />
            )}
          </Upload>

          <Elem name="upload-zone-description">
            <Elem name="upload-zone-description-icon">
              <IconInfo size={20} color="#334466" />
            </Elem>
            <Elem name="upload-zone-description-content">
              All files need to be grouped in the same folder before compressing
              to zip file
            </Elem>
          </Elem>
        </Elem>

        <Elem name="upload-plus">
          <IconPlus size={50} />
        </Elem>

        <Elem name="upload-zone">
          <Upload
            className={className}
            id="formatted-file-input"
            disabled={!!formattedFile}
            loading={loading}
            multiple={false}
            dataTypes={dataTypes.format}
            onHover={(value) => setHoveredFormatted(value)}
            sendFiles={handleSendFormattedFile}
          >
            {formattedFile ? (
              <FileItem
                widthNameTooltip={139}
                data={formattedFile}
                onRemove={handleRemoveFormattedFile}
              />
            ) : (
              <DropzoneDes
                icon={<IconFormatted />}
                subDescription="formatted file"
                dataTypes={dataTypes.format?.map((dataType: string) =>
                  dataType.toUpperCase()
                )}
                hovered={hoveredFormatted}
              />
            )}
          </Upload>

          <Elem name="upload-zone-description">
            <Elem name="upload-zone-description-icon">
              <IconInfo size={20} color="#334466" />
            </Elem>
            <Elem name="upload-zone-description-content">
              Local path of image needs to include zip file name
            </Elem>
          </Elem>
        </Elem>
      </Elem>
    </Block>
  );
};
