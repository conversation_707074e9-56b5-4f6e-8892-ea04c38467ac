import { useAP<PERSON> } from "@/providers/ApiProvider";
import { useProject } from "@/providers/ProjectProvider";
import { useWebSocket } from "@/providers/WebSocketProvider";
import {
  createContext,
  useCallback,
  useContext,
  useMemo,
  useReducer,
  useRef,
  useState,
} from "react";
import { v4 as uuidv4 } from "uuid";
import Message from "../component/Message/Message";
import {
  ACTIONS,
  BatchImportDataReducer,
  fileStatusOptions,
  initialState,
} from "../pages/BatchImportData/BatchImportDataReducer";
import { checkFileTypeOtherZip } from "../pages/ImportData/const/validate";

interface BatchImportDataContextValue {
  isZipProgress: boolean;
  openImport: boolean;
  batchImportState: any;
  fileTree: any;
  filesSelected: any;
  filesCompletedLength: any;
  filesLength: number;
  filesLengthMess: string;
  filesSelectedLength: number;
  filesSelectedLengthMess: string;
  percentImportProgress: number;
  dataImportProgress: any;
  dispatch: (action: any) => any;
  handleUpdateImportData: (type: any, payload: any) => any;
  handleSaveFiles: (files: any) => void;
  handleRemoveFiles: (files: any) => void;
  handleSelectFiles: (files: any) => void;
  handleAddInvalidFiles: (invalidFiles: any) => void;
  handleChangeBatchName: (batchName: string) => void;
  handleUpdateBatchData: (batchDetail: any) => void;
  fetchProjectDataTypes: () => void;
  onUploadFiles: () => void;
  pause: any;
  handlePause: (value: boolean) => void;
  handleClear: (value: boolean) => void;
  handleCancelUploading: (value: boolean) => void;
  handleCloseImport: () => void;
  handleSavePreFiles: (fileType: any, file: any) => void;
  handleRemovePreFiles: (fileType: any) => void;
  handleOpenImportModal: (dataBatchDetail?: any) => void;
  handleCloseImportModal: () => void;
  handleMinusImportModal: () => void;
  isContinue: boolean;
  setIsContinue: (value: boolean) => void;

  funcCloseModal: any;
  setFuncCloseModal: any;

  funcFetchDataSets: any;
  setFuncFetchDataSets: any;

  expandMiniBar: any;
  setExpandMiniBar: any;

  failedFiles?: any;
  isBatchNameAlready?: boolean;
  rejectUploadingProgress: any;
}

const CANNOT_ADD_MORE_FILES_AFTER_DEDUPLICATED_MESS =
  "Cannot add more files after data batch deduplicated.";

export const BatchImportDataContext =
  createContext<BatchImportDataContextValue>({} as BatchImportDataContextValue);
BatchImportDataContext.displayName = "BatchImportDataContext";

export const BatchImportDataProvider = ({ children }: any) => {
  const rejectUploadingProgress = useRef<any>();
  const uploadingProgressId = useRef<any>();
  const api = useAPI();
  const { client_dm } = useWebSocket();
  const { projectDetail } = useProject();

  const isPreReadyToWork = useMemo(
    () => !!projectDetail?.preReadyToWork,
    [projectDetail?.preReadyToWork]
  );

  const preProcessingNextStep = useMemo(() => {
    if (!projectDetail?.preProccessConfig) return {};

    const preProcessing = JSON.parse(projectDetail.preProccessConfig);

    const preProcessingEdge = JSON.parse(projectDetail.preProccessConfig)?.edge;

    if (!preProcessingEdge?.length) return {};

    const preProcessingNewEdge = preProcessingEdge?.find(
      (edge: any) => edge.sourceHandle === "preProcessingNew"
    );

    if (!preProcessingNewEdge) return {};

    const { target } = preProcessingNewEdge;

    return preProcessing?.node?.find((node: any) => node.id === target);
  }, [projectDetail?.preProccessConfig]);

  const [expandMiniBar, setExpandMiniBar] = useState(false);

  const [funcFetchDataSets, setFuncFetchDataSets] = useState(() => {});
  const [funcCloseModal, setFuncCloseModal] = useState(() => {});

  const [isContinue, setIsContinue] = useState(false);
  const pause = useRef<any>(false);
  const [isZipProgress, setIsZipProgress] = useState(false);

  const [isClear, setIsClear] = useState(false);
  const [isCancelUploadingProgress, setIsCancelUploadingProgress] =
    useState(false);

  const [openImport, setOpenImport] = useState(false);

  const [isBatchNameAlready, setIsBatchNameAlready] = useState(false);

  const [isBatchDeduplicated, setIsBatchDeduplicated] = useState(false);
  const [isShowMessCompleted, setIsShowMessCompleted] = useState(0);

  const handleBatchDeduplicated = useCallback((value: boolean) => {
    setIsBatchDeduplicated((props: any) => {
      props = value;
      return props;
    });
  }, []);

  const [batchImportState, dispatch] = useReducer<any>(
    BatchImportDataReducer,
    initialState
  );
  const {
    progressing,
    currentProject,
    files,
    uploadZipTasks,
    batchName,
    batchData,
    invalidFiles,
    filesInProgress,
  } = batchImportState;
  const projectId = currentProject?.id;

  client_dm.onmessage = (data: any) => {
    const msg = JSON.parse(data.data)?.message;

    // pre-processed: attached => zip & csv
    const zipCsvTaskId = msg?.zip_csv_task_id;

    // pre-processed: detached => csv
    const csvTaskId = msg?.csv_task_id;

    const currentUploadTaskId = msg?.current_upload_task_id;
    const fileName = msg?.zip_file_id;
    const fileCompleted = msg?.uploaded_number;
    const totalNumber = msg?.total_number;
    const percent = msg?.percent;

    const success = msg?.success;
    const error_list = msg?.error_list;

    // raw: zip file & pre-processed: detached
    const zipFile = files.find(
      (file: any) => file.file.name === (fileName ?? csvTaskId)
    );

    if (files?.length) {
      if (success && zipFile) {
        if (!error_list?.length) {
          dispatch({
            type: ACTIONS.COMPLETED,
            payload: [zipFile],
          });
        } else {
          dispatch({
            type: ACTIONS.FAILED,
            payload: [zipFile],
          });
        }

        !csvTaskId &&
          dispatch({
            type: ACTIONS.REMOVE_UPLOAD_ZIP_TASKS,
            payload: currentUploadTaskId,
          });
      } else {
        !csvTaskId &&
          dispatch({
            type: ACTIONS.ADD_UPLOAD_ZIP_TASKS,
            payload: currentUploadTaskId,
          });

        openImport &&
          dispatch({
            type: ACTIONS.SET_PROGRESSING,
            payload: true,
          });

        dispatch({
          type: ACTIONS.UPDATE_PROGRESS_FILE,
          payload: { file: [zipFile], progress: percent },
        });
      }
    }
  };

  const handlePause = useCallback((value: boolean) => {
    if (value) {
      dispatch({
        type: ACTIONS.SET_PROGRESSING,
        payload: false,
      });
    }

    pause.current = value;
  }, []);

  const handleClear = useCallback((value: boolean) => {
    if (value) {
      dispatch({
        type: ACTIONS.SET_PROGRESSING,
        payload: false,
      });
    }

    setIsClear((props: any) => {
      props = value;
      return props;
    });
  }, []);

  const handleCancelUploading = useCallback((value: boolean) => {
    if (value) {
      dispatch({
        type: ACTIONS.SET_PROGRESSING,
        payload: false,
      });
    }

    setIsCancelUploadingProgress((props: any) => {
      props = value;
      return props;
    });
  }, []);

  const handleCreateDataBatch = useCallback(async () => {
    if (batchData?.id) {
      return;
    }

    const res = await api.callApi("createDataBatch", {
      params: { pk: projectId },
      body: {
        batchName,
      },
    });

    if (res?.success) {
      dispatch({
        type: ACTIONS.UPDATE_BATCH_DATA,
        payload: { id: res?.data },
      });

      funcFetchDataSets();

      return res?.data;
    } else {
      if (res?.errorCode === "Batch name already existed") {
        setIsBatchNameAlready(true);
      }
    }

    return;
  }, [batchName, batchData]);

  const importFiles = useCallback(
    async (files, batchId, progressId) => {
      await new Promise(async (resolve, reject) => {
        rejectUploadingProgress.current = reject;
        for (let i = 0; i < files.length; i++) {
          const checkPause = pause.current;
          let checkDeduplicated = isBatchDeduplicated;
          let checkClear = isClear;
          let checkCancelUploading = isCancelUploadingProgress;

          if (progressId !== uploadingProgressId.current) {
            reject(new Error("Upload Process was Cancelled"));
            break;
          }

          setIsClear((prov) => {
            checkClear = prov;
            return prov;
          });

          setIsCancelUploadingProgress((prov) => {
            checkCancelUploading = prov;
            return prov;
          });

          if (checkPause || checkClear || checkCancelUploading) {
            dispatch({
              type: ACTIONS.SET_PROGRESSING,
              payload: false,
            });
            reject(new Error("Upload Process was Cancelled"));
            break;
          }

          setIsBatchDeduplicated((prov) => {
            if (prov) {
              checkDeduplicated = prov;
            }
            return prov;
          });

          // if data batch deduplicated => update file status is failed
          if (checkDeduplicated) {
            dispatch({ type: ACTIONS.INPROGRESS_TO_FAILED });
            break;
          }

          const fileNameSplit = files[i].file.name.split(".");
          const fileType =
            fileNameSplit?.[fileNameSplit?.length - 1].toLowerCase();

          // dispatch({ type: ACTIONS.UPLOADING, payload: [files[i]] });

          // if (step === files[i].step) {
          dispatch({
            type: ACTIONS.SET_PROGRESSING,
            payload: true,
          });
          if (fileType !== "zip") {
            dispatch({
              type: ACTIONS.ADD_FILE_INPROGRESS,
              payload: files[i],
            });

            const res = await api.callApi("uploadDataBatchFileRaw", {
              params: { pk: projectId },
              headers: { "Content-Type": "multipart/form-data" },
              body: {
                // action: "save",
                // path,
                // uploadFiles: file,
                file: files[i].file,
                tempStorageBatchFileId: batchId ?? batchData?.id,
                preReadyToWorkProject: isPreReadyToWork,
                nextWorkFlowStep: preProcessingNextStep?.name,
                nextWorkFlowStepId: preProcessingNextStep?.id,
                nextWorkFlowState: preProcessingNextStep?.type,
              },
            });

            if (res?.ok) {
              const responseText = await res.text();

              if (responseText) {
                const responseJson = JSON.parse(responseText);

                if (responseJson && responseJson?.success) {
                  dispatch({ type: ACTIONS.COMPLETED, payload: [files[i]] });
                } else {
                  // re-get batch deduplicated
                  setIsBatchDeduplicated((prov) => {
                    if (prov) {
                      checkDeduplicated = prov;
                    }
                    return prov;
                  });

                  if (
                    !checkDeduplicated &&
                    responseJson?.message ===
                      CANNOT_ADD_MORE_FILES_AFTER_DEDUPLICATED_MESS
                  ) {
                    Message.error({
                      content: responseJson?.message,
                    });
                    handleBatchDeduplicated(true);
                    setIsShowMessCompleted((prop: any) => prop + 1);
                  }
                  dispatch({ type: ACTIONS.FAILED, payload: [files[i]] });
                }
              }
            } else {
              dispatch({ type: ACTIONS.FAILED, payload: [files[i]] });
            }

            dispatch({
              type: ACTIONS.REMOVE_FILE_INPROGRESS,
              payload: files[i],
            });
          } else {
            const res = await api.callApi("uploadDataBatchFileZipRaw", {
              params: { pk: projectId },
              headers: { "Content-Type": "multipart/form-data" },
              body: {
                id: files[i].file.name,
                file: files[i].file,
                tempStorageBatchFileId: batchId ?? batchData?.id,
                // parent_path: path,
                preReadyToWorkProject: isPreReadyToWork,
                nextWorkFlowStep: preProcessingNextStep?.name,
                nextWorkFlowStepId: preProcessingNextStep?.id,
                nextWorkFlowState: preProcessingNextStep?.type,
              },
            });
          }
        }
        resolve("Updated!");
      });
    },
    [
      batchData,
      projectId,
      pause,
      isPreReadyToWork,
      preProcessingNextStep,
      isBatchDeduplicated,
      uploadingProgressId,
    ]
  );

  const onUploadFiles = useCallback(
    async (isRetry = false, fileRetry = []) => {
      const progressId = uuidv4();

      uploadingProgressId.current = progressId;

      // reset clear default
      isClear && handleClear(false);

      setIsZipProgress(false);

      const perUpload = 6;
      let batchId: any = batchData?.id;

      if (!batchId) {
        await handleCreateDataBatch().then((value) => {
          batchId = value;
        });

        if (!batchId) {
          return;
        }
      }

      if (batchId) {
        dispatch({ type: ACTIONS.UNSELECT_ALL });

        const imgFilesRetry = fileRetry?.filter((file: any) =>
          checkFileTypeOtherZip(file)
        );
        const zipFilesRetry = fileRetry?.filter(
          (file: any) => !checkFileTypeOtherZip(file)
        );

        const imgFilesFinal = isRetry
          ? imgFilesRetry?.length > 0
            ? imgFilesRetry
            : files.filter(
                (file: any) =>
                  file.status === fileStatusOptions.failed &&
                  checkFileTypeOtherZip(file)
              )
          : [
              ...files.filter(
                (file: any) =>
                  file.status === fileStatusOptions.save &&
                  !filesInProgress.includes(file.fullPath) &&
                  checkFileTypeOtherZip(file)
              ),
            ]; // they can be array-like object

        const zipFilesFinal = isRetry
          ? zipFilesRetry?.length > 0
            ? zipFilesRetry
            : files.filter(
                (file: any) =>
                  file.status === fileStatusOptions.failed &&
                  !checkFileTypeOtherZip(file)
              )
          : [
              ...files.filter(
                (file: any) =>
                  (file.status === fileStatusOptions.save ||
                    file.status === fileStatusOptions.uploading) &&
                  !filesInProgress.includes(file.fullPath) &&
                  !checkFileTypeOtherZip(file)
              ),
            ]; // they can be array-like object

        const sliceFiles = [];
        const filesLength = imgFilesFinal.length + zipFilesFinal.length;

        isRetry
          ? !fileRetry?.length
            ? dispatch({ type: ACTIONS.RETRY_ALL })
            : dispatch({ type: ACTIONS.UPLOADING, payload: fileRetry })
          : dispatch({ type: ACTIONS.UPLOADING_ALL });
        dispatch({
          type: ACTIONS.SET_PROGRESSING,
          payload: true,
        });

        handleBatchDeduplicated(false);

        for (let i = 0; i < filesLength; i += perUpload) {
          sliceFiles.push(imgFilesFinal.slice(i, i + perUpload));
        }

        const filesUpload = [];
        const sliceFilesLength = sliceFiles.length;

        for (let i = 0; i < perUpload; i++) {
          const childFilesUpload = [];

          for (let j = 0; j < sliceFilesLength; j++) {
            if (sliceFiles[j].length > i)
              childFilesUpload.push(sliceFiles[j][i]);
          }
          filesUpload.push(childFilesUpload);
        }

        await Promise.all(
          filesUpload.map((files) => {
            if (!files?.length) {
              return undefined;
            }
            return importFiles(files, batchId, progressId);
          })
        )
          .then(() => {
            if (zipFilesFinal?.length) {
              setIsZipProgress(true);
              importFiles(zipFilesFinal, batchId, progressId);
            }
          })
          .catch((error) => {
            console.error(error.message);
            setIsZipProgress(false);
          });

        const isUploadingFiles = !!files.find(
          (file: any) => file.status === fileStatusOptions.uploading
        );

        // Handle when zip uploading
        !isUploadingFiles &&
          dispatch({
            type: ACTIONS.SET_PROGRESSING,
            payload: false,
          });
      }
    },
    [
      isClear,
      batchData,
      files,
      projectId,
      pause,
      handleCreateDataBatch,
      importFiles,
      filesInProgress,
    ]
  );

  const getFileTree = useCallback((files) => {
    const finalFiles: any = [];

    files.forEach((file: any) => {
      const splitFullPath = file.fullPath
        .replace(/^\/+/, "")
        .replace(/\/+$/, "")
        .split("/");

      if (splitFullPath.length > 1) {
        const parentPath = splitFullPath[0];

        const findPath = finalFiles.find(
          (finalFile: any) => finalFile.fullPath === parentPath
        );

        if (findPath) {
          findPath.size += file.file.size;
          findPath.children.push({ ...file, type: "image" });
        } else {
          finalFiles.push({
            checked: false,
            progress: 0,
            type: "folder",
            status: fileStatusOptions.save,
            fullPath: parentPath,
            size: file.file.size,
            children: [{ ...file, type: "image" }],
          });
        }
      } else {
        const fileNameSplit = file.file.name.split(".");
        const fileType =
          fileNameSplit?.[fileNameSplit?.length - 1].toLowerCase();

        let type = "image";

        if (fileType === "zip") {
          type = "zip";
        } else if (fileType === "csv") {
          type = "csv";
        }

        finalFiles.push({ type, ...file });
      }
    });

    return finalFiles.map((file: any) => {
      const children = file?.children;

      if (children) {
        const childrenTotal = children.length;
        const childrenCompleted = children.filter(
          (file: any) => file.status === fileStatusOptions.completed
        ).length;
        const childrenFailed = children.find(
          (file: any) => file.status === fileStatusOptions.failed
        );
        const childrenUploading = children.find(
          (file: any) => file.status === fileStatusOptions.uploading
        );
        const childrenPaused = children.find(
          (file: any) => file.status === fileStatusOptions.paused
        );

        file.progress = (childrenCompleted / childrenTotal) * 100;

        if (childrenTotal === childrenCompleted) {
          file.status = fileStatusOptions.completed;
        } else if (childrenPaused) {
          file.status = fileStatusOptions.paused;
        } else if (childrenUploading) {
          file.status = fileStatusOptions.uploading;
        } else if (childrenFailed) {
          file.status = fileStatusOptions.failed;
        }

        const childrenChecked = children.filter(
          (file: any) => file.checked
        ).length;

        if (childrenChecked === childrenTotal) {
          file.checked = true;
        }
      }

      return file;
    });
  }, []);

  const fileTree = useMemo(() => getFileTree(files), [files]);

  const handleMessageUploadCompleted = useCallback(
    (completed, failed, total) => {
      if (completed + failed === total) {
        // reset pausing and progressing of raw uploading
        handlePause(false);
        dispatch({
          type: ACTIONS.SET_PROGRESSING,
          payload: false,
        });
        setIsZipProgress(false);

        Message.success({
          // title: "Invalid or unknown formats",
          content: `${completed} ${
            completed > 1 ? "items" : "item"
          } uploaded, ${failed} ${failed > 1 ? "items" : "item"} failed.`,
        });
      }
    },
    []
  );

  const handleShowMess = useMemo(() => {
    const fileTreeFinal = getFileTree(files);

    let completed = 0;
    let failed = 0;

    fileTreeFinal.forEach((item: any) => {
      const itemStatus = item.status;

      if (itemStatus === fileStatusOptions.completed) {
        completed += 1;
      } else if (itemStatus === fileStatusOptions.failed) {
        failed += 1;
      }
    });

    if (fileTreeFinal?.length && isShowMessCompleted < 2) {
      handleMessageUploadCompleted(completed, failed, fileTreeFinal?.length);
    }

    return {
      completed,
      failed,
    };
  }, [
    isShowMessCompleted,
    files.filter(
      (file: any) =>
        file.status === fileStatusOptions.completed ||
        file.status === fileStatusOptions.failed
    ).length,
  ]);

  const percentImportProgress = useMemo(() => {
    const fileTotal = files.length;

    let countProgress = 0;
    let sumProgress = 0;
    const findFilesUploadingLength = files?.filter((file: any) => {
      countProgress += file?.progress ? 1 : 0;
      sumProgress += file?.progress ?? 0;
      return (
        file.status === fileStatusOptions.save ||
        file.status === fileStatusOptions.uploading ||
        file.status === fileStatusOptions.paused
      );
    })?.length;

    const percent =
      findFilesUploadingLength === 0
        ? 0
        : sumProgress / (countProgress === 0 ? 1 : countProgress);

    return fileTotal === 0
      ? 0
      : Math.floor(
          ((fileTotal - findFilesUploadingLength) * 100 + percent) / fileTotal
        );

    // return fileTotal
    //   ? ((sumProgress / countProgress + fileUploaded) / fileTotal) * 100
    //   : 0;
  }, [files]);

  const dataImportProgress = useMemo(() => {
    const fileTreeFinal = getFileTree(files);

    let completed = 0;
    let failed = 0;

    fileTreeFinal.forEach((item: any) => {
      const itemStatus = item.status;

      if (itemStatus === fileStatusOptions.completed) {
        completed += 1;
      } else if (itemStatus === fileStatusOptions.failed) {
        failed += 1;
      }
    });

    return {
      completed,
      failed,
    };
  }, [files]);

  const filesSelected = useMemo(() => {
    return files?.filter((file: any) => file.checked);
  }, [files?.filter((file: any) => file.checked)?.length]);

  const filesLength = useMemo(() => {
    return files?.length ?? 0;
  }, [files?.length]);

  const filesCompletedLength = useMemo(
    () =>
      files?.filter?.(
        (file: any) => file.status === fileStatusOptions.completed
      )?.length ?? 0,
    [files]
  );

  const filesLengthMess = useMemo(() => {
    return filesLength > 1 ? `${filesLength} files` : `${filesLength} file`;
  }, [filesLength]);

  const filesSelectedLength = useMemo(() => {
    return filesSelected?.length ?? 0;
  }, [filesSelected?.length]);

  const filesSelectedLengthMess = useMemo(() => {
    return filesSelectedLength > 1
      ? `${filesSelectedLength} files selected`
      : `${filesSelectedLength} file selected`;
  }, [filesSelectedLength]);

  const handleUpdateImportData = useCallback((type, payload) => {
    dispatch({ type, payload });
  }, []);

  const handleSaveFiles = useCallback(
    (files) => {
      // reset clear default
      isClear && handleClear(false);

      handleBatchDeduplicated(false);

      handleUpdateImportData(
        ACTIONS.SAVE,
        files.map((file: any) => {
          return {
            progress: null,
            checked: false,
            status: fileStatusOptions.save,
            ...file,
          };
        })
      );
    },
    [isClear]
  );

  const handleRemoveFiles = useCallback((files) => {
    handleUpdateImportData(ACTIONS.REMOVE, files);
  }, []);

  const handleSelectFiles = useCallback((files) => {
    handleUpdateImportData(ACTIONS.SELECT, files);
  }, []);

  const handleAddInvalidFiles = useCallback((invalidFiles) => {
    handleUpdateImportData(ACTIONS.ADD_INVALID_FILES, invalidFiles);
  }, []);

  const handleChangeBatchName = useCallback((batchName) => {
    setIsBatchNameAlready(false);
    handleUpdateImportData(ACTIONS.CHANGE_BATCH_NAME, batchName);
  }, []);

  const handleUpdateBatchData = useCallback((batchDetail) => {
    handleUpdateImportData(ACTIONS.UPDATE_BATCH_DATA, batchDetail);
  }, []);

  const fetchProjectDataTypes = useCallback(async () => {
    if (!currentProject?.id) {
      return;
    }

    handleUpdateImportData(ACTIONS.WAITING_STATUS, true);

    const res = await api.callApi("projectDataTypes", {
      params: { pk: currentProject.id },
    });

    handleUpdateImportData(ACTIONS.WAITING_STATUS, false);

    if (res?.success) {
      handleUpdateImportData(
        ACTIONS.LOAD_DATA_TYPES,
        [...res?.data, ".zip"] || []
      );
    }
  }, [currentProject]);

  const handleCancelZip = useCallback(
    async (taskUploadId) => {
      await api.callApi("cancelZip", {
        params: { pk: currentProject.id },
        body: { task_upload_id: taskUploadId },
      });
    },
    [currentProject]
  );

  const handleCloseImport = useCallback(async () => {
    uploadZipTasks?.forEach((task: string) => {
      handleCancelZip(task);
    });
    setIsShowMessCompleted(0);

    dispatch({
      type: ACTIONS.RESET,
    });
  }, [uploadZipTasks?.toString(), handleCancelZip]);

  const failedFiles = useMemo(() => {
    const failedList = files
      .filter((file: any) => file.status === fileStatusOptions.failed)
      .map((file: any) => ({
        fileName: file.file.name,
        imagePath: file.fullPath,
        status: "Failed",
      }));

    const invalidList = invalidFiles.map((file: any) => ({
      ...file,
      status: "Invalid",
    }));

    return [...invalidList, ...failedList];
  }, [files, invalidFiles]);

  const handleOpenImportModal = useCallback(
    (dataBatchDetail = {}) => {
      !files?.length && handleUpdateBatchData(dataBatchDetail);
      isCancelUploadingProgress && setIsCancelUploadingProgress(false);
      setOpenImport(true);
    },
    [isCancelUploadingProgress, files, handleUpdateBatchData]
  );

  const handleCloseImportModal = useCallback(() => {
    setIsBatchNameAlready(false);
    setOpenImport(false);
    handlePause(false);
  }, []);

  const handleMinusImportModal = useCallback(() => {
    setOpenImport(false);
  }, []);

  return (
    <BatchImportDataContext.Provider
      value={{
        isZipProgress,
        isBatchNameAlready,
        openImport,
        batchImportState,
        fileTree,
        filesCompletedLength,
        filesSelected,
        filesLength,
        filesLengthMess,
        filesSelectedLength,
        filesSelectedLengthMess,
        percentImportProgress,
        dataImportProgress,
        dispatch,
        handleUpdateImportData,
        handleSaveFiles,
        handleRemoveFiles,
        handleSelectFiles,
        handleAddInvalidFiles,
        handleChangeBatchName,
        handleUpdateBatchData,
        fetchProjectDataTypes,
        onUploadFiles,
        pause: pause.current,
        handlePause,
        handleClear,
        handleCancelUploading,
        handleCloseImport,

        handleOpenImportModal,
        handleCloseImportModal,
        handleMinusImportModal,

        isContinue,
        setIsContinue,

        funcCloseModal,
        setFuncCloseModal,

        funcFetchDataSets,
        setFuncFetchDataSets,

        expandMiniBar,
        setExpandMiniBar,

        failedFiles,
        rejectUploadingProgress,
      }}
    >
      {children}
    </BatchImportDataContext.Provider>
  );
};

export const useBatchImportData = () => {
  return useContext(BatchImportDataContext);
};
