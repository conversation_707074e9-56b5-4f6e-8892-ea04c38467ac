.taureau-checkbox
    display: flex;
    align-items: center;
    gap: 10px;

    &__checkbox
        display: flex
        justify-content: center
        align-items: center
        width: 20px;
        height: 20px;

        input[type=checkbox]
            position: relative;
            cursor pointer

            transition: all .3s

            &:hover
                border-color: #C3CAD9

        input[type=checkbox]:before
            content: "";
            display: block;
            position: absolute;
            width: 20px;
            height: 20px;
            top: -4px;
            left: -6px;

            border-radius: 5px;
            border: 2px solid var(--gray-blue-grey-blue-96, #F2F3F5);
            background: var(--white-white, #FFF);
            box-shadow: 0px 2px 10px 0px rgba(38, 51, 77, 0.03);

        input[type=checkbox]:checked:before 
            content: "";
            display: block;
            position: absolute;
            width: 22px;
            height: 22px;
            top: -4px;
            left: -6px;

            border-radius: 5px;
            box-shadow: 0px 2px 10px 0px rgba(51, 97, 255, 0.15);
            background-color:#3361FF;

        input[type=checkbox]:checked:after 
            content: "";
            display: block;
            width: 5px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
            position: absolute;
            top 1px
            left: 3px;

    &__label
        cursor pointer

        color: var(--gray-blue-grey-blue-55, #62708C);
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;

        transition: color .3s

    .checked
        color: var(--blue-blue, #3361FF);
