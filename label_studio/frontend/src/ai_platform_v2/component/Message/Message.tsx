import { Alert } from "@taureau/ui";
import { message } from "antd";
import React from "react";

import "./message.styles.scss";

interface MessageProps {
  title?: string;
  content?: string;
}
const Message = {
  success: ({ title = "Success", content }: MessageProps) => {
    message.open({
      className: "tas-message-custom",
      content: (
        <div className="max-w-[1000px]">
          <Alert.Success title={title} content={content} />
        </div>
      ),
    });
  },
  error: ({ title = "Error", content }: MessageProps) => {
    message.open({
      className: "tas-message-custom",
      content: (
        <div className="max-w-[1000px]">
          <Alert.Error title={title} content={content} />
        </div>
      ),
    });
  },
  info: ({ title = "Inform", content }: MessageProps) => {
    message.open({
      className: "tas-message-custom",
      content: (
        <div className="max-w-[1000px]">
          <Alert.Info title={title} content={content} />
        </div>
      ),
    });
  },
  warning: ({ title = "Warning", content }: MessageProps) => {
    message.open({
      className: "tas-message-custom",
      content: (
        <div className="max-w-[1000px]">
          <Alert.Warning title={title} content={content} />
        </div>
      ),
    });
  },
};

export default Message;
