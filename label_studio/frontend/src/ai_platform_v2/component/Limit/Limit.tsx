import { Button } from "@taureau/ui";
import React, { useMemo } from "react";

const IconError = () => (
  <svg
    width="100"
    height="100"
    viewBox="0 0 100 100"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M32.8125 12.5C32.8125 9.04822 35.6107 6.25 39.0625 6.25H57.8125C61.2643 6.25 64.0625 9.04822 64.0625 12.5V15.625H85.9375C89.3893 15.625 92.1875 18.4232 92.1875 21.875V73.4375C92.1875 76.8893 89.3893 79.6875 85.9375 79.6875H64.0625V85.9375C64.0625 89.3893 61.2643 92.1875 57.8125 92.1875H39.0625C35.6107 92.1875 32.8125 89.3893 32.8125 85.9375V79.6875H14.0625C10.6107 79.6875 7.8125 76.8893 7.8125 73.4375V21.875C7.8125 18.4232 10.6107 15.625 14.0625 15.625H32.8125V12.5Z"
      fill="#433A3F"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.4375 18.75H85.9375C87.6634 18.75 89.0625 20.1491 89.0625 21.875V73.4375C89.0625 75.1634 87.6634 76.5625 85.9375 76.5625H23.4375C21.7116 76.5625 20.3125 75.1634 20.3125 73.4375V21.875C20.3125 20.1491 21.7116 18.75 23.4375 18.75ZM52.5214 30.4406C54.3974 27.9784 58.1026 27.9784 59.9786 30.4406L78.669 54.9717C81.0199 58.0572 78.8195 62.5 74.9404 62.5H37.5597C33.6806 62.5 31.4802 58.0572 33.8311 54.9717L52.5214 30.4406Z"
      fill="#33BFFF"
    />
    <path
      d="M43.75 79.6875H60.9375V85.9375C60.9375 87.6634 59.5384 89.0625 57.8125 89.0625H46.875C45.1491 89.0625 43.75 87.6634 43.75 85.9375V79.6875Z"
      fill="#F1EEE1"
    />
    <path
      d="M43.75 12.5C43.75 10.7741 45.1491 9.375 46.875 9.375H57.8125C59.5384 9.375 60.9375 10.7741 60.9375 12.5V15.625H43.75V12.5Z"
      fill="#F1EEE1"
    />
    <path
      d="M53.9062 39.8438C53.9062 38.5493 54.9556 37.5 56.25 37.5C57.5444 37.5 58.5938 38.5493 58.5938 39.8438V46.0938C58.5938 47.3882 57.5444 48.4375 56.25 48.4375C54.9556 48.4375 53.9062 47.3882 53.9062 46.0938V39.8438Z"
      fill="#F1EEE1"
    />
    <path
      d="M53.9062 53.9062C53.9062 52.6118 54.9556 51.5625 56.25 51.5625C57.5444 51.5625 58.5938 52.6118 58.5938 53.9062C58.5938 55.2007 57.5444 56.25 56.25 56.25C54.9556 56.25 53.9062 55.2007 53.9062 53.9062Z"
      fill="#F1EEE1"
    />
  </svg>
);

export const LimitWrapper = ({
  content = "Sorry about that, but you’ve exceeded the 5 free projects for your plan",
  onClickRequest,
}: any) => {
  return (
    <div className="flex flex-col items-center gap-[24px]">
      <div className="flex flex-col items-center gap-[20px]">
        <IconError />

        <div className="flex flex-col items-center gap-[4px]">
          <div className="text-[#334466] text-[20px] font-semibold leading-[27px]">
            REACHED RESOURCE LIMIT
          </div>
          <div className="text-[#6B7A99] text-[14px] font-normal leading-[21px]">
            {content}
          </div>
        </div>
      </div>

      <Button
        className="h-[40px] w-fit px-[20px] py-[10px] text-[#fff] text-[16px] font-medium leading-[24px] rounded-[20px]"
        theme="Primary"
        onClick={() => {
          window.open(
            "mailto:<EMAIL>?subject=Request For More Resources"
          );
          onClickRequest?.();
        }}
      >
        Request for more
      </Button>
    </div>
  );
};
