import React, { useState } from "react";
import { Popover, Tooltip } from "antd";
import { Button } from "@taureau/ui";
import IconSettingSystem from "@/ai_platform_v2/assets/Icons/IconSettingSystem";
import { ContentSettingSystem } from "./ContentSettingSystem";
import "./PopoverSettingSystem.scss";
import classNames from "classnames";

export const PopoverSettingSystem = () => {
  const [isOpen, setOpen] = useState(false);

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
  };

  const hide = () => {
    setOpen(false);
  };

  return (
    <Popover
      content={<ContentSettingSystem hide={hide} />}
      trigger="click"
      placement="bottomRight"
      overlayClassName="popover-setting-system"
      open={isOpen}
      onOpenChange={handleOpenChange}
    >
      <Tooltip placement="left" title="Settings">
        <Button
          className={classNames("taureau-btn-navbar", {
            "bg-[#3361FF1A]": isOpen,
            // "hover:bg-[#f5f6f7] hover:brightness-[0.98]": !isOpen,
          })}
          size="sm"
          icon={
            <IconSettingSystem
              color={isOpen ? "#3361FF" : "#C3CAD9"}
              size={30}
            />
          }
          corner="Circle"
        ></Button>
      </Tooltip>
    </Popover>
  );
};
