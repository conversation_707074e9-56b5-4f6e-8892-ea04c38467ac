import IconAccessToken from "@/ai_platform_v2/assets/Icons/IconAccessToken";
import IconNotificationSetting from "@/ai_platform_v2/assets/Icons/IconNotificationSetting";
import IconProfile from "@/ai_platform_v2/assets/Icons/IconProfile";
import IconSystemManagement from "@/ai_platform_v2/assets/Icons/IconSystemManagement";
import IconThemes from "@/ai_platform_v2/assets/Icons/IconThemes";
import useSettingProjectStore from "@/ai_platform_v2/stores/settingProjectStore";
import useSystemManagementPermission from "@/pages/SystemManagement/hooks/useSystemManagementPermission";
import { useHistory } from "react-router";

export const ContentSettingSystem = (props: any) => {
  const { hide } = props;
  const history = useHistory();

  const { canAccessSystemManagement } = useSystemManagementPermission();
  const options = [
    {
      icon: <IconSystemManagement size={26} />,
      label: "System Management",
      router: "/system-management-v2",
    },
    {
      icon: <IconProfile size={26} />,
      label: "Profile",
      router: "/setting-account-v2",
    },
    {
      icon: <IconNotificationSetting size={26} />,
      label: "Notification",
      router: "/setting-account-v2?tab=notifications",
    },
  ];

  return (
    <div className="flex flex-col gap-1">
      {options.map((item, i) => {
        if (!canAccessSystemManagement && item.label === "System Management") {
          return <></>;
        }
        return (
          <div key={`option-setting-${i}`}>
            <div
              onClick={() => {
                hide();
                history.push(item.router);
              }}
              key={`option-setting-${item.label}`}
              className="flex gap-2 items-center py-[2px] pl-1 pr-2 hover:bg-gray-blue-97 cursor-pointer rounded-[5px]"
            >
              {item.icon}

              <span className="text-[14px] leading-[21px] text-[#346]">
                {item.label}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );
};
