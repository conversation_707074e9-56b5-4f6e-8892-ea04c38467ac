import { memo, useCallback, useEffect, useMemo, useRef, useState } from "react";

import EditorModalComponent from "./EditorModal";
import MiniEditor from "./MiniEditor";
import useEditorModalStore from "./store";

import useDeduplicationStore from "@/ai_platform_v2/pages/TemporaryStorage/BatchDetail/Deduplicate/store";
import useAdvancedConfigStore from "@/ai_platform_v2/pages/SettingsProject/component/AdvancedAnnotation/store";
import { useAPI } from "@/providers/ApiProvider";
import { useCurrentUser } from "@/providers/CurrentUser";
import { useParams } from "@/providers/RoutesProvider";
import { Modal } from "antd";
import { w3cwebsocket } from "websocket";
import Message from "../Message/Message";
import { useProject } from "@/providers/ProjectProvider";

const baseURL = new URL(APP_SETTINGS.hostname || location.origin);
const ws_scheme = window.location.protocol === "https:" ? "wss" : "ws";

const maxReconnectTime = 2;

const GlobalEditor = () => {
  const {
    setEditorRef,
    isOpenEditor,
    isHideScreen,
    resetState,
    setIsHideScreen,
    setOpenEditor,
    setCurrentFile,
    setLastFileId,

    isDestroyOldEditor,
  } = useEditorModalStore((state) => ({
    setEditorRef: state.setEditorRef,
    isOpenEditor: state.isOpenEditor,
    isHideScreen: state.isHideScreen,

    resetState: state.resetState,

    setIsHideScreen: state.setIsHideScreen,
    setOpenEditor: state.setOpenEditor,
    setCurrentFile: state.setCurrentFile,
    setLastFileId: state.setLastFileId,

    isDestroyOldEditor: state.isDestroyOldEditor,
  }));

  const {
    setNeedForceClose: setNeedForceCloseDeduplication,
    setCancelingReport: setCancelingReportDeduplication,
    setNeedRefetchData: setNeedRefetchDataDeduplication,
  } = useDeduplicationStore((state) => ({
    setNeedForceClose: state.setNeedForceClose,
    setCancelingReport: state.setCancelingReport,
    setNeedRefetchData: state.setNeedRefetchData,
  }));

  const api = useAPI();

  const { setAdvancedConfig } = useAdvancedConfigStore((state) => {
    return {
      setAdvancedConfig: state.setAdvancedConfig,
    };
  });

  const ref = useRef(null);

  const params = useParams();

  const currentModuleId = params.moduleId;

  const currentProjectId = params.projectId;

  const urlParams = new URLSearchParams(location.search);

  const batchId = urlParams.get("batchId");

  const { user } = useCurrentUser();

  const { fetchProject } = useProject();

  const [reconnectTime, setReconnectTime] = useState(0);

  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");

  const [isOpenModalConfirm, setOpenModalConfirm] = useState(false);

  const [isOpenModalReload, setIsOpenModalReload] = useState(false);

  const [handleClickOk, setHandleClickOk] = useState<() => void>();

  const handleFetchAdvancedConfig = useCallback(async () => {
    try {
      if (currentProjectId) {
        const res = await api.callApi("getAdvancedConfig", {
          params: {
            projectId: currentProjectId,
          },
        });

        if (res.success) {
          setAdvancedConfig(res.data);
        }
      }
    } catch (error) {
      Message.error({ title: "Error", content: "Get Advanced Config Error" });
    }
  }, [currentProjectId]);

  useEffect(() => {
    handleFetchAdvancedConfig();
  }, [currentProjectId]);

  const forceCloseEditor = useCallback(() => {
    setIsHideScreen(false);
    setOpenEditor(false);
    setCurrentFile(null);
    setLastFileId(null);
    setOpenModalConfirm(false);
  }, []);

  const wsEditor = useMemo(
    () => new w3cwebsocket(`${ws_scheme}://${baseURL.host}/ws/prj/`),
    [reconnectTime]
  );

  const heartbeat = () => {
    wsEditor.send(
      JSON.stringify({
        message: "heartbeat",
        user: user?.dms_id,
        userName: user?.username,
      })
    );
    setTimeout(heartbeat, 5 * 60 * 1000);
  };

  wsEditor.onopen = () => {
    heartbeat();
  };

  wsEditor.onclose = () => {
    if (reconnectTime < maxReconnectTime) {
      setReconnectTime(reconnectTime + 1);
    }
  };

  wsEditor.onmessage = (data) => {
    const msg = JSON.parse(data.data)?.message;

    const { type, project_id, batchId: batchIdWS, projectId } = msg;

    const isCurrentProject = (projectId || project_id) === currentProjectId;
    const isCurrentBatch = batchId === batchIdWS;

    if (isCurrentProject) {
      if (isOpenEditor || isHideScreen) {
        setHandleClickOk(() => forceCloseEditor);
        switch (type) {
          case "PROJECT_SETTINGS_TOOLSCONFIG":
            setTitle("Tool configuration of project has just been updated");
            setContent(
              "To apply new changes, the Editor will be automatically closed. Please try a new one!"
            );
            setOpenModalConfirm(true);
            break;

          case "PROJECT_SETTINGS_WORKFLOW":
            setTitle("Project workflow has just been updated");
            setContent(
              "To apply new changes, the Editor will be automatically closed. Please try a new one! "
            );
            setOpenModalConfirm(true);
            break;

          case "PROJECT_SETTINGS_METADATA":
            setTitle("Meta data in project has just been updated");
            setContent(
              "To apply new changes, the Editor will be automatically closed. Please try a new one! "
            );
            setOpenModalConfirm(true);
            break;

          case "PROJECT_SETTINGS_ADVANCED_ANNOTATION_ONEK":
            setTitle("Tool configuration of project has just been updated");
            setContent(
              "To apply new changes, the Editor will be automatically closed. Please try a new one!"
            );
            setOpenModalConfirm(true);

            setHandleClickOk(() => () => {
              fetchProject(projectId, true);
              forceCloseEditor();
            });

            break;

          default:
            break;
        }
      }

      switch (type) {
        case "PROJECT_SETTINGS_MEMBERS":
          setTitle("You have just been removed from project");
          setContent(
            "You are being redirected to Home screen because you are no longer member of this project. "
          );

          setHandleClickOk(() => () => {
            window.location.pathname = "/home";
          });
          setOpenModalConfirm(true);

          break;

        case "PROJECT_SETTINGS_MEMBER_ROLE-Assignment_ON-NOT_DatasetManagement.Update-HAVE_ProjectsManagement.Update":
        case "PROJECT_SETTINGS_MEMBER_ROLE-NOT_DatasetManagement.Update-HAVE_ProjectsManagement.Update":
          setTitle("Your project role has just been updated");
          setContent(
            "You are being redirected to other screen because you are no longer granted permission to access to this page."
          );
          setHandleClickOk(() => () => {
            window.location.pathname = `/modules/${currentModuleId}/projects/${currentProjectId}/settings-project`;
          });

          setOpenModalConfirm(true);
          break;

        case "PROJECT_SETTINGS_MEMBER_ROLE-Assignment_ON-NOT_DatasetManagement.Update-NOT_ProjectsManagement.Update":
        case "PROJECT_SETTINGS_MEMBER_ROLE-NOT_DatasetManagement.Update-NOT_ProjectsManagement.Update":
          setTitle("Your project role has just been updated");
          setContent(
            "You are being redirected to other screen because you are no longer granted permission to access to this page."
          );
          setHandleClickOk(() => () => {
            window.location.pathname = "/home";
          });
          setOpenModalConfirm(true);
          break;

        case "PROJECT_SETTINGS_MEMBER_ROLE-Assignment_ON-HAVE_DatasetManagement.Update-NOT_ProjectsManagement.Update":
        case "PROJECT_SETTINGS_MEMBER_ROLE-HAVE_DatasetManagement.Update-NOT_ProjectsManagement.Update":
          setTitle("Your permission has just been changed. Please retry!");
          setContent(
            "You are being redirected to other screen because you are no longer granted permission to access to this page."
          );
          setHandleClickOk(() => () => {
            window.location.pathname = `/modules/${currentModuleId}/projects/${currentProjectId}/dashboard-v2`;
          });
          setOpenModalConfirm(true);
          break;

        case "PROJECT_SETTINGS_DEDUPLICATION_MODE_ON2OFF":
          setNeedForceCloseDeduplication(true);
          break;

        case "PROJECT_SETTINGS_DEDUPLICATION_MODE_OFF2ON":
          setNeedRefetchDataDeduplication(true);
          break;

        case "Deduplication.Cancelling":
          if (isCurrentBatch) {
            setCancelingReportDeduplication(true);
          }
          break;

        case "PROJECT_SETTINGS_ADVANCED_ANNOTATION_ONEK":
          fetchProject(projectId, true);
          break;

        case "PROJECT_SETTINGS_SECURITYMODE":
          setIsOpenModalReload(true);
          break;
        case "PROJECT_SETTINGS_MEMBER_ROLE-Assignment_ON-HAVE_DatasetManagement.Update-HAVE_ProjectsManagement.Update":
        case "PROJECT_SETTINGS_MEMBER_ROLE-HAVE_DatasetManagement.Update-HAVE_ProjectsManagement.Update":
        default:
          break;
      }
    }
  };

  useEffect(() => {
    return () => {
      ref?.current?.destroy(); // destroy store of editor
      resetState();
      wsEditor.close();
    };
  }, [ref]);

  return (
    <>
      {isDestroyOldEditor ? (
        <div ref={ref} />
      ) : (
        <EditorModalComponent ref={ref} />
      )}
      {isHideScreen && <MiniEditor />}
      <Modal
        open={isOpenModalConfirm}
        centered
        closable={false}
        footer={null}
        bodyStyle={{ padding: "16px 20px" }}
        width={386}
        zIndex={1001}
      >
        <div className="flex flex-col">
          <div className="flex flex-row items-center gap-[11px]">
            <div className="w-10 aspect-square rounded-full flex items-center justify-center bg-[#0000001A]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <path
                  d="M10.5 7.375C10.3342 7.375 10.1753 7.44085 10.0581 7.55806C9.94085 7.67527 9.875 7.83424 9.875 8V10.5C9.875 10.6658 9.94085 10.8247 10.0581 10.9419C10.1753 11.0592 10.3342 11.125 10.5 11.125C10.6658 11.125 10.8247 11.0592 10.9419 10.9419C11.0592 10.8247 11.125 10.6658 11.125 10.5V8C11.125 7.83424 11.0592 7.67527 10.9419 7.55806C10.8247 7.44085 10.6658 7.375 10.5 7.375ZM11.075 12.7625C11.0613 12.7227 11.0424 12.6848 11.0188 12.65L10.9438 12.5562C10.8559 12.4695 10.7443 12.4108 10.623 12.3874C10.5018 12.3641 10.3763 12.3771 10.2625 12.425C10.1868 12.4567 10.117 12.501 10.0563 12.5562C9.99833 12.6146 9.9525 12.6839 9.9214 12.7601C9.89029 12.8362 9.87453 12.9177 9.875 13C9.87599 13.0817 9.89298 13.1624 9.925 13.2375C9.95307 13.3151 9.99786 13.3855 10.0562 13.4438C10.1145 13.5021 10.1849 13.5469 10.2625 13.575C10.3373 13.6081 10.4182 13.6251 10.5 13.6251C10.5818 13.6251 10.6627 13.6081 10.7375 13.575C10.8151 13.5469 10.8855 13.5021 10.9438 13.4438C11.0021 13.3855 11.0469 13.3151 11.075 13.2375C11.107 13.1624 11.124 13.0817 11.125 13C11.1281 12.9584 11.1281 12.9166 11.125 12.875C11.1142 12.8351 11.0974 12.7972 11.075 12.7625ZM10.5 4.25C9.26387 4.25 8.0555 4.61656 7.02769 5.30331C5.99988 5.99007 5.1988 6.96619 4.72576 8.10823C4.25271 9.25027 4.12894 10.5069 4.37009 11.7193C4.61125 12.9317 5.20651 14.0453 6.08059 14.9194C6.95466 15.7935 8.06831 16.3888 9.28069 16.6299C10.4931 16.8711 11.7497 16.7473 12.8918 16.2742C14.0338 15.8012 15.0099 15.0001 15.6967 13.9723C16.3834 12.9445 16.75 11.7361 16.75 10.5C16.75 9.67924 16.5883 8.86651 16.2743 8.10823C15.9602 7.34994 15.4998 6.66095 14.9194 6.08058C14.3391 5.50022 13.6501 5.03984 12.8918 4.72575C12.1335 4.41166 11.3208 4.25 10.5 4.25ZM10.5 15.5C9.5111 15.5 8.5444 15.2068 7.72215 14.6573C6.89991 14.1079 6.25904 13.327 5.88061 12.4134C5.50217 11.4998 5.40315 10.4945 5.59608 9.52455C5.789 8.55464 6.26521 7.66373 6.96447 6.96447C7.66373 6.2652 8.55465 5.789 9.52455 5.59607C10.4945 5.40315 11.4998 5.50216 12.4134 5.8806C13.3271 6.25904 14.1079 6.8999 14.6574 7.72215C15.2068 8.54439 15.5 9.51109 15.5 10.5C15.5 11.8261 14.9732 13.0979 14.0355 14.0355C13.0979 14.9732 11.8261 15.5 10.5 15.5Z"
                  fill="black"
                  fillOpacity="0.8"
                />
              </svg>
            </div>

            <span className="text-[16px] font-medium leading-[24px] text-[#000000CC]">
              Action required
            </span>
          </div>

          <div className="mt-[14px] text-[14px] font-medium leading-[21px] text-[#000000CC]">
            {title}
          </div>

          <div className="mt-[4px] text-[12px] leading-[21px] text-[#000000CC]">
            {content}
          </div>

          <div
            className="w-full h-[40px] mt-[16px] rounded-full bg-[#000000CC] flex items-center justify-center text-[14px] font-medium leading-[30px] text-[#fff] cursor-pointer"
            onClick={() => handleClickOk?.()}
          >
            OK
          </div>
        </div>
      </Modal>

      <Modal
        open={isOpenModalReload}
        centered
        closable={false}
        footer={null}
        bodyStyle={{ padding: "16px 20px" }}
        width={386}
        zIndex={1001}
      >
        <div className="flex flex-col">
          <div className="flex flex-row items-center gap-[11px]">
            <div className="w-10 aspect-square rounded-full flex items-center justify-center bg-[#0000001A]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
              >
                <path
                  d="M10.5 7.375C10.3342 7.375 10.1753 7.44085 10.0581 7.55806C9.94085 7.67527 9.875 7.83424 9.875 8V10.5C9.875 10.6658 9.94085 10.8247 10.0581 10.9419C10.1753 11.0592 10.3342 11.125 10.5 11.125C10.6658 11.125 10.8247 11.0592 10.9419 10.9419C11.0592 10.8247 11.125 10.6658 11.125 10.5V8C11.125 7.83424 11.0592 7.67527 10.9419 7.55806C10.8247 7.44085 10.6658 7.375 10.5 7.375ZM11.075 12.7625C11.0613 12.7227 11.0424 12.6848 11.0188 12.65L10.9438 12.5562C10.8559 12.4695 10.7443 12.4108 10.623 12.3874C10.5018 12.3641 10.3763 12.3771 10.2625 12.425C10.1868 12.4567 10.117 12.501 10.0563 12.5562C9.99833 12.6146 9.9525 12.6839 9.9214 12.7601C9.89029 12.8362 9.87453 12.9177 9.875 13C9.87599 13.0817 9.89298 13.1624 9.925 13.2375C9.95307 13.3151 9.99786 13.3855 10.0562 13.4438C10.1145 13.5021 10.1849 13.5469 10.2625 13.575C10.3373 13.6081 10.4182 13.6251 10.5 13.6251C10.5818 13.6251 10.6627 13.6081 10.7375 13.575C10.8151 13.5469 10.8855 13.5021 10.9438 13.4438C11.0021 13.3855 11.0469 13.3151 11.075 13.2375C11.107 13.1624 11.124 13.0817 11.125 13C11.1281 12.9584 11.1281 12.9166 11.125 12.875C11.1142 12.8351 11.0974 12.7972 11.075 12.7625ZM10.5 4.25C9.26387 4.25 8.0555 4.61656 7.02769 5.30331C5.99988 5.99007 5.1988 6.96619 4.72576 8.10823C4.25271 9.25027 4.12894 10.5069 4.37009 11.7193C4.61125 12.9317 5.20651 14.0453 6.08059 14.9194C6.95466 15.7935 8.06831 16.3888 9.28069 16.6299C10.4931 16.8711 11.7497 16.7473 12.8918 16.2742C14.0338 15.8012 15.0099 15.0001 15.6967 13.9723C16.3834 12.9445 16.75 11.7361 16.75 10.5C16.75 9.67924 16.5883 8.86651 16.2743 8.10823C15.9602 7.34994 15.4998 6.66095 14.9194 6.08058C14.3391 5.50022 13.6501 5.03984 12.8918 4.72575C12.1335 4.41166 11.3208 4.25 10.5 4.25ZM10.5 15.5C9.5111 15.5 8.5444 15.2068 7.72215 14.6573C6.89991 14.1079 6.25904 13.327 5.88061 12.4134C5.50217 11.4998 5.40315 10.4945 5.59608 9.52455C5.789 8.55464 6.26521 7.66373 6.96447 6.96447C7.66373 6.2652 8.55465 5.789 9.52455 5.59607C10.4945 5.40315 11.4998 5.50216 12.4134 5.8806C13.3271 6.25904 14.1079 6.8999 14.6574 7.72215C15.2068 8.54439 15.5 9.51109 15.5 10.5C15.5 11.8261 14.9732 13.0979 14.0355 14.0355C13.0979 14.9732 11.8261 15.5 10.5 15.5Z"
                  fill="black"
                  fillOpacity="0.8"
                />
              </svg>
            </div>

            <span className="text-[16px] font-medium leading-[24px] text-[#000000CC]">
              Persistent notification
            </span>
          </div>

          <div className="mt-[14px] text-[14px] font-medium leading-[21px] text-[#000000CC]">
            Data Security mode has been changed
          </div>

          <div className="mt-[4px] text-[12px] leading-[21px] text-[#000000CC]">
            Permission to handle data in this project has just been updated!
            <br></br>
            Click OK to renew.
          </div>

          <div
            className="w-full h-[40px] mt-[16px] rounded-full bg-[#000000CC] flex items-center justify-center text-[14px] font-medium leading-[30px] text-[#fff] cursor-pointer"
            onClick={() => {
              location.reload();
            }}
          >
            OK
          </div>
        </div>
      </Modal>
    </>
  );
};

export default memo(GlobalEditor);
