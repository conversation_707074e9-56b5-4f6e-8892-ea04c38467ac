import {
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { <PERSON><PERSON>, Modal } from "antd";

import { TasksPage } from "../../../pages/Task/Tasks";

import { dfs } from "@/ai_platform_v2/pages/TaskManagers/helper";
import { ABILITY_NEW } from "@/config/PermissionsConfig";
import { API, useAPI } from "@/providers/ApiProvider";
import { useCurrentUser } from "@/providers/CurrentUser";
import { useCheckPermission } from "@/providers/PermissionProvider";
import isEmpty from "lodash/isEmpty";
import { ListFile } from "./ListFile";
import useEditorModalStore from "./store";

import IconResize from "@/ai_platform_v2/assets/Icons/IconResize";
import { useResize } from "@/ai_platform_v2/hooks/useResize";
import { useParams } from "@/providers/RoutesProvider";
import * as ort from "onnxruntime-web";
import BarResizer from "./BarResizer";
import "./editorModal.scss";
import { IconClose, IconFullScreen, IconMinimize, IconRestore } from "./Icons";

import EmptyListData from "./EmptyListData";

const DEFAULT_SLIDER_HEIGHT = 90;
const MIN_SLIDER_HEIGHT = 70;
const MINIMIZE_SLIDER_HEIGHT = 19;

const fetcher = async (
  _url: string,
  { arg }: { arg: any }
): Promise<{
  total: number;
  items: any;
}> => {
  const result = await API[_url](arg);

  if (!result.success) {
    const error = new Error("An error occurred while fetching the data.");

    throw error;
  }

  return result;
};

interface IProps {
  [key: string]: any;
}

const EditorModal = forwardRef((props: IProps, ref) => {
  const {
    isDataManager,
    isOpen,
    setOpenEditor,
    currentFile,
    setCurrentFile,
    onHideEditor,
    onOutEditor,

    dataList,
    setDataList,
    totalItems,
    fetchUrl,
    fetchParams,
    setTotalItems,
    isInitEditor,
    setInitEditor,
    setFetchParams,

    resetState,

    fileDetail,
    setFileDetail,

    lastFileId,
    setLastFileId,
    prevPointer,
    setPrevPointer,

    nextPointer,
    setNextPointer,
    isLoadingFileDetail,
    setLoadingFileDetail,
    setAssignedMembers,
    assignedMembersWorkflow,
  } = useEditorModalStore((state) => ({
    isDataManager: state.isDataManager,
    isOpen: state.isOpenEditor,
    setOpenEditor: state.setOpenEditor,
    currentFile: state.currentFile,
    setCurrentFile: state.setCurrentFile,
    onHideEditor: state.onHideEditor,
    onOutEditor: state.onOutEditor,

    dataList: state.dataList,
    setDataList: state.setDataList,
    totalItems: state.totalItems,
    setTotalItems: state.setTotalItems,

    fetchUrl: state.fetchUrl,

    fetchParams: state.fetchParams,
    setFetchParams: state.setFetchParams,

    isInitEditor: state.isInitEditor,
    setInitEditor: state.setInitEditor,

    fileDetail: state.fileDetail,
    setFileDetail: state.setFileDetail,
    lastFileId: state.lastFileId,
    setLastFileId: state.setLastFileId,
    prevPointer: state.prevPointer,
    setPrevPointer: state.setPrevPointer,

    nextPointer: state.nextPointer,
    setNextPointer: state.setNextPointer,

    resetState: state.resetState,

    isLoadingFileDetail: state.isLoadingFileDetail,
    setLoadingFileDetail: state.setLoadingFileDetail,
    setAssignedMembers: state.setAssignedMembers,
    assignedMembersWorkflow: state.assignedMembersWorkflow,
  }));

  const [isLoadingSlider, setLoadingSlider] = useState(false);

  const trigger = useCallback((url, params) => {
    return fetcher(url, {
      arg: params,
    });
  }, []);

  const fetchData = useCallback(
    async (url = fetchUrl, params = fetchParams, type = "") => {
      if (isLoadingSlider || !url) return;

      try {
        setLoadingSlider(true);
        const res = await trigger?.(url, params);

        if (res.success) {
          if (type === "prev") {
            setDataList([...res?.items, ...dataList] ?? []);
          } else if (type === "next") {
            setDataList([...dataList, ...res?.items] ?? []);
          } else {
            setDataList(res?.items ?? []);
            setTotalItems(res?.total ?? 0);
          }
        }
      } catch (error) {
        console.log(error?.message);
      } finally {
        setLoadingSlider(false);
      }
    },
    [dataList, fetchParams, fetchUrl, isLoadingSlider]
  );

  useEffect(() => {
    if (isInitEditor && isOpen) {
      fetchData();
      setInitEditor(false);
    }
  }, [isInitEditor, isOpen]);

  const [project, setProject] = useState();

  const params = useParams();
  const currentModule = params?.moduleId;
  const currentProject = params?.projectId;

  const { user } = useCurrentUser();

  const api = useAPI();

  const resizeRef = useRef<HTMLDivElement>(null);

  const [isFullScreen, setIsFullScreen] = useState(
    localStorage.getItem("fullScreenEditor")
      ? localStorage.getItem("fullScreenEditor") === "true"
        ? true
        : false
      : false
  );

  const keepStoreEditor = useMemo(
    () => (currentFile ? lastFileId === currentFile : false),
    [currentFile]
  );

  const [isMinimizeSlider, toggleMinimizeSlider] = useState(false);

  const [widthEditor, setWidthEditor] = useState(
    localStorage.getItem("widthEditor")
      ? parseInt(localStorage.getItem("widthEditor"))
      : (screen.width * 2) / 3
  );

  const [heightEditor, setHeightEditor] = useState(
    localStorage.getItem("heightEditor")
      ? parseInt(localStorage.getItem("heightEditor"))
      : (screen.height * 2) / 3
  );

  const [sliderHeight, setSliderHeight] = useState(DEFAULT_SLIDER_HEIGHT);

  const resizeSliderHeight = useCallback((value?: number) => {
    if (value) {
      if (value < MIN_SLIDER_HEIGHT) {
        setSliderHeight(
          value < MINIMIZE_SLIDER_HEIGHT ? MINIMIZE_SLIDER_HEIGHT : value
        );
        toggleMinimizeSlider(true);
      } else {
        toggleMinimizeSlider(false);
        setSliderHeight(value);
      }
    } else {
      setSliderHeight(DEFAULT_SLIDER_HEIGHT);
    }
  }, []);

  const { initResize, resetResize, handleMouseUp } = useResize({
    elementRef: resizeRef,
    onResize: resizeSliderHeight,
    onReset: resizeSliderHeight,
    initialHeight: sliderHeight,
  });

  const { hasPermissionAllScope } = useCheckPermission();

  const canUpdateMetaData = hasPermissionAllScope(
    ABILITY_NEW.can_update_dataset,
    currentModule,
    currentProject
  );

  const canAssignTask = hasPermissionAllScope(
    ABILITY_NEW.can_assign_task,
    currentModule,
    currentProject
  );

  const canViewDataset = hasPermissionAllScope(
    ABILITY_NEW.can_view_dataset,
    currentModule,
    currentProject
  );

  const canUpdateTags = hasPermissionAllScope(
    ABILITY_NEW.can_update_tags,
    currentModule,
    currentProject
  );

  const { workflow, setWorkflow, assignmentConfig, setAssignmentConfig } =
    useEditorModalStore((state) => ({
      workflow: state.workflow,
      setWorkflow: state.setWorkflow,
      assignmentConfig: state.assignmentConfig,
      setAssignmentConfig: state.setAssignmentConfig,
    }));

  const orderWorkflow = useCallback(() => {
    if (project?.annotationWorkFlow) {
      const workflow = JSON.parse(project?.annotationWorkFlow);

      const nodes = [];
      let newNode;
      let completeNode;

      workflow.node.map((node) => {
        nodes.push(node.id);
        if (node?.type === "New") {
          newNode = node.id;
        } else if (node?.type === "Completed") {
          completeNode = node.id;
        }
      });

      const results = dfs({
        edge: workflow.edge,
        start: newNode,
        end: completeNode,
      });

      const result = [];

      results.map((i) => {
        workflow.node.map((node) => {
          if (node.id === i) {
            result.push({
              id: node.id,
              name: node.name,
              type: node?.type,
              settings: node.settings,
              members: node.members,
            });
          }
        });
      });

      setWorkflow(result);
    }
  }, [project?.annotationWorkFlow]);

  const [valueClass, setValueClass] = useState({});

  const [config, setConfig] = useState<any>();

  const [aisHost, setAISHost] = useState();
  const [userToken, setUserToken] = useState();
  const [samModelSession, setSamModelSession] = useState("N/A");

  const onZoomInEditor = useCallback(() => {
    setIsFullScreen(true);
    localStorage.setItem("fullScreenEditor", "true");
  }, []);

  const onZoomOutEditor = useCallback(() => {
    setIsFullScreen(false);
    localStorage.setItem("fullScreenEditor", "false");
  }, []);

  const handleResizeEditor = useCallback(
    (evt) => {
      let initialX = evt.pageX;
      let initialY = evt.pageY;
      // let newWidth = width;
      let width = widthEditor;
      let height = heightEditor;

      const onResize = (evt: MouseEvent) => {
        if (
          width + evt.pageX - initialX > screen.width / 3 &&
          height + evt.pageY - initialY > screen.height / 3
        ) {
          width = width + evt.pageX - initialX;
          height = height + evt.pageY - initialY;

          setHeightEditor(height);
          setWidthEditor(width);
          initialX = evt.pageX;
          initialY = evt.pageY;
          localStorage.setItem("widthEditor", width);
          localStorage.setItem("heightEditor", height);
        }
      };

      const stopResize = (evt) => {
        document.removeEventListener("mousemove", onResize);
        document.removeEventListener("mouseup", stopResize);
        document.body.style.removeProperty("user-select");
      };

      document.addEventListener("mousemove", onResize);
      document.addEventListener("mouseup", stopResize);
      document.body.style.userSelect = "none";
    },
    [heightEditor, widthEditor]
  );

  const fetchClass = useCallback(async () => {
    if (currentProject) {
      const res_class = await api.callApi("projectClassTree", {
        params: {
          pk: currentProject,
        },
      });

      if (res_class?.success) {
        const trees: any = {};

        res_class.data.map((item: any) => {
          const key = item.key.slice(29);

          trees[key] = item.value;
        });
        setValueClass(trees);
      }
    }
  }, [currentProject]);

  const fetchProject = useCallback(async () => {
    if (currentProject) {
      const response = await api.callApi("project", {
        params: {
          pk: currentProject,
        },
      });

      if (response?.success) {
        setProject(response.data);

        if (response?.data?.enableAvancedAnnotation) {
          setConfig(`
            <View>
              <Image name="img" value="$image" editControl="true"></Image>
              <OneK name="onek" toName="img"></OneK>
            </View>
            `);
        } else if (response?.data?.labelConfig) {
          setConfig(response?.data?.labelConfig);
        } else {
          setConfig(`
            <View>
              <Image name="img" value="$image"></Image>
            </View>
          `);
        }
      }
    }
  }, [currentProject]);

  const fetchDetailFile = useCallback(async () => {
    try {
      if (currentProject) {
        if (isLoadingFileDetail) return;
        setLoadingFileDetail(true);
        const res = await api.callApi("dataManager", {
          params: {
            pk: currentFile,
            project: currentProject,
          },
          errorFilter: (error) => {
            return true;
          },
        });

        if (!res?.success) {
          setFileDetail(undefined);
          return;
        }
        setFileDetail(res);

        // if (dataList.length) {
        //   const convertedData = insertAnElementToArray(
        //     dataList,
        //     res?.data,
        //     isDataManager
        //   );

        //   updateDataList(convertedData);
        // }
      }
    } catch (error) {
      console.log(error?.message);
    } finally {
      setLoadingFileDetail(false);
      // setInitEditor(false);
    }
  }, [
    currentFile,
    currentProject,
    dataList,
    isDataManager,
    isLoadingFileDetail,
  ]);

  const fetchSettingAssign = useCallback(async () => {
    try {
      if (currentProject) {
        const res = await api.callApi("projectTaskAssignmentConfigs", {
          params: {
            pk: currentProject,
          },
        });

        setAssignmentConfig(res?.data);
      }
    } catch (error) {
      console.log(error);
    }
  }, [currentProject]);

  const fetchAssignedMemberWorkflow = useCallback(async () => {
    try {
      if (currentProject) {
        const res = await api.callApi("assignedMembersWorkflowInfo", {
          params: {
            pk: currentProject,
          },
          errorFilter: (error) => {
            return true;
          },
        });

        if (res?.success) {
          setAssignedMembers(res?.data);
        }
      }
    } catch (error) {
      console.log(error?.message);
    }
  }, [currentProject]);

  useEffect(() => {
    if (!isEmpty(project)) {
      orderWorkflow();
    }
  }, [project, orderWorkflow]);

  useEffect(() => {
    if (canViewDataset && currentProject && isInitEditor) {
      fetchClass();
      fetchProject();
    }
  }, [canViewDataset, currentProject, isInitEditor]);

  useEffect(() => {
    // Refetch Assignment config when reopen Editor
    if (canViewDataset && currentProject && isInitEditor) {
      fetchSettingAssign();
      fetchAssignedMemberWorkflow();
    }
  }, [canViewDataset, currentProject, isInitEditor]);

  useEffect(() => {
    if (currentFile && dataList.length) {
      if (currentFile !== lastFileId || isInitEditor) {
        if (dataList.find((n) => n.fileId === currentFile)) {
          fetchDetailFile();
          setLastFileId(currentFile);
        } else {
          setCurrentFile(dataList[0].fileId);
        }
      }
    } else {
      setFileDetail(undefined);
    }
  }, [currentFile, lastFileId, isInitEditor, dataList]);

  useEffect(() => {
    (async () => {
      const resAISHost = await api.callApi("getAISHost");

      if (resAISHost?.data) {
        setAISHost(resAISHost.data);
      }

      const userTokenRes: any = await api.callApi("getUserToken");

      if (userTokenRes?.token) {
        setUserToken(userTokenRes.token);
      }

      // if (samModelSession) return;
      if (!config) return;

      if (config?.includes("sam")) {
        const onnxSession = await ort.InferenceSession.create(
          `${resAISHost.data}/api/ai_segment/sam_model`,
          { executionProviders: ["wasm"] }
        );

        setSamModelSession(onnxSession);
      } else {
        setSamModelSession("N/A");
      }
    })();
  }, [config]);

  const closeEditor = () => {
    onOutEditor();
    resetState();
    setSliderHeight(DEFAULT_SLIDER_HEIGHT);
    toggleMinimizeSlider(false);
  };

  const [archiveInEditor, setArchiveInEditor] = useState(false);

  const updateDataList = (newDataList) => {
    setDataList(newDataList);
  };

  useEffect(() => {
    if (archiveInEditor) {
      setArchiveInEditor(false);
      const index = dataList.findIndex((item) => item.fileId === currentFile);

      if (index !== -1) {
        if (dataList.length > 1) {
          setDataList(dataList.filter((item) => item.fileId !== currentFile));

          if (index >= dataList?.length - 1) {
            setCurrentFile(dataList?.[index - 1].fileId);
          } else {
            setCurrentFile(dataList?.[index + 1].fileId);
          }
        } else {
          closeEditor();
        }
      }
    }
  }, [archiveInEditor, currentFile, dataList]);

  return (
    <Modal
      width={isFullScreen ? "100%" : widthEditor}
      bodyStyle={{
        padding: 0,
      }}
      closable={false}
      onCancel={() => {
        closeEditor();
      }}
      style={{
        maxWidth: isFullScreen ? "100vw" : "calc(100vw - 32px)",
      }}
      className="modal-editor"
      title={
        <div
          style={{
            display: "flex",
            alignItems: "center",
            textAlign: "right",
            justifyContent: "space-between",
            margin: -15,
          }}
        >
          <div className="text-[#334466] font-normal leading-[20px] text-[14px]">
            {fileDetail?.data?.fileName}
          </div>
          <div>
            <Button
              onClick={() => onHideEditor()}
              type="text"
              icon={<IconMinimize />}
            ></Button>
            {!isFullScreen ? (
              <Button
                onClick={() => onZoomInEditor()}
                type="text"
                icon={<IconFullScreen />}
              ></Button>
            ) : (
              <Button
                onClick={() => onZoomOutEditor()}
                type="text"
                icon={<IconRestore />}
              ></Button>
            )}

            <Button
              onClick={() => {
                closeEditor();
              }}
              type="text"
              icon={<IconClose />}
            ></Button>
          </div>
        </div>
      }
      centered
      open={isOpen}
      footer={null}
    >
      <TasksPage
        lsf={ref}
        project={project}
        moduleId={currentModule}
        projectId={currentProject}
        fileId={currentFile}
        setFileId={setCurrentFile}
        config={config}
        workflow={workflow}
        valueClass={valueClass}
        dataSets={dataList}
        setDataList={setDataList}
        item={fileDetail}
        setItem={setFileDetail}
        samModelSession={samModelSession}
        aisHost={aisHost}
        userToken={userToken}
        canUpdateMetaData={canUpdateMetaData}
        configUser={user}
        loadDetailFile={isLoadingFileDetail}
        heightEditor={heightEditor}
        sliderHeight={sliderHeight}
        isFullScreen={isFullScreen}
        setOpenEditor={setOpenEditor}
        checkAssign={
          assignmentConfig?.enableAutoAssign ||
          assignmentConfig?.enableManualAssign
        }
        setArchiveInEditor={setArchiveInEditor}
        canAssignTask={canAssignTask}
        assignmentConfig={assignmentConfig}
        openFromDataManager={true}
        keepStoreEditor={keepStoreEditor}
        updateDataList={updateDataList}
        isDataManager={isDataManager}
        assignedMembersWorkflow={assignedMembersWorkflow}
        canUpdateTags={canUpdateTags}
      />

      <div className="relative flex flex-col">
        <BarResizer
          initResize={initResize}
          resetResize={resetResize}
          handleMouseUp={handleMouseUp}
        />
        {isMinimizeSlider ? (
          <div
            className="w-full max-h-[120px] flex items-center justify-center text-[#ADB8CC] text-[12px] font-medium leading-[15px] bg-[#EDEFF2] cursor-pointer rounded-b-[10px]"
            onClick={() => {
              toggleMinimizeSlider(false);
              resizeSliderHeight(DEFAULT_SLIDER_HEIGHT);
            }}
            ref={resizeRef}
            style={{ height: sliderHeight }}
          >
            Data slider
          </div>
        ) : (
          <>
            {dataList.length > 0 ? (
              <div
                className="flex flex-row w-full max-h-[120px]"
                style={{ height: sliderHeight }}
                ref={resizeRef}
              >
                <ListFile
                  isload={isLoadingSlider}
                  fileId={currentFile}
                  setFileId={setCurrentFile}
                  dataSets={dataList}
                  currentPage={fetchParams?.page}
                  setCurrentPage={(page) => {
                    setFetchParams({ ...fetchParams, page });

                    if (page < prevPointer && page > 0) {
                      fetchData(fetchUrl, { ...fetchParams, page }, "prev");
                      setPrevPointer(page);
                    }

                    if (page > nextPointer && dataList?.length < totalItems) {
                      fetchData(fetchUrl, { ...fetchParams, page }, "next");
                      setNextPointer(page);
                    }
                  }}
                  currentPageSize={fetchParams?.pageSize}
                  totalItems={totalItems}
                  isDataManager={isDataManager}
                  enableManualAssign={assignmentConfig?.enableManualAssign}
                  workflow={workflow}
                  moduleId={currentModule}
                  projectId={currentProject}
                  isLoadingFileDetail={isLoadingFileDetail}
                />
              </div>
            ) : (
              <EmptyListData />
            )}
          </>
        )}
        {!isFullScreen && (
          <div
            className="absolute right-[6px] bottom-0 cursor-se-resize"
            onMouseDown={handleResizeEditor}
          >
            <IconResize />
          </div>
        )}
      </div>
    </Modal>
  );
});

export default memo(EditorModal);
