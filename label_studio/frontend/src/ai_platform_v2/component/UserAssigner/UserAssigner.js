import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useReducer,
  useState,
} from "react";
import { Button as ButtonT } from "@taureau/ui";
import { Button } from "../../../components";
import { Space } from "../../../components/Space/Space";
import { UsersList } from "../UsersList/UsersList";
import { useAPI } from "../../../providers/ApiProvider";
import { Block, Elem } from "../../utils/bem";
import "./UserAssigner.styl";
import {
  ACTIONS,
  initialState,
  UserAssignerReducer,
} from "./UserAssignerReducer";
import { IconArrowLeft, IconArrowRight } from "../../assets";
import { useProject } from "../../../providers/ProjectProvider";

export const UserAssigner = forwardRef(
  (
    {
      useRolePermissions = false,
      usersListTitle = "",
      selectedUsersListTitle, // "To Be Assigned"
      usersListSubtitle,
      selectedUsersListSubtitle,
      showCount, // ['selected']
      // canCancel = true,
      // cancelButtonTitle = "Cancel",
      // saveButtonTitle = "Save",
      usersDisabledMessage = null,
      selectedUsersDisabledMessage = null,
      usersLoader,
      usersAssigner,
      usersAssignerCreate,
      displayRole = true,
      disabled = false,
      disabledSave = true,
      style,
      styleBody,
      styleLayout,

      className,

      footerStyle,

      fetchUsersScroll,
      fetchMembersScroll,
      fetchUsersSearch,
      fetchMembersSearch,

      isSearchFetchUsers = false,
      isSearchFetchMembers = false,

      // pageSize,
      // waiting,

      enableScopeUser = true,
      hideSaveAction = false,
      setDirty = () => {},
    },
    ref
  ) => {
    const api = useAPI();

    const [usersState, dispatch] = useReducer(
      UserAssignerReducer,
      initialState
    );
    const [disabledBtnMoveRight, setDisabledBtnMoveRight] = useState();
    const [disabledBtnMoveLeft, setDisabledBtnMoveLeft] = useState();
    const [massageUsersList, setMassageUsersList] = useState();
    const [massageMembersList, setMassageMembersList] = useState();

    const [searchUser, setSearchUser] = useState("");
    const [searchMember, setSearchMember] = useState("");

    const { project } = useProject();

    useImperativeHandle(ref, () => ({
      getUserState: () => usersState,
    }));

    const onSaveButtonClick = useCallback(async () => {
      await usersAssigner(
        usersState,
        (selected) => {
          onCheckUsers(ACTIONS.CHECK_USERS, [selected]);
          onCheckUsers(ACTIONS.MOVE_USERS);
        },
        (selected) => {
          onCheckUsers(ACTIONS.CHECK_MEMBERS, [selected]);
          onCheckUsers(ACTIONS.MOVE_MEMBERS);
        }
      );

      dispatch({ type: ACTIONS.CLEANUP });
      setDirty(false);

      fetchUsers();
    }, [dispatch, usersAssigner, usersState]);

    const fetchUsers = useCallback(async () => {
      const {
        rolesList,
        usersList,
        totalUsersList,
        selectedUsersList,
        totalSelectedUsersList,

        disabledBtnMoveRight,
        disabledBtnMoveLeft,
        massageUsersList,
        massageMembersList,
      } = await usersLoader(usersState);

      setDisabledBtnMoveRight(disabledBtnMoveRight);
      setDisabledBtnMoveLeft(disabledBtnMoveLeft);
      setMassageUsersList(massageUsersList);
      setMassageMembersList(massageMembersList);

      dispatch({
        type: ACTIONS.LOAD_ROLES,
        payload: rolesList,
      });

      dispatch({
        type: ACTIONS.LOAD_MEMBERS,
        payload: selectedUsersList,
      });

      dispatch({
        type: ACTIONS.LOAD_TOTAL_MEMBERS,
        payload: totalSelectedUsersList,
      });

      dispatch({
        type: ACTIONS.LOAD_USERS,
        payload: usersList,
      });

      dispatch({
        type: ACTIONS.LOAD_TOTAL_USERS,
        payload: totalUsersList,
      });
    }, [api, dispatch, usersLoader, usersState]);

    const onCheckUsers = useCallback(
      (type, payload) => {
        dispatch({ type, payload });
      },
      [dispatch]
    );

    const onScrollUsers = useCallback(
      async (keyword, itemCount, total) => {
        if (itemCount < total) {
          // const page = (usersState.users.length / pageSize) + 1;
          const page = Math.ceil(itemCount / total + 1);
          const usersListScroll = await fetchUsersScroll?.(page, keyword);
          const usersList = usersListScroll?.items || [];

          dispatch({
            type: ACTIONS.LOAD_USERS,
            payload: usersState.users.concat(usersList),
          });
        }
      },
      [api, dispatch, usersState.users, usersState.totalUsers, fetchUsersScroll]
    );

    const onScrollMembers = useCallback(
      async (keyword, itemCount, total) => {
        if (itemCount < total) {
          // const page = (usersState.members.length / pageSize) + 1;
          const page = Math.ceil(itemCount / total + 1);
          const membersListScroll = await fetchMembersScroll?.(page, keyword);
          const membersList = membersListScroll?.items || [];

          dispatch({
            type: ACTIONS.LOAD_MEMBERS,
            payload: usersState.members.concat(membersList),
          });
        }
      },
      [
        api,
        dispatch,
        usersState.members,
        usersState.totalMembers,
        fetchMembersScroll,
      ]
    );

    const onSearchUsers = useCallback(
      async (keyword) => {
        const usersListSearch = await fetchUsersSearch?.(keyword);
        const usersList = usersListSearch?.items;
        // const totalUsersList = usersListSearch?.total;

        if (usersListSearch) {
          dispatch({
            type: ACTIONS.LOAD_USERS,
            payload: usersList,
          });

          // dispatch({
          //   type: ACTIONS.LOAD_TOTAL_USERS,
          //   payload: totalUsersList,
          // });
        }
      },
      [api, dispatch, fetchUsersSearch]
    );

    const onSearchMembers = useCallback(
      async (keyword) => {
        const membersListSearch = await fetchMembersSearch?.(keyword);
        const membersList = membersListSearch?.items;
        // const totalMembersList = membersListSearch?.total;

        if (membersListSearch) {
          dispatch({
            type: ACTIONS.LOAD_MEMBERS,
            payload: membersList,
          });

          // dispatch({
          //   type: ACTIONS.LOAD_TOTAL_MEMBERS,
          //   payload: totalMembersList,
          // });
        }
      },
      [api, dispatch, fetchMembersSearch]
    );

    useEffect(() => {
      fetchUsers();
    }, []);

    const memberIds = useMemo(
      () => usersState.members.map((u) => (u?.userId ? u?.userId : u?.id)),
      [usersState.members]
    );
    // const memberIds = useMemo(() => usersState.members.map(u => u.userId), [usersState.members]);
    // const memberIds = useMemo(() => usersState.members.map(u => u.userId), [usersState.users, usersState.members]);

    const isDisableArrowLeft = useMemo(
      () => usersState.checkedMembers.length === 0 || disabledBtnMoveLeft,
      [usersState.checkedMembers, disabledBtnMoveLeft]
    );

    const isDisableArrowRight = useMemo(
      () => usersState.checkedUsers.length === 0 || disabledBtnMoveRight,
      [usersState.checkedUsers, disabledBtnMoveRight]
    );

    return (
      <div
        className={`w-full h-full flex flex-col items-center ${className}`}
        style={styleLayout}
      >
        <Block
          name="user-assigner"
          mod={{ disabled: disabled || usersState.loading }}
          style={style}
        >
          <Elem name="body" style={styleBody}>
            <UsersList
              title={usersListTitle}
              subtitle={usersListSubtitle}
              showCount={showCount?.includes("source")}
              disabledMessage={usersDisabledMessage}
              list={usersState.users.filter((u) => !memberIds.includes(u.id))}
              onChange={(selected) =>
                onCheckUsers(ACTIONS.CHECK_USERS, selected)
              }
              selected={usersState.checkedUsers}
              roles={usersState.roles}
              setRole={(userRole) => onCheckUsers(ACTIONS.SET_ROLES, userRole)}
              displayRole={displayRole && enableScopeUser}
              disabledSave={disabledSave}
              disabled={!disabledSave}
              isSearchFetch={
                isSearchFetchUsers &&
                usersState.users.length < usersState.totalUsers
              }
              onScrollList={onScrollUsers}
              onSearchList={onSearchUsers}
              massageList={massageUsersList}
              total={usersState.totalUsers}
              searchTerm={searchUser}
              setSearchTerm={setSearchUser}
            />

            <Elem name="movers">
              <Space direction="vertical" size="small">
                <Button
                  style={{ width: 40, height: 110 }}
                  icon={
                    <IconArrowRight
                      size={30}
                      color={
                        isDisableArrowRight ? "rgba(0, 0, 0, 0.1)" : "#334466"
                      }
                    />
                  }
                  disabled={
                    usersState.checkedUsers.length === 0 || disabledBtnMoveRight
                  }
                  onClick={() => {
                    dispatch({ type: ACTIONS.MOVE_USERS });
                    setDirty(true);
                  }}
                />
                <Button
                  style={{ width: 40, height: 110 }}
                  icon={
                    <IconArrowLeft
                      size={30}
                      color={
                        isDisableArrowLeft ? "rgba(0, 0, 0, 0.1)" : "#334466"
                      }
                    />
                  }
                  disabled={isDisableArrowLeft}
                  onClick={() => {
                    dispatch({ type: ACTIONS.MOVE_MEMBERS });
                    setDirty(true);
                  }}
                />
              </Space>
            </Elem>

            <UsersList
              title={selectedUsersListTitle}
              subtitle={selectedUsersListSubtitle}
              list={usersState.members}
              disabledMessage={selectedUsersDisabledMessage}
              showCount={showCount?.includes("selected")}
              onChange={(selected) =>
                onCheckUsers(ACTIONS.CHECK_MEMBERS, selected)
              }
              selected={usersState.checkedMembers}
              roles={usersState.roles}
              setRole={(userRole) => {
                onCheckUsers(ACTIONS.SET_ROLES, userRole);
                setDirty(true);
              }}
              // displayRole={displayRole}
              members={(value) => {
                if (useRolePermissions) {
                  usersAssignerCreate?.(
                    value?.map((member) => ({
                      ...member,
                      permissions: usersState.roles.find(
                        (role) => role.id === member.role
                      )?.permissions,
                    }))
                  );
                } else {
                  usersAssignerCreate?.(value);
                }
              }}
              disabledSave={disabledSave}
              disabled={!disabledSave}
              isSearchFetch={
                isSearchFetchMembers &&
                usersState.members.length < usersState.totalMembers
              }
              onScrollList={onScrollMembers}
              onSearchList={onSearchMembers}
              massageList={massageMembersList}
              total={usersState.totalMembers}
              searchTerm={searchMember}
              setSearchTerm={setSearchMember}
            />
          </Elem>
        </Block>
        {!hideSaveAction && (
          <div
            className={
              !footerStyle &&
              "flex flex-col items-end w-full px-[22px] py-[10px] bg-gray-blue-97 rounded-b-[10px]"
            }
            style={
              usersState.users.length
                ? { ...footerStyle }
                : {
                    marginTop: "auto",
                    display: "flex",
                    width: "100%",
                    justifyContent: "flex-end",
                  }
            }
          >
            <ButtonT
              className="text-[14px] font-medium px-[40px]"
              corner="Rounded"
              theme="Primary"
              size="xs"
              onClick={onSaveButtonClick}
            >
              Save
            </ButtonT>
          </div>
        )}
      </div>
    );
  }
);
