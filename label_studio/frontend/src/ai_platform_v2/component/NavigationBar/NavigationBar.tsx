import React, { useCallback } from "react";
import IconRefresh from "../../assets/Icons/IconRefresh";
import { Tooltip } from "antd";
import { <PERSON><PERSON>, ModalConfirmBig } from "@taureau/ui";
import { PopoverUserDetail } from "../PopoverUserDetail/PopoverUserDetail";
import { PopoverSettingSystem } from "../PopoverSettingSystem/PopoverSettingSystem";
import PopoverNotification from "./PopOverNotification";
import IconDescription from "@/ai_platform_v2/assets/Icons/IconDescription";
import "./NavigationBar.scss";
import { IconFeedback, IconSlack } from "./Icons";

export const NavigationBar = () => {
  const handleNavigateToDocumentations = useCallback(() => {
    window.open(
      "https://taureauai.notion.site/Documentation-edcde08d0ee3453187532aa660003124",
      "_blank",
      "noopener,noreferrer"
    );
  }, []);

  const handleSendFeedback = useCallback(() => {
    window.location.assign(
      "mailto:<EMAIL>?subject=Feedback From User"
    );
  }, []);

  const handleJoinSlack = useCallback(() => {
    window.open(
      "https://allbyai.slack.com/join/shared_invite/zt-2g35cvk69-W6qJAx1UBt6vp4flA7PeYg#/shared-invite/email",
      "_blank",
      "noopener,noreferrer"
    );
  }, []);

  return (
    <div className="h-full flex items-center gap-3">
      <Tooltip placement="left" title="Send feedback">
        <Button
          className="taureau-btn-navbar"
          size="sm"
          icon={<IconFeedback />}
          corner="Circle"
          onClick={handleSendFeedback}
        />
      </Tooltip>

      <Tooltip placement="left" title="Contact us via Slack">
        <Button
          className="taureau-btn-slack"
          size="sm"
          icon={<IconSlack />}
          corner="Circle"
          onClick={handleJoinSlack}
        />
      </Tooltip>

      <Tooltip placement="left" title="Documentations">
        <Button
          className="taureau-btn-navbar"
          size="sm"
          icon={<IconDescription color="#C3CAD9" size={30} />}
          corner="Circle"
          onClick={handleNavigateToDocumentations}
        />
      </Tooltip>
      {/* <Button
        size="sm"
        icon={<IconRefresh color="#C3CAD9" size={30} />}
        corner="Circle"
      ></Button> */}
      <PopoverSettingSystem />
      <PopoverNotification />
      <PopoverUserDetail />
    </div>
  );
};
