import React from "react";

const IconFeedback = () => {
  return (
    <svg
      width="30"
      height="30"
      viewBox="0 0 30 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.61905 5H20.2381C21.6821 5 22.8571 6.17501 22.8571 7.61905V17.0473C22.8571 18.4914 21.6821 19.6664 20.2381 19.6664H12.8137L9.97869 22.5014C9.77755 22.702 9.51103 22.8087 9.23893 22.8087C9.104 22.8087 8.96739 22.7824 8.83692 22.7285C8.44414 22.5659 8.19047 22.186 8.19047 21.7608V19.6667H6.61905C5.17501 19.6667 4 18.4916 4 17.0476V7.61905C4 6.17501 5.17501 5 6.61905 5ZM10.2857 16.5238V11.8154C10.2857 11.6363 10.305 11.46 10.3346 11.2857H9.23809C8.65953 11.2857 8.19047 11.7548 8.19047 12.3333V15.4762C8.19047 16.0547 8.65953 16.5238 9.23809 16.5238H10.2857ZM18.1582 15.5307L18.6505 12.5624C18.7703 11.8391 18.2124 11.1809 17.4791 11.1809H14.8952L15.3006 8.74908C15.3992 8.15738 14.9433 7.61905 14.3435 7.61905C13.9465 7.61905 13.5758 7.81767 13.3556 8.14788L11.6853 10.6532C11.4557 10.9974 11.3333 11.4016 11.3333 11.8154V16.5238H16.9868C17.5676 16.5238 18.0632 16.1036 18.1582 15.5307ZM23.9048 17.0477V8.196C25.0988 8.43933 26 9.49728 26 10.762V20.1908C26 21.6348 24.825 22.8099 23.381 22.8099H21.8095V24.904C21.8095 25.3289 21.5559 25.7091 21.1631 25.8717C21.0326 25.9256 20.896 25.9519 20.7611 25.9519C20.489 25.9519 20.2225 25.8454 20.0213 25.6446L17.1863 22.8096H11.1523L13.2476 20.7143H20.2381C22.2596 20.7143 23.9048 19.0694 23.9048 17.0477Z"
        fill="#C3CAD9"
      />
    </svg>
  );
};

const IconSlack = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
    >
      <path
        d="M4.62215 13.9048C4.62215 15.1768 3.58306 16.2159 2.31108 16.2159C1.03909 16.2159 0 15.1768 0 13.9048C0 12.6328 1.03909 11.5938 2.31108 11.5938H4.62215V13.9048Z"
        fill="#C3CAD9"
      />
      <path
        d="M5.78711 13.9048C5.78711 12.6328 6.8262 11.5938 8.09818 11.5938C9.37017 11.5938 10.4093 12.6328 10.4093 13.9048V19.6915C10.4093 20.9635 9.37017 22.0025 8.09818 22.0025C6.8262 22.0025 5.78711 20.9635 5.78711 19.6915V13.9048Z"
        fill="#C3CAD9"
      />
      <path
        d="M8.09818 4.62215C6.8262 4.62215 5.78711 3.58306 5.78711 2.31108C5.78711 1.03909 6.8262 0 8.09818 0C9.37017 0 10.4093 1.03909 10.4093 2.31108V4.62215H8.09818Z"
        fill="#C3CAD9"
      />
      <path
        d="M8.09772 5.78906C9.36971 5.78906 10.4088 6.82815 10.4088 8.10014C10.4088 9.37212 9.36971 10.4112 8.09772 10.4112H2.31108C1.03909 10.4112 0 9.37212 0 8.10014C0 6.82815 1.03909 5.78906 2.31108 5.78906H8.09772Z"
        fill="#C3CAD9"
      />
      <path
        d="M17.3779 8.10014C17.3779 6.82815 18.417 5.78906 19.689 5.78906C20.961 5.78906 22.0001 6.82815 22.0001 8.10014C22.0001 9.37212 20.961 10.4112 19.689 10.4112H17.3779V8.10014Z"
        fill="#C3CAD9"
      />
      <path
        d="M16.2139 8.09772C16.2139 9.36971 15.1749 10.4088 13.9029 10.4088C12.6309 10.4088 11.5918 9.36971 11.5918 8.09772V2.31108C11.5918 1.03909 12.6309 0 13.9029 0C15.1749 0 16.2139 1.03909 16.2139 2.31108V8.09772Z"
        fill="#C3CAD9"
      />
      <path
        d="M13.9019 17.3828C15.1739 17.3828 16.213 18.4219 16.213 19.6939C16.213 20.9659 15.1739 22.005 13.9019 22.005C12.6299 22.005 11.5908 20.9659 11.5908 19.6939V17.3828H13.9019Z"
        fill="#C3CAD9"
      />
      <path
        d="M13.9019 16.2159C12.6299 16.2159 11.5908 15.1768 11.5908 13.9048C11.5908 12.6328 12.6299 11.5938 13.9019 11.5938H19.6885C20.9605 11.5938 21.9996 12.6328 21.9996 13.9048C21.9996 15.1768 20.9605 16.2159 19.6885 16.2159H13.9019Z"
        fill="#C3CAD9"
      />
    </svg>
  );
};

export { IconFeedback, IconSlack };
