import React, { memo, useCallback, useEffect, useMemo, useState } from "react";
import { useHistory } from "react-router";
import classnames from "classnames";

import { useWebSocket } from "@/providers/WebSocketProvider";

import { useAPI } from "@/providers/ApiProvider";
import { usePermissions } from "@/providers/PermissionProvider";
import { IconDone, IconUnread } from "./Icon";
import { Button, ModalConfirmBig } from "@taureau/ui";
import { Tooltip } from "antd";
import unreadIcon from "@v2/assets/Images/UnreadNotification.png";
import { convertTimeFromNow } from "../../../utils/dateTime";
import useSettingProjectStore from "@/ai_platform_v2/stores/settingProjectStore";

const baseURL = window.APP_SETTINGS?.origin || location?.origin;

const htmlRegexG = /<(?:"[^"]*"['"]*|'[^']*'['"]*|[^'">])+>/g;

const NotificationItem = ({
  userNotification,
  setId,
  ids,
  markAsReadCallback = () => {},
}: any) => {
  const api = useAPI();
  const history = useHistory();

  const {
    boxUrl,
    sourceNotification,
    taskAvatar,
    shortDescription,
    taskIcon,
    description,
    projectUrl,
    sentDate,
    status,
    id,
  } = userNotification;
  const { setCountNotification, countNotification } = useWebSocket();
  const { fetchPermissions } = usePermissions();
  const [loadingApi, setLoadingApi] = useState(false);
  const [checkReading, setCheckReading] = useState(false);

  const { isChange, setIsChange } = useSettingProjectStore((state) => ({
    isChange: state.isChange,
    setIsChange: state.setIsChange,
  }));

  useEffect(() => {
    let checkData = true;

    switch (status) {
      // Read
      case 0:
        if (ids.includes(id)) {
          checkData = true;
        } else {
          checkData = false;
        }
        break;
      // UnRead
      case 1:
        checkData = true;
        break;
      default:
        break;
    }
    setCheckReading(!checkData);
  }, [id, ids, status]);

  useEffect(() => {
    if (countNotification === 0) {
      setCheckReading(false);
    }
  }, [countNotification]);

  const changeStatusNotification = async () => {
    if (loadingApi) return;

    setLoadingApi(true);

    const res = await api.callApi("putNotification", {
      params: {
        id_noti: id,
      },
      body: { status: 1 },
    });

    if (Number(sourceNotification) === 1) {
      fetchPermissions();
    }

    if (res) {
      const listId = [...ids, id];

      setId(listId);
      setCountNotification(() =>
        countNotification > 0 ? countNotification - 1 : 0
      );
    }
    setLoadingApi(false);
  };

  const renderAvatarNotification = useCallback(() => {
    return (
      <div className="w-[50px] min-w-[50px] aspect-square rounded-full relative flex items-center justify-center">
        <img
          src={taskAvatar}
          className="w-full h-full object-cover rounded-full"
        />
        {taskIcon ? (
          <div className="w-[20px] aspect-square flex items-center justify-center rounded-full absolute right-[-3px] bottom-[-5px]">
            <img src={taskIcon} className="w-full" />
          </div>
        ) : null}
      </div>
    );
  }, [taskAvatar, taskIcon]);

  const handleClickShortDescription = useCallback(
    (e) => {
      e.stopPropagation();

      if (isChange) {
        ModalConfirmBig.warning({
          header: "Warning",
          title: "Wanna leave?",
          content: `If you continue, changes you made may not be saved.`,
          // bodyClassName: "text-[#3361FF]",
          onOk: () => {
            setIsChange(false);
            history.push(
              `${new URL(projectUrl).pathname + new URL(projectUrl).search}`
            );
          },
          okText: "Continue",
          cancelText: "Cancel",
        });
      } else {
        history.push(
          `${new URL(projectUrl).pathname + new URL(projectUrl).search}`
        );
      }
    },
    [history, isChange, projectUrl, setIsChange]
  );

  const renderSeenWrapper = useCallback(() => {
    if (checkReading)
      return (
        <div className="flex items-center justify-center z-10 min-w-[30px]">
          <Tooltip title="Mark as read">
            <Button
              id="btn-markAsRead"
              corner="Circle"
              size="xs"
              icon={<IconDone />}
              onClick={(e) => {
                e.stopPropagation();
                changeStatusNotification();
                markAsReadCallback?.();
              }}
            />
          </Tooltip>
          <div id="icon-unread">
            <img src={unreadIcon} />
          </div>
        </div>
      );
    return;
  }, [changeStatusNotification, checkReading]);

  const handleClickBox = useCallback(() => {
    if (isChange) {
      ModalConfirmBig.warning({
        header: "Warning",
        title: "Wanna leave?",
        content: `If you continue, changes you made may not be saved.`,
        // bodyClassName: "text-[#3361FF]",
        onOk: () => {
          setIsChange(false);
          if (boxUrl) {
            history.push(
              `${new URL(boxUrl).pathname + new URL(boxUrl).search}`
            );
            if (Number(sourceNotification) === 1) {
              fetchPermissions();
            }
          }
          if (checkReading) {
            changeStatusNotification();
          }
        },
        okText: "Continue",
        cancelText: "Cancel",
      });
    } else {
      if (boxUrl) {
        history.push(`${new URL(boxUrl).pathname + new URL(boxUrl).search}`);
        if (Number(sourceNotification) === 1) {
          fetchPermissions();
        }
      }
      if (checkReading) {
        changeStatusNotification();
      }
    }
  }, [
    isChange,
    setIsChange,
    boxUrl,
    checkReading,
    history,
    sourceNotification,
    fetchPermissions,
    changeStatusNotification,
  ]);

  return (
    <div
      className="notification-item flex flex-row p-2.5 w-full gap-5 hover:bg-[#3361FF1A] rounded-[10px] items-center cursor-pointer relative"
      onClick={handleClickBox}
    >
      {renderAvatarNotification()}
      <div className="flex flex-col gap-[5px] grow text-[13px]  leading-5 text-[#346] overflow-hidden">
        <div dangerouslySetInnerHTML={{ __html: description }}></div>
        <div className="flex flex-row items-center text-[13px] leading-5 gap-[5px]">
          <Tooltip
            title={shortDescription?.replace(htmlRegexG, "")}
            getPopupContainer={(triggerNode) => triggerNode.parentNode}
          >
            <div
              onClick={handleClickShortDescription}
              className="cursor-pointer hover:underline whitespace-nowrap text-ellipsis overflow-hidden shrink"
              dangerouslySetInnerHTML={{ __html: shortDescription }}
            />
          </Tooltip>
          <span className="text-[#346] whitespace-nowrap">
            ● {convertTimeFromNow(sentDate)}
          </span>
        </div>
      </div>
      {renderSeenWrapper()}
    </div>
  );
};

export default memo(NotificationItem);
