import React, { memo, useEffect } from "react";
import Outside<PERSON><PERSON><PERSON>and<PERSON> from "react-outside-click-handler";
import { useWebSocket } from "@/providers/WebSocketProvider";

import IconNotification from "../../../assets/Icons/IconNotification";

import "./styles.scss";
import Notification from "./Notification";
import classNames from "classnames";
import { Tooltip } from "antd";

const PopoverNotification = () => {
  const {
    countNotification,
    openPopupNoti,
    setOpenPopupNoti,
    fetchCountNotification,
  } = useWebSocket();

  useEffect(() => {
    fetchCountNotification();
  }, [openPopupNoti]);

  return (
    <OutsideClickHandler
      onOutsideClick={() => {
        setOpenPopupNoti(false);
      }}
    >
      <div className="notification-wrapper">
        <Tooltip title="Notifications" placement="left">
          <div
            className={classNames("taureau-btn-navbar notification-btn", {
              "bg-[#3361FF1A]": openPopupNoti,
              // "hover:bg-[#f5f6f7] hover:brightness-[0.98]": !openPopupNoti,
            })}
            onClick={() => {
              setOpenPopupNoti(!openPopupNoti);
            }}
          >
            <IconNotification
              color={openPopupNoti ? "#3361FF" : "#C3CAD9"}
              size={30}
            />
          </div>
        </Tooltip>
        {countNotification ? (
          <div className="notification-dot">
            {countNotification > 99 ? "99+" : countNotification}
          </div>
        ) : null}
        {openPopupNoti && <Notification />}
      </div>
    </OutsideClickHandler>
  );
};

export default memo(PopoverNotification);
