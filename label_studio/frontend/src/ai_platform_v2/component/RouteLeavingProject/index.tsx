import { ModalConfirmBig } from "@taureau/ui";
import React, { useEffect, useState } from "react";
import { Prompt, useHistory, useParams } from "react-router-dom";

interface Props {
  when: boolean;
  isShowConfirm?: boolean;
  handleBeforeUnload?: () => void;
  handleAfterUnload?: () => void;
  handleAfterSuccess?: () => void;
}

const RouteLeavingProject: React.FC<Props> = (props) => {
  const {
    isShowConfirm = true,
    when,
    handleBeforeUnload,
    handleAfterUnload,
    handleAfterSuccess,
  } = props;
  const [confirmedNavigation, setConfirmedNavigation] = useState(false);

  const history = useHistory();
  const params = useParams();
  const currentProjectId = params?.projectId;

  const handleBlockedNavigation = (nextLocation) => {
    if (!isShowConfirm) {
      handleBeforeUnload?.();
      if (nextLocation?.pathname?.search(currentProjectId) === -1) {
        handleAfterSuccess?.();
      }
      return true;
    }

    if (
      !confirmedNavigation &&
      nextLocation?.pathname?.search(currentProjectId) === -1
    ) {
      ModalConfirmBig.warning({
        title: "Wanna leave?",
        content:
          "If you continue, the current import progress will be closed and stopped.",
        okText: "Continue",
        cancelText: "Cancel",
        onOk: () => {
          handleAfterUnload?.();
          handleLeave();
          // Delay re-render bug
          setTimeout(() => {
            history.push(nextLocation.pathname + nextLocation.search);
          }, 100);
        },
      });

      return false;
    }

    if (nextLocation?.pathname?.search(currentProjectId) === -1) {
      handleAfterSuccess?.();
    }
    return true;
  };

  const handleLeave = () => {
    setConfirmedNavigation(true);
  };

  const beforeUnloadListener = (e) => {
    handleBeforeUnload?.();
    if (isShowConfirm && when) {
      // Cancel the event as stated by the standard.
      e.preventDefault();
      // Chrome requires returnValue to be set.
      e.returnValue = "";
    }
    return;
  };

  useEffect(() => {
    window.addEventListener("beforeunload", beforeUnloadListener);
    return () =>
      window.removeEventListener("beforeunload", beforeUnloadListener);
  }, [isShowConfirm, when, beforeUnloadListener]);

  return <Prompt when={when} message={handleBlockedNavigation} />;
};

export default React.memo(RouteLeavingProject);
