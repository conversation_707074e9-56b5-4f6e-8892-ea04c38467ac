.taureau-tag
    cursor pointer
    display: flex;
    // min-width: 81px
    width: fit-content;
    height: 35px;
    padding: 3px 18px 3px 13px;
    justify-content: center;
    align-items: center;
    // gap: 5px;

    border-radius: 5px;
    border: 1px solid rgba(38, 51, 77, 0.1);
    box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.03);

    transition: all .2s

    &__tag-name
        color: var(--gray-blue-grey-blue-60, #6B7A99);
        font-feature-settings: 'clig' off, 'liga' off;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 30px;
        min-width: 50px
        text-align: center

    &__tag-checked
        position relative
        left: 3px
        bottom: 11px
        width 0px

        animation fadein 0.2s

.taureau-tag-checked
    border: 1px solid var(--blue-blue-20, rgba(51, 97, 255, 0.20));
    background: var(--blue-blue-10, rgba(51, 97, 255, 0.10));

    :global(.ls-taureau-tag__tag-name)
        color: var(--blue-blue, #3361FF);

.taureau-tag-select
    display: flex
    flex-wrap: wrap
    gap: 10px

@keyframes fadein
    0%
        opacity 0

    100%
        opacity 1

