import { Input } from "@taureau/ui";
import classNames from "classnames";
import { useCallback, useEffect, useMemo, useState } from "react";
import "./InputLabel.scss";

const IconInfo = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="15"
    viewBox="0 0 16 15"
    fill="none"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.96875 2.5C5.20875 2.5 2.96875 4.74 2.96875 7.5C2.96875 10.26 5.20875 12.5 7.96875 12.5C10.7288 12.5 12.9688 10.26 12.9688 7.5C12.9688 4.74 10.7288 2.5 7.96875 2.5ZM7.96875 10C7.69375 10 7.46875 9.775 7.46875 9.5V7.5C7.46875 7.225 7.69375 7 7.96875 7C8.24375 7 8.46875 7.225 8.46875 7.5V9.5C8.46875 9.775 8.24375 10 7.96875 10ZM8.46875 6H7.46875V5H8.46875V6Z"
      fill="#CC1414"
    />
  </svg>
);

const IconPencil = () => (
  <svg
    width="12"
    height="13"
    viewBox="0 0 12 13"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.2241 5.17839L8.89039 3.84464L7.55665 2.5109L1.92529 8.14226C1.54051 8.52703 1.29091 9.02623 1.21396 9.56492L1.00649 11.0172C0.94721 11.4322 1.30287 11.7878 1.71782 11.7285L3.17011 11.5211C3.7088 11.4441 4.208 11.1945 4.59277 10.8097L10.2241 5.17839Z"
      fill="#334466"
    />
    <path
      d="M7.55665 2.5109L1.92529 8.14226C1.54051 8.52703 1.29091 9.02623 1.21396 9.56492L1.00649 11.0172C0.94721 11.4322 1.30287 11.7878 1.71782 11.7285L3.17011 11.5211C3.7088 11.4441 4.208 11.1945 4.59277 10.8097L10.2241 5.17839M7.55665 2.5109L8.25669 1.81086C8.98892 1.07862 10.184 1.08654 10.9163 1.81877C11.6485 2.55101 11.6564 3.74611 10.9242 4.47834L10.2241 5.17839M7.55665 2.5109L8.89039 3.84464L10.2241 5.17839"
      stroke="#334466"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export interface InputLabelType {
  classNameLabel?: string;
  classNameInput?: string;
  placeholder?: string;
  value?: string;
  errorText?: string;
  isError?: boolean;
  onChange?: (event: any) => void;
  onEnter?: () => void;
}

export const InputLabel = (props: InputLabelType) => {
  const {
    classNameLabel,
    classNameInput,
    placeholder,
    value,
    errorText,
    isError,
    onChange,
    onEnter,
  } = props;

  const [mode, setMode] = useState("label"); // edit | label
  const [isFocusSearch, setFocusSearch] = useState(false);

  const handleChangeMode = useCallback((value) => {
    setMode(value);
  }, []);

  useEffect(() => {
    setMode(value ? "label" : "edit");
    setFocusSearch(!value);
  }, []);

  const render = useMemo(() => {
    if (mode === "label") {
      return (
        <div className={`flex items-center gap-[4px] ${classNameLabel}`}>
          <pre className="flex text-[#334466] text-[18px] font-medium leading-[30px] input-label-content">
            {value}
          </pre>
          <div
            className="flex px-[1px] py-[2px] cursor-pointer"
            onClick={() => {
              setFocusSearch(true);
              handleChangeMode("edit");
            }}
          >
            <IconPencil />
          </div>
        </div>
      );
    }

    return (
      <Input
        placeholder={placeholder}
        className={classNames(
          `flex items-end bg-white w-[156px] h-[30px] p-0 pb-[4px] rounded-none border-0 border-b-2 border-[#D4D4D8] taureau-input-label ${classNameInput}`,
          {
            "border-[#3361FF]": isFocusSearch,
            "border-[#CC1414]": !value?.trim() && !isFocusSearch,
          }
        )}
        inputClassName={classNames(
          "h-[18px] w-[156px] text-[#3361FF] text-[14px] font-normal leading-[16px] bg-white rounded-none",
          {
            "taureau-input-disabled": !value?.trim() && !isFocusSearch,
          }
        )}
        size="sm"
        value={value}
        onChange={onChange}
        onKeyUp={(e) => {
          if (e.key === "Enter") {
            if (value?.trim()) {
              handleChangeMode("label");
            }
            onEnter?.();
            setFocusSearch(false);
          }
        }}
        maxLength={50}
        autoFocus={isFocusSearch}
        onFocus={() => setFocusSearch(true)}
        onBlur={() => {
          if (value?.trim()) {
            handleChangeMode("label");
          }
          onEnter?.();
          setFocusSearch(false);
        }}
      />
    );
  }, [
    classNameInput,
    classNameLabel,
    placeholder,
    mode,
    value,
    isFocusSearch,
    onChange,
    onEnter,
  ]);

  return (
    <div className="flex items-center gap-[8px]">
      {render}
      {isError && (
        <div className="h-[18px] flex items-center">
          <div className="h-full flex items-center">
            <IconInfo />
          </div>
          <div className="flex text-[#CC1414] text-[12px] font-normal leading-[18px]">
            {errorText}
          </div>
        </div>
      )}
    </div>
  );
};
