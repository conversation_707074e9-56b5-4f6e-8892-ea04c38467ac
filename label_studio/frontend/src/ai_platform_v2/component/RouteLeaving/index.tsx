import { ModalConfirmBig } from "@taureau/ui";
import React, { useEffect, useState } from "react";
import { Prompt, useHistory } from "react-router-dom";

interface Props {
  when: boolean;
  isShowConfirm?: boolean;
  handleBeforeUnload?: () => void;
  handleAfterUnload?: () => void;
}

const RouteLeaving: React.FC<Props> = (props) => {
  const {
    isShowConfirm = true,
    when,
    handleBeforeUnload,
    handleAfterUnload,
  } = props;
  const [confirmedNavigation, setConfirmedNavigation] = useState(false);

  const history = useHistory();

  const handleBlockedNavigation = (nextLocation) => {
    if (!isShowConfirm) {
      handleBeforeUnload?.();
      return true;
    }

    if (!confirmedNavigation) {
      ModalConfirmBig.warning({
        title: "Wanna leave?",
        content: "If you continue, changes you made may not be saved",
        okText: "Continue",
        cancelText: "Cancel",
        onOk: () => {
          handleAfterUnload?.();
          handleLeave();
          // Delay re-render bug
          setTimeout(() => {
            history.push(nextLocation.pathname + nextLocation.search);
          }, 100);
        },
      });

      return false;
    }
    return true;
  };

  const handleLeave = () => {
    setConfirmedNavigation(true);
  };

  const beforeUnloadListener = (e) => {
    handleBeforeUnload?.();
    if (isShowConfirm && when) {
      // Cancel the event as stated by the standard.
      e.preventDefault();
      // Chrome requires returnValue to be set.
      e.returnValue = "";
    }
    return;
  };

  useEffect(() => {
    window.addEventListener("beforeunload", beforeUnloadListener);
    return () =>
      window.removeEventListener("beforeunload", beforeUnloadListener);
  }, [isShowConfirm, when, beforeUnloadListener]);

  return <Prompt when={when} message={handleBlockedNavigation} />;
};

export default React.memo(RouteLeaving);
