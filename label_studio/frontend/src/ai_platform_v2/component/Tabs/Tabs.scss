.taureau-tabs {
  display: flex;
  flex-direction: column;
  height: calc(100% - 115px);

  .tab-nav-list {
    display: flex;
    padding: 10px 5px 0px 5px;
    align-items: center;
    gap: 5px;
    align-self: stretch;
    background: #f5f6f7;
  }

  .tab-btn {
    cursor: pointer;
    display: flex;
    padding: 4px 10px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    align-self: stretch;
    border-radius: 5px 5px 0px 0px;
    background: var(--gray-blue-grey-blue-95, #f5f6f7);

    color: var(--gray-blue-grey-blue-40, #346);
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;

    transition:
      background 0.3s,
      box-shadow 0.3s;
  }
  .tab-btn:hover {
    background: var(--gray-blue-grey-blue-90, rgba(255, 255, 255, 0.5));
  }
  .tabs-tab-btn-active {
    background: var(--white-white, #fff);
    box-shadow: 0px 2px 5px 0px rgba(13, 17, 26, 0.15);
  }
  //   .middle {
  //     height: 10px;
  //     background: #fff;
  //   }
  .content {
    height: 100%;
    background: #fff;

    overflow-y: auto;
    overflow-x: hidden;

    ::-webkit-scrollbar {
      width: 4px;
      height: 4px;
      background-color: #fff;
    }

    ::-webkit-scrollbar-thumb {
      background: #b4b4b4;
    }
  }
  .tab-tabpane-active {
    height: 100%;
    display: flex;
    padding: 8px 15px;
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    align-self: stretch;
  }
  .tab-tabpane-hidden {
    display: none;
  }
}
