import { Block } from "@/ai_platform_v2/utils/bem";
import { absoluteURL } from "@/utils/helpers";
import classNames from "classnames";
import { twMerge } from "tailwind-merge";
import "./LayoutLogin.styl";
import "./LayoutLogin.css";
import { useAppStore } from "@/providers/AppStoreProvider";

interface Props {
  children?: any;
}

export const LayoutLogin = (props: Props) => {
  const { children } = props;

  const { isPublicVersion } = useAppStore();

  //check url exist confirm-user-v2
  // const url = window.location.href;
  // const isConfirmUser = url.includes("confirm-user-v2");

  return (
    <Block name="taureau-base">
      <div className="h-full w-full absolute top-0 left-0 overflow-hidden">
        <img
          src={absoluteURL("/static/images/login-background.png")}
          alt="background"
          className={twMerge(classNames("h-full w-full"))}
        />
        {!isPublicVersion && (
          <img
            src={absoluteURL("/static/images/login-background-2.png")}
            alt="background"
            className={twMerge(
              classNames(
                "absolute top-[0px] left-[200px] w-[1300px] h-full object-contain"
              )
            )}
          />
        )}
      </div>

      <div className="absolute top-[20px] left-[20px] flex items-center">
        <img
          src={absoluteURL("/static/images/Allbyai-logo.png")}
          alt="logo"
          className="h-[150px] w-[150px]"
        />
        <div className="flex flex-col">
          <div className="font-bevietnam text-[#26334D] text-[52px] font-semibold leading-[60px]">
            allbyai
          </div>
          <div className="flex px-[5px]">
            <div className="font-bevietnam text-gray-blue-50 text-[22px] font-medium leading-[25px]">
              by Taureau.AI
            </div>
          </div>
        </div>
      </div>
      <div className="w-full h-full flex gap-[40px] items-center justify-center px-[114px] py-[88px]">
        {!isPublicVersion && (
          <div className="w-full h-full flex items-center justify-center">
            <div className="slogan">
              Empower Your AI Development With Our All-In-One Platform:
              Unleashing Remarkable Innovation, Accelerating Speed, And Reducing
              Costs
            </div>
          </div>
        )}
        {children}
      </div>
    </Block>
  );
};
