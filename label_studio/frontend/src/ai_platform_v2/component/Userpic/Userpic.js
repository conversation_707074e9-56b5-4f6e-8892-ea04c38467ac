import chroma from "chroma-js";
import {
  forwardRef,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { Block, Elem } from "../../utils/bem";
import { FF_DEV_1495, isFF } from "../../utils/feature-flags";
import { isDefined, userDisplayName } from "../../utils/helpers";
import { Tooltip } from "antd";
import "./UserpicV2.styl";

import random from "lodash/random";
import { PROJECT_COLORS } from "../../utils/colors";

const FALLBACK_IMAGE =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";

export const UserpicV2 = forwardRef(
  (
    {
      badge = null,
      className,
      faded = false,
      showUsername,
      size,
      imgSize = 26,
      src,
      icon,
      style,
      addCount,
      user,
      username,
      hightLightColor,
      useHightLight = false,
      backgroundColor,
      useRandomBackground = true,

      tooltipColor,
      overlayInnerTooltipStyle,
      overlayTooltipTitle,
      ...rest
    },
    ref
  ) => {
    const propsSrc = user?.avatar_url ?? user?.avatar ?? src;
    const imgRef = useRef();
    const [finalSrc, setFinalSrc] = useState(propsSrc);
    const [imgVisible, setImgVisible] = useState(false);
    const [nameVisible, setNameVisible] = useState(true);

    useEffect(() => {
      if (isFF(FF_DEV_1495) && propsSrc !== finalSrc) {
        setFinalSrc(propsSrc);
        setImgVisible(false);
        setNameVisible(true);
      }
    }, [propsSrc]);

    if (size) {
      style = Object.assign(
        { width: size, height: size, fontSize: size * 0.4 },
        style
      );
    }

    const displayName = useMemo(() => {
      if (user || username) {
        return user ? userDisplayName(user) : username;
      }

      return "";
    }, [user, username]);

    const background = useMemo(() => {
      if (!useRandomBackground) return null;
      if (backgroundColor) return backgroundColor;
      const userId = user?.userId ?? user?.id ?? user?.userAssigned;

      if (isDefined(userId)) {
        const color =
          localStorage.getItem(`userpic-color-${userId}`) ??
          chroma.average([chroma.random(), "#cfcfcf"]);

        localStorage.setItem(`userpic-color-${userId}`, color);
        return color;
      }

      return null;
    }, [user, useRandomBackground, backgroundColor]);

    const textColor = useMemo(() => {
      return PROJECT_COLORS[random(0, PROJECT_COLORS.length)];
    }, []);

    const borderHightLight = useMemo(() => {
      if (useHightLight) {
        return hightLightColor ? hightLightColor : "red";
      }

      return null;
    }, [useHightLight, hightLightColor]);

    const onImageLoaded = useCallback(() => {
      setImgVisible(true);
      if (finalSrc !== FALLBACK_IMAGE) setNameVisible(false);
    }, [finalSrc]);

    const onImageErrored = useCallback(() => {
      setImgVisible(false);
      setNameVisible(true);
    }, []);

    const stylesheet = {
      ...(style ?? {}),
      background: "#F5F6F7",
      color: textColor,
      border: borderHightLight ? `solid 3px ${borderHightLight}` : null,
    };

    const renderName = () => {
      let name = "";

      if (addCount) {
        name = addCount;
      } else if (nameVisible) {
        if (icon) {
          name = icon;
        } else if (displayName) {
          name = displayName.slice(0, 1).toUpperCase();
        }
      }

      return (
        <Elem tag="span" name="username" style={style}>
          {name}
        </Elem>
      );
    };

    const userpic = (
      <Block
        ref={ref}
        name="userpicV2"
        mix={className}
        mod={{ faded }}
        style={stylesheet}
        {...rest}
      >
        <Elem
          tag="img"
          name="avatar"
          ref={imgRef}
          src={finalSrc}
          alt={(displayName ?? "").toUpperCase()}
          style={{
            opacity: imgVisible ? (faded ? 0.3 : 1) : 0,
            width: imgSize,
          }}
          onLoad={onImageLoaded}
          onError={onImageErrored}
          mod={{ faded }}
        />
        {renderName()}
        {badge &&
          Object.entries(badge).map(([align, content], i) => {
            return (
              <Elem key={`badge-${i}`} name="badge" mod={{ [align]: true }}>
                {content}
              </Elem>
            );
          })}
      </Block>
    );

    const userFullName = useMemo(() => {
      if (overlayTooltipTitle) {
        return overlayTooltipTitle;
      } else if (user?.firstName || user?.lastName) {
        return `${user?.firstName ?? ""} ${user?.lastName ?? ""}`.trim();
      } else if (user?.email) {
        return user.email;
      } else if (user?.username || user?.userName) {
        return user?.username ?? user?.userName;
      } else {
        return username;
      }
    }, [user, username, overlayTooltipTitle]);

    return showUsername && userFullName ? (
      <Tooltip
        overlayInnerStyle={{
          ...overlayInnerTooltipStyle,
          textAlign: "center",
          padding: "0 11px",
          display: "flex",
          alignItems: "center",
          fontSize: 14,
          fontWeight: 500,
          lineHeight: "20px",
        }}
        title={userFullName}
        color={tooltipColor}
        // getPopupContainer={(triggerNode) => triggerNode.parentNode}
      >
        {userpic}
      </Tooltip>
    ) : (
      userpic
    );
  }
);
