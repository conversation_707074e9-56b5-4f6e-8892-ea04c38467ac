import React from "react";
import { Props } from "./base";

function IconAIPredictFile({
  className,
  active = false,
  size = 25,
}: Partial<Props>) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 25 26"
      fill="none"
    >
      <path
        className="change-color-fill"
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M11.5769 3C10.9928 3.10446 10.4396 3.41741 10.0411 3.93881C9.49867 4.64837 8.61791 5.01319 7.73266 4.895C6.10562 4.67775 4.71701 6.06636 4.93426 7.6934C5.05245 8.57867 4.68761 9.45943 3.97809 10.0018C3.89295 10.0669 3.81339 10.1361 3.73937 10.2089H6.00226L7.46078 11.6674L6.83439 12.2938L5.63532 11.0947H3.16143C2.78891 12.0795 3.06113 13.2584 3.97809 13.9594C4.68761 14.5018 5.05245 15.3825 4.93426 16.2678C4.71701 17.8948 6.10562 19.2834 7.73266 19.0661C8.61791 18.9481 9.49867 19.3129 10.0411 20.0223C10.228 20.2669 10.4489 20.4655 10.691 20.6183V19.327L12.0198 17.9982V16.5709H10.3317L9.00288 15.0812H7.95783C7.77541 15.5973 7.28323 15.967 6.70466 15.967C5.97078 15.967 5.37586 15.3721 5.37586 14.6382C5.37586 13.9043 5.97078 13.3094 6.70466 13.3094C7.28323 13.3094 7.77541 13.6791 7.95783 14.1952H9.39978L10.7286 15.6849H12.0198V14.4082L9.80518 12.6364V11.6177L7.75142 10.2889V8.80447C7.23533 8.62205 6.86556 8.12984 6.86556 7.55127C6.86556 6.81739 7.46048 6.22248 8.19436 6.22248C8.92824 6.22248 9.52315 6.81739 9.52315 7.55127C9.52315 8.12984 9.15339 8.62205 8.63729 8.80447V9.80698L9.80518 10.5626V8.72471L11.5769 6.51004V3ZM11.5769 20.9647C12.4537 21.1215 13.4003 20.8086 13.9986 20.0258C14.4859 19.3884 15.2462 19.0293 16.0375 19.0485L14.5136 17.7422H12.9056V18.3687L11.5769 19.6975V20.9647ZM14.8418 16.8528L17.3353 18.9902C18.4693 18.659 19.2754 17.5357 19.106 16.2678C18.9879 15.3825 19.3526 14.5018 20.0622 13.9594C20.9791 13.2584 21.2515 12.0795 20.8788 11.0947H16.8926V10.2089H20.3008C20.2269 10.1361 20.1473 10.0669 20.0622 10.0018C19.3526 9.45943 18.9879 8.57867 19.106 7.69341C19.3232 6.06636 17.9347 4.67775 16.3076 4.895C15.4224 5.01319 14.5416 4.64837 13.9992 3.93883C13.6006 3.41741 13.0475 3.10446 12.4634 3V6.82078L11.0416 8.59806H12.8359L13.2788 7.10834H14.8526C15.0352 6.59225 15.5273 6.22248 16.1058 6.22248C16.8397 6.22248 17.4346 6.8174 17.4346 7.55127C17.4346 8.28515 16.8397 8.88007 16.1058 8.88007C15.5273 8.88007 15.0352 8.5103 14.8526 7.99421H13.9397L13.4967 9.48391H10.6917V11.5377H13.5477L14.8764 13.0274H15.9215C16.104 12.5113 16.5962 12.1415 17.1747 12.1415C17.9086 12.1415 18.5035 12.7364 18.5035 13.4703C18.5035 14.2042 17.9086 14.7991 17.1747 14.7991C16.5962 14.7991 16.104 14.4294 15.9215 13.9133H14.4797L13.1508 12.4235H10.9578L12.9063 13.9824V16.8528H14.8418ZM16.1044 7.10334C15.8598 7.10334 15.6616 7.30165 15.6616 7.54628C15.6616 7.79091 15.8598 7.98922 16.1044 7.98922C16.349 7.98922 16.5474 7.79091 16.5474 7.54628C16.5474 7.30165 16.349 7.10334 16.1044 7.10334ZM17.1738 13.9127C16.9292 13.9127 16.7309 13.7143 16.7309 13.4697C16.7309 13.225 16.9292 13.0267 17.1738 13.0267C17.4185 13.0267 17.6168 13.225 17.6168 13.4697C17.6168 13.7143 17.4185 13.9127 17.1738 13.9127ZM7.14948 14.6362C7.14948 14.3916 6.95117 14.1932 6.70654 14.1932C6.46192 14.1932 6.26361 14.3916 6.26361 14.6362C6.26361 14.8808 6.46192 15.0792 6.70654 15.0792C6.95117 15.0792 7.14948 14.8808 7.14948 14.6362ZM8.19359 7.98922C8.43822 7.98922 8.63653 7.79091 8.63653 7.54628C8.63653 7.30165 8.43822 7.10334 8.19359 7.10334C7.94896 7.10334 7.75065 7.30165 7.75065 7.54628C7.75065 7.79091 7.94896 7.98922 8.19359 7.98922Z"
        fill="black"
        fill-opacity="0.3"
      />
    </svg>
  );
}

export default IconAIPredictFile;
