import React from "react";
import { Props } from "./base";

function IconPreProcessingHuman({
  color = "#33BFFF",
  size = 30,
}: Partial<Props>) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="1" y="1" width="30" height="30" rx="15" fill="#EDB7ED" />
      <rect
        x="1"
        y="1"
        width="30"
        height="30"
        rx="15"
        stroke="#F5F6F7"
        stroke-width="2"
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M16.8333 11.8333C16.8333 13.6743 15.341 15.1667 13.5 15.1667C11.6591 15.1667 10.1667 13.6743 10.1667 11.8333C10.1667 9.99238 11.6591 8.5 13.5 8.5C15.341 8.5 16.8333 9.99238 16.8333 11.8333ZM16 23.5H11C10.3376 23.498 9.70283 23.234 9.23442 22.7656C8.76601 22.2972 8.50198 21.6624 8.5 21V20.1667C8.5 19.0616 8.93899 18.0018 9.72039 17.2204C10.5018 16.439 11.5616 16 12.6667 16H14.3333C15.4384 16 16.4982 16.439 17.2796 17.2204C18.061 18.0018 18.5 19.0616 18.5 20.1667V21C18.498 21.6624 18.234 22.2972 17.7656 22.7656C17.2972 23.234 16.6624 23.498 16 23.5ZM20.1667 15.1667C21.5474 15.1667 22.6667 14.0474 22.6667 12.6667C22.6667 11.286 21.5474 10.1667 20.1667 10.1667C18.786 10.1667 17.6667 11.286 17.6667 12.6667C17.6667 14.0474 18.786 15.1667 20.1667 15.1667ZM18.5417 16.1333C18.7967 16.0466 19.064 16.0016 19.3333 16H21C21.6624 16.002 22.2972 16.266 22.7656 16.7344C23.234 17.2028 23.498 17.8376 23.5 18.5V19.3333C23.4987 19.775 23.3227 20.1981 23.0104 20.5104C22.6981 20.8227 22.2749 20.9987 21.8333 21H20.1667V20.1667C20.169 18.6623 19.5863 17.2159 18.5417 16.1333Z"
        fill="white"
      />
    </svg>
  );
}

export default IconPreProcessingHuman;
