import React from "react";
import { Props } from "./base";

function IconFileUpload({
  className,
  color = "#CC7429",
  size = 60,
}: Partial<Props>) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 60 60"
      fill="none"
    >
      <rect
        x="1"
        y="1"
        width="58"
        height="58"
        rx="4"
        stroke={color}
        strokeWidth="2"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M34.6218 8.63757C34.2047 8.45158 33.7701 8.30453 33.3237 8.19874C32.4843 7.99979 31.603 7.99987 30.1414 8.00001C30.1031 8.00001 30.0644 8.00001 30.0252 8.00001H23.1042C21.3367 8.00001 19.9577 8.00001 18.8508 8.08929C17.7258 8.18002 16.8066 8.36718 15.9781 8.78393C14.6072 9.47348 13.4926 10.5738 12.7941 11.9271C12.3719 12.745 12.1823 13.6524 12.0904 14.763C12 15.8557 12 17.2171 12 18.9619V41.0382C12 42.783 12 44.1443 12.0904 45.237C12.1823 46.3476 12.3719 47.255 12.7941 48.0729C13.4926 49.4263 14.6072 50.5265 15.9781 51.2161C16.8066 51.6328 17.7258 51.82 18.8508 51.9107C19.9577 52 21.3367 52 23.1042 52H36.8958C38.6633 52 40.0423 52 41.1492 51.9107C42.2742 51.82 43.1934 51.6328 44.0219 51.2161C45.3928 50.5265 46.5074 49.4263 47.2059 48.0729C47.6281 47.255 47.8176 46.3476 47.9096 45.237C48 44.1443 48 42.783 48 41.0382L48 25.6296C48.0001 24.1867 48.0002 23.3168 47.7987 22.4881C47.6915 22.0474 47.5425 21.6184 47.3541 21.2066C47.3489 21.1944 47.3433 21.1823 47.3375 21.1705C47.2162 20.9092 47.0789 20.6549 46.9264 20.4092C46.4753 19.6825 45.8521 19.0674 44.8184 18.0472L37.8223 11.1407C36.7888 10.1204 36.1658 9.50515 35.4297 9.05987C35.1807 8.90927 34.9232 8.77381 34.6586 8.65406C34.6465 8.64829 34.6342 8.64279 34.6218 8.63757ZM35.1428 10.9465V18.1539C35.1428 18.7602 35.1435 19.164 35.1692 19.4743C35.1941 19.7748 35.238 19.9122 35.283 19.9993C35.4062 20.2381 35.6029 20.4323 35.8449 20.554C35.9331 20.5984 36.0723 20.6418 36.3767 20.6663C36.691 20.6917 37.1001 20.6923 37.7143 20.6923H45.0153C44.6963 20.323 44.2444 19.8738 43.5241 19.1628L36.6922 12.4185C35.972 11.7074 35.5169 11.2613 35.1428 10.9465Z"
        fill={color}
      />
    </svg>
  );
}

export default IconFileUpload;
