import React from "react";

function IconPreviewZip({ size = 86 }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 86 86"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M18.2273 17.9855C14.5391 21.3049 14.5391 26.6475 14.5391 37.3327V48.666C14.5391 59.3512 14.5391 64.6938 18.2273 68.0132C21.9156 71.3327 27.8518 71.3327 39.7242 71.3327H46.0205C57.893 71.3327 63.8292 71.3327 67.5174 68.0132C71.2057 64.6938 71.2057 59.3512 71.2057 48.666V37.3327C71.2057 26.6475 71.2057 21.3049 67.5174 17.9855C63.8292 14.666 57.893 14.666 46.0205 14.666H42.8724V17.4993V20.3327H47.5946C49.0787 20.3327 49.8207 20.3327 50.2817 20.7476C50.7428 21.1625 50.7428 21.8304 50.7428 23.166C50.7428 24.5017 50.7428 25.1695 50.2817 25.5844C49.8207 25.9993 49.0787 25.9993 47.5946 25.9993H42.8724V31.666H47.5946C49.0787 31.666 49.8207 31.666 50.2817 32.0809C50.7428 32.4959 50.7428 33.1637 50.7428 34.4993C50.7428 35.835 50.7428 36.5028 50.2817 36.9178C49.8207 37.3327 49.0787 37.3327 47.5946 37.3327H46.0205C44.5365 37.3327 43.7945 37.3327 43.3334 36.9178C42.8724 36.5028 42.8724 35.835 42.8724 34.4993V31.666H38.1502C36.6661 31.666 35.9241 31.666 35.4631 31.2511C35.002 30.8362 35.002 30.1683 35.002 28.8327C35.002 27.497 35.002 26.8292 35.4631 26.4143C35.9241 25.9993 36.6661 25.9993 38.1502 25.9993H42.8724V20.3327H38.1502C36.6661 20.3327 35.9241 20.3327 35.4631 19.9178C35.002 19.5028 35.002 18.835 35.002 17.4993V14.6756C26.2065 14.7334 21.3903 15.1388 18.2273 17.9855ZM35.002 45.4785V45.8327C35.002 49.7447 38.5257 52.916 42.8724 52.916C47.2191 52.916 50.7428 49.7447 50.7428 45.8327V45.4785C50.7428 44.1093 49.5095 42.9994 47.9881 42.9994H37.7567C36.2353 42.9994 35.002 44.1093 35.002 45.4785Z"
        fill="#C3CAD9"
      />
    </svg>
  );
}

export default IconPreviewZip;
