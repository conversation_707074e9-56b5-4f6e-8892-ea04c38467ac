import React from "react";
import { Props } from "./base";

function IconPreProcessingCompleted({
  color = "#33BFFF",
  size = 30,
}: Partial<Props>) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M16 1C7.72 1 1 7.72 1 16C1 24.28 7.72 31 16 31C24.28 31 31 24.28 31 16C31 7.72 24.28 1 16 1Z"
        fill="#15803D"
        stroke="#F5F6F7"
        stroke-width="2"
      />
      <path
        d="M24.6211 10.3473C24.5119 10.2372 24.3821 10.1499 24.239 10.0903C24.096 10.0307 23.9425 10 23.7875 10C23.6326 10 23.4791 10.0307 23.3361 10.0903C23.193 10.1499 23.0631 10.2372 22.954 10.3473L14.2078 19.1052L10.5333 15.4189C10.4199 15.3094 10.2862 15.2233 10.1396 15.1656C9.99301 15.1078 9.83649 15.0795 9.67897 15.0822C9.52144 15.0849 9.366 15.1187 9.2215 15.1815C9.07701 15.2443 8.9463 15.3349 8.83684 15.4482C8.72738 15.5615 8.64131 15.6953 8.58355 15.8419C8.52578 15.9885 8.49745 16.145 8.50018 16.3025C8.5029 16.46 8.53663 16.6155 8.59943 16.76C8.66223 16.9045 8.75288 17.0352 8.86619 17.1446L13.3743 21.6527C13.4834 21.7628 13.6133 21.8501 13.7563 21.9097C13.8994 21.9693 14.0528 22 14.2078 22C14.3628 22 14.5163 21.9693 14.6593 21.9097C14.8024 21.8501 14.9322 21.7628 15.0414 21.6527L24.6211 12.073C24.7402 11.9631 24.8353 11.8297 24.9004 11.6812C24.9654 11.5326 24.999 11.3723 24.999 11.2101C24.999 11.048 24.9654 10.8876 24.9004 10.7391C24.8353 10.5906 24.7402 10.4572 24.6211 10.3473Z"
        fill="white"
      />
    </svg>
  );
}

export default IconPreProcessingCompleted;
