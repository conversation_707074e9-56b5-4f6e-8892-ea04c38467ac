import React from "react";
import { Props } from "./base";

function IconWorkflowAIPrediction({
  color = "#33BFFF",
  size = 30,
}: Partial<Props>) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="30" height="30" rx="15" fill="#33BFFF" />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M14.5294 5C13.8804 5.11606 13.2658 5.46377 12.823 6.04307C12.2204 6.83143 11.2418 7.23676 10.2582 7.10544C8.4505 6.86407 6.90768 8.40688 7.14906 10.2146C7.28037 11.1982 6.87502 12.1768 6.08671 12.7794C5.99211 12.8517 5.90371 12.9286 5.82147 13.0094H8.33566L9.95616 14.6299L9.2602 15.3259L7.92797 13.9937H5.17935C4.76546 15.0878 5.06792 16.3977 6.08671 17.1765C6.87502 17.779 7.28037 18.7576 7.14906 19.7412C6.90768 21.5489 8.4505 23.0917 10.2582 22.8503C11.2418 22.7191 12.2204 23.1245 12.823 23.9127C13.0306 24.1845 13.2761 24.405 13.5451 24.5748V23.1401L15.0215 21.6637V20.078H13.1459L11.6695 18.4228H10.5084C10.3057 18.9962 9.75888 19.407 9.11606 19.407C8.30068 19.407 7.6397 18.746 7.6397 17.9306C7.6397 17.1153 8.30068 16.4543 9.11606 16.4543C9.75888 16.4543 10.3057 16.8651 10.5084 17.4384H12.1105L13.5868 19.0936H15.0215V17.675L12.5609 15.7066V14.5747L10.2791 13.0984V11.4491C9.70566 11.2464 9.29484 10.6995 9.29484 10.0567C9.29484 9.24132 9.95582 8.58034 10.7712 8.58034C11.5866 8.58034 12.2476 9.24132 12.2476 10.0567C12.2476 10.6995 11.8367 11.2464 11.2633 11.4491V12.5629L12.5609 13.4025V11.3604L14.5294 8.89983V5ZM14.5288 24.9573C15.503 25.1314 16.5547 24.7839 17.2194 23.9141C17.7608 23.2059 18.6056 22.8069 19.4848 22.8283L17.7916 21.377H16.0051V22.0729L14.5288 23.5493V24.9573ZM18.156 20.3912L20.9263 22.7659C22.1862 22.3979 23.0818 21.1499 22.8936 19.7412C22.7624 18.7576 23.1676 17.779 23.956 17.1765C24.9748 16.3977 25.2773 15.0878 24.8634 13.9937H20.4345V13.0094H24.2212C24.139 12.9286 24.0506 12.8517 23.956 12.7794C23.1676 12.1768 22.7624 11.1982 22.8936 10.2146C23.135 8.40688 21.5923 6.86407 19.7845 7.10544C18.8009 7.23676 17.8224 6.83143 17.2198 6.04309C16.7769 5.46377 16.1623 5.11606 15.5134 5V9.24508L13.9337 11.2197H15.9272L16.4193 9.56457H18.168C18.3707 8.99117 18.9175 8.58034 19.5603 8.58034C20.3757 8.58034 21.0367 9.24132 21.0367 10.0567C21.0367 10.8721 20.3757 11.5331 19.5603 11.5331C18.9175 11.5331 18.3707 11.1222 18.168 10.5488H17.1536L16.6614 12.204H13.5449V14.4858H16.7181L18.1943 16.1409H19.3555C19.5583 15.5675 20.1051 15.1567 20.7479 15.1567C21.5633 15.1567 22.2242 15.8177 22.2242 16.633C22.2242 17.4484 21.5633 18.1094 20.7479 18.1094C20.1051 18.1094 19.5583 17.6986 19.3555 17.1253H17.7536L16.2771 15.47H13.8405L16.0055 17.2021V20.3912H18.156ZM19.5605 9.56055C19.2888 9.56055 19.0686 9.78088 19.0686 10.0527C19.0686 10.3245 19.2888 10.5448 19.5605 10.5448C19.8323 10.5448 20.0527 10.3245 20.0527 10.0527C20.0527 9.78088 19.8323 9.56055 19.5605 9.56055ZM20.7485 17.1269C20.4767 17.1269 20.2563 16.9065 20.2563 16.6347C20.2563 16.3629 20.4767 16.1426 20.7485 16.1426C21.0203 16.1426 21.2407 16.3629 21.2407 16.6347C21.2407 16.9065 21.0203 17.1269 20.7485 17.1269ZM9.60848 17.9336C9.60848 17.6618 9.38815 17.4414 9.11635 17.4414C8.84456 17.4414 8.62423 17.6618 8.62423 17.9336C8.62423 18.2054 8.84456 18.4258 9.11635 18.4258C9.38815 18.4258 9.60848 18.2054 9.60848 17.9336ZM10.7716 10.5448C11.0434 10.5448 11.2638 10.3245 11.2638 10.0527C11.2638 9.78088 11.0434 9.56055 10.7716 9.56055C10.4998 9.56055 10.2795 9.78088 10.2795 10.0527C10.2795 10.3245 10.4998 10.5448 10.7716 10.5448Z"
        fill="white"
      />
    </svg>
  );
}

export default IconWorkflowAIPrediction;
