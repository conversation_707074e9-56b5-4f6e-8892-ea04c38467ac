import React, { memo } from "react";
import { Props } from "./base";

function IconLayoutGrid02({ color, size }: Props) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 35 35"
      fill="none"
    >
      <path
        d="M21.6 27.7992H14.4V14.5992H21.6V27.7992ZM24 27.7992V14.5992H30V26.5992C30 26.9175 29.8736 27.2227 29.6485 27.4477C29.4235 27.6728 29.1183 27.7992 28.8 27.7992H24ZM12 27.7992H7.2C6.88174 27.7992 6.57652 27.6728 6.35147 27.4477C6.12643 27.2227 6 26.9175 6 26.5992V14.5992H12V27.7992ZM30 12.1992H6V7.39922C6 7.08096 6.12643 6.77573 6.35147 6.55069C6.57652 6.32565 6.88174 6.19922 7.2 6.19922H28.8C29.1183 6.19922 29.4235 6.32565 29.6485 6.55069C29.8736 6.77573 30 7.08096 30 7.39922V12.1992Z"
        fill={color}
      />
    </svg>
  );
}

export default memo(IconLayoutGrid02);
