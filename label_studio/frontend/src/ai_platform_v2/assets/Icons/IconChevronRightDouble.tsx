import React from "react";
import { Props } from "./base";

function IconChevronRightDouble({
  className,
  color = "#62708C",
  size = 14,
}: Partial<Props>) {
  return (
    <svg
      className={className}
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.08492 3.67085C3.31272 3.44305 3.68207 3.44305 3.90987 3.67085L6.82654 6.58752C7.05435 6.81533 7.05435 7.18467 6.82654 7.41248L3.90987 10.3291C3.68207 10.557 3.31272 10.557 3.08492 10.3291C2.85711 10.1013 2.85711 9.73199 3.08492 9.50419L5.5891 7L3.08492 4.49581C2.85711 4.26801 2.85711 3.89866 3.08492 3.67085ZM7.16825 3.67085C7.39606 3.44305 7.7654 3.44305 7.99321 3.67085L10.9099 6.58752C11.1377 6.81533 11.1377 7.18467 10.9099 7.41248L7.99321 10.3291C7.7654 10.557 7.39606 10.557 7.16825 10.3291C6.94044 10.1013 6.94044 9.73199 7.16825 9.50419L9.67244 7L7.16825 4.49581C6.94044 4.26801 6.94044 3.89866 7.16825 3.67085Z"
        fill={color}
      />
    </svg>
  );
}

export default IconChevronRightDouble;
