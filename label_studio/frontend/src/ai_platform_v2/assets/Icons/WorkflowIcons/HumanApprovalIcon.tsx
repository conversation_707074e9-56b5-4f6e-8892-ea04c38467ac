import React from "react";
import { Props } from "../base";

function HumanApprovalIcon({ size = 28 }: Partial<Props>) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="4" y="4" width="16" height="16" rx="8" fill="#EDB7ED" />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.4445 9.77778C12.4445 10.7596 11.6485 11.5556 10.6667 11.5556C9.68483 11.5556 8.8889 10.7596 8.8889 9.77778C8.8889 8.79594 9.68483 8 10.6667 8C11.6485 8 12.4445 8.79594 12.4445 9.77778ZM12 16H9.33333C8.98004 15.9989 8.64151 15.8581 8.39169 15.6083C8.14187 15.3585 8.00106 15.02 8 14.6667V14.2222C8 13.6329 8.23413 13.0676 8.65087 12.6509C9.06762 12.2341 9.63285 12 10.2222 12H11.1111C11.7005 12 12.2657 12.2341 12.6825 12.6509C13.0992 13.0676 13.3333 13.6329 13.3333 14.2222V14.6667C13.3323 15.02 13.1915 15.3585 12.9416 15.6083C12.6918 15.8581 12.3533 15.9989 12 16ZM14.2222 11.5556C14.9586 11.5556 15.5556 10.9586 15.5556 10.2222C15.5556 9.48587 14.9586 8.88892 14.2222 8.88892C13.4858 8.88892 12.8889 9.48587 12.8889 10.2222C12.8889 10.9586 13.4858 11.5556 14.2222 11.5556ZM13.3555 12.0711C13.4916 12.0249 13.6341 12.0008 13.7778 12H14.6667C15.02 12.0011 15.3585 12.1419 15.6083 12.3917C15.8581 12.6415 15.9989 12.98 16 13.3333V13.7778C15.9993 14.0133 15.9054 14.239 15.7389 14.4055C15.5723 14.5721 15.3466 14.666 15.1111 14.6667H14.2222V14.2222C14.2235 13.4199 13.9127 12.6485 13.3555 12.0711Z"
        fill="white"
      />
    </svg>
  );
}

export default HumanApprovalIcon;
