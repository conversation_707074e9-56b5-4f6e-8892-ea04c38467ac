import React from "react";
import { Props } from "../base";

function PreProcessingIcon({ color = "#346", size = 24 }: Partial<Props>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 25 25"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.25566 19.969H16.3564C16.5832 20.0069 16.8157 20.0272 17.0483 20.0272C17.6557 20.0272 18.2661 19.8963 18.8329 19.6289C18.8826 19.6077 18.9323 19.5825 18.9836 19.5566L19.0189 19.5388L19.0974 19.4952L20.4054 20.905C20.8851 21.4223 21.6961 21.437 22.1931 20.9399C22.6901 20.4429 22.6756 19.6319 22.1611 19.1523L20.7485 17.8413C21.5988 16.4939 21.5039 14.2146 20.0014 12.7488C19.7195 12.4726 19.4055 12.243 19.0742 12.0657V5.45878C19.0742 4.49083 18.2864 3.70312 17.3186 3.70312H6.25566C5.2877 3.70312 4.5 4.49083 4.5 5.45878V18.2133C4.5 19.1813 5.2877 19.969 6.25566 19.969ZM19.5974 13.1673C21.01 14.5459 21.0948 16.8598 19.6614 18.3413C18.2266 19.8134 15.8976 19.7806 14.4874 18.4082C13.1263 17.0889 12.926 14.7792 14.4177 13.2342C15.8458 11.7877 18.1594 11.7551 19.5974 13.1673ZM17.0075 5.10126C17.2924 5.10126 17.522 5.3309 17.522 5.61575C17.522 5.9006 17.2924 6.13024 17.0075 6.13024C16.7227 6.13024 16.493 5.9006 16.493 5.61575C16.493 5.3309 16.7227 5.10126 17.0075 5.10126ZM15.3797 5.10126C15.6618 5.10126 15.8942 5.3309 15.8942 5.61575C15.8942 5.9006 15.6618 6.13024 15.3797 6.13024C15.0949 6.13024 14.8653 5.9006 14.8653 5.61575C14.8653 5.3309 15.0949 5.10126 15.3797 5.10126ZM13.752 5.10126C14.034 5.10126 14.2636 5.3309 14.2636 5.61575C14.2636 5.9006 14.034 6.13024 13.752 6.13024C13.4672 6.13024 13.2375 5.9006 13.2375 5.61575C13.2375 5.3309 13.4672 5.10126 13.752 5.10126ZM6.05511 5.60412C6.05511 5.3309 6.27602 5.11006 6.54923 5.11006H10.2495C10.5227 5.11006 10.7436 5.3309 10.7436 5.60412V5.62739C10.7436 5.9006 10.5227 6.12159 10.2495 6.12159H6.54923C6.27602 6.12159 6.05511 5.9006 6.05511 5.62739V5.60412ZM5.08134 7.49347H18.4929V11.8041C17.3534 11.3886 16.0716 11.4815 14.9961 12.0715H13.7811C13.6212 12.0715 13.4904 12.2024 13.4904 12.3622C13.4904 12.525 13.6212 12.6529 13.7811 12.6529H14.1851C14.1212 12.7081 14.0602 12.7663 14.002 12.8273C13.4323 13.4116 13.0689 14.115 12.9033 14.859H6.48239C6.31959 14.859 6.19172 14.9898 6.19172 15.1497C6.19172 15.3096 6.31959 15.4403 6.48239 15.4403H12.8161C12.7957 15.702 12.7986 15.9636 12.8248 16.2252H6.48239C6.31959 16.2252 6.19172 16.3561 6.19172 16.5159C6.19172 16.6757 6.31959 16.8065 6.48239 16.8065H12.9265C13.1097 17.5507 13.4933 18.2541 14.0834 18.8267C14.3043 19.0418 14.5485 19.2307 14.8042 19.3876H6.25566C5.60747 19.3876 5.08134 18.8587 5.08134 18.2133V7.49347ZM12.0749 11.195C12.0749 12.7181 10.8367 13.9563 9.31358 13.9563C8.26721 13.9563 7.32544 13.375 6.8487 12.4391C6.65107 12.0495 6.55222 11.631 6.55222 11.195C6.55222 9.67193 7.79047 8.4336 9.31358 8.4336C10.8367 8.4336 12.0749 9.67193 12.0749 11.195ZM9.02291 11.0177V9.03538C7.95617 9.17773 7.13355 10.0935 7.13355 11.195C7.13355 11.4421 7.17429 11.6805 7.25277 11.9101L9.02291 11.0177ZM17.0919 9.81571H13.7811C13.6213 9.81571 13.4904 9.68486 13.4904 9.52504C13.4904 9.36509 13.6213 9.23438 13.7811 9.23438H17.0919C17.2517 9.23438 17.3825 9.36509 17.3825 9.52504C17.3825 9.68486 17.2517 9.81571 17.0919 9.81571ZM13.7811 11.2344H17.0918C17.2517 11.2344 17.3825 11.1036 17.3825 10.9438C17.3825 10.7839 17.2517 10.6531 17.0918 10.6531H13.7811C13.6212 10.6531 13.4904 10.7839 13.4904 10.9438C13.4904 11.1036 13.6212 11.2344 13.7811 11.2344ZM11.7871 18.1727H6.48239C6.31959 18.1727 6.19172 18.0419 6.19172 17.8821C6.19172 17.7221 6.31959 17.5914 6.48239 17.5914H11.7871C11.947 17.5914 12.0778 17.7221 12.0778 17.8821C12.0778 18.0419 11.947 18.1727 11.7871 18.1727ZM15.0949 17.7831C14.0349 16.7442 13.919 15.0142 15.0455 13.8415C16.1105 12.7802 17.8193 12.6275 19.0742 13.8764C20.1001 14.9692 20.0584 16.6422 19.0742 17.6958C18.3124 18.6527 16.3625 19.0158 15.0949 17.7831ZM15.7722 17.1755C15.9321 17.1755 16.0629 17.0448 16.0629 16.8848V16.1001C16.0629 15.9373 15.9321 15.8094 15.7722 15.8094C15.6094 15.8094 15.4815 15.9373 15.4815 16.1001V16.8848C15.4815 17.0448 15.6094 17.1755 15.7722 17.1755ZM17.0424 17.1755C17.2023 17.1755 17.3331 17.0448 17.3331 16.8848V15.3124C17.3331 15.1524 17.2023 15.0217 17.0424 15.0217C16.8915 15.0217 16.7518 15.1432 16.7518 15.3124V16.8848C16.7518 17.0448 16.8826 17.1755 17.0424 17.1755ZM18.6033 16.8848V14.6903C18.6033 14.5284 18.4696 14.3997 18.3126 14.3997C18.1527 14.3997 18.022 14.5275 18.022 14.6903V16.8848C18.022 17.0448 18.1527 17.1755 18.3126 17.1755C18.4686 17.1755 18.6033 17.0475 18.6033 16.8848Z"
        fill={color}
      />
    </svg>
  );
}

export default PreProcessingIcon;
