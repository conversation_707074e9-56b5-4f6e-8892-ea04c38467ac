import React from "react";
import { Props } from "../base";

function PreProcessingCompletedIcon({ size = 24 }: Partial<Props>) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_13984_431701)">
        <g filter="url(#filter0_d_13984_431701)">
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M12 4C7.584 4 4 7.584 4 12C4 16.416 7.584 20 12 20C16.416 20 20 16.416 20 12C20 7.584 16.416 4 12 4ZM15.1052 9.03194L10.4012 13.7359L8.89716 12.2319C8.74769 12.0821 8.54477 11.998 8.33316 11.998C8.12154 11.998 7.91862 12.0821 7.76916 12.2319C7.45716 12.5439 7.45716 13.0479 7.76916 13.3599L9.84116 15.4319C10.1532 15.7439 10.6572 15.7439 10.9692 15.4319L16.2412 10.1599C16.5532 9.84794 16.5532 9.34394 16.2412 9.03194C15.9292 8.71994 15.4172 8.71994 15.1052 9.03194Z"
            fill="#15803D"
          />
          <path
            d="M15.1052 9.03194L10.4012 13.7359L8.89716 12.2319C8.74769 12.0821 8.54477 11.998 8.33316 11.998C8.12154 11.998 7.91862 12.0821 7.76916 12.2319C7.45716 12.5439 7.45716 13.0479 7.76916 13.3599L9.84116 15.4319C10.1532 15.7439 10.6572 15.7439 10.9692 15.4319L16.2412 10.1599C16.5532 9.84794 16.5532 9.34394 16.2412 9.03194C15.9292 8.71994 15.4172 8.71994 15.1052 9.03194Z"
            fill="white"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_13984_431701"
          x="-1"
          y="1"
          width="26"
          height="26"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="2" />
          <feGaussianBlur stdDeviation="2.5" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0.14902 0 0 0 0 0.2 0 0 0 0 0.301961 0 0 0 0.15 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_13984_431701"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_13984_431701"
            result="shape"
          />
        </filter>
        <clipPath id="clip0_13984_431701">
          <rect width="24" height="24" rx="12" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default PreProcessingCompletedIcon;
