import React from "react";
import { Props } from "../base";

function PreProcessingNewIcon({ className, size = 24 }: Partial<Props>) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 25 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="4.5" y="4" width="16" height="16" rx="8" fill="#818CF8" />
      <path
        d="M9.49872 13.3359C9.45493 13.3358 9.41153 13.3442 9.37101 13.3608C9.3305 13.3774 9.29365 13.4019 9.26257 13.4327C9.23149 13.4636 9.2068 13.5002 9.1899 13.5406C9.173 13.581 9.16421 13.6244 9.16406 13.6681V14.5023C9.16406 14.9586 9.54194 15.3365 9.99825 15.3365H10.83C10.874 15.3367 10.9176 15.3282 10.9583 15.3114C10.9989 15.2946 11.0359 15.27 11.067 15.2389C11.0981 15.2078 11.1228 15.1708 11.1395 15.1301C11.1563 15.0895 11.1648 15.0459 11.1646 15.0019C11.1645 14.9581 11.1557 14.9147 11.1388 14.8743C11.1219 14.8339 11.0972 14.7973 11.0661 14.7664C11.0351 14.7356 10.9982 14.7112 10.9577 14.6945C10.9172 14.6779 10.8738 14.6695 10.83 14.6697H9.99825C9.90225 14.6697 9.83092 14.5983 9.83092 14.5023V13.6681C9.83062 13.5801 9.79552 13.4958 9.73328 13.4336C9.67105 13.3713 9.58674 13.3362 9.49872 13.3359Z"
        fill="white"
      />
      <path
        d="M9.50031 11C9.41232 10.9998 9.32783 11.0345 9.26527 11.0964L8.59793 11.7637C8.56689 11.7947 8.54226 11.8315 8.52545 11.872C8.50865 11.9125 8.5 11.9559 8.5 11.9997C8.5 12.0436 8.50865 12.087 8.52545 12.1275C8.54226 12.168 8.56689 12.2048 8.59793 12.2357L9.26527 12.9031C9.32774 12.9652 9.41223 13 9.50031 13C9.58839 13 9.67289 12.9652 9.73535 12.9031L10.402 12.2357C10.4331 12.2048 10.4577 12.168 10.4745 12.1275C10.4913 12.087 10.5 12.0436 10.5 11.9997C10.5 11.9559 10.4913 11.9125 10.4745 11.872C10.4577 11.8315 10.4331 11.7947 10.402 11.7637L9.73535 11.0964C9.67279 11.0345 9.58831 10.9998 9.50031 11Z"
        fill="white"
      />
      <path
        d="M9.99825 8.66407C9.54194 8.66407 9.16406 9.04195 9.16406 9.49826V10.3324C9.16421 10.3762 9.173 10.4196 9.1899 10.46C9.2068 10.5004 9.23149 10.537 9.26257 10.5679C9.29365 10.5987 9.3305 10.6232 9.37101 10.6398C9.41153 10.6564 9.45493 10.6648 9.49872 10.6646C9.58674 10.6643 9.67105 10.6293 9.73328 10.567C9.79552 10.5048 9.83062 10.4205 9.83092 10.3324V9.49826C9.83092 9.40225 9.90224 9.33093 9.99825 9.33093H10.83C10.8738 9.3311 10.9172 9.32264 10.9577 9.30604C10.9982 9.28944 11.0351 9.26502 11.0661 9.23417C11.0972 9.20332 11.1219 9.16665 11.1388 9.12625C11.1557 9.08586 11.1645 9.04253 11.1646 8.99874C11.1648 8.95474 11.1563 8.91113 11.1395 8.87045C11.1228 8.82977 11.0981 8.79281 11.067 8.7617C11.0359 8.73059 10.9989 8.70594 10.9583 8.68918C10.9176 8.67243 10.874 8.66389 10.83 8.66407H9.99825Z"
        fill="white"
      />
      <path
        d="M10.1706 11.6641C10.1266 11.6639 10.083 11.6724 10.0423 11.6892C10.0017 11.7059 9.96469 11.7306 9.93358 11.7617C9.90247 11.7928 9.87782 11.8298 9.86107 11.8705C9.84431 11.9111 9.83577 11.9547 9.83594 11.9987C9.83609 12.0425 9.84488 12.0859 9.86178 12.1263C9.87868 12.1666 9.90337 12.2033 9.93445 12.2342C9.96552 12.265 10.0024 12.2894 10.0429 12.306C10.0834 12.3226 10.1268 12.3311 10.1706 12.3309H10.835C10.8788 12.3311 10.9222 12.3226 10.9627 12.306C11.0032 12.2894 11.0401 12.265 11.0712 12.2342C11.1022 12.2033 11.1269 12.1666 11.1438 12.1263C11.1607 12.0859 11.1695 12.0425 11.1697 11.9987C11.1698 11.9547 11.1613 11.9111 11.1445 11.8705C11.1278 11.8298 11.1031 11.7928 11.072 11.7617C11.0409 11.7306 11.004 11.7059 10.9633 11.6892C10.9226 11.6724 10.879 11.6639 10.835 11.6641H10.1706Z"
        fill="white"
      />
      <path
        d="M12.502 8C11.9537 8 11.5 8.45307 11.5 9.00134C11.5 9.5496 11.9537 10.0007 12.502 10.0007H15.5015C16.0497 10.0007 16.5002 9.5496 16.5002 9.00134C16.5002 8.45306 16.0497 8 15.5015 8H12.502Z"
        fill="white"
      />
      <path
        d="M12.502 14C11.9537 14 11.5 14.4511 11.5 14.9994C11.5 15.5477 11.9537 15.9981 12.502 15.9981H15.5015C16.0497 15.9981 16.5002 15.5477 16.5002 14.9994C16.5002 14.4511 16.0497 14 15.5015 14H12.502Z"
        fill="white"
      />
      <path
        d="M12.5013 11C11.9531 11 11.5 11.4511 11.5 11.9994C11.5 12.5477 11.9531 12.9981 12.5013 12.9981H15.5015C16.0497 12.9981 16.5002 12.5477 16.5002 11.9994C16.5002 11.4511 16.0497 11 15.5015 11H12.5013Z"
        fill="white"
      />
    </svg>
  );
}

export default PreProcessingNewIcon;
