import React from "react";
import { Props } from "../base";

function AnnotationBlockIcon({ color = "#346", size = 24 }: Partial<Props>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M20.6657 8.18666C20.6776 8.19265 20.6895 8.19867 20.7015 8.20772C21.5257 8.63511 21.8208 9.65517 21.4276 10.4406L18.4656 16.522V19.8019C18.4656 20.7157 17.7212 21.46 16.8075 21.46H5.65804C4.7443 21.46 4 20.7157 4 19.8019V6.58898C4 5.67523 4.7443 4.93093 5.65804 4.93093H7.53998V4.87036C7.53998 4.48004 7.85466 4.16847 8.24195 4.16847H9.56116C9.5551 4.13818 9.5551 4.10789 9.5551 4.07761C9.5551 3.15189 10.3055 2.39844 11.2313 2.39844C12.1571 2.39844 12.9105 3.15189 12.9105 4.07761C12.9105 4.10789 12.9105 4.13818 12.9044 4.16847H14.2206C14.6109 4.16847 14.9256 4.48004 14.9256 4.87036V4.93093H16.8075C17.7212 4.93093 18.4656 5.67523 18.4656 6.58898V8.8551C18.4988 8.80073 18.5321 8.74932 18.5685 8.70086C19.0173 8.06468 19.9167 7.81901 20.6652 8.18645L20.6657 8.18666ZM11.8243 3.89914C11.8243 3.57235 11.558 3.30908 11.2343 3.30908C10.9076 3.30908 10.6413 3.57235 10.6413 3.89914C10.6413 4.22593 10.9076 4.4892 11.2343 4.4892C11.558 4.4892 11.8243 4.22593 11.8243 3.89914ZM15.3976 15.1546L17.8605 10.0896V6.58898C17.8605 6.00793 17.3884 5.53606 16.8075 5.53606H14.9256V5.75087C14.9256 6.14414 14.6109 6.46177 14.2206 6.46177H8.24195C7.85466 6.46177 7.53998 6.14414 7.53998 5.75087V5.53606H5.65804C5.07714 5.53606 4.60513 6.00793 4.60513 6.58898V19.8019C4.60513 20.3828 5.07714 20.8548 5.65804 20.8548H16.8075C17.3884 20.8548 17.8605 20.3828 17.8605 19.8019V17.581C17.7512 17.6904 17.7191 17.7111 16.8606 18.2625C16.5644 18.4528 16.1698 18.7063 15.6396 19.0485C15.553 19.1082 15.4315 19.111 15.3431 19.0666C15.2505 19.0203 15.1818 18.9241 15.1737 18.8186L15.0617 17.4449H6.54756C6.37818 17.4449 6.245 17.3087 6.245 17.1424C6.245 16.9759 6.37818 16.8398 6.54756 16.8398H15.0134C15.0125 16.782 15.0058 16.7184 14.9984 16.6489C14.9739 16.4171 14.9424 16.1204 15.101 15.7597H6.54756C6.37818 15.7597 6.245 15.6264 6.245 15.4571C6.245 15.2906 6.37818 15.1546 6.54756 15.1546H15.3976ZM16.9536 17.4829C17.3301 17.2432 17.3629 17.2224 17.4429 17.1424L15.5912 16.2286C15.5893 16.2422 15.5876 16.2536 15.5862 16.265C15.5733 16.3701 15.5886 16.4785 15.7365 18.2649C16.3434 17.8713 16.7166 17.6337 16.9536 17.4829ZM20.638 10.6796L20.8831 10.1713C21.15 9.64355 20.9324 8.99483 20.3989 8.731C20.3871 8.7281 20.3782 8.72222 20.3692 8.71627L20.3687 8.71593C19.8423 8.47394 19.2129 8.69776 18.9557 9.22119L18.6955 9.75378L20.638 10.6796ZM11.7002 10.4285C11.7335 10.4496 11.7728 10.4588 11.8122 10.4588C11.8515 10.4588 11.8939 10.4467 11.9272 10.4254L14.0148 9.16372C14.0663 9.13344 14.1026 9.08202 14.1177 9.02455C14.1329 8.96413 14.1207 8.90356 14.0905 8.85215C13.6003 8.04728 12.7501 7.56625 11.8122 7.56625C11.6852 7.56625 11.5853 7.66907 11.5853 7.79317V10.2319C11.5853 10.3136 11.6276 10.3892 11.7002 10.4285ZM14.0753 9.95929L11.9877 11.2239C11.9301 11.2603 11.8878 11.3208 11.8818 11.3903C11.8727 11.46 11.8968 11.5325 11.9483 11.581L13.7032 13.2813C13.7455 13.3237 13.8031 13.3448 13.8635 13.3448H13.8666C13.9271 13.3448 13.9846 13.3207 14.0239 13.2783C14.511 12.776 14.7803 12.1165 14.7803 11.4237C14.7803 10.9364 14.6411 10.4524 14.387 10.0349C14.3204 9.9259 14.1813 9.89266 14.0753 9.95929ZM10.7381 8.976V11.2875L12.4022 12.9031C12.5232 13.0182 12.5263 13.2088 12.4113 13.3298C11.8879 13.8714 11.1859 14.171 10.4356 14.171C8.91974 14.171 7.68828 12.9365 7.68828 11.4237C7.68828 9.90482 8.91974 8.67344 10.4356 8.67344C10.602 8.67344 10.7381 8.8095 10.7381 8.976ZM6.54756 19.1302H11.1133C11.2797 19.1302 11.4158 18.9941 11.4158 18.8276C11.4158 18.6612 11.2797 18.525 11.1133 18.525H6.54756C6.37818 18.525 6.245 18.6612 6.245 18.8276C6.245 18.9941 6.37818 19.1302 6.54756 19.1302Z"
        fill={color}
      />
    </svg>
  );
}

export default AnnotationBlockIcon;
