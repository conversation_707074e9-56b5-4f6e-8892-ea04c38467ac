import logging
from drf_yasg.utils import swagger_auto_schema

from django.utils.decorators import method_decorator
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>arser, J<PERSON>NParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.authtoken.models import Token
from rest_framework import generics
from rest_framework.views import APIView
from django.conf import settings
from django.contrib import auth

from core.permissions import all_permissions, ViewClassPermission
from dms_connector.api import AuthenticatedAPIView, DMS_USER_API, DMS_AUTH_API, DMS_AUTH_SETTING_API
from dms_connector.serializers import FilterSerializer, CreateEditMemberRoleSerializer
from label_studio.dms_connector import serializers

from auth.serializers import *
from rest_framework.permissions import AllowAny

logger = logging.getLogger(__name__)


@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Get my-profile",
    operation_description="Get information about my profile",
))
@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Update my-profile",
    operation_description="Update information of my profile",
    request_body=AuthPutMyProfileSerializer
))
class AuthGetPutMyProfileAPI(AuthenticatedAPIView):
    def get(self, request, *args, **kwargs):
        user = request.user
        status_code, json_data = DMS_AUTH_API().get_profile(user=user)

        return Response(json_data, status_code)

    def put(self, request, *args, **kwargs):
        user = request.user
        serializer = AuthPutMyProfileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        status_code, json_data = DMS_AUTH_API().update_profile(user, serializer.data)

        return Response(json_data, status_code)
    

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Get my-permission",
    operation_description="Get information about my permission",
))
class AuthGetMyPermissionAPI(AuthenticatedAPIView):
    def get(self, request, *args, **kwargs):
        user = request.user
        status_code, json_data = DMS_AUTH_API().get_permission(user=user)

        return Response(json_data, status_code)


@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Register",
    operation_description="Register a new account",
    request_body=AuthRegisterSerializer
))
class AuthRegisterAPI(APIView):
    permission_classes =[AllowAny]

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = AuthRegisterSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.validate_pw()
        status_code, json_data = DMS_AUTH_API().register(serializer.data)

        return Response(json_data, status_code)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Register",
    operation_description="Validate register a new account",
    request_body=AuthRegisterSerializer
))
class AuthValidateRegisterAPI(APIView):
    permission_classes =[AllowAny]

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = AuthRegisterSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.validate_pw()
        status_code, json_data = DMS_AUTH_API().validate_account_register(serializer.data)

        return Response(json_data, status_code)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Forgot password",
    operation_description="User forgot password",
    request_body=AuthForgotPasswordSerializer
))
class AuthForgotPasswordAPI(APIView):
    permission_classes =[AllowAny]
    
    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = AuthForgotPasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        status_code, json_data = DMS_AUTH_API().forgot_password(serializer.data)

        return Response(json_data, status_code)


@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Reset password",
    operation_description="User reset password",
    request_body=AuthResetPasswordSerializer
))
class AuthResetPasswordAPI(APIView):
    permission_classes =[AllowAny]

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = AuthResetPasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        status_code, json_data = DMS_AUTH_API().reset_password(serializer.data)

        return Response(json_data, status_code)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Validate forgot password token",
    operation_description="User validate forgot password token",
    request_body=AuthValidateForgotPassSerializer
))
class AuthValidateForgotPasswordAPI(APIView):
    permission_classes =[AllowAny]
    
    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = AuthValidateForgotPassSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        status_code, json_data = DMS_AUTH_API().validate_forgot_password(serializer.data)

        return Response(json_data, status_code)


@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Change password",
    operation_description="User change password",
    request_body=AuthChangePasswordSerializer
))
class AuthChangePasswordAPI(AuthenticatedAPIView):
    def put(self, request, *args, **kwargs):
        user = request.user
        serializer = AuthChangePasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        status_code, json_data = DMS_AUTH_API().change_password(user, serializer.data)

        return Response(json_data, status_code)


@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Validate forgot password token",
    operation_description="Validate forgot password token",
    request_body=AuthValidateForgotPasswordTokenSerializer
))
class AuthValidateForgotPasswordTokenAPI(AuthenticatedAPIView):
    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = AuthValidateForgotPasswordTokenSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        status_code, json_data = DMS_AUTH_API().validate_forgot_password_token(serializer.data)

        return Response(json_data, status_code)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=["AI Service"], operation_summary="get AIS HOST",
    operation_description="get AIS HOST"
))
class AIServiceHostAPI(APIView):
    def get(self, request, *args, **kwargs):
        user = request.user
        ai_host = settings.AIS_HOST
        result = {
            "success": True,
            "error": None,
            "message": None,
            "data": ai_host
        }

        return Response(result, 200)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Login",
    operation_description="Login",
    request_body=AuthLoginSerializer
))
class AuthLoginAPI(APIView):
    permission_classes =[AllowAny]
    def post(self, request, *args, **kwargs):
        user = request.user

        serializer = AuthLoginSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        # validate_pw = serializer.validate_pw()
        # if validate_pw['success'] == False:
        #     result = {
        #         "success": False,
        #         "errorCode": None,
        #         "message": validate_pw['message'],
        #         "data": None
        #     }
        #     return Response(result, 401)
        status_code, json_data, user_authen = DMS_AUTH_API().login(serializer.data)

        if status_code==200 and json_data['success'] == True and user is not None:
        # if user is not None:
            auth.login(request, user_authen, backend='django.contrib.auth.backends.ModelBackend')

        return Response(json_data, status_code)

@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Logout",
    operation_description="Logout"
))   
class AuthLogoutAPI(AuthenticatedAPIView):

    def put(self, request, *args, **kwargs):
        user=request.user
        status_code, json_data = DMS_AUTH_API().logout(user)
        if status_code==200 and json_data['success'] == True:
            auth.logout(request)

        return Response(json_data, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=["Auth Setting"], operation_summary="Get authentication setting",
    operation_description="Get authentication setting"
))
class AuthSettingGetAPI(AuthenticatedAPIView):
    def get(self, request, *args, **kwargs):
        user=request.user
        status_code, json_data = DMS_AUTH_SETTING_API().list(user)

        return Response(json_data, status_code)
    
@method_decorator(name='patch', decorator=swagger_auto_schema(
    tags=["Auth Setting"], operation_summary="Eidt authentication setting",
    operation_description="Eidt authentication setting",
    request_body=AuthSettingEditSerializer
))
class AuthSettingDetailAPI(AuthenticatedAPIView):
    def patch(self, request, pk, *args, **kwargs):
        user=request.user
        serializer = AuthSettingEditSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_AUTH_SETTING_API().edit(user, pk, data=serializer.data)

        return Response(json_data, status_code)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="LoginSSO",
    operation_description="LoginSSO",
    request_body=AuthLoginSSOSerializer
))
class AuthLoginSSOAPI(APIView):
    permission_classes =[AllowAny]

    def post(self, request, *args, **kwargs):
        user = request.user

        serializer = AuthLoginSSOSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        status_code, json_data, user_authen = DMS_AUTH_API().login_sso(serializer.data)

        if status_code==200:
            if json_data['success'] == True:
                if user is not None:
                    auth.login(request, user_authen, backend='django.contrib.auth.backends.ModelBackend')

        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Resend active",
    operation_description="Resend active",
    request_body=AuthPublicTASResendActiveSerializer
))
class AuthResendActiveAPI(APIView):
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        user = request.user

        serializer = AuthPublicTASResendActiveSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_AUTH_API().resend_active_account(serializer.data)

        return Response(json_data, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=["Auth"], operation_summary="Active account",
    operation_description="Active account",
    request_body=AuthPublicTASActiveAccountSerializer
))
class AuthActiveAccountAPI(APIView):
    permission_classes = [AllowAny]

    def post(self, request, *args, **kwargs):
        user = request.user

        serializer = AuthPublicTASActiveAccountSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_AUTH_API().active_account(serializer.data)

        return Response(json_data, status_code)