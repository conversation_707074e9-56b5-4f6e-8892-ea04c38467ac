import json
import re
from rest_framework import serializers

from label_studio.dms_connector.serializers import FilterSerializer
# from django.contrib.auth import password_validation
from django.core.exceptions import ValidationError

class AuthPutMyProfileSerializer(serializers.Serializer):
    avatar = serializers.CharField(required=False)
    firstName = serializers.CharField(max_length=1000, required=False, allow_blank=True)
    lastName =serializers.CharField(max_length=1000, required=False, allow_blank=True)
    phoneNumber = serializers.Char<PERSON>ield(max_length=1000, required=False, allow_blank=True)
    address = serializers.Char<PERSON>ield(max_length=1000, required=False, allow_blank=True)
    organization = serializers.Char<PERSON>ield(max_length=1000, required=False, allow_blank=True)
    description = serializers.Char<PERSON>ield(max_length=1000, required=False, allow_blank=True)
    
class AuthRegisterSerializer(serializers.Serializer):
    userName = serializers.Cha<PERSON><PERSON><PERSON>(max_length=1000, required=False)
    email = serializers.Email<PERSON>ield(required=False)
    password = serializers.CharField(min_length=8, required=False, style={'input_type': 'password', 'placeholder': 'Password'})
    firstName = serializers.CharField(max_length=1000, required=False)
    lastName = serializers.CharField(max_length=1000, required=False)

    def validate_pw(self):
        if not re.findall('\d', self.data['password']):
            raise ValidationError(
                ("The password must contain at least 1 digit, 0-9."),
                code='password_no_number',
            )
        if not re.findall('[A-Z]', self.data['password']):
            raise ValidationError(
                ("The password must contain at least 1 uppercase letter, A-Z."),
                code='password_no_upper',
            )
        if not re.findall('[a-z]', self.data['password']):
            raise ValidationError(
                ("The password must contain at least 1 lowercase letter, a-z."),
                code='password_no_lower',
            )
        # if not re.findall('[()[\]{}|\\`~!@#$%^&*_\-+=;:\'",<>./?]', self.data['password']):
        #     raise ValidationError(
        #         ("The password must contain at least 1 symbol: " +
        #           "()[]{}|\`~!@#$%^&*_-+=;:'\",<>./?"),
        #         code='password_no_symbol',
        #     )
        

class AuthForgotPasswordSerializer(serializers.Serializer):
    email = serializers.EmailField(required=False)

class AuthResetPasswordSerializer(serializers.Serializer):
    newPassword = serializers.CharField(min_length=8, required=False, style={'input_type': 'password', 'placeholder': 'Password'})
    token = serializers.CharField(max_length=1000, required=False)

    def validate_pw(self):
        if not re.findall('\d', self.data['newPassword']):
            raise ValidationError(
                ("The password must contain at least 1 digit, 0-9."),
                code='password_no_number',
            )
        if not re.findall('[A-Z]', self.data['newPassword']):
            raise ValidationError(
                ("The password must contain at least 1 uppercase letter, A-Z."),
                code='password_no_upper',
            )
        if not re.findall('[a-z]', self.data['newPassword']):
            raise ValidationError(
                ("The password must contain at least 1 lowercase letter, a-z."),
                code='password_no_lower',
            )
        # if not re.findall('[()[\]{}|\\`~!@#$%^&*_\-+=;:\'",<>./?]', self.data['newPassword']):
        #     raise ValidationError(
        #         ("The password must contain at least 1 symbol: " +
        #           "()[]{}|\`~!@#$%^&*_-+=;:'\",<>./?"),
        #         code='password_no_symbol',
        #     )

class AuthValidateForgotPassSerializer(serializers.Serializer):
    token = serializers.CharField(max_length=1000, required=False)
    otpCode = serializers.CharField(max_length=1000, required=False)

class AuthChangePasswordSerializer(serializers.Serializer):
    oldPassword = serializers.CharField(min_length=8, required=False, style={'input_type': 'password', 'placeholder': 'Password'})
    password = serializers.CharField(min_length=8, required=False, style={'input_type': 'password', 'placeholder': 'Password'})

    def validate_pw(self):
        if not re.findall('\d', self.data['newPassword']):
            raise ValidationError(
                ("The password must contain at least 1 digit, 0-9."),
                code='password_no_number',
            )
        if not re.findall('[A-Z]', self.data['newPassword']):
            raise ValidationError(
                ("The password must contain at least 1 uppercase letter, A-Z."),
                code='password_no_upper',
            )
        if not re.findall('[a-z]', self.data['newPassword']):
            raise ValidationError(
                ("The password must contain at least 1 lowercase letter, a-z."),
                code='password_no_lower',
            )
        # if not re.findall('[()[\]{}|\\`~!@#$%^&*_\-+=;:\'",<>./?]', self.data['newPassword']):
        #     raise ValidationError(
        #         ("The password must contain at least 1 symbol: " +
        #           "()[]{}|\`~!@#$%^&*_-+=;:'\",<>./?"),
        #         code='password_no_symbol',
        #     )

class AuthValidateForgotPasswordTokenSerializer(serializers.Serializer):
    token = serializers.CharField(max_length=1000, required=False)

class AuthLoginSerializer(serializers.Serializer):
    userName = serializers.CharField(max_length=1000, required=False)
    password = serializers.CharField(min_length=8, required=False, style={'input_type': 'password', 'placeholder': 'Password'})
    ssoAccessToken = serializers.CharField(max_length=1000, required=False)

    def validate_pw(self):
        result = {
            "success": True,
            "message": None
        }
        if not re.findall('\d', self.data['password']):
            # raise ValidationError(
            #     ("The password must contain at least 1 digit, 0-9."),
            #     code='password_no_number',
            # )
            result['success']=False
            result['message']="The password must contain at least 1 digit, 0-9."
        if not re.findall('[A-Z]', self.data['password']):
            # raise ValidationError(
            #     ("The password must contain at least 1 uppercase letter, A-Z."),
            #     code='password_no_upper',
            # )
            result['success']=False
            result['message']="The password must contain at least 1 uppercase letter, A-Z."
        if not re.findall('[a-z]', self.data['password']):
            # raise ValidationError(
            #     ("The password must contain at least 1 lowercase letter, a-z."),
            #     code='password_no_lower',
            # )
            result['success']=False
            result['message']="The password must contain at least 1 lowercase letter, a-z."
        # if not re.findall('[()[\]{}|\\`~!@#$%^&*_\-+=;:\'",<>./?]', self.data['password']):
        #     raise ValidationError(
        #         ("The password must contain at least 1 symbol: " +
        #           "()[]{}|\`~!@#$%^&*_-+=;:'\",<>./?"),
        #         code='password_no_symbol',
        #     )
        return result
    
class AuthSettingEditSerializer(serializers.Serializer):
    anonymousAccess = serializers.BooleanField(required=False)
    selfRegister = serializers.BooleanField(required=False)
    localSignIn = serializers.BooleanField(required=False)
    enableSSO = serializers.BooleanField(required=False)

class AuthLoginSSOSerializer(serializers.Serializer):
    ssoToken = serializers.CharField(max_length=999999, allow_null=True, required=False)
    ssoType = serializers.ChoiceField(choices=[("AAD", "AAD"), ("Cognito", "Cognito")], required=False)

class AuthPublicTASResendActiveSerializer(serializers.Serializer):
    email = serializers.EmailField(required=False)

class AuthPublicTASActiveAccountSerializer(serializers.Serializer):
    token = serializers.CharField(max_length=1000, required=False)