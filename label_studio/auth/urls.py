"""This file and its contents are licensed under the Apache License 2.0. Please see the included NOTICE for copyright information and LICENSE for a copy of the license.
"""
from django.conf.urls import url
from django.shortcuts import redirect
from django.urls import include, path

from . import api, views

app_name = 'auth'

_api_urlpatterns = [
    path('my-profile/', api.AuthGetPutMyProfileAPI.as_view(), name='my-profile'),
    path('my-permission/', api.AuthGetMyPermissionAPI.as_view(), name='my-permission'),
    path('register', api.AuthRegisterAPI.as_view(), name='register'),
    path('validate_register', api.AuthValidateRegisterAPI.as_view(), name='validate-register'),
    path('forgot-password', api.AuthForgotPasswordAPI.as_view(), name='forgot-password'),
    path('reset-password', api.AuthResetPasswordAPI.as_view(), name='reset-password'),
    path('validate-forgot-password-token', api.AuthValidateForgotPasswordAPI.as_view()),
    path('change-password', api.AuthChangePasswordAPI.as_view(), name='change-password'),
    path('validate-forgot-password-token', api.AuthValidateForgotPasswordTokenAPI.as_view(), name='validate-forgot-password-token'),

    path('login', api.AuthLoginAPI.as_view(), name='login'),
    path('logout', api.AuthLogoutAPI.as_view(), name='logout'),

    path('login-sso', api.AuthLoginSSOAPI.as_view(), name='login-sso'),
    path('active-account', api.AuthActiveAccountAPI.as_view(), name='active-account'),
    path('active-account-resend', api.AuthResendActiveAPI.as_view(), name='resend-active-account'),
]

urlpatterns = [
    path('api/auth/', include((_api_urlpatterns, app_name), namespace='api')),
    path('api/ais/get_host', api.AIServiceHostAPI.as_view(), name='get-ais-host'),
    path('api/auth-setting', api.AuthSettingGetAPI.as_view(), name='get-auth-setting'),
    path('api/auth-setting/<uuid:pk>', api.AuthSettingDetailAPI.as_view(), name='edit-auth-setting'),
]