"""This file and its contents are licensed under the Apache License 2.0. Please see the included NOTICE for copyright information and LICENSE for a copy of the license.
"""
from django.conf import settings
from rest_framework import serializers
from dms_connector.utils import convert_bytes

from dms_connector.serializers import IdByProjectSerializer, FilterSerializer

DATATYPE_CHOICES = [
    ('Png', 'Png'),
    ('Jpg', 'Jpg'),
    ('Jpeg', 'Jpeg'),
    ('<PERSON><PERSON>', '<PERSON>son'),
    ('Csv', 'Csv')
]

IMAGETYPE_CHOICES = [
    ('Original', 'Original'),
    ('Thumbnail', 'Thumbnail'),
    ('Mobile', 'Mobile'),
    ('Desktop', 'Desktop'),
    ('Tablet', 'Tablet')
]

DATA_ACTION_CHOICES = [
    ('create', 'create'),
    ('delete', 'delete'),
    ('read', 'read'),
    ('rename', 'rename'),
    ('details', 'details'),
    ('search', 'search'),
    ('copy', 'copy'),
    ('move', 'move'),
    ('save', 'save'),
    ('download', 'download'),
]

SEARCH_TYPE_CHOICES = [
    ('ProjectFile', 'ProjectFile'),
    ('TaskAssigment', 'TaskAssigment')
]

CODE_OPERATORS = [
    ('Equal', 'Equal'),
    ('Like', 'Like'),
    ('StartWith', 'StartWith'),
    ('EndWith', 'EndWith'),
]

VALUE_OPERATORS = [
    ('Equal', 'Equal'),
    ('Like', 'Like'),
    ('StartWith', 'StartWith'),
    ('EndWith', 'EndWith'),
    ('Between', 'Between'),
    ('GreaterThan', 'GreaterThan'),
    ('GreaterThanOrEqual', 'GreaterThanOrEqual'),
    ('LesserThan', 'LesserThan'),
    ('LesserThanOrEqual', 'LesserThanOrEqual'),
]

LOGICAL_OPERATORS_CHOICES = [
    ('And', 'And'),
    ('Or', 'Or'),
]
QUERY_OPERATORS_CHOICES = [
    'Equal', 'Equal',
    'Contain', 'Contain',
    'Different', 'Different',
    'GreaterThan', 'GreaterThan',
    'GreaterThanOrEqual', 'GreaterThanOrEqual',
    'LesserThan', 'LesserThan',
    'LesserThanOrEqual', 'LesserThanOrEqual',
    'DoesNotContain', 'DoesNotContain',
]

STATISTIC_FILTER = [
    ('Status', 'Status'),
    ('Priority', 'Priority'),
]

CONVERTOR_CHOICES = [
    ('default', 'default'),
    ('Bounding-Box', 'Bounding-Box'),
    ('Circle', 'Circle'),
    ('Polygon', 'Polygon'),
    ('Scoring', 'Scoring'),
]

FILTER_FILE_CHOICES = [
    ('WorkFlowStepOrder', 'WorkFlowStepOrder'),
    ('FileName', 'FileName'),
    ('FileSize', 'FileSize'),
    ('Priority', 'Priority'),
    ('CreatedAt', 'CreatedAt'),
    ('UpdatedAt', 'UpdatedAt')
]

TAG_OPERATOR_TYPE = [
    ('And', 'And'),
    ('Or', 'Or'),
]

class QueryClauseSerializer(serializers.Serializer):
    logicalOperator = serializers.ChoiceField(choices=LOGICAL_OPERATORS_CHOICES)
    fieldName = serializers.CharField(max_length=500, allow_null=True, allow_blank=True)
    operator = serializers.ChoiceField(choices=QUERY_OPERATORS_CHOICES)
    value = serializers.CharField(max_length=1000, allow_null=True, allow_blank=True)


class DatasetQuerySerializer(serializers.Serializer):
    clauses = serializers.ListField(child=QueryClauseSerializer())

class NextStepAfterAutoPredictionSerializer(serializers.Serializer):
    nextWorkFlowStep = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowState = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class FileOperationSerializer(serializers.Serializer):
    action = serializers.ChoiceField(choices=DATA_ACTION_CHOICES)
    path = serializers.CharField(max_length=1000)
    data = serializers.ListField()
    page = serializers.IntegerField(default=1)
    pageSize = serializers.IntegerField(default=100)
    readyToWorkProject = serializers.BooleanField(required=False, default=False)
    nextWorkFlowStep = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowState = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowStepIsAutoPrediction = serializers.BooleanField(required=False, default=False)
    nextStepAfterAutoPrediction = NextStepAfterAutoPredictionSerializer(required=False, allow_null=True)
    
class FileReadSerializer(FileOperationSerializer):
    showHiddenItems = serializers.BooleanField(required=False)


class FileCreateSerializer(FileOperationSerializer):
    name = serializers.CharField(max_length=255)


class FileDeleteSerializer(FileOperationSerializer):
    names = serializers.ListField()


class FileRenameSerializer(FileOperationSerializer):
    name = serializers.CharField(max_length=255)
    newName = serializers.CharField(max_length=255)


class FileDetailsSerializer(FileOperationSerializer):
    names = serializers.ListField()


class FileSearchSerializer(FileOperationSerializer):
    searchString = serializers.CharField(max_length=255)
    showHiddenItems = serializers.BooleanField()
    caseSensitive = serializers.BooleanField()


class FileCopySerializer(FileOperationSerializer):
    names = serializers.ListField()
    renameFiles = serializers.ListField()
    targetPath = serializers.CharField(max_length=255)


class FileMoveSerializer(FileOperationSerializer):
    names = serializers.ListField()
    renameFiles = serializers.ListField()
    targetPath = serializers.CharField(max_length=255)
    targetData = serializers.JSONField()


class FileUploadSerializer(FileOperationSerializer):
    uploadFiles = serializers.FileField()

class FileUploadMultipleTypesSerializer(FileOperationSerializer):
    uploadFiles = serializers.FileField()
    fileVersionId = serializers.UUIDField(required=False, allow_null=True)


class FileDetailSerializer(FileOperationSerializer):
    names = serializers.ListField()


class FileDetailResultSerializer(serializers.Serializer):
    fileName = serializers.CharField()
    isFile = serializers.BooleanField(default=False, allow_null=True)
    size = serializers.CharField(source='fileSize')
    created = serializers.CharField(source='dateCreated')
    modified = serializers.CharField(source='dateModified')
    multipleFiles = serializers.BooleanField(default=False, allow_null=True)
    permission = serializers.CharField(allow_null=True, allow_blank=True)
    attributes = serializers.ListField()

    def to_representation(self, instance):
        data = super().to_representation(instance)
        attributes = data.pop('attributes')
        location = None
        for attr in attributes:
            if attr['code'] == settings.ATTR_VIEW_FOLDER:
                location = attr['value']
                break
        data['location'] = location
        data['size'] = convert_bytes(int(data['size']))

        return data


class FileDownloadSerializer(serializers.Serializer):
    id = serializers.UUIDField(allow_null=True)
    name = serializers.CharField(max_length=500)
    filterPath = serializers.CharField(max_length=1000)
    url = serializers.CharField(max_length=5000, source='_fm_imageUrl', allow_null=True, allow_blank=True)
    isFile = serializers.BooleanField()


class FileDownloadInputSerializer(serializers.Serializer):
    downloadInput = serializers.CharField(max_length=5000)


class FileMoveResultSerializer(serializers.Serializer):
    # cwd = serializers.JSONField(default=None, allow_null=True)
    # error = serializers.CharField(max_length=1000, default=None, allow_null=True, allow_blank=True)
    # details = serializers.CharField(max_length=1000, default=None, allow_null=True, allow_blank=True)
    path = serializers.CharField(max_length=1000, default=None, allow_null=True, allow_blank=True)
    action = serializers.CharField(max_length=255, default=None, allow_null=True, allow_blank=True)
    newName = serializers.CharField(max_length=1000, default=None, allow_null=True, allow_blank=True)
    name = serializers.CharField(max_length=1000, default=None, allow_null=True, allow_blank=True)
    size = serializers.IntegerField(default=0)
    previousName = serializers.CharField(max_length=1000, default=None, allow_null=True, allow_blank=True)
    dateModified = serializers.CharField(max_length=255, default=None, allow_null=True, allow_blank=True)
    dateCreated = serializers.CharField(max_length=255, default=None, allow_null=True, allow_blank=True)
    hasChild = serializers.BooleanField()
    isFile = serializers.BooleanField()
    type = serializers.CharField(max_length=10, default=None, allow_null=True, allow_blank=True)
    id = serializers.UUIDField(default=None, allow_null=True)
    filterPath = serializers.SerializerMethodField()

    def get_filterPath(self, obj):
        return self.context['filterPath']


class AttributeSerializer(serializers.Serializer):
    code = serializers.CharField(max_length=500)
    value = serializers.CharField(allow_blank=True)

class AnnotationHistorySerializer(serializers.Serializer):
    projectId = serializers.UUIDField()
    fileId = serializers.UUIDField()
    labelStudioId = serializers.IntegerField()
    createdBy = serializers.UUIDField()
    createdAt = serializers.DateTimeField()
    createdFullName = serializers.CharField(max_length=500)
    email = serializers.CharField(max_length=500)
    createdAvatar = serializers.CharField(max_length=50000, allow_null=True, required=False, allow_blank=True)
    actionType = serializers.CharField(max_length=500)
    actionStep = serializers.CharField(max_length=500)
    actionStepId = serializers.UUIDField()
    isArchived = serializers.BooleanField()
    result = serializers.CharField(max_length=500000)
    config = serializers.CharField(max_length=500000)
    isCrop = serializers.BooleanField(allow_null=True, required=False)
    cropInfo = serializers.CharField(max_length=500000, allow_null=True, required=False, allow_blank=True)

class AnnotationHistoryResponseSerializer(serializers.Serializer):
    success = serializers.BooleanField(default=True)
    errorCode = serializers.CharField(max_length=500, allow_null=True, allow_blank=True)
    errorMessage = serializers.CharField(max_length=500, allow_null=True, allow_blank=True)
    data = serializers.ListField(child=AnnotationHistorySerializer())

class CheckTaskCurrentStepSerializer(serializers.Serializer):
    fileId = serializers.UUIDField(required=False, allow_null=True)
    currentStepId = serializers.UUIDField(required=False, allow_null=True)
    nextStepId = serializers.UUIDField(required=False, allow_null=True)
    userId = serializers.UUIDField(required=False, allow_null=True)

class ListAttributeSerializer(serializers.Serializer):
    checkTaskCurrentStep = CheckTaskCurrentStepSerializer(required=False, allow_null=True)
    viewWorkFlowStepOrder = serializers.CharField(max_length=5000, allow_null=True, required=False, allow_blank=True)
    timestamp = serializers.IntegerField(required=False, allow_null=True)
    ignoreTimestamp = serializers.BooleanField(required=False, allow_null=True, default=False)
    isPredict = serializers.BooleanField(required=False, allow_null=True)
    attributes = serializers.ListField(child=AttributeSerializer(), required=False, allow_null=True)
    annotateHistoryVersion = AnnotationHistorySerializer(required=False, allow_null=True)
    stepStatus = serializers.CharField(max_length=1000, required=False, allow_null=True)

    def to_representation(self, instance):
        data = super(ListAttributeSerializer, self).to_representation(instance)
        if data['annotateHistoryVersion'] is None:
            data['annotateHistoryVersion'] = {}
        return data

class ListFileAttributeSerializer(serializers.Serializer):
    fileId = serializers.UUIDField()
    isPredict = serializers.BooleanField(required=False, allow_null=True)
    nextViewState = serializers.CharField(max_length=1000, required=False, allow_null=True)
    nextWorkFlowStep = serializers.CharField(max_length=1000, required=False, allow_null=True)
    nextWorkFlowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True)
    attributes = serializers.ListField(child=AttributeSerializer())
    stepStatus = serializers.CharField(max_length=1000, required=False, allow_null=True)

class ConversationSerializer(serializers.Serializer):
    content = serializers.CharField(max_length=1000)
    mentionIds = serializers.ListField(child=serializers.UUIDField(), required=False)

class CoversationEditSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    content = serializers.CharField(max_length=1000, allow_null=True)
    mentionIds = serializers.ListField(child=serializers.UUIDField(), required=False)

class RedoReasonSerializer(serializers.Serializer):
    contentRedoReason = serializers.CharField(max_length=1000)

class RedoReasonEditSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    contentRedoReason = serializers.CharField(max_length=1000, allow_null=True)

class RejectReasonSerializer(serializers.Serializer):
    contentRejectReason = serializers.CharField(max_length=1000)

class RejectReasonEditSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    contentRejectReason = serializers.CharField(max_length=1000, allow_null=True)

class DataFollowEditSerializer(serializers.Serializer):
    isFollowed = serializers.BooleanField()

class ConversationFilterSerializer(FilterSerializer):
    project = serializers.UUIDField()

class RedoReasonFilterSerializer(FilterSerializer):
    project = serializers.UUIDField()

class RejectReasonFilterSerializer(FilterSerializer):
    project = serializers.UUIDField()

class ValueAttributeSerializer(serializers.Serializer):
    id = serializers.UUIDField()
    keyword = serializers.CharField(max_length=1000, allow_null=True, required=False)


class AttributeBetweenSerializer(serializers.Serializer):
    start = serializers.CharField(max_length=1000, allow_null=True, required=False)
    end = serializers.CharField(max_length=1000, allow_null=True, required=False)


# class DatasetFilterSerializer(FilterSerializer):
#     name = serializers.CharField(max_length=1000, allow_null=True, required=False)
#     valueAttribute = serializers.ListField(child=ValueAttributeSerializer(), required=False)
#     attributeBetween = serializers.ListField(child=AttributeBetweenSerializer(), required=False)

#     attributeCode = serializers.CharField(max_length=255, required=False, allow_null=True)
#     attributeCodeOperator = serializers.ChoiceField(choices=CODE_OPERATORS, required=False, allow_null=True)
#     attributeKeyword = serializers.ListField(required=False, allow_null=True)
#     attributeValuesOperator = serializers.ChoiceField(choices=VALUE_OPERATORS, required=False, allow_null=True)
#     attributeValues = serializers.ListField(required=False, allow_null=True)

#     dataType = serializers.ChoiceField(choices=DATATYPE_CHOICES, required=False)
#     fileId = serializers.UUIDField(required=False)
#     fileUrl = serializers.CharField(max_length=1000, allow_null=True, required=False)
#     fileName = serializers.CharField(max_length=1000, allow_null=True, required=False)
#     fileSize = serializers.CharField(max_length=500, allow_null=True, required=False)

#     def to_representation(self, instance):
#         data = super().to_representation(instance)
#         data['valueAttribute.AttributeCode'] = data.pop('attributeCode', '')
#         data['valueAttribute.CodeOperator'] = data.pop('attributeCodeOperator', '')
#         data['valueAttribute.Keyword'] = data.pop('attributeKeyword', '')
#         data['valueAttribute.ValuesOperator'] = data.pop('attributeValuesOperator', '')
#         data['valueAttribute.Values'] = data.pop('attributeValues', '')

#         return data
    
class DatasetFilterSerializer(FilterSerializer):
    name = serializers.CharField(max_length=1000, allow_null=True, required=False)
    attributeBetween = serializers.ListField(child=AttributeBetweenSerializer(), required=False)

    valueAttribute = serializers.ListField(required=False, allow_null=True)
    valueAttributeOperator = serializers.CharField(required=False, allow_null=True)

    dataType = serializers.ChoiceField(choices=DATATYPE_CHOICES, required=False)
    fileId = serializers.UUIDField(required=False)
    fileUrl = serializers.CharField(max_length=1000, allow_null=True, required=False)
    fileName = serializers.CharField(max_length=1000, allow_null=True, required=False)
    fileSize = serializers.CharField(max_length=500, allow_null=True, required=False)

class DatasetNewFilterSerializer(FilterSerializer):
    project = serializers.UUIDField()
    name = serializers.CharField(max_length=1000, allow_null=True, required=False)
    attributeBetween = serializers.ListField(child=AttributeBetweenSerializer(), required=False)

    valueAttribute = serializers.ListField(required=False, allow_null=True)
    valueAttributeOperator = serializers.CharField(required=False, allow_null=True)

    dataType = serializers.ChoiceField(choices=DATATYPE_CHOICES, required=False)
    fileId = serializers.UUIDField(required=False)
    fileUrl = serializers.CharField(max_length=1000, allow_null=True, required=False)
    fileName = serializers.CharField(max_length=1000, allow_null=True, required=False)
    fileSize = serializers.CharField(max_length=500, allow_null=True, required=False)
    userIds = serializers.ListField(child=serializers.UUIDField(), required=False)
    stepStatus = serializers.ListField(child=serializers.CharField(), required=False)
    projectTagIds = serializers.ListField(child=serializers.UUIDField(), required=False)
    tagOperatorType = serializers.ChoiceField(choices=TAG_OPERATOR_TYPE, required=False)
    workFlowStepOrders = serializers.ListField(required=False, allow_null=True)
    searchType = serializers.ChoiceField(choices=SEARCH_TYPE_CHOICES, required=False)
    sortBy = serializers.ChoiceField(choices=FILTER_FILE_CHOICES, allow_null=True, required=False, default='CreatedAt')

class DatasetNewWithSecurityFilterSerializer(FilterSerializer):
    project = serializers.UUIDField()
    name = serializers.CharField(max_length=1000, allow_null=True, required=False)
    attributeBetween = serializers.ListField(child=AttributeBetweenSerializer(), required=False)

    valueAttribute = serializers.ListField(required=False, allow_null=True)
    valueAttributeOperator = serializers.CharField(required=False, allow_null=True)

    dataType = serializers.ChoiceField(choices=DATATYPE_CHOICES, required=False)
    fileId = serializers.UUIDField(required=False)
    fileUrl = serializers.CharField(max_length=1000, allow_null=True, required=False)
    fileName = serializers.CharField(max_length=1000, allow_null=True, required=False)
    fileSize = serializers.CharField(max_length=500, allow_null=True, required=False)
    userIds = serializers.ListField(child=serializers.UUIDField(), required=False)
    stepStatus = serializers.ListField(child=serializers.CharField(), required=False)
    projectTagIds = serializers.ListField(child=serializers.UUIDField(), required=False)
    tagOperatorType = serializers.ChoiceField(choices=TAG_OPERATOR_TYPE, required=False)
    workFlowStepOrders = serializers.ListField(required=False, allow_null=True)
    searchType = serializers.ChoiceField(choices=SEARCH_TYPE_CHOICES, required=False)
    userWorkFlowSteps = serializers.ListField(child=serializers.CharField(), required=False)
    sortBy = serializers.ChoiceField(choices=FILTER_FILE_CHOICES, allow_null=True, required=False, default='CreatedAt')

class FileSerializer(serializers.Serializer):
    projectId = serializers.UUIDField()
    fileId = serializers.UUIDField()
    fileName = serializers.CharField()
    fileSize = serializers.CharField()
    dataType = serializers.CharField()
    isDelete = serializers.BooleanField()
    isDataFollowed = serializers.BooleanField()
    isArchive = serializers.BooleanField()
    viewState = serializers.CharField()
    viewWorkFlowStepId = serializers.CharField()
    viewWorkFlowStep = serializers.CharField()
    viewWorkFlowStepOrder = serializers.IntegerField()
    priority = serializers.CharField()
    pathFolder = serializers.CharField()
    shortTaskAssignments = serializers.ListField()

    id = serializers.UUIDField(source='fileId')
    name = serializers.CharField(source='fileName')
    size = serializers.CharField(source='fileSize')
    dateCreated = serializers.CharField(source='createdAt')
    dateModified = serializers.CharField(source='updatedAt')
    type = serializers.CharField(source='dataType')
    attributes = serializers.ListField()
    currentStep = serializers.JSONField()

    def to_representation(self, instance):
        data = super(FileSerializer, self).to_representation(instance)
        if data['type']:
            data['type'] = '.' + data['type']
        data.update(
            {'isFile': True, 'filterPath': '\\', 'hasChild': False}
            # {"action": None, "newName": None, "names": None,
            #          "previousName": None,
            #          "hasChild": False, "isFile": True,
            #          "id": None, "filterPath": "\\", "filterId": None, "parentId": None, "targetPath": None,
            #          "renameFiles": None, "uploadFiles": None, "caseSensitive": False, "searchString": None,
            #          "showHiddenItems": False, "data": None, "targetData": None,
            #          "permission": None}
        )
        try:
            data['size'] = int(data['size'])
        except:
            data['size'] = 0
        return data


class FileOperationDataSerializer(serializers.Serializer):
    files = serializers.ListField(child=FileSerializer(), source='items')
    error = serializers.BooleanField(allow_null=True)
    details = serializers.CharField(allow_null=True)

    def to_representation(self, instance):
        data = super(FileOperationDataSerializer, self).to_representation(instance)
        root = self.context['root']
        if root['path'] == '/':
            data['cwd'] = {
                "name": "Project",
                "size": 0,
                "dateModified": "2022-09-27T10:16:53.7636669+00:00",
                "dateCreated": "2022-09-27T10:17:03.4665001+00:00",
                "hasChild": False,
                "isFile": False,
                "type": "",
                "filterPath": ""
            }
        else:
            data['cwd'] = root['data'][0]
        # {"path": None, "action": None, "newName": None, "names": None, "name": "Files", "size": 0,
        #            "previousName": None, "dateModified": "2022-09-27T10:16:53.7636669+00:00",
        #            "dateCreated": "2022-09-27T10:17:03.4665001+00:00", "hasChild": True, "isFile": False,
        #            "type": "", "id": None, "filterPath": "", "filterId": None, "parentId": None,
        #            "targetPath": None, "renameFiles": None, "uploadFiles": None, "caseSensitive": False,
        #            "searchString": None, "showHiddenItems": False, "data": None, "targetData": None,
        #            "permission": None}

        return data


class ImageSerializer(IdByProjectSerializer):
    file_id = serializers.UUIDField()
    type = serializers.ChoiceField(choices=IMAGETYPE_CHOICES, default='Thumbnail', required=False)


class SampleCSVSerializer(IdByProjectSerializer):
    convert_data = serializers.ChoiceField(choices=CONVERTOR_CHOICES, default='default', required=False)

class ImportFileSerializer(IdByProjectSerializer):
    file = serializers.FileField()
    convert_data = serializers.ChoiceField(choices=CONVERTOR_CHOICES, default='default', required=False)

class ImportFileProjectAvatarSerializer(IdByProjectSerializer):
    file1 = serializers.FileField(allow_empty_file=True)
    file2 = serializers.FileField(allow_empty_file=True)
    file3 = serializers.FileField(allow_empty_file=True)

class ImportZipSerializer(IdByProjectSerializer):
    file = serializers.FileField()
    csv_file_name = serializers.CharField(max_length=1000, required=False, allow_null=False)
    parent_path = serializers.CharField(max_length=1000, allow_null=True, required=False)
    convert_data = serializers.ChoiceField(choices=CONVERTOR_CHOICES, default='default', required=False)


class ConfirmUploadZipSerializer(serializers.Serializer):
    confirm = serializers.BooleanField()
    parent_path = serializers.CharField(max_length=1000, allow_null=True, required=False)
    csv_file_name = serializers.CharField(max_length=1000, required=False, allow_null=False)
    zip_task_id = serializers.UUIDField()
    convert_data = serializers.ChoiceField(choices=CONVERTOR_CHOICES, default='default', required=False)

class StatisticSerializer(IdByProjectSerializer):
    static_filter = serializers.ChoiceField(choices=STATISTIC_FILTER)

class UploadFileAsAttrbSerializer(serializers.Serializer):
    file = serializers.FileField()
    attribute_code = serializers.CharField(max_length=1000, required=False)

class UploadZipV2Serializer(serializers.Serializer):
    id = serializers.CharField(max_length=1000, required=False)
    file = serializers.FileField()
    parent_path = serializers.CharField(max_length=1000, allow_null=True, required=False)
    readyToWorkProject = serializers.BooleanField(required=False, default=False)
    nextWorkFlowStep = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowState = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class UploadFillCSVDataV2Serializer(serializers.Serializer):
    id = serializers.CharField(max_length=1000, required=False)
    file = serializers.FileField()

class UploadZipV2CancelSerializer(serializers.Serializer):
    task_upload_id = serializers.UUIDField(required=False)

class UploadZipAndCSVV2Serializer(serializers.Serializer):
    id = serializers.CharField(max_length=1000, required=False)
    zip_file = serializers.FileField()
    csv_file = serializers.FileField()
    parent_path = serializers.CharField(max_length=1000, allow_null=True, required=False)
    readyToWorkProject = serializers.BooleanField(required=False, default=False)
    nextWorkFlowStep = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowStepId = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)
    nextWorkFlowState = serializers.CharField(max_length=1000, required=False, allow_null=True, allow_blank=True)

class FolderManagerBranchSerializer(serializers.Serializer):
    path = serializers.CharField(max_length=200, allow_null=True, required=False)

class FileProjectCropInfoAreaSerializer(serializers.Serializer):
    x = serializers.FloatField()
    y = serializers.FloatField()
    width = serializers.FloatField()
    height = serializers.FloatField()

class FileProjectCropInfoRotateSerializer(serializers.Serializer):
    angle = serializers.FloatField()

class FileProjectCropInfoSerializer(serializers.Serializer):
    area_info = FileProjectCropInfoAreaSerializer(allow_null=True)
    rotate_info = FileProjectCropInfoRotateSerializer(allow_null=True)
class FileProjectCropDuplicateSerializer(serializers.Serializer):
    file = serializers.FileField(required=False)
    projectId = serializers.UUIDField()
    projectFileId = serializers.UUIDField()
    masterFileId = serializers.UUIDField(required=False)
    annotateResult = serializers.CharField()
    cropInfo = serializers.CharField()
    timestamp = serializers.IntegerField(required=False, allow_null=True)
    viewAnnotationResult = serializers.CharField(required=False, allow_null=True)
    deleteAnnotationFileId = serializers.UUIDField(required=False, allow_null=True)

class FileProjectSaveAsSerializer(serializers.Serializer):
    file = serializers.FileField(required=False)
    projectId = serializers.UUIDField()
    cropInfo = serializers.CharField(required=False)
    attributes = serializers.CharField(required=False)
    viewState = serializers.CharField(required=False, allow_null=True)
    viewWorkFlowStep = serializers.CharField(required=False, allow_null=True)
    viewWorkFlowStepId = serializers.CharField(required=False, allow_null=True)
    priority = serializers.IntegerField(required=False, allow_null=True)
    pathFolder = serializers.CharField(required=False, allow_null=True)
    isArchive = serializers.BooleanField(required=False, allow_null=True, default=False)
