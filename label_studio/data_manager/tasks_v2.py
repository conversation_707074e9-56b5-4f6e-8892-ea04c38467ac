import shutil
import datetime
from nanoid import generate
import celery
from celery import shared_task
import zipfile, os, io
from pathlib import Path
import csv
import json
import xml.etree.ElementTree as ET
from users.models import User
from django.conf import settings
import concurrent.futures

from dms_connector.api import DMS_PROJECT_ATTR_API, DMS_FILE_API, DMS_PROJECT_API
from django.core.files import File
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

from data_manager.models import BackgroundTaskDataManager
from data_manager.tasks_v3 import folder_sync_es_multiple_files

channel_layer = get_channel_layer()

def recursive_auto_create_folder(user, project_id, parent_path, data_folder_path, data_folder_path_root,
                                 current_folder_project):
    for file in os.listdir(data_folder_path):
        d = os.path.join(data_folder_path, file)
        if os.path.isdir(d):
            parent_absolute = Path("{}".format(d.replace(data_folder_path_root, ""))).parent.absolute()
            filterPath = "{}{}".format(parent_path[:-1], parent_absolute) if str(
                parent_absolute) == "/" else "{}{}/".format(parent_path[:-1], parent_absolute)
            is_existed = False
            # Check folder is existed or not
            for folder_item in current_folder_project:
                if folder_item['filterPath'] == filterPath and folder_item['name'] == file:
                    is_existed = True
                    break

            if not is_existed:
                new_folder = {
                    'dateCreated': "2022-09-27T10:17:03.4665001+00:00",
                    'dateModified': "2022-09-27T10:16:53.7636669+00:00",
                    'filterPath': filterPath,
                    'hasChild': False,
                    'isFile': False,
                    'name': file,
                    'size': 0,
                    'type': "",
                    '_fm_htmlAttr': {},
                    '_fm_icon': "e-fe-folder",
                    '_fm_id': "fe_tree"
                }
                # print(new_folder)
                current_folder_project.append(new_folder)

            recursive_auto_create_folder(user, project_id, parent_path, d, data_folder_path_root,
                                         current_folder_project)
            
def post_img_in_zip_and_csv_to_s3(data):
    user = data["user"]
    project_id = data["project_id"]
    parent_path = data["parent_path"]
    file = data["file"]
    data_folder_path_root = data["data_folder_path_root"]
    attributes_view_default = data["attributes_view_default"]
    readyToWorkProject = data.get("readyToWorkProject", False)
    nextWorkFlowStep = data.get("nextWorkFlowStep", 'New')
    nextWorkFlowStepId = data.get("nextWorkFlowStepId", '00000000-0000-0000-0000-000000000000')
    nextWorkFlowState = data.get("nextWorkFlowState", 'New')

    attributes = attributes_view_default.copy()

    file_path =  file.replace(data_folder_path_root, "")[1:]

    view_folder_path = ""
    if parent_path != "/":
        view_folder_path = parent_path[:-1] + str(Path("/{}".format(file_path)).parent.absolute())
    else:
        view_folder_path = str(Path("/{}".format(file_path)).parent.absolute())

    for attrib in attributes:
        if attrib["code"] == "view_folder":
            attrib["value"] = "{}/".format(view_folder_path)
            break

    if readyToWorkProject:
        for attrib in attributes:
            if attrib['code'] == settings.ATTR_VIEW_STATE:
                attrib['value'] = nextWorkFlowState
            if attrib['code'] == 'view_workflow_step':
                attrib['value'] = nextWorkFlowStep
            if attrib['code'] == 'view_workflow_step_id':
                attrib['value'] = nextWorkFlowStepId

    # post to "Import a file to the dataset" with attribute
    file_post = File(open(file, 'rb'))

    result = {
        "success": True,
        "file": None
    }
    print("+++++++++")
    print(file)
    print(attributes)
    try:
        data_view = settings.DEFAULT_FILE_FIELDS.copy()
        data_view['pathFolder'] = "{}/".format(view_folder_path)
        if readyToWorkProject:
            data_view['viewState'] = nextWorkFlowState
            data_view['viewWorkFlowStep'] = nextWorkFlowStep
            data_view['viewWorkFlowStepId'] = nextWorkFlowStepId

        status_code, json_data_post = DMS_FILE_API().create(user, file_post, project_id, attributes, data_view)
        if status_code == 200 and json_data_post is not None:
            if json_data_post['success']:    
                folder_sync_es_multiple_files(project_master_id=project_id, file_ids=[json_data_post['data']])
            else:
                result["success"] = False
                result["file"] = file
                result["message"] = json_data_post['message']
                return result
        if status_code != 200:
            result["success"] = False
            result["file"] = file
            result["message"] = "Upload Fail"
            return result
    except Exception as e:
        result["success"] = False
        result["file"] = file
        result["message"] = "Connect to server error"
        return result

    if 'success' in json_data_post.keys() and json_data_post['success'] == True:
        print("{}: Done!".format(file))
    else:
        result["success"] = False
        result["file"] = file

    return result

@shared_task
def upload_data_v2(user_id, project_id, file_save, zip_id, parent_path="/",
                   readyToWorkProject=False, nextWorkFlowStep='New',
                   nextWorkFlowStepId='00000000-0000-0000-0000-000000000000', nextWorkFlowState='New'):
    current_task_id = celery.current_task.request.id
    start_time = datetime.datetime.now()

    user = User.objects.get(dms_id=user_id)

    async_to_sync(channel_layer.group_send)(
        str(user.id),
        {
            'type': 'chat.message',
            'message': {
                "current_upload_task_id": current_task_id,
                'created_time': str(start_time),
            }
        }
    )
    
    zip_task_id = zip_id
    working_dir = "data_manager"
    folder_path = "{}/unzip_data_temp/{}".format(working_dir, zip_task_id)

    if os.path.exists(folder_path) == False:
        os.mkdir(folder_path)

    if (os.path.exists(
            "{}/{}".format(folder_path, file_save)) == False):
        shutil.move("{}/{}".format(settings.MEDIA_ROOT, file_save),
                    folder_path,
                    copy_function=shutil.copy2)
    else:
        shutil.move("{}/{}".format(settings.MEDIA_ROOT, file_save),
                    "{}/{}".format(folder_path, file_save),
                    copy_function=shutil.copy2)
        
    folder_zip_path = "data_manager/unzip_data_temp/{}".format(zip_task_id)

    data_zip_path = None
    dir_list = os.listdir(folder_path)
    for dir in dir_list:
        if ".zip" in dir:
            data_zip_path = folder_path + "/" + dir
    # unzip file data (.zip)
    with zipfile.ZipFile(data_zip_path, 'r') as zip_ref:
        zip_ref.extractall(folder_path)

    for dir in os.listdir(folder_zip_path):
        if Path(folder_zip_path + "/" + dir).is_dir():
            data_folder_path = folder_zip_path + "/" + dir
            break

    # get all files
    file_path_list = []
    for root, dirs, files in os.walk(data_folder_path, topdown=True):
        for name in files:
            full_path = os.path.join(root, name)
            file_path_list.append(full_path)

    # recursive and create folder
    status_code, project_res = DMS_PROJECT_API().detail(user, project_id)
    project_data = project_res['data']
    try:
        current_folders = json.loads(project_data['annotationFlow'])
    except:
        current_folders = []
    data_folder_path_root = folder_zip_path
    recursive_auto_create_folder(user, project_id, parent_path, folder_zip_path, data_folder_path_root,
                                 current_folder_project=current_folders)

    data = {
        'annotationFlow': json.dumps(current_folders)
    }
    try:
        DMS_PROJECT_API().edit(user, project_id, data)
        print("[Info]: Auto create folder structure successfully")
    except Exception as e:
        print(e)

    error_upload_file_list = []  # list save all file upload fail
    success_upload_count = 0
    error_upload_count = 0
    data_wait_upload = []

    attributes_view_default = settings.DEFAULT_VIEW_ATTRIBUTES.copy()

    for file in file_path_list:
        data_item_wait_upload = {
            "user": user,
            "project_id": project_id,
            "parent_path": parent_path,
            "file": file,
            "data_folder_path_root": data_folder_path_root,
            "attributes_view_default": attributes_view_default,
            "readyToWorkProject": readyToWorkProject,
            "nextWorkFlowStep": nextWorkFlowStep,
            "nextWorkFlowStepId": nextWorkFlowStepId,
            "nextWorkFlowState": nextWorkFlowState
        }
        data_wait_upload.append(data_item_wait_upload)
        
    executor = concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_WORKERS_UPLOAD)
    count = 0
    jump = 1
    if len(file_path_list) < 10:
        jump = len(file_path_list)
    else:
        jump = int(len(file_path_list) / 10)
    is_limited = False
    for _ in executor.map(post_img_in_zip_and_csv_to_s3, data_wait_upload):
        if _["success"] == True:
            success_upload_count += 1
        if _["success"] == False:
            if _['message'] == "Create.SizeLimit":
                message_outlimit = {
                    "current_upload_task_id": current_task_id,
                    "file_save": file_save,
                    "zip_file_id": zip_task_id,
                    "success": True,
                    "message": _['message'],
                    'file_type': 's3'
                }

                async_to_sync(channel_layer.group_send)(
                    str(user.id),
                    {
                        'type': 'chat.message',
                        'message': message_outlimit
                    }
                )
            if _['message'] == "Create.ResourceLimit":
                message_outlimit = {
                    "current_upload_task_id": current_task_id,
                    "file_save": file_save,
                    "zip_file_id": zip_task_id,
                    "success": True,
                    "message": _['message'],
                    'file_type': 's3'
                }

                async_to_sync(channel_layer.group_send)(
                    str(user.id),
                    {
                        'type': 'chat.message',
                        'message': message_outlimit
                    }
                )
                is_limited = True
                break

            error_upload_count += 1
            error_upload_file_list.append(_["file"])

        count += 1
        if count % jump == 0:
            async_to_sync(channel_layer.group_send)(
                str(user.id),
                {
                    'type': 'chat.message',
                    'message': {
                        "current_upload_task_id": current_task_id,
                        "file_save": file_save,
                        "zip_file_id": zip_task_id,
                        "uploaded_number": count,
                        "total_number": len(file_path_list),
                        "percent": round(count / len(file_path_list) * 100, 2)
                    }
                }
            )

            # async_to_sync(channel_layer.group_send)(
            #     str(user.id),
            #     {
            #         'type': 'chat.message',
            #         'message': "{}: Upload Fail".format(_["file"])
            #     }
            # )

    delete_data_folder(zip_task_id=zip_task_id)

    results = {
        "current_upload_task_id": current_task_id,
        'created_time': str(start_time),
        "success": True,
        "file_save": file_save,
        "zip_file_id": zip_task_id,
        "message": "Upload Completed",
        'file_type': 's3',
        "success_number": success_upload_count,
        "error_number": error_upload_count,
        "error_list": error_upload_file_list
    }
    
    if is_limited:
        return results
    async_to_sync(channel_layer.group_send)(
        str(user.id),
        {
            'type': 'chat.message',
            'message': results
        }
    )

    return results

# @shared_task
# def cancel_upload_v2(user_id, upload_task_id):
#     from core.celery import app
#     app.control.revoke(upload_task_id)

@shared_task
def delete_data_folder(zip_task_id):
    folder_zip_path = "data_manager/unzip_data_temp/{}".format(zip_task_id)

    shutil.rmtree(folder_zip_path)
    return {
        "status": "Cancel",
        "message": "Cancel upload"
    }

def find_index_col(row):
    file_id_index = None
    file_name_index = None
    image_path_local_index = None
    image_width_index = None
    image_height_index = None
    pre_annotated_Bounding_Box_index = None
    pre_annotated_Circle_index = None
    pre_annotated_Polygon_index = None
    pre_annotated_Scoring_index = None
    pre_annotated_Brush_index = None
    pre_annotated_Instance_index = None
    metadata_index = []
    for i, col in enumerate(row):
        if col == "file_id":
            file_id_index = i
        elif col == "file_name":
            file_name_index = i
        elif col == "image_path_local":
            image_path_local_index = i
        elif col == "image_width":
            image_width_index = i
        elif col == "image_height":
            image_height_index = i
        elif col == "view_annotation_result_Bounding-Box":
            pre_annotated_Bounding_Box_index = i
        elif col == "view_annotation_result_Circle":
            pre_annotated_Circle_index = i
        elif col == "view_annotation_result_Polygon":
            pre_annotated_Polygon_index = i
        elif col == "view_annotation_result_Scoring":
            pre_annotated_Scoring_index = i
        elif col == "view_annotation_result_Brush":
            pre_annotated_Brush_index = i
        elif col == "view_annotation_result_Instance":
            pre_annotated_Instance_index = i
        else:
            metadata_index.append({
                "meta_data_index": i,
                "meta_data_code": col
            })
    return file_id_index, file_name_index, image_path_local_index, image_width_index, image_height_index, \
        pre_annotated_Bounding_Box_index, \
        pre_annotated_Circle_index, \
        pre_annotated_Polygon_index, \
        pre_annotated_Scoring_index, \
        pre_annotated_Brush_index, \
        pre_annotated_Instance_index, \
        metadata_index

def fill_pre_annotated_data_to_images(data):
    user = data["user"]
    project_id = data["project_id"]
    update_attrb_list_data = data["update_attrb_list_data"]

    result = {
        "success": True,
        "file": None,
        "message": None
    }
    print("update_attrb_list_data: {}".format(update_attrb_list_data))
    status_code_put, json_data_put = DMS_FILE_API().put_multi_file(user, update_attrb_list_data, project_id)
    if status_code_put == 200:
        if json_data_put['success']:
            print("{}: Done!".format("Upload pre-annotated data"))
            result["success"] = True
            result["message"] = "Upload pre-annotated data successfully"
    else:
        result["success"] = False
        result["message"] = "Upload Fail"
    
    return result 

@shared_task
def fill_pre_annotated_data(user_id, project_id, csv_id, csv_data):
    user = User.objects.get(dms_id=user_id)

    reader = csv.reader(io.StringIO(csv_data), delimiter=',')
    for index, row in enumerate(reader):
        if index == 0:
            file_id_index, file_name_index, image_path_local_index, image_width_index, image_height_index, \
            pre_annotated_Bounding_Box_index, \
            pre_annotated_Circle_index, \
            pre_annotated_Polygon_index, \
            pre_annotated_Scoring_index, \
            pre_annotated_Brush_index, \
            pre_annotated_Instance_index, \
            meta_data_index = find_index_col(row)
            break

    print("file_id_index: {}".format(file_id_index))
    print("file_name_index: {}".format(file_name_index))
    print("image_path_local_index: {}".format(image_path_local_index))
    print("image_width_index: {}".format(image_width_index))
    print("image_height_index: {}".format(image_height_index))
    print("pre_annotated_Bounding_Box_index: {}".format(pre_annotated_Bounding_Box_index))
    print("pre_annotated_Circle_index: {}".format(pre_annotated_Circle_index))
    print("pre_annotated_Polygon_index: {}".format(pre_annotated_Polygon_index))
    print("pre_annotated_Scoring_index: {}".format(pre_annotated_Scoring_index))
    print("pre_annotated_Brush_index: {}".format(pre_annotated_Brush_index))
    print("pre_annotated_Instance_index: {}".format(pre_annotated_Instance_index))
    print("meta_data_index: {}".format(meta_data_index))

    bounding_box_config_name = None
    circle_config_name = None
    polygon_config_name = None
    scoring_config_name = None
    brush_config_name = None

    instance_config_name = None
    instance_bounding_box_config_name =  None
    instance_keypoints_config_name = None

    status_code_prj, json_data_prj = DMS_PROJECT_API().detail(user, project_id)
    if status_code_prj == 200:
        if json_data_prj['success']:
            project_data = json_data_prj['data']
            project_class_config = project_data['labelConfig']
            xml_class_config = ET.fromstring(project_class_config)
            for tool in ['RectangleLabels', 'EllipseLabels', 'PolygonLabels', 'ScoringLabels', 'BrushLabels', 'Tools']:
                for item in xml_class_config.findall(tool):
                    if tool == 'RectangleLabels':
                        bounding_box_config_name = item.attrib['name']
                    if tool == 'EllipseLabels':
                        circle_config_name = item.attrib['name']
                    if tool == 'PolygonLabels':
                        polygon_config_name = item.attrib['name']
                    if tool == 'ScoringLabels':
                        scoring_config_name = item.attrib['name']
                    if tool == 'BrushLabels':
                        brush_config_name = item.attrib['name']
                    if tool == 'Tools':
                        instance_config_name = item.attrib['name']
                        for item_tool in item.iter():
                            if item_tool.tag == 'RectangleLabels':
                                instance_bounding_box_config_name = item_tool.attrib['name']
                            if item_tool.tag == 'KeyPointLabels':
                                instance_keypoints_config_name = item_tool.attrib['name']

    reader = csv.reader(io.StringIO(csv_data), delimiter=',')
    update_attrb_list_data = []
    for index, row in enumerate(reader):
        if index > 0:
            update_attrb_item = {
                "fileId": row[file_id_index],
                "attributes": []
            }
            result_annotate = [
                {
                    'result': [],
                    'createdBy': ""
                }
            ]

            if pre_annotated_Scoring_index != None:
                if row[pre_annotated_Scoring_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Scoring_index]))
                    scoring_config_upload_name = 'scoringlabels' if scoring_config_name is None else scoring_config_name
                    for item in annotate_data:
                            scoring_results = {
                                'values': [],
                                'size': item['size']
                            }
                            try:
                                for row_item in item['score']:
                                    row_re_format = []
                                    for col in row_item:
                                        row_re_format.append({
                                            'id': generate(size=10),
                                            'score': col
                                        })
                                    scoring_results['values'].append(row_re_format)

                                result_annotate[0]['result'].append({
                                    'original_width': int(float(row[image_width_index])),
                                    'original_height': int(float(row[image_height_index])),
                                    'image_rotation': 0,
                                    'value': {
                                        'scoringlabels': [json.dumps(scoring_results)]
                                    },
                                    'id': generate(size=10),
                                    'from_name': scoring_config_upload_name,
                                    'to_name': 'image',
                                    'type': 'scoringlabels',
                                    'origin': 'manual'
                                })
                            except Exception as e:
                                print(f"Line: {index + 1} - {e}")

            if pre_annotated_Bounding_Box_index != None:
                if row[pre_annotated_Bounding_Box_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Bounding_Box_index]))
                    bounding_box_config_upload_name = 'rectanglelabels' if bounding_box_config_name is None else bounding_box_config_name
                    for item in annotate_data:
                        label_id = generate(size=10)
                        try:
                            result_annotate[0]['result'].append({
                                'original_width': int(float(row[image_width_index])),
                                'original_height': int(float(row[image_height_index])),
                                'image_rotation': 0,
                                'value': {
                                    'x': item['x'] / float(row[image_width_index]) * 100,
                                    'y': item['y'] / float(row[image_height_index]) * 100,
                                    'width': item['width'] / float(row[image_width_index]) * 100,
                                    'height': item['height'] / float(row[image_height_index]) * 100,
                                    'rectanglelabels': [item['class_name']]
                                },
                                'id': label_id,
                                'from_name': bounding_box_config_upload_name,
                                'to_name': 'image',
                                'type': 'rectanglelabels',
                                'origin': 'manual'
                            })
                        except Exception as e:
                            print(f"Line: {index + 1} - {e}")
                        if item.get('meta_label', None):
                            meta_label = item['meta_label']
                            meta_label_keys = meta_label.keys()
                            for meta_label_key in meta_label_keys:
                                if meta_label[meta_label_key]['type'] == 'choices':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'x': item['x'] / float(row[image_width_index]) * 100,
                                            'y': item['y'] / float(row[image_height_index]) * 100,
                                            'width': item['width'] / float(row[image_width_index]) * 100,
                                            'height': item['height'] / float(row[image_height_index]) * 100,
                                            'choices': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'choices',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'textarea':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'x': item['x'] / float(row[image_width_index]) * 100,
                                            'y': item['y'] / float(row[image_height_index]) * 100,
                                            'width': item['width'] / float(row[image_width_index]) * 100,
                                            'height': item['height'] / float(row[image_height_index]) * 100,
                                            'text': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'text',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'number':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'x': item['x'] / float(row[image_width_index]) * 100,
                                            'y': item['y'] / float(row[image_height_index]) * 100,
                                            'width': item['width'] / float(row[image_width_index]) * 100,
                                            'height': item['height'] / float(row[image_height_index]) * 100,
                                            'number': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'number',
                                        'origin': 'manual'
                                    })
            
            if pre_annotated_Circle_index != None:
                if row[pre_annotated_Circle_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Circle_index]))
                    circle_config_upload_name = 'ellipselabels' if circle_config_name is None else circle_config_name
                    for item in annotate_data:
                        try:
                            result_annotate[0]['result'].append({
                                'original_width': int(float(row[image_width_index])),
                                'original_height': int(float(row[image_height_index])),
                                'image_rotation': 0,
                                'value': {
                                    'x': item['x'] / float(row[image_width_index]) * 100,
                                    'y': item['y'] / float(row[image_height_index]) * 100,
                                    'radiusX': item['radius'] / float(row[image_width_index]) * 100,
                                    'radiusY': item['radius'] / float(row[image_height_index]) * 100,
                                    'ellipselabels': [item['class_name']]
                                },
                                'id': generate(size=10),
                                'from_name': circle_config_upload_name,
                                'to_name': 'image',
                                'type': 'ellipselabels',
                                'origin': 'manual'
                            })
                        except Exception as e:
                            print(f"Line: {index + 1} - {e}")

            if pre_annotated_Polygon_index != None:
                if row[pre_annotated_Polygon_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Polygon_index]))
                    polygon_config_upload_name = 'polygonlabels' if polygon_config_name is None else polygon_config_name
                    for item in annotate_data:
                        label_id = generate(size=10)
                        try:
                            points_list = item['points']
                            points_list_normalizing = []
                            for point in points_list:
                                x_point = point[0] / float(row[image_width_index]) * 100
                                y_point = point[1] / float(row[image_height_index]) * 100
                                points_list_normalizing.append([x_point, y_point])

                            result_annotate[0]['result'].append({
                                'original_width': int(float(row[image_width_index])),
                                'original_height': int(float(row[image_height_index])),
                                'image_rotation': 0,
                                'value': {
                                    'points': points_list_normalizing,
                                    'polygonlabels': [item['class_name']]
                                },
                                'id': label_id,
                                'from_name': polygon_config_upload_name,
                                'to_name': 'image',
                                'type': 'polygonlabels',
                                'origin': 'manual'
                            })
                        except Exception as e:
                            print(f"Line: {index + 1} - {e}")
                        if item.get('meta_label', None):
                            meta_label = item['meta_label']
                            meta_label_keys = meta_label.keys()
                            for meta_label_key in meta_label_keys:
                                if meta_label[meta_label_key]['type'] == 'choices':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'points': points_list_normalizing,
                                            'choices': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'choices',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'textarea':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'points': points_list_normalizing,
                                            'text': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'text',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'number':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'points': points_list_normalizing,
                                            'number': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'number',
                                        'origin': 'manual'
                                    })

            if pre_annotated_Brush_index != None:
                if row[pre_annotated_Brush_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Brush_index]))
                    brush_config_upload_name = 'brushlabels' if brush_config_name is None else brush_config_name
                    for item in annotate_data:
                        label_id = generate(size=10)
                        try:
                            result_annotate[0]['result'].append({
                                'original_width': int(float(row[image_width_index])),
                                'original_height': int(float(row[image_height_index])),
                                'image_rotation': 0,
                                'value': {
                                    'format': 'rle',
                                    'rle': item['rle'],
                                    'brushlabels': [item['class_name']]
                                },
                                'id': label_id,
                                'from_name': brush_config_upload_name,
                                'to_name': 'image',
                                'type': 'brushlabels',
                                'origin': 'manual'
                            })
                        except Exception as e:
                            print(f"Line: {index + 1} - {e}")
                        if item.get('meta_label', None):
                            meta_label = item['meta_label']
                            meta_label_keys = meta_label.keys()
                            for meta_label_key in meta_label_keys:
                                if meta_label[meta_label_key]['type'] == 'choices':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'format': 'rle',
                                            'rle': item['rle'],
                                            'choices': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'choices',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'textarea':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'format': 'rle',
                                            'rle': item['rle'],
                                            'text': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'text',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'number':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'format': 'rle',
                                            'rle': item['rle'],
                                            'number': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'number',
                                        'origin': 'manual'
                                    })

            if pre_annotated_Instance_index != None:
                if row[pre_annotated_Instance_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Instance_index]))
                    for item in annotate_data:
                        try:
                            for component in item['components']:
                                component_item = {
                                    'original_width': int(float(row[image_width_index])),
                                    'original_height': int(float(row[image_height_index])),
                                    'image_rotation': 0,
                                    'parentID': item['instance_id'],
                                    'id': generate(size=10),
                                    'from_name': 'kp01',
                                    'to_name': 'image',
                                    'origin': 'manual',
                                }
                                if component['type'] == 'rectangle':
                                    component_item['value'] = {
                                        'x': float(component['value']['x']) / float(row[image_width_index]) * 100,
                                        'y': float(component['value']['y']) / float(row[image_height_index]) * 100,
                                        'width': float(component['value']['width']) / float(row[image_width_index]) * 100,
                                        'height': float(component['value']['height']) / float(row[image_height_index]) * 100,
                                        'rectanglelabels': [component['class_name']],
                                        'rotation': 0
                                    }
                                    component_item['type'] = 'rectanglelabels'
                                    component_item['from_name'] = instance_bounding_box_config_name
                                
                                if component['type'] == 'keypoint':
                                    component_item['value'] = {
                                        'x': float(component['value']['x']) / float(row[image_width_index]) * 100,
                                        'y': float(component['value']['y']) / float(row[image_height_index]) * 100,
                                        'width': float(component['value']['radius']) / float(row[image_width_index]) * 100,
                                        'keypointlabels': [component['class_name']],
                                        'rotation': 0
                                    }
                                    component_item['type'] = 'keypointlabels'
                                    component_item['from_name'] = instance_keypoints_config_name


                                result_annotate[0]['result'].append(component_item)

                            result_annotate[0]['result'].append({
                                'id': item['instance_id'],
                                'name': item['instance_name'],
                                'type': 'instance',
                            })

                        except Exception as e:
                            print(f"Line: {index + 1} - {e}")

            if result_annotate[0]['result'] != []:
                update_attrb_item["attributes"].append({
                    "code": "view_annotation_result",
                    "value": json.dumps(result_annotate)
                })
            
            if meta_data_index != []:
                for meta_data in meta_data_index:
                    if row[meta_data["meta_data_index"]] != "":
                        update_attrb_item["attributes"].append({
                            "code": meta_data["meta_data_code"],
                            "value": row[meta_data["meta_data_index"]]
                        })

            if update_attrb_item["attributes"] != []:
                update_attrb_list_data.append(update_attrb_item)
                
    if len(update_attrb_list_data) < 2:
        update_attrb_list_split = [update_attrb_list_data[x:x+1] for x in range(0, len(update_attrb_list_data), 1)]
    else:
        update_attrb_list_split = [update_attrb_list_data[x:x+2] for x in range(0, len(update_attrb_list_data), 2)]

    data_item = {
        "user": user,
        "project_id": project_id,
        "update_attrb_list_data": update_attrb_list_data
    }
    data_item = [{"user": user, "project_id": project_id, "update_attrb_list_data": update_attrb_list} for update_attrb_list in update_attrb_list_split]
    
    executor_update_attrb = concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_WORKERS_UPLOAD)
    count = 0
    for _ in executor_update_attrb.map(fill_pre_annotated_data_to_images, data_item):
        if _["success"] == True:
            print(_["message"])
        if _["success"] == False:
            print(_["message"])

        count += 1
        if count % 1 == 0:
            async_to_sync(channel_layer.group_send)(
                str(user.id),
                {
                    'type': 'chat.message',
                    'message': {
                        "csv_task_id": csv_id,
                        "uploaded_number": count,
                        "total_number": len(data_item),
                        "percent": round(count / len(data_item) * 100, 2)
                    }
                }
            )

    results = {
        # "current_upload_task_id": current_task_id,
        # 'created_time': str(start_time),
        "csv_task_id": csv_id,
        "success": True,
        # "file_save": file_save,
        # "zip_file_id": zip_task_id,
        "message": "Upload Completed",
        # 'file_type': 's3',
        # "success_number": success_upload_count,
        # "error_number": error_upload_count,
        # "error_list": error_upload_file_list
    }

    async_to_sync(channel_layer.group_send)(
        str(user.id),
        {
            'type': 'chat.message',
            'message': results
        }
    )