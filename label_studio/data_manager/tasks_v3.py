import shutil
import datetime
from nanoid import generate
import celery
from celery import shared_task
import zipfile, os, io
from pathlib import Path
import csv
import json
import xml.etree.ElementTree as ET
from users.models import User
from django.conf import settings
import concurrent.futures

from dms_connector.api import DMS_FILE_API, DMS_PROJECT_API
from django.core.files import File
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

from data_manager.models import BackgroundTaskDataManager

channel_layer = get_channel_layer()


def mapping_data(folder_zip_path, file_csv_data):
    data_folder_path = None

    for dir in os.listdir(folder_zip_path):
        if Path(folder_zip_path + "/" + dir).is_dir():
            data_folder_path = folder_zip_path + "/" + dir

    # get all files
    file_path_list = []
    for root, dirs, files in os.walk(data_folder_path, topdown=True):
        for name in files:
            full_path = os.path.join(root, name)
            file_path_list.append(full_path)

    # get data and upload
    image_in_zip_not_in_csv = []
    image_in_csv_not_in_zip = []
    image_in_zip_and_in_csv = []
    for file in file_path_list:
        has_attribute = False  # Check image name in csv
        csv_reader = csv.reader(io.StringIO(file_csv_data), delimiter=',')
        for row in csv_reader:
            if row[0] in file:
                has_attribute = True
                # with attribute
                image_in_zip_and_in_csv.append(file)
                print(f"Line:{file} mapped")
                break
        if has_attribute == False:
            # without attribute
            image_in_zip_not_in_csv.append(file)
    
    # csv_reader = csv.reader(io.StringIO(file_csv_data), delimiter=',')
    # line_count = 0
    # for row in csv_reader:
    #     if line_count >= 1:
    #         is_in_zip = False
    #         for file in file_path_list:
    #             if row[0] in file:
    #                 is_in_zip = True
    #                 break
    #         if is_in_zip == False:
    #             image_in_csv_not_in_zip.append(row[0])
    #     line_count += 1

    data_filter = {
        "image_in_zip_but_not_in_csv": image_in_zip_not_in_csv,
        "image_in_csv_but_not_in_zip": image_in_csv_not_in_zip,
        "image_in_csv_and_in_zip": image_in_zip_and_in_csv
    }
    return data_filter


def post_img_in_zip_and_csv_to_s3(data):
    user = data["user"]
    project_id = data["project_id"]
    parent_path = data["parent_path"]
    csv_file_data = data["csv_file_data"]
    file = data["file"]
    column_names = data["column_names"]
    attributes_view_default = data["attributes_view_default"]
    readyToWorkProject = data.get("readyToWorkProject", False)
    nextWorkFlowStep = data.get("nextWorkFlowStep", 'New')
    nextWorkFlowStepId = data.get("nextWorkFlowStepId", '00000000-0000-0000-0000-000000000000')
    nextWorkFlowState = data.get("nextWorkFlowState", 'New')
    
    image_path_local_index = None
    image_width_index = None
    image_height_index = None
    pre_annotated_Bounding_Box_index = None
    pre_annotated_Circle_index = None
    pre_annotated_Polygon_index = None
    pre_annotated_Scoring_index = None
    pre_annotated_Brush_index = None
    pre_annotated_Instance_index = None
    metadata_index = []
    
    for i, col in enumerate(column_names):
        if col == "image_path_local":
            image_path_local_index = i
        elif col == "image_width":
            image_width_index = i
        elif col == "image_height":
            image_height_index = i
        elif col == "view_annotation_result_Bounding-Box":
            pre_annotated_Bounding_Box_index = i
        elif col == "view_annotation_result_Circle":
            pre_annotated_Circle_index = i
        elif col == "view_annotation_result_Polygon":
            pre_annotated_Polygon_index = i
        elif col == "view_annotation_result_Scoring":
            pre_annotated_Scoring_index = i
        elif col == "view_annotation_result_Brush":
            pre_annotated_Brush_index = i
        elif col == "view_annotation_result_Instance":
            pre_annotated_Instance_index = i
        else:
            metadata_index.append({
                "meta_data_index": i,
                "meta_data_code": col
            })

    bounding_box_config_name = None
    circle_config_name = None
    polygon_config_name = None
    scoring_config_name = None
    brush_config_name = None

    instance_config_name = None
    instance_bounding_box_config_name =  None
    instance_keypoints_config_name = None

    status_code_prj, json_data_prj = DMS_PROJECT_API().detail(user, project_id)
    if status_code_prj == 200:
        if json_data_prj['success']:
            project_data = json_data_prj['data']
            project_class_config = project_data['labelConfig']
            xml_class_config = ET.fromstring(project_class_config)
            for tool in ['RectangleLabels', 'EllipseLabels', 'PolygonLabels', 'ScoringLabels', 'BrushLabels', 'Tools']:
                for item in xml_class_config.findall(tool):
                    if tool == 'RectangleLabels':
                        bounding_box_config_name = item.attrib['name']
                    if tool == 'EllipseLabels':
                        circle_config_name = item.attrib['name']
                    if tool == 'PolygonLabels':
                        polygon_config_name = item.attrib['name']
                    if tool == 'ScoringLabels':
                        scoring_config_name = item.attrib['name']
                    if tool == 'BrushLabels':
                        brush_config_name = item.attrib['name']
                    if tool == 'Tools':
                        instance_config_name = item.attrib['name']
                        for item_tool in item.iter():
                            if item_tool.tag == 'RectangleLabels':
                                instance_bounding_box_config_name = item_tool.attrib['name']
                            if item_tool.tag == 'KeyPointLabels':
                                instance_keypoints_config_name = item_tool.attrib['name']

    attributes = attributes_view_default.copy()
    csv_reader = csv.reader(io.StringIO(csv_file_data), delimiter=',')
    for index, row in enumerate(csv_reader):
        if row[image_path_local_index] in file and index > 0:
            view_folder_path = ""
            if parent_path != "/":
                view_folder_path = parent_path[:-1] + str(Path("/{}".format(row[image_path_local_index])).parent.absolute())
            else:
                view_folder_path = str(Path("/{}".format(row[image_path_local_index])).parent.absolute())
            
            result_annotate = [
                {
                    'result': [],
                    'createdBy': ""
                }
            ]

            if pre_annotated_Scoring_index != None:
                if row[pre_annotated_Scoring_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Scoring_index]))
                    scoring_config_upload_name = 'scoringlabels' if scoring_config_name is None else scoring_config_name
                    for item in annotate_data:
                            scoring_results = {
                                'values': [],
                                'size': item['size']
                            }
                            try:
                                for row_item in item['score']:
                                    row_re_format = []
                                    for col in row_item:
                                        row_re_format.append({
                                            'id': generate(size=10),
                                            'score': col
                                        })
                                    scoring_results['values'].append(row_re_format)

                                result_annotate[0]['result'].append({
                                    'original_width': int(float(row[image_width_index])),
                                    'original_height': int(float(row[image_height_index])),
                                    'image_rotation': 0,
                                    'value': {
                                        'scoringlabels': [json.dumps(scoring_results)]
                                    },
                                    'id': generate(size=10),
                                    'from_name': scoring_config_upload_name,
                                    'to_name': 'image',
                                    'type': 'scoringlabels',
                                    'origin': 'manual'
                                })
                            except Exception as e:
                                print(f"Line: {index + 1} - {e}")

            if pre_annotated_Bounding_Box_index != None:
                if row[pre_annotated_Bounding_Box_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Bounding_Box_index]))
                    bounding_box_config_upload_name = 'rectanglelabels' if bounding_box_config_name is None else bounding_box_config_name
                    for item in annotate_data:
                        label_id = generate(size=10)
                        try:
                            result_annotate[0]['result'].append({
                                'original_width': int(float(row[image_width_index])),
                                'original_height': int(float(row[image_height_index])),
                                'image_rotation': 0,
                                'value': {
                                    'x': item['x'] / float(row[image_width_index]) * 100,
                                    'y': item['y'] / float(row[image_height_index]) * 100,
                                    'width': item['width'] / float(row[image_width_index]) * 100,
                                    'height': item['height'] / float(row[image_height_index]) * 100,
                                    'rectanglelabels': [item['class_name']]
                                },
                                'id': label_id,
                                'from_name': bounding_box_config_upload_name,
                                'to_name': 'image',
                                'type': 'rectanglelabels',
                                'origin': 'manual'
                            })
                        except Exception as e:
                            print(f"Line: {index + 1} - {e}")
                        if item.get('meta_label', None):
                            meta_label = item['meta_label']
                            meta_label_keys = item['meta_label'].keys()
                            for meta_label_key in meta_label_keys:
                                if meta_label[meta_label_key]['type'] == 'choices':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'x': item['x'] / float(row[image_width_index]) * 100,
                                            'y': item['y'] / float(row[image_height_index]) * 100,
                                            'width': item['width'] / float(row[image_width_index]) * 100,
                                            'height': item['height'] / float(row[image_height_index]) * 100,
                                            'choices': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'choices',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'textarea':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'x': item['x'] / float(row[image_width_index]) * 100,
                                            'y': item['y'] / float(row[image_height_index]) * 100,
                                            'width': item['width'] / float(row[image_width_index]) * 100,
                                            'height': item['height'] / float(row[image_height_index]) * 100,
                                            'text': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'textarea',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'number':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'x': item['x'] / float(row[image_width_index]) * 100,
                                            'y': item['y'] / float(row[image_height_index]) * 100,
                                            'width': item['width'] / float(row[image_width_index]) * 100,
                                            'height': item['height'] / float(row[image_height_index]) * 100,
                                            'number': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'number',
                                        'origin': 'manual'
                                    })
            if pre_annotated_Circle_index != None:
                if row[pre_annotated_Circle_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Circle_index]))
                    circle_config_upload_name = 'ellipselabels' if circle_config_name is None else circle_config_name
                    for item in annotate_data:
                        try:
                            result_annotate[0]['result'].append({
                                'original_width': int(float(row[image_width_index])),
                                'original_height': int(float(row[image_height_index])),
                                'image_rotation': 0,
                                'value': {
                                    'x': item['x'] / float(row[image_width_index]) * 100,
                                    'y': item['y'] / float(row[image_height_index]) * 100,
                                    'radiusX': item['radius'] / float(row[image_width_index]) * 100,
                                    'radiusY': item['radius'] / float(row[image_height_index]) * 100,
                                    'ellipselabels': [item['class_name']]
                                },
                                'id': generate(size=10),
                                'from_name': circle_config_upload_name,
                                'to_name': 'image',
                                'type': 'ellipselabels',
                                'origin': 'manual'
                            })
                        except Exception as e:
                            print(f"Line: {index + 1} - {e}")

            if pre_annotated_Polygon_index != None:
                if row[pre_annotated_Polygon_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Polygon_index]))
                    polygon_config_upload_name = 'polygonlabels' if polygon_config_name is None else polygon_config_name
                    for item in annotate_data:
                        label_id = generate(size=10)
                        try:
                            points_list = item['points']
                            points_list_normalizing = []
                            for point in points_list:
                                x_point = point[0] / float(row[image_width_index]) * 100
                                y_point = point[1] / float(row[image_height_index]) * 100
                                points_list_normalizing.append([x_point, y_point])

                            result_annotate[0]['result'].append({
                                'original_width': int(float(row[image_width_index])),
                                'original_height': int(float(row[image_height_index])),
                                'image_rotation': 0,
                                'value': {
                                    'points': points_list_normalizing,
                                    'polygonlabels': [item['class_name']]
                                },
                                'id': label_id,
                                'from_name': polygon_config_upload_name,
                                'to_name': 'image',
                                'type': 'polygonlabels',
                                'origin': 'manual'
                            })
                        except Exception as e:
                            print(f"Line: {index + 1} - {e}")
                        if item.get('meta_label', None):
                            meta_label = item['meta_label']
                            meta_label_keys = item['meta_label'].keys()
                            for meta_label_key in meta_label_keys:
                                if meta_label[meta_label_key]['type'] == 'choices':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'points': points_list_normalizing,
                                            'choices': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'choices',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'textarea':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'points': points_list_normalizing,
                                            'text': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'textarea',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'number':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'points': points_list_normalizing,
                                            'number': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'number',
                                        'origin': 'manual'
                                    })

            if pre_annotated_Brush_index != None:
                if row[pre_annotated_Brush_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Brush_index]))
                    brush_config_upload_name = 'brushlabels' if brush_config_name is None else brush_config_name
                    for item in annotate_data:
                        label_id = generate(size=10)
                        try:
                            result_annotate[0]['result'].append({
                                'original_width': int(float(row[image_width_index])),
                                'original_height': int(float(row[image_height_index])),
                                'image_rotation': 0,
                                'value': {
                                    'format': 'rle',
                                    'rle': item['rle'],
                                    'brushlabels': [item['class_name']]
                                },
                                'id': label_id,
                                'from_name': brush_config_upload_name,
                                'to_name': 'image',
                                'type': 'brushlabels',
                                'origin': 'manual'
                            })
                        except Exception as e:
                            print(f"Line: {index + 1} - {e}")
                        if item.get('meta_label', None):
                            meta_label = item['meta_label']
                            meta_label_keys = item['meta_label'].keys()
                            for meta_label_key in meta_label_keys:
                                if meta_label[meta_label_key]['type'] == 'choices':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'format': 'rle',
                                            'rle': item['rle'],
                                            'choices': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'choices',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'textarea':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'format': 'rle',
                                            'rle': item['rle'],
                                            'text': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'textarea',
                                        'origin': 'manual'
                                    })
                                if meta_label[meta_label_key]['type'] == 'number':
                                    result_annotate[0]['result'].append({
                                        'original_width': int(float(row[image_width_index])),
                                        'original_height': int(float(row[image_height_index])),
                                        'image_rotation': 0,
                                        'value': {
                                            'format': 'rle',
                                            'rle': item['rle'],
                                            'number': meta_label[meta_label_key]['value']
                                        },
                                        'id': label_id,
                                        'from_name': meta_label_key,
                                        'to_name': 'image',
                                        'type': 'number',
                                        'origin': 'manual'
                                    })

            if pre_annotated_Instance_index != None:
                if row[pre_annotated_Instance_index] != "":
                    annotate_data = list(eval(row[pre_annotated_Instance_index]))
                    for item in annotate_data:
                        try:
                            for component in item['components']:
                                component_item = {
                                    'original_width': int(float(row[image_width_index])),
                                    'original_height': int(float(row[image_height_index])),
                                    'image_rotation': 0,
                                    'parentID': item['instance_id'],
                                    'id': generate(size=10),
                                    'from_name': 'kp01',
                                    'to_name': 'image',
                                    'origin': 'manual',
                                }
                                if component['type'] == 'rectangle':
                                    component_item['value'] = {
                                        'x': float(component['value']['x']) / float(row[image_width_index]) * 100,
                                        'y': float(component['value']['y']) / float(row[image_height_index]) * 100,
                                        'width': float(component['value']['width']) / float(row[image_width_index]) * 100,
                                        'height': float(component['value']['height']) / float(row[image_height_index]) * 100,
                                        'rectanglelabels': [component['class_name']],
                                        'rotation': 0
                                    }
                                    component_item['type'] = 'rectanglelabels'
                                    component_item['from_name'] = instance_bounding_box_config_name
                                
                                if component['type'] == 'keypoint':
                                    component_item['value'] = {
                                        'x': float(component['value']['x']) / float(row[image_width_index]) * 100,
                                        'y': float(component['value']['y']) / float(row[image_height_index]) * 100,
                                        'width': float(component['value']['radius']) / float(row[image_width_index]) * 100,
                                        'keypointlabels': [component['class_name']],
                                        'rotation': 0
                                    }
                                    component_item['type'] = 'keypointlabels'
                                    component_item['from_name'] = instance_keypoints_config_name


                                result_annotate[0]['result'].append(component_item)

                            result_annotate[0]['result'].append({
                                'id': item['instance_id'],
                                'name': item['instance_name'],
                                'type': 'instance',
                            })

                        except Exception as e:
                            print(f"Line: {index + 1} - {e}")

            if result_annotate[0]['result'] != []:
                attributes_view_default_copy = [
                    {'code': 'view_state', 'value': 'New'},
                    {'code': 'view_priority', 'value': '0'},
                    {'code': 'view_category', 'value': ''},
                    {'code': 'view_folder', 'value': "{}/".format(view_folder_path)},
                    {'code': 'view_annotation_result', 'value': json.dumps(result_annotate)},
                    {'code': 'view_archive', 'value': 'false'},
                    {'code': 'view_workflow_step', 'value': 'New'},
                    {'code': 'view_workflow_step_id', 'value': '00000000-0000-0000-0000-000000000000'},
                    {'code': 'annotate_instance', 'value': ''},
                    {'code': 'annotate_history', 'value': ''},
                ]
            else:
                attributes_view_default_copy = [
                    {'code': 'view_state', 'value': 'New'},
                    {'code': 'view_priority', 'value': '0'},
                    {'code': 'view_category', 'value': ''},
                    {'code': 'view_folder', 'value': "{}/".format(view_folder_path)},
                    {'code': 'view_annotation_result', 'value': ''},
                    {'code': 'view_archive', 'value': 'false'},
                    {'code': 'view_workflow_step', 'value': 'New'},
                    {'code': 'view_workflow_step_id', 'value': '00000000-0000-0000-0000-000000000000'},
                    {'code': 'annotate_instance', 'value': ''},
                    {'code': 'annotate_history', 'value': ''},
                ]

            if readyToWorkProject:
                for attrib in attributes_view_default_copy:
                    if attrib['code'] == settings.ATTR_VIEW_STATE:
                        attrib['value'] = nextWorkFlowState
                    if attrib['code'] == 'view_workflow_step':
                        attrib['value'] = nextWorkFlowStep
                    if attrib['code'] == 'view_workflow_step_id':
                        attrib['value'] = nextWorkFlowStepId

            # post to "Import a file to the dataset" with attribute
            file_post = File(open(file, 'rb'))
            
            if metadata_index != []:
                for meta_data in metadata_index:
                    if row[meta_data['meta_data_index']] != "":
                        if meta_data['meta_data_code'].startswith("meta_"):
                            attributes_view_default_copy.append({
                                'code': meta_data['meta_data_code'],
                                'value': row[meta_data['meta_data_index']]
                            })

            attributes = attributes_view_default_copy

            result = {
                "success": True,
                "file": None
            }
            print("+++++++++")
            print(row[0])
            print(attributes)

            data_view = settings.DEFAULT_FILE_FIELDS
            data_view['pathFolder'] = "{}/".format(view_folder_path)
            if readyToWorkProject:
                data_view['viewState'] = nextWorkFlowState
                data_view['viewWorkFlowStep'] = nextWorkFlowStep
                data_view['viewWorkFlowStepId'] = nextWorkFlowStepId
            try:
                status_code_post, json_data_post = DMS_FILE_API().create(user, file_post, project_id, attributes, data_view)
                if status_code_post == 200:
                    if json_data_post['success']:
                        print("{}: Done!".format(row[0]))
                        result["file"] = row[0]
                        result["message"] = "Upload Success"
                    else:
                        result["success"] = False
                        result["file"] = row[0]
                        result["message"] = json_data_post['message']
                else:
                    result["success"] = False
                    result["file"] = row[0]
                    result["message"] = "Upload Fail"
                    return result
            except Exception as e:
                result["success"] = False
                result["file"] = row[0]
                result["message"] = "Connect to server error"
                return result
            
            return result

def post_img_in_zip_not_csv_to_s3(data):
    user = data["user"]
    project_id = data["project_id"]
    parent_path = data["parent_path"]
    file = data["file"]
    folder_zip_path = data["folder_zip_path"]
    attributes_view_default = data["attributes_view_default"]
    readyToWorkProject = data.get("readyToWorkProject", False)
    nextWorkFlowStep = data.get("nextWorkFlowStep", 'New')
    nextWorkFlowStepId = data.get("nextWorkFlowStepId", '00000000-0000-0000-0000-000000000000')
    nextWorkFlowState = data.get("nextWorkFlowState", 'New')

    # for file in image_in_zip_not_in_csv:
    attributes = attributes_view_default.copy()
    view_folder_path = ""
    relative_file_path = str(Path(file.replace(folder_zip_path, '')).parent.absolute())
    if parent_path != "/":
        view_folder_path = parent_path[:-1] + relative_file_path
    else:
        view_folder_path = relative_file_path
    for attribute in attributes:
        if attribute['code'] == settings.ATTR_VIEW_FOLDER:
            attribute['value'] = "{}/".format(view_folder_path)
            break
    if readyToWorkProject:
        for attrib in attributes:
            if attrib['code'] == settings.ATTR_VIEW_STATE:
                attrib['value'] = nextWorkFlowState
            if attrib['code'] == 'view_workflow_step':
                attrib['value'] = nextWorkFlowStep
            if attrib['code'] == 'view_workflow_step_id':
                attrib['value'] = nextWorkFlowStepId

    file_post = File(open(file, 'rb'))

    result = {
        "success": True,
        "file": None
    }
    data_view = settings.DEFAULT_FILE_FIELDS
    if readyToWorkProject:
        data_view['viewState'] = nextWorkFlowState
        data_view['viewWorkFlowStep'] = nextWorkFlowStep
        data_view['viewWorkFlowStepId'] = nextWorkFlowStepId
    try:
        status_code_post, json_data_post = DMS_FILE_API().create(user, file_post, project_id, attributes, data_view)
        if status_code_post == 200:
            if json_data_post['success']:
                print("{}: Done!".format(str(Path(file.replace(folder_zip_path, '')))))
                result["file"] = str(Path(file.replace(folder_zip_path, '')))
                result["message"] = "Upload Success"
            else:
                result["success"] = False
                result["file"] = file
                result["message"] = json_data_post['message']
                return result
        else:
            result["success"] = False
            result["file"] = str(Path(file.replace(folder_zip_path, '')))
            result["message"] = "Upload Fail"
            return result
    except Exception as e:
        result["success"] = False
        result["file"] = str(Path(file.replace(folder_zip_path, '')))
        result["message"] = "Connect to server error"
        return result

    return result

def recursive_auto_create_folder(user, project_id, parent_path, data_folder_path, data_folder_path_root,
                                 current_folder_project):
    for file in os.listdir(data_folder_path):
        d = os.path.join(data_folder_path, file)
        if os.path.isdir(d):
            parent_absolute = Path("{}".format(d.replace(data_folder_path_root, ""))).parent.absolute()
            filterPath = "{}{}".format(parent_path[:-1], parent_absolute) if str(
                parent_absolute) == "/" else "{}{}/".format(parent_path[:-1], parent_absolute)
            is_existed = False
            # Check folder is existed or not
            for folder_item in current_folder_project:
                if folder_item['filterPath'] == filterPath and folder_item['name'] == file:
                    is_existed = True
                    break

            if not is_existed:
                new_folder = {
                    'dateCreated': "2022-09-27T10:17:03.4665001+00:00",
                    'dateModified': "2022-09-27T10:16:53.7636669+00:00",
                    'filterPath': filterPath,
                    'hasChild': False,
                    'isFile': False,
                    'name': file,
                    'size': 0,
                    'type': "",
                    '_fm_htmlAttr': {},
                    '_fm_icon': "e-fe-folder",
                    '_fm_id': "fe_tree"
                }
                # print(new_folder)
                current_folder_project.append(new_folder)

            recursive_auto_create_folder(user, project_id, parent_path, d, data_folder_path_root,
                                         current_folder_project)


@shared_task
def upload_data_v3(user_id, project_id, zip_csv_id, zip_file_save, csv_file_data, parent_path="/",
                   readyToWorkProject=False, nextWorkFlowStep='New',
                   nextWorkFlowStepId='00000000-0000-0000-0000-000000000000', nextWorkFlowState='New'):
    current_task_id = celery.current_task.request.id
    start_time = datetime.datetime.now()

    user = User.objects.get(dms_id=user_id)
    
    folder_zip_path = "data_manager/unzip_data_temp/{}".format(zip_csv_id)
    if os.path.exists(folder_zip_path) == False:
        os.mkdir(folder_zip_path)

    if (os.path.exists(
            "{}/{}".format(folder_zip_path, zip_file_save)) == False):
        shutil.move("{}/{}".format(settings.MEDIA_ROOT, zip_file_save),
                    folder_zip_path,
                    copy_function=shutil.copy2)
    else:
        shutil.move("{}/{}".format(settings.MEDIA_ROOT, zip_file_save),
                    "{}/{}".format(folder_zip_path, zip_file_save),
                    copy_function=shutil.copy2)
        
    data_zip_path = None
    dir_list = os.listdir(folder_zip_path)
    for dir in dir_list:
        if ".zip" in dir:
            data_zip_path = folder_zip_path + "/" + dir
    # unzip file data (.zip)
    with zipfile.ZipFile(data_zip_path, 'r') as zip_ref:
        zip_ref.extractall(folder_zip_path)

    for dir in os.listdir(folder_zip_path):
        if Path(folder_zip_path + "/" + dir).is_dir():
            data_folder_path = folder_zip_path + "/" + dir
            break
    
    # get all files
    file_path_list = []
    for root, dirs, files in os.walk(data_folder_path, topdown=True):
        for name in files:
            full_path = os.path.join(root, name)
            file_path_list.append(full_path)
            
    data_folder_path_root = folder_zip_path

    status_code, project_res = DMS_PROJECT_API().detail(user, project_id)
    project_data = project_res['data']
    try:
        current_folders = json.loads(project_data['annotationFlow'])
    except:
        current_folders = []

    recursive_auto_create_folder(user, project_id, parent_path, folder_zip_path, data_folder_path_root,
                                 current_folder_project=current_folders)
    data = {
        'annotationFlow': json.dumps(current_folders)
    }
    try:
        DMS_PROJECT_API().edit(user, project_id, data)
        print("[Info]: Auto create folder structure successfully")
    except Exception as e:
        print(e)

    # get attributes in csv file
    column_names = []
    csv_reader = csv.reader(io.StringIO(csv_file_data), delimiter=',')
    for row in csv_reader:
        for i in range(len(row)):
            column_names.append(row[i])
        break

    # get data and upload
    data_filter = mapping_data(folder_zip_path=folder_zip_path, file_csv_data=csv_file_data)
    image_in_zip_not_in_csv = data_filter["image_in_zip_but_not_in_csv"]
    image_in_csv_not_in_zip = data_filter["image_in_csv_but_not_in_zip"]
    image_in_zip_and_in_csv = data_filter["image_in_csv_and_in_zip"]

    attributes_view_default = settings.DEFAULT_VIEW_ATTRIBUTES.copy()

    error_upload_file_list = []  # list save all file upload fail
    success_upload_count = 0
    error_upload_count = 0

    count = 0
    jump = 1
    if len(file_path_list) < 10:
        jump = len(file_path_list)
    else:
        jump = int(len(file_path_list) / 10)
    data_in_zip_and_in_csv = []
    for file in image_in_zip_and_in_csv:
        data_item_in_zip_and_in_csv = {
            "user": user,
            "project_id": project_id,
            "parent_path": parent_path,
            "csv_file_data": csv_file_data,
            "file": file,
            "column_names": column_names,
            "attributes_view_default": attributes_view_default,
            "readyToWorkProject": readyToWorkProject,
            "nextWorkFlowStep": nextWorkFlowStep,
            "nextWorkFlowStepId": nextWorkFlowStepId,
            "nextWorkFlowState": nextWorkFlowState
        }
        data_in_zip_and_in_csv.append(data_item_in_zip_and_in_csv)
    data_in_zip_not_in_csv = []
    for file in image_in_zip_not_in_csv:
        data_item_in_zip_not_in_csv = {
            "user": user,
            "project_id": project_id,
            "parent_path": parent_path,
            "file": file,
            "folder_zip_path": folder_zip_path,
            "attributes_view_default": attributes_view_default,
            "readyToWorkProject": readyToWorkProject,
            "nextWorkFlowStep": nextWorkFlowStep,
            "nextWorkFlowStepId": nextWorkFlowStepId,
            "nextWorkFlowState": nextWorkFlowState
        }
        data_in_zip_not_in_csv.append(data_item_in_zip_not_in_csv)
    is_limited = False
    executor_in_zip_and_in_csv = concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_WORKERS_UPLOAD)
    for _ in executor_in_zip_and_in_csv.map(post_img_in_zip_and_csv_to_s3, data_in_zip_and_in_csv):
        if _["success"] == True:
            success_upload_count += 1
        if _["success"] == False:
            if _['message'] == "Create.SizeLimit":
                message_outlimit = {
                    "zip_csv_task_id": zip_csv_id,
                    "current_upload_task_id": current_task_id,
                    "success": True,
                    "message": _['message'],
                    'file_type': 's3'
                }

                async_to_sync(channel_layer.group_send)(
                    str(user.id),
                    {
                        'type': 'chat.message',
                        'message': message_outlimit
                    }
                )
            if _['message'] == "Create.ResourceLimit":
                message_outlimit = {
                    "zip_csv_task_id": zip_csv_id,
                    "current_upload_task_id": current_task_id,
                    "success": True,
                    "message": _['message'],
                    'file_type': 's3'
                }

                async_to_sync(channel_layer.group_send)(
                    str(user.id),
                    {
                        'type': 'chat.message',
                        'message': message_outlimit
                    }
                )
                is_limited = True
                break

            error_upload_count += 1
            error_upload_file_list.append(_["file"])

        count += 1
        if count % jump == 0:
            async_to_sync(channel_layer.group_send)(
                str(user.id),
                {
                    'type': 'chat.message',
                    'message': {
                        "zip_csv_task_id": zip_csv_id,
                        "uploaded_number": count,
                        "total_number": len(data_in_zip_and_in_csv) +  len(data_in_zip_not_in_csv),
                        "percent": round(count / (len(data_in_zip_and_in_csv) +  len(data_in_zip_not_in_csv)) * 100, 2)
                    }
                }
            )

    executor_in_zip_not_in_csv = concurrent.futures.ThreadPoolExecutor(max_workers=settings.MAX_WORKERS_UPLOAD)
    for _ in executor_in_zip_not_in_csv.map(post_img_in_zip_not_csv_to_s3, data_in_zip_not_in_csv):
        if _["success"] == True:
            success_upload_count += 1
        if _["success"] == False:
            if _['message'] == "Create.SizeLimit":
                message_outlimit = {
                    "zip_csv_task_id": zip_csv_id,
                    "current_upload_task_id": current_task_id,
                    "success": True,
                    "message": _['message'],
                    'file_type': 's3'
                }

                async_to_sync(channel_layer.group_send)(
                    str(user.id),
                    {
                        'type': 'chat.message',
                        'message': message_outlimit
                    }
                )
            if _['message'] == "Create.ResourceLimit":
                message_outlimit = {
                    "zip_csv_task_id": zip_csv_id,
                    "current_upload_task_id": current_task_id,
                    "success": True,
                    "message": _['message'],
                    'file_type': 's3'
                }

                async_to_sync(channel_layer.group_send)(
                    str(user.id),
                    {
                        'type': 'chat.message',
                        'message': message_outlimit
                    }
                )
                is_limited = True
                break
            
            error_upload_count += 1
            error_upload_file_list.append(_["file"])

        count += 1
        if count % jump == 0:
            async_to_sync(channel_layer.group_send)(
                str(user.id),
                {
                    'type': 'chat.message',
                    'message': {
                        "zip_csv_task_id": zip_csv_id,
                        "uploaded_number": count,
                        "total_number": len(data_in_zip_and_in_csv) +  len(data_in_zip_not_in_csv),
                        "percent": round(count / (len(data_in_zip_and_in_csv) +  len(data_in_zip_not_in_csv)) * 100, 2)
                    }
                }
            )

    delete_data_folder(zip_task_id=zip_csv_id)

    results = {
        "zip_csv_task_id": zip_csv_id,
        "current_upload_task_id": current_task_id,
        'created_time': str(start_time),
        "success": True,
        "zip_file_save": zip_file_save,
        "message": "Upload Completed",
        'file_type': 's3',
        "success_number": success_upload_count,
        "error_number": error_upload_count,
        "error_list": error_upload_file_list
    }
    if is_limited:
        return results
    
    async_to_sync(channel_layer.group_send)(
        str(user.id),
        {
            'type': 'chat.message',
            'message': results
        }
    )

    return results

def delete_data_folder(zip_task_id):
    folder_zip_path = "data_manager/unzip_data_temp/{}".format(zip_task_id)

    shutil.rmtree(folder_zip_path)
    return {
        "status": "Cancel",
        "message": "Cancel upload"
    }


es = settings.ES_CONNECTION
index_name = settings.ES_INDEX_ATTRB

view_annotation_result_id = settings.VIEW_ANNOTATION_RESULT_ATTRB_ID

import pymysql
@shared_task
def folder_sync_es_multiple_files(project_master_id, file_ids):
    db = pymysql.connect(
        host = settings.DMS_MYSQL['HOST'],
        port = settings.DMS_MYSQL['PORT'],
        user = settings.DMS_MYSQL['USER'],
        password = settings.DMS_MYSQL['PASSWORD'],
        database = settings.DMS_MYSQL['DATABASE']
    )

    cursor = db.cursor()

    cursor.execute(f"SELECT * FROM ProjectFiles \
                   WHERE ProjectId = '{project_master_id}' \
                    AND FileId IN {str(tuple(file_ids)).replace(',)', ')')}")

    result = cursor.fetchall()

    for x in result:
        # print(x)
        doc = {
            "fileId": x[1],
            "projectId": x[2],
            "isDelete": x[7],
            "isArchive": x[8],
            "priority": x[9],
            "viewState": x[10],
            "viewWorkFlowStepId": x[11],
            "viewWorkFlowStepOrder": x[12],
            "pathFolder": x[13],
            "viewWorkFlowStep": x[14],
            "folder": [{}],
        }

        if x[13] is not None:
            view_folder = x[13]
            view_folder_list = view_folder.split('/')

            j = 1

            for item in view_folder_list:
                if item != '':
                    doc['folder'][0].update({
                        f"level{j}": item
                    })
                    j = j + 1

        file_id = x[1]

        print(f"{x[0]}------------------------- index doc")
        print(doc)

        if es.exists(index=index_name, id=x[0]):
            es_action_update = es.update(
                index=index_name,
                id=x[0],
                doc=doc
            )
            print(es_action_update)
            print(es.get(index=index_name, id=x[0]))
        else:
            es_action_index = es.index(
                index=index_name,
                id=x[0],
                document=doc
            )
            print(es_action_index)
            print(es.get(index=index_name, id=x[0]))
        
        # if es_action_index['result'] in ['created', 'updated']:
        #     print("index successfull for file_id: ", file_id)

    db.close()