import logging, json, csv, io, time
from collections import OrderedDict
from drf_yasg.utils import swagger_auto_schema

from django.conf import settings
from django.utils.decorators import method_decorator
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication, SessionAuthentication
from django.http import HttpResponse

from core.authentication import TokenParamAuthentication
from core.permissions import all_permissions, ViewClassPermission
from dms_connector.api import AuthenticatedAPIView, DMS_FILE_API, DMS_PROJECT_API, DMS_STATISTIC_API, DMS_PROJECT_ATTR_API, DMS_TASK_ASSIGNMENT_API, DMS_STORAGE_API, DMS_PROJECT_TASK_CONFIG_API
from dms_connector.serializers import IdByProjectSerializer, FilterSerializer

from data_manager.tasks import confirm_zip, upload_data, delete_data_folder
from data_manager.tasks_v2 import upload_data_v2, fill_pre_annotated_data
from data_manager.tasks_v3 import upload_data_v3, folder_sync_es_multiple_files
from data_manager.folder_sync_es_project import *
from multilevel_classification.attrb_sync_es import *
from multilevel_classification.tasks import attrb_sync_es_multiple_files
from django.core.files.storage import FileSystemStorage
from data_manager.serializers import *
from data_manager.file_manager import FileManager
from data_manager.file_manager_es import FileManagerEs
from channels.layers import get_channel_layer
from asgiref.sync import async_to_sync

logger = logging.getLogger(__name__)

channel_layer = get_channel_layer()


class Test(AuthenticatedAPIView):

    def get(self, request):
        user = request.user
        data_filter = {
            "image_in_zip_but_not_in_csv": ['1.jpg'],
            "image_in_csv_but_not_in_zip": ['2.jpg'],
            "image_in_csv_and_in_zip": []
        }
        bgr_task_id = '64506bf1-7a48-4de3-a10f-afb5ce50b921'
        csv_file_name = 'test_8839414d8b7.csv'
        file_save = 'test.zip'

        # results = {
        #     'success': True,
        #     'zip_task_id': bgr_task_id,
        #     'csv_file_name': csv_file_name,
        #     'zip_file_name': file_save,
        #     'file_type': 'zip',
        #     'parent_path': '/',
        #     "data": data_filter,
        #     'created_time': '14/10/2022'
        # }

        results = {
            'created_time': '14/10/2022',
            "success": True,
            "message": "Upload Completed",
            'csv_file_name': csv_file_name,
            'file_type': 's3',
            "success_number": 1,
            "error_number": 2,
            "error_list": 3
        }

        # results['success'] = False
        # results['data'] = {
        #     'attr_not_in_project': ['attr_1', 'attr_2'],
        #     'attr_not_in_csv': ['attr_3'],
        # }

        async_to_sync(channel_layer.group_send)(
            str(user.id),
            {
                'type': 'chat.message',
                'message': results
            }
        )

        return Response('OK')


@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Get folders dataset by project ID",
    operation_description="Get folders dataset by project ID.",
    query_serializer=IdByProjectSerializer
))
class DataListFoldertAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        POST=all_permissions.tasks_create,
    )

    def get(self, request):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']
        status_code, project_res = DMS_PROJECT_API().detail(user, project_id)
        project_data = project_res['data']
        try:
            all_folders = json.loads(project_data[FileManager.project_folder_field])
        except:
            all_folders = []

        tree = []
        for folder in all_folders:
            name = folder['name']
            path = folder['filterPath']
            split_path = [path_sub for path_sub in path.split('/') if path_sub]
            split_path.append(name)

            if split_path not in tree:
                tree.append(split_path)

        results = [{'id': '/', 'pId': 0, 'level': 0,
                    'value': '/', 'title': 'Project', 'isLeaf': False
                    }]
        for node in tree:
            for i, folder in enumerate(node):
                path = ('/' + '/'.join(node[:i + 1]) + '/') if len(node[:i + 1]) > 1 else f'/{folder}/'
                is_leaf = False
                if i == len(node) - 1:
                    is_leaf = True
                parent = ('/' + '/'.join(node[:i]) + '/') if i > 0 else '/'
                index_exist = next((i for i, result in enumerate(results) if result['value'] == path), None)

                if index_exist == None:
                    results.append(
                        {'id': path, 'pId': parent, 'level': i + 1,
                         'value': path, 'title': folder, 'isLeaf': is_leaf
                         }
                    )
                else:
                    results[index_exist].update({'isLeaf': False})

        if len(results) == 1:
            results[0]['isLeaf'] = True

        project_res['data'] = results

        return Response(project_res)


@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="File Operation Download",
    operation_description="File Operation Download",
    query_serializer=IdByProjectSerializer,
    request_body=FileOperationSerializer,
))
class DataDownloadAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        POST=all_permissions.tasks_view,
    )

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = FileDownloadInputSerializer(data=request.POST)
        serializer.is_valid(raise_exception=True)

        data = json.loads(serializer.data['downloadInput'])

        serializer = FileOperationSerializer(data=data)
        serializer.is_valid(raise_exception=True)
        data = FileDownloadSerializer(data['data'], many=True).data

        results = FileManager().download(user, project_id, data)

        resp = HttpResponse(results, 'application/force-download')
        resp['Content-Disposition'] = 'attachment;filename=%s' % ('project.zip')

        return resp


@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="File Operation (create, rename, move, detail, upload, ...)",
    operation_description="File Operation (create, rename, move, detail, upload, ...)",
    query_serializer=IdByProjectSerializer,
    request_body=FileOperationSerializer,
))
class DataExplorerAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        POST=all_permissions.tasks_view,
    )

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = FileOperationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data = serializer.data
        action = data['action']

        status_code = 200

        if action == 'read':
            serializer = FileReadSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManager().read(user, project_id, serializer.data)
        elif action == 'create':
            serializer = FileCreateSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManager().create(user, project_id, serializer.data)
        elif action == 'delete':
            serializer = FileDeleteSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManagerEs().delete(user, project_id, serializer.data)
        elif action == 'move':
            serializer = FileMoveSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManagerEs().move(user, project_id, serializer.data)
        elif action == 'rename':
            serializer = FileRenameSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManagerEs().rename(user, project_id, serializer.data)
        elif action == 'details':
            serializer = FileDetailSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManager().details(user, project_id, serializer.data)
        elif action == 'save':
            serializer = FileUploadSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results, status_code = FileManager().upload(user, project_id, request.FILES['uploadFiles'], serializer.data)

        return Response(results, status_code)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="File Operation (create, rename, move, detail, upload, ...) multiple types",
    operation_description="File Operation (create, rename, move, detail, upload, ...) multiple types",
    query_serializer=IdByProjectSerializer,
    request_body=FileOperationSerializer,
))
class DataExplorerMultipleTypesAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        POST=all_permissions.tasks_view,
    )

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = FileOperationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data = serializer.data
        action = data['action']

        status_code = 200

        if action == 'read':
            serializer = FileReadSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManager().read(user, project_id, serializer.data)
        elif action == 'create':
            serializer = FileCreateSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManager().create(user, project_id, serializer.data)
        elif action == 'delete':
            serializer = FileDeleteSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManagerEs().delete(user, project_id, serializer.data)
        elif action == 'move':
            serializer = FileMoveSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManagerEs().move(user, project_id, serializer.data)
        elif action == 'rename':
            serializer = FileRenameSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManagerEs().rename(user, project_id, serializer.data)
        elif action == 'details':
            serializer = FileDetailSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results = FileManager().details(user, project_id, serializer.data)
        elif action == 'save':
            serializer = FileUploadMultipleTypesSerializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            results, status_code = FileManager().upload_multi_types(user, project_id, request.FILES['uploadFiles'], serializer.data)

        return Response(results, status_code)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Upload attribute as file",
    operation_description="Upload attribute as file.",
    query_serializer=IdByProjectSerializer,
    request_body=UploadFileAsAttrbSerializer
))
class UploadFileAsAttrbAPI(AuthenticatedAPIView):

    def post(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = UploadFileAsAttrbSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        file = request.FILES['file']
        attribute_code = request.data['attribute_code']

        status_code, json_data = DMS_FILE_API().upload_file_attribute(user, file, project_id, pk, attribute_code)

        return Response(json_data, status_code)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Get conversation\'s data item by project ID",
    operation_description="Get conversation\'s data item by project ID.",
    query_serializer=ConversationFilterSerializer
))
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Create a message to data item\'s conversation by project ID",
    operation_description="Create a message to data item\'s conversation by project ID.",
    query_serializer=IdByProjectSerializer,
    request_body=ConversationSerializer
))
@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Edit a message to data item\'s conversation by project ID",
    operation_description="Edit a message to data item\'s conversation by project ID.",
    query_serializer=IdByProjectSerializer,
    request_body=CoversationEditSerializer
))
class DataItemConversationAPI(AuthenticatedAPIView):

    def get(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = ConversationFilterSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        params = serializer.data
        del params['project']

        status_code, json_data = DMS_FILE_API().get_conversations(user, pk, project_id, params)

        return Response(json_data, status_code)

    def post(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = ConversationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_FILE_API().create_conversations(user, pk, serializer.data, project_id)

        return Response(json_data, status_code)
    
    def put(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = CoversationEditSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_FILE_API().edit_conversations(user, pk, serializer.data, project_id)

        return Response(json_data, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Get redo reason\'s data item by project ID",
    operation_description="Get redo reason\'s data item by project ID.",
    query_serializer=RedoReasonFilterSerializer
))
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Create a message to data item\'s redo reason by project ID",
    operation_description="Create a message to data item\'s redo reason by project ID.",
    query_serializer=IdByProjectSerializer,
    request_body=RedoReasonSerializer
))
@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Edit a message to data item\'s redo reason by project ID",
    operation_description="Edit a message to data item\'s redo reason by project ID.",
    query_serializer=IdByProjectSerializer,
    request_body=RedoReasonEditSerializer
))
class DataItemRedoReasonAPI(AuthenticatedAPIView):

    def get(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = RedoReasonFilterSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        params = serializer.data
        del params['project']

        status_code, json_data = DMS_FILE_API().get_redoReason(user, pk, project_id, params)

        return Response(json_data, status_code)

    def post(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = RedoReasonSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_FILE_API().create_redoReason(user, pk, serializer.data, project_id)

        return Response(json_data, status_code)
    
    def put(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = RedoReasonEditSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_FILE_API().edit_redoReason(user, pk, serializer.data, project_id)

        return Response(json_data, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Get reject reason\'s data item by project ID",
    operation_description="Get reject reason\'s data item by project ID.",
    query_serializer=RejectReasonFilterSerializer
))
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Create a message to data item\'s reject reason by project ID",
    operation_description="Create a message to data item\'s reject reason by project ID.",
    query_serializer=IdByProjectSerializer,
    request_body=RejectReasonSerializer
))
@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Edit a message to data item\'s reject reason by project ID",
    operation_description="Edit a message to data item\'s reject reason by project ID.",
    query_serializer=IdByProjectSerializer,
    request_body=RejectReasonEditSerializer
))
class DataItemRejectReasonAPI(AuthenticatedAPIView):

    def get(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = RejectReasonFilterSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        params = serializer.data
        del params['project']

        status_code, json_data = DMS_FILE_API().get_rejectReason(user, pk, project_id, params)

        return Response(json_data, status_code)

    def post(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = RejectReasonSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_FILE_API().create_rejectReason(user, pk, serializer.data, project_id)

        return Response(json_data, status_code)
    
    def put(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = RejectReasonEditSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_FILE_API().edit_rejectReason(user, pk, serializer.data, project_id)

        return Response(json_data, status_code)
    
@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Edit data follow by project ID",
    operation_description="Edit data follow by project ID.",
    query_serializer=IdByProjectSerializer,
    request_body=DataFollowEditSerializer
))
class DataItemFollowAPI(AuthenticatedAPIView):

    def put(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = DataFollowEditSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_FILE_API().edit_datafollows(user, pk, serializer.data, project_id)

        return Response(json_data, status_code)
    
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Delete conversation by ID",
    operation_description="Delete conversation by file ID.",
    query_serializer=IdByProjectSerializer
))
class DataItemConversationDetailAPI(AuthenticatedAPIView):

    def delete(self, request, pk, conversation_id, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        status_code, json_data = DMS_FILE_API().delete_conversations(user, pk, project_id, conversation_id)

        return Response(json_data, status_code)
    
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Delete redo reason by ID",
    operation_description="Delete redo reason by file ID.",
    query_serializer=IdByProjectSerializer
))
class DataItemRedoReasonDetailAPI(AuthenticatedAPIView):

    def delete(self, request, pk, redo_reason_id, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        status_code, json_data = DMS_FILE_API().delete_redoReason(user, pk, project_id, redo_reason_id)

        return Response(json_data, status_code)
    
@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Delete reject reason by ID",
    operation_description="Delete reject reason by file ID.",
    query_serializer=IdByProjectSerializer
))
class DataItemRejectReasonDetailAPI(AuthenticatedAPIView):

    def delete(self, request, pk, reject_reason_id, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        status_code, json_data = DMS_FILE_API().delete_rejectReason(user, pk, project_id, reject_reason_id)

        return Response(json_data, status_code)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Get list attributes of a project dataset by project ID",
    operation_description="Get list attributes of a project dataset by project ID.",
    query_serializer=IdByProjectSerializer
))
class DataAttrAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
    )

    def get(self, request, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        status_code, json_data = DMS_FILE_API().get_attrs(user, project_id)

        return Response(json_data, status_code)


@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Get data item info by project ID and data item ID",
    operation_description="Get data item info by project ID and data item ID.",
    query_serializer=IdByProjectSerializer
))
@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Update attributes of the data item",
    operation_description="Update attributes of the data item.",
    query_serializer=IdByProjectSerializer,
    request_body=ListAttributeSerializer
))
# @method_decorator(name='delete', decorator=swagger_auto_schema(
#     tags=['Data Manager'], operation_summary="Delete file",
#     operation_description="Delete a file."
# ))
class DataItemDetailAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
        PUT=all_permissions.tasks_change,
    )

    def get(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        status_code, json_data = DMS_FILE_API().detail(user, pk, project_id)

        return Response(json_data, status_code)

    def put(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = ListAttributeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data_body = serializer.data

        checkTaskCurrentStep = data_body.get('checkTaskCurrentStep', None)
        if checkTaskCurrentStep is not None:
            if checkTaskCurrentStep['fileId'] is None:
                checkTaskCurrentStep['fileId'] = str(pk)
            if checkTaskCurrentStep['userId'] is None:
                checkTaskCurrentStep['userId'] = str(user.dms_id)

            check_task_current_step = False
            status_code_prj, json_data_prj = DMS_PROJECT_API().fetch_steps_workflow_include_new(user=user, project_id=project_id)
            if status_code_prj == 200:
                if json_data_prj['success']:
                    for node in json_data_prj['data']:
                        if node['id'] == checkTaskCurrentStep['currentStepId']:
                            print(node)
                            if node['type'] == 'New':
                                status_code_assignment_config, json_data_assignment_config = DMS_PROJECT_TASK_CONFIG_API().list(user=user, project=project_id)
                                if status_code_assignment_config == 200:
                                    if json_data_assignment_config['success']:
                                        if json_data_assignment_config['data']['enableManualAssign'] == True or json_data_assignment_config['data']['enableAutoAssign'] == True:
                                            status_code_detail, json_data_detail = DMS_FILE_API().detail(user, pk, project_id)
                                            # print(json_data_detail['data']['shortTaskAssignments'])
                                            status_code_deleted_files, json_data_deleted_files = DMS_FILE_API().fetch_deleted_files(user, project_id=project_id, params={'fileId': str(pk)})
                                            if len(json_data_deleted_files['items']) == 0:
                                                for task_item in json_data_detail['data']['shortTaskAssignments']:
                                                    if task_item['fileId'] == checkTaskCurrentStep['fileId'] and task_item['workFlowStepId'] == checkTaskCurrentStep['nextStepId'] and task_item['userAssigned'] == checkTaskCurrentStep['userId']:
                                                        check_task_current_step = True
                                                        break
                                            else:
                                                check_task_current_step = True
                                                break
                                        else:
                                            check_task_current_step = True
                            elif node['type'] == 'Completed':
                                check_task_current_step = True
                            else:
                                status_code_assignment_config, json_data_assignment_config = DMS_PROJECT_TASK_CONFIG_API().list(user=user, project=project_id)
                                if status_code_assignment_config == 200:
                                    if json_data_assignment_config['success']:
                                        if json_data_assignment_config['data']['enableManualAssign'] == True or json_data_assignment_config['data']['enableAutoAssign'] == True:
                                            status_code_detail, json_data_detail = DMS_FILE_API().detail(user, pk, project_id)
                                            # print(json_data_detail['data']['shortTaskAssignments'])
                                            status_code_deleted_files, json_data_deleted_files = DMS_FILE_API().fetch_deleted_files(user, project_id=project_id, params={'fileId': str(pk)})
                                            if len(json_data_deleted_files['items']) == 0:
                                                for task_item in json_data_detail['data']['shortTaskAssignments']:
                                                    if task_item['fileId'] == checkTaskCurrentStep['fileId'] and task_item['workFlowStepId'] == checkTaskCurrentStep['currentStepId'] and task_item['userAssigned'] == checkTaskCurrentStep['userId']:
                                                        check_task_current_step = True
                                                        break
                                            else:
                                                check_task_current_step = True
                                                break
                                        else:
                                            if node['members'] == ['all']:
                                                check_task_current_step = True
                            break

            if check_task_current_step == False:
                return Response({'success': False, 'errorCode': 'AnnotatorChanged'}, 200)
        
        data_body.pop('checkTaskCurrentStep')

        data_dict = {
            "attributes": []
        }
        for data in data_body['attributes']:
            data_dict['attributes'].append(data)
        for data in data_body['attributes']:
            if data['code'] == "view_annotation_result" and data['value'] == "":
                params = {
                    'code': 'annotate_',
                    'codeOperator': 'StartWith'
                }
                status_code_attrb_list, json_data_attrb_list = DMS_PROJECT_ATTR_API().list(user=user, project=project_id, params=params)
                if status_code_attrb_list == 200:
                    if json_data_attrb_list['success']:
                        for item in json_data_attrb_list['items']:
                            data_dict['attributes'].append(OrderedDict([('code', item['code']), ('value', '')]))
                break

        for data in data_body['attributes']:
            if data['code'] == "view_state" and data['value'] == "Completed":
                status_code_detail, json_data_detail = DMS_FILE_API().detail(user, pk, project_id)
                if status_code_detail==200:
                    if json_data_detail['success']:
                        file_name = json_data_detail['data']['fileName']
                        payload = {
                            "fileName": file_name
                        }
                        status_code_request_noti, json_data_request_noti = DMS_FILE_API().post_request_completed_noti(user=user, id=pk, data=payload, project=project_id)
                        break

        # data_body = serializer.data
        for attrb in data_body['attributes']:
            if attrb['code'] == 'view_archive':
                if attrb['value'] == 'true':
                    param={
                        'fileId': str(pk)
                    }
                    status_code_tasks_list, json_data_tasks_list = DMS_TASK_ASSIGNMENT_API().list(user=user, params=param, project=project_id)
                    if status_code_tasks_list==200:
                        if json_data_tasks_list['success']:
                            for item in json_data_tasks_list['items']:
                                DMS_TASK_ASSIGNMENT_API().delete(user=user, id=item['taskId'], project=project_id)
        for attrb in data_body['attributes']:
            if attrb['code'] == 'view_archive':
                if attrb['value'] == 'false':
                    param={
                        'fileId': str(pk),
                        'isDelete': True
                    }
                    status_code_tasks_list, json_data_tasks_list = DMS_TASK_ASSIGNMENT_API().list(user=user, params=param, project=project_id)
                    if status_code_tasks_list==200:
                        if json_data_tasks_list['success']:
                            for item in json_data_tasks_list['items']:
                                DMS_TASK_ASSIGNMENT_API().patch_revert_task(user=user, project_id=project_id, task_id=item['taskId'])

        status_code, json_data = DMS_FILE_API().put(user, pk, data_body, project_id)

        if status_code == 200:
            if json_data['success']:
                attrb_sync_es(project_master_id=project_id, file_id=pk)

        return Response(json_data, status_code)

    # def delete(self, request, pk, *args, **kwargs):
    #     user = request.user
    #     status_code, json_data = DMS_FILE_API().delete(user, pk)

    #     return Response(json_data, status_code)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Get annotate history by project ID and data item ID",
    operation_description="Get annotate history by project ID and data item ID.",
    query_serializer=IdByProjectSerializer,
    responses={200: AnnotationHistoryResponseSerializer}
))
class DataAnnotateHistoryDetailAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view
    )

    def get(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        status_code, json_data = DMS_FILE_API().get_annotate_history(user, pk, project_id)
        if status_code==200:
            if json_data['success']:
                status_code_storage, json_data_storage = DMS_STORAGE_API().list(user)
                storage_endpoints = None
                for item in json_data_storage['items']:
                    if item['isPrimary']:
                        storage_endpoints = "{}/{}/".format(settings.MINIO_HOST, item['configValue']['bucketName'])
                for item in json_data['data']:
                    item['createdAvatar'] = storage_endpoints + str(item['createdAvatar'])

        return Response(json_data, status_code)
@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Update attributes of multiple data item",
    operation_description="Update attributes of multiple data item.",
    query_serializer=IdByProjectSerializer,
    request_body=ListFileAttributeSerializer(many=True)
))
class DataMultipleItemDetailAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        PUT=all_permissions.tasks_change,
    )

    def put(self, request, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        serializer = ListFileAttributeSerializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = DMS_FILE_API().put_multi_file(user, data=request.data, project_id=project_id)
        if status_code == 200:
            if json_data['success']:
                file_ids = [item["fileId"] for item in request.data]
                attrb_sync_es_multiple_files.delay(project_master_id=project_id, file_ids=file_ids)
                folder_sync_es_multiple_files.delay(project_master_id=project_id, file_ids=file_ids)

        return Response(json_data, status_code)

@method_decorator(name='patch', decorator=swagger_auto_schema(
    tags=['Data Manager'],
    operation_summary='Revert file by ID',
    operation_description='Revert file by file ID.',
    query_serializer=IdByProjectSerializer
))
class DataItemRevertAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        PATCH=all_permissions.tasks_change,
    )

    def patch(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        status_code, json_data = DMS_FILE_API().patch_revert_file(user, project_id, pk)

        folder_sync_es_multiple_files.delay(project_master_id=project_id, file_ids=[str(pk)])

        return Response(json_data, status_code)


@method_decorator(name='delete', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Delete temporary file by ID",
    operation_description="Delete temporary file by file ID.",
    query_serializer=IdByProjectSerializer
))
class DataItemDeleteTemporaryAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        DELETE=all_permissions.tasks_delete,
    )

    def delete(self, request, pk, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        status_code, json_data = DMS_FILE_API().delete_temporary_file(user, project_id, pk)
        if status_code==200:
            if json_data['success']:
                param={
                    'fileId': str(pk)
                }
                status_code_tasks_list, json_data_tasks_list = DMS_TASK_ASSIGNMENT_API().list(user=user, params=param, project=project_id)
                if status_code_tasks_list==200:
                    if json_data_tasks_list['success']:
                        for item in json_data_tasks_list['items']:
                            DMS_TASK_ASSIGNMENT_API().delete(user=user, id=item['taskId'], project=project_id)

        folder_sync_es_multiple_files.delay(project_master_id=project_id, file_ids=[str(pk)])

        return Response(json_data, status_code)


@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Get data item\'s image by file ID",
    operation_description="Get data item\'s image by file ID.",
    query_serializer=ImageSerializer
))
class DataItemImageAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
    )
    authentication_classes = [TokenAuthentication, TokenParamAuthentication, SessionAuthentication]

    def get(self, request, *args, **kwargs):
        start = time.time()
        user = request.user
        serializer = ImageSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        status_code, img = DMS_FILE_API().image(user, serializer.data)
        try:
            if img[:9].decode('utf8') == '<!doctype':
                return HttpResponse('Not Found', status=404)
        except:
            pass
        end = time.time()
        print(f"execute_time TAS: {end - start}")
        return HttpResponse(img, content_type="image/png")


@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="List data items by project ID",
    operation_description="List data items by project ID.",
    query_serializer=IdByProjectSerializer
))
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Import a file to the dataset",
    operation_description="Import a file to the dataset.",
    request_body=ImportFileSerializer
))
class DataListImportAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
        POST=all_permissions.tasks_change,
    )

    def get(self, request, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']
        data = request.query_params.copy()
        # if 'attributeValues' in data:
        #     data['attributeValues'] = [x.strip() for x in data['attributeValues'].split(',')]
        # if 'attributeKeyword' in data:
        #     data['attributeKeyword'] = [x.strip() for x in data['attributeKeyword'].split(',')]

        if 'valueAttribute' in data:
            data['valueAttribute'] = [x.strip() for x in data['valueAttribute'].split(';')]

        filters = DatasetFilterSerializer(data).data
        if 'valueAttribute' in data:
            status_code, json_data = DMS_FILE_API().list_new_managers(user, params=filters, project=project_id)
        else:
            status_code, json_data = DMS_FILE_API().list(user, params=filters, project=project_id)

        return Response(json_data, status_code)
    
    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = ImportFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        file = request.FILES['file']
        project_id = serializer.data['project']

        attributes = settings.DEFAULT_VIEW_ATTRIBUTES
        data_view = settings.DEFAULT_FILE_FIELDS

        status_code, json_data = DMS_FILE_API().create(user, file, project_id, attributes=attributes, data_view=data_view)

        return Response(json_data, status_code)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Import a file to the dataset for project avatar",
    operation_description="Import a file to the dataset for project avatar",
    request_body=ImportFileProjectAvatarSerializer
))
class DataListImportProjectAvatarAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
        POST=all_permissions.tasks_change,
    )

    def post(self, request, *args, **kwargs):
        user = request.user
        
        files = [request.FILES[key] for key in request.FILES.keys()]
        project_id = request.data['project']

        status_code, json_data = DMS_FILE_API().create_without_projectId(user, files, project_id)

        return Response(json_data, status_code)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="List data items by project ID",
    operation_description="List data items by project ID",
    query_serializer=IdByProjectSerializer
))
class DataListImportNewAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
        POST=all_permissions.tasks_change,
    )

    def get(self, request, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']
        data = request.query_params.copy()

        if 'valueAttribute' in data:
            data['valueAttribute'] = [x.strip() for x in data['valueAttribute'].split(';')]
        if 'userIds' in data:
            data['userIds'] = [x.strip() for x in data['userIds'].split(';')]
        if 'projectTagIds' in data:
            data['projectTagIds'] = [x.strip() for x in data['projectTagIds'].split(';')]
        if 'workFlowStepOrders' in data:
            data['workFlowStepOrders'] = [x.strip() for x in data['workFlowStepOrders'].split(';')]
        if 'stepStatus' in data:
            data['stepStatus'] = [x.strip() for x in data['stepStatus'].split(';')]

        filters = DatasetNewFilterSerializer(data).data
        if 'valueAttribute' in data:
            status_code, json_data = DMS_FILE_API().list_new_managers(user, params=filters, project=project_id)
        else:
            status_code, json_data = DMS_FILE_API().list_with_task(user, params=filters, project=project_id)

        return Response(json_data, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="List data items by project ID",
    operation_description="List data items by project ID",
    query_serializer=IdByProjectSerializer
))
class DataListImportWithSecurityAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
        POST=all_permissions.tasks_change,
    )

    def get(self, request, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']
        data = request.query_params.copy()

        if 'valueAttribute' in data:
            data['valueAttribute'] = [x.strip() for x in data['valueAttribute'].split(';')]
        if 'userIds' in data:
            data['userIds'] = [x.strip() for x in data['userIds'].split(';')]
        if 'projectTagIds' in data:
            data['projectTagIds'] = [x.strip() for x in data['projectTagIds'].split(';')]
        if 'workFlowStepOrders' in data:
            data['workFlowStepOrders'] = [x.strip() for x in data['workFlowStepOrders'].split(';')]
        if 'stepStatus' in data:
            data['stepStatus'] = [x.strip() for x in data['stepStatus'].split(';')]
        if 'userWorkFlowSteps' in data:
            data['userWorkFlowSteps'] = [x.strip() for x in data['userWorkFlowSteps'].split(';')]

        filters = DatasetNewWithSecurityFilterSerializer(data).data
        if 'valueAttribute' in data:
            status_code, json_data = DMS_FILE_API().list_new_managers_with_security(user, params=filters, project=project_id)
        else:
            status_code, json_data = DMS_FILE_API().list_with_task(user, params=filters, project=project_id)

        return Response(json_data, status_code)
    
@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="List deleted data items by project ID",
    operation_description="List deleted data items by project ID",
    query_serializer=IdByProjectSerializer
))
class DataListDeletedAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
    )

    def get(self, request, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']
        data = request.query_params.copy()
        filters = data

        status_code, json_data = DMS_FILE_API().fetch_deleted_files(user, project_id=project_id, params=filters)

        return Response(json_data, status_code)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Statistic data by project ID",
    operation_description="Statistic data by project ID",
    query_serializer=StatisticSerializer
))
class DataStatisticProjectAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
    )

    def get(self, request, *args, **kwargs):
        user = request.user
        serializer = StatisticSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']
        statistic_filter = serializer.data['static_filter']

        status_code, json_data = DMS_FILE_API().statistic_data_status(user, project_id=project_id, statistic_filter=statistic_filter)

        return Response(json_data, status_code)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Statistic data of instance tool by project ID",
    operation_description="Statistic data of instance tool by project ID",
    query_serializer=IdByProjectSerializer
))
class DataStatisticClassTreeProjectAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
    )

    def get(self, request, *args, **kwargs):
        user = request.user
        serializer = IdByProjectSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        project_id = serializer.data['project']

        status_code, json_data = DMS_STATISTIC_API().statistic_multi_class_tree(user, project_id=project_id)

        return Response(json_data, status_code)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['Data manager'], operation_summary="Get sample of csv file",
    operation_description="Get csv file sample",
    query_serializer=SampleCSVSerializer
))
class ImportDataGetSampleAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view
    )

    def get(self, request, *args, **kwargs):
        user = request.user
        serializer = SampleCSVSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)
        project_id = serializer.data['project']
        convertor_data = serializer.data['convert_data']

        contents = DMS_FILE_API().get_csv_sample(user=user, project_id=project_id, convertor_data=convertor_data)

        response = HttpResponse(
            content_type='text/csv',
            headers={'Content-Disposition': 'attachment; filename="csv_sample.csv"'},
        )
        writer = csv.writer(response)
        writer.writerows(contents)

        return response


@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data manager'], operation_summary="Confirm csv file",
    operation_description="Confirm attributes in csv file",
    request_body=ImportFileSerializer
))
class ImportDataCSVAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
        POST=all_permissions.tasks_change,
    )

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = ImportFileSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        file = request.FILES['file']
        project_id = serializer.data['project']
        convertor_data = serializer.data['convert_data']

        file_save = FileSystemStorage().save(name=file.name, content=file)

        result = DMS_FILE_API.confirm_csv(user=user, project_id=project_id, file_save=file_save, convertor_data=convertor_data)

        return Response(result)


@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data manager'], operation_summary="Upload and check zip file",
    operation_description="Upload data zip file and mapping with csv file",
    request_body=ImportZipSerializer
))
class ImportDataZipAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
        POST=all_permissions.tasks_change,
    )

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = ImportZipSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        file = request.FILES['file']
        project_id = serializer.data['project']
        csv_file = serializer.data['csv_file_name']
        parent_path = serializer.data['parent_path']
        convertor_data = serializer.data['convert_data']

        file_save = FileSystemStorage().save(name=file.name, content=file)

        confirm_zip.delay(user_id=user.dms_id, project_id=project_id, file_save=file_save, csv_file=csv_file, convertor_data=convertor_data,
                          parent_path=parent_path)

        return Response()


@method_decorator(name='put', decorator=swagger_auto_schema(
    tags=['Data manager'], operation_summary="Confirm upload",
    operation_description="Confirm upload data in zip file and csv file",
    request_body=ConfirmUploadZipSerializer
))
class ConfirmUploadAPI(AuthenticatedAPIView):
    permission_required = ViewClassPermission(
        GET=all_permissions.tasks_view,
        POST=all_permissions.tasks_change,
    )

    def put(self, request, *args, **kwargs):
        user = request.user
        serializer = ConfirmUploadZipSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        csv_file_name = serializer.data['csv_file_name']
        zip_task_id = serializer.data['zip_task_id']
        confirm = serializer.data['confirm']
        parent_path = serializer.data['parent_path']
        convertor_data = serializer.data['convert_data']

        if confirm == True:
            upload_data.delay(csv_file_name=csv_file_name, zip_task_id=zip_task_id, convertor_data=convertor_data, parent_path=parent_path)
        else:
            delete_data_folder.delay(csv_file_name=csv_file_name, zip_task_id=zip_task_id)

        return Response()
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['File Folder Management'], operation_summary="sync folder data to es",
    operation_description="sync folder data to es"
))
class FileManagerSyncESProjectAPI(AuthenticatedAPIView):

    def post(self, request, project_id, *args, **kwargs):
        user = request.user
        folder_data_sync_es_project(project_master_id=project_id)

        return Response("Done", 200)

@method_decorator(name='get', decorator=swagger_auto_schema(
    tags=['File Folder Management'], operation_summary="Count files in folder",
    operation_description="Count files in folder",
    query_serializer=FolderManagerBranchSerializer
))
class FileManagerCountFilesInFolderAPI(AuthenticatedAPIView):

    def get(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = FolderManagerBranchSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        status_code, json_data = FileManagerEs().count_files_in_folder(user, project_id, serializer.data)

        return Response(json_data, status_code)

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Projects Import'], operation_summary="Confirm upload",
    operation_description="Confirm upload data in zip file and csv file",
    request_body=UploadZipV2Serializer
))
class UploadLargeZipV2API(AuthenticatedAPIView):

    def post(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = UploadZipV2Serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        parent_path = serializer.data['parent_path']
        zip_id = serializer.data['id']
        file = request.FILES['file']
        readyToWorkProject = serializer.data.get('readyToWorkProject', False)
        nextWorkFlowStep = serializer.data.get('nextWorkFlowStep', 'New')
        nextWorkFlowStepId = serializer.data.get('nextWorkFlowStepId', '00000000-0000-0000-0000-000000000000')
        nextWorkFlowState = serializer.data.get('nextWorkFlowState', 'New')

        file_save = FileSystemStorage().save(name=file.name, content=file)

        upload_data_v2.delay(user.dms_id, project_id=project_id, file_save=file_save, zip_id=zip_id, parent_path=parent_path,
                             readyToWorkProject=readyToWorkProject, 
                             nextWorkFlowStep=nextWorkFlowStep, 
                             nextWorkFlowStepId=nextWorkFlowStepId, 
                             nextWorkFlowState=nextWorkFlowState)

        return Response({
            "success": True,
            "errorCode": None,
            "message": None,
            "data": None
        }, 200)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Projects Import'], operation_summary="Fill data in csv file",
    operation_description="Fill data in csv file",
    request_body=UploadFillCSVDataV2Serializer
))
class UploadFillCSVDataV2API(AuthenticatedAPIView):

    def post(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = UploadFillCSVDataV2Serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        csv_id = serializer.data['id']
        file = request.FILES['file']
        try:
            file = file.read().decode('utf-8')
        except Exception as e:
            print(e)
            return Response({
                "success": False,
                "errorCode": None,
                "message": "Incorrect format",
                "data": None
            }, 200)

        reader = csv.reader(io.StringIO(file), delimiter=',')
        view_column_index_list = []
        for index, row in enumerate(reader):
            if index == 0:
                for col in row:
                    if col.startswith('view_annotation_result_'):
                        view_column_index_list.append(row.index(col))
                break

        reader = csv.reader(io.StringIO(file), delimiter=',')
        for index, row in enumerate(reader):
            if index > 0:
                for col_index, col in enumerate(row):
                    if col != "":
                        if col_index in view_column_index_list:
                            try:
                                annotate_data = list(eval(row[col_index]))
                            except Exception as e:
                                print(e)
                                return Response({
                                    "success": False,
                                    "errorCode": None,
                                    "message": "Incorrect format",
                                    "data": None
                                }, 200)

        fill_pre_annotated_data.delay(user.dms_id, project_id=project_id, csv_id=csv_id, csv_data=file)

        return Response({
            "success": True,
            "errorCode": None,
            "message": None,
            "data": None
        }, 200)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Projects Import'], operation_summary="Cancel upload",
    operation_description="Cancel upload data in zip file",
    request_body=UploadZipV2CancelSerializer
))
class UploadLargeZipV2CancelAPI(AuthenticatedAPIView):

    def post(self, request, project_id, *args, **kwargs):
        user = request.user
        serializer = UploadZipV2CancelSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        task_upload_id = serializer.data['task_upload_id']

        # cancel_upload_v2.delay(user.dms_id, upload_task_id=task_upload_id)
        from core.celery import app
        app.control.revoke(task_upload_id, terminate=True)

        return Response({
            "success": True,
            "errorCode": None,
            "message": None,
            "data": None
        }, 200)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Projects Import'], operation_summary="Upload zip and csv concurrently",
    operation_description="Upload zip and csv concurrently",
    request_body=UploadZipAndCSVV2Serializer
))
class UploadZipAndCSVV2API(AuthenticatedAPIView):

    def post(self, request, project_id, *args, **kwargs):
        user =request.user
        serializer = UploadZipAndCSVV2Serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        parent_path = serializer.data['parent_path']
        zip_csv_id = serializer.data['id']

        csv_file = request.FILES['csv_file']
        zip_file = request.FILES['zip_file']
        readyToWorkProject = serializer.data.get('readyToWorkProject', False)
        nextWorkFlowStep = serializer.data.get('nextWorkFlowStep', 'New')
        nextWorkFlowStepId = serializer.data.get('nextWorkFlowStepId', '00000000-0000-0000-0000-000000000000')
        nextWorkFlowState = serializer.data.get('nextWorkFlowState', 'New')

        try:
            csv_file_data = csv_file.read().decode('utf-8')
        except Exception as e:
            print(e)
            return Response({
                "success": False,
                "errorCode": None,
                "message": "Incorrect format",
                "data": None
            }, 200)

        reader = csv.reader(io.StringIO(csv_file_data), delimiter=',')
        view_column_index_list = []
        for index, row in enumerate(reader):
            if index == 0:
                for col in row:
                    if col.startswith('view_annotation_result_'):
                        view_column_index_list.append(row.index(col))
                break

        reader = csv.reader(io.StringIO(csv_file_data), delimiter=',')
        for index, row in enumerate(reader):
            if index > 0:
                for col_index, col in enumerate(row):
                    if col != "":
                        if col_index in view_column_index_list:
                            try:
                                annotate_data = list(eval(row[col_index]))
                            except Exception as e:
                                print(e)
                                return Response({
                                    "success": False,
                                    "errorCode": None,
                                    "message": "Incorrect format",
                                    "data": None
                                }, 200)
                            
        zip_file_save = FileSystemStorage().save(name=zip_file.name, content=zip_file)

        upload_data_v3.delay(user.dms_id, project_id=project_id, zip_csv_id=zip_csv_id, zip_file_save=zip_file_save,
                       csv_file_data=csv_file_data, parent_path=parent_path,
                        readyToWorkProject=readyToWorkProject, 
                        nextWorkFlowStep=nextWorkFlowStep, 
                        nextWorkFlowStepId=nextWorkFlowStepId, 
                        nextWorkFlowState=nextWorkFlowState)

        return Response({
            "success": True,
            "errorCode": None,
            "message": None,
            "data": None
        }, 200)
    
@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Upload cropped image to duplicate",
    operation_description="Upload cropped image to duplicate",
    request_body=FileProjectCropDuplicateSerializer
))
class DataListImportCroppedImageAPI(AuthenticatedAPIView):

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = FileProjectCropDuplicateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        file = request.FILES['file'] if 'file' in request.FILES else None
        project_id = serializer.data['projectId']
        project_file_id = serializer.data['projectFileId']
        data = serializer.data

        status_code, json_data = DMS_FILE_API().upload_cropped_file(user, file, project_id, project_file_id, data=data)

        return Response(json_data, status_code)
    

@method_decorator(name='post', decorator=swagger_auto_schema(
    tags=['Data Manager'], operation_summary="Save as new image file",
    operation_description="Save as new image file",
    request_body=FileProjectSaveAsSerializer
))
class DataListImportSaveAsImageAPI(AuthenticatedAPIView):

    def post(self, request, *args, **kwargs):
        user = request.user
        serializer = FileProjectSaveAsSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        file = request.FILES['file'] if 'file' in request.FILES else None
        project_id = serializer.data['projectId']
        data = serializer.data

        status_code, json_data = DMS_FILE_API().upload_save_as_file(user, file, project_id, data=data)

        return Response(json_data, status_code)