import json
import zipfile, io
from django.conf import settings

from dms_connector.api import DMS_FILE_API, DMS_PROJECT_ATTR_API, DMS_PROJECT_API, DMS_ATTIBUTE_API, DMS_PROJECT_DATATYPE_API, \
    DMS_PROJECT_AIMODEL_PREDICTION_API
from data_manager.serializers import DatasetFilterSerializer, FileOperationDataSerializer, FileMoveResultSerializer, \
    FileDetailResultSerializer, FileDownloadSerializer, FileReadSerializer
from data_manager.tasks_v3 import folder_sync_es_multiple_files


class FileManager:
    project_folder_field = 'annotationFlow'

    def download(self, user, project_id, data):
        list_file = []
        list_folder = []
        for file in data:
            if file['isFile'] and file['id']:
                list_file.append(file)
            else:
                list_folder.append(file)

        memfile = io.BytesIO()
        with zipfile.ZipFile(memfile, mode='w', compression=zipfile.ZIP_DEFLATED) as zf:
            for folder in list_folder:
                folder_name = folder['name']
                path = folder['filterPath'].replace('\\', '/') + folder_name + '/'
                status_code, json_data = DMS_FILE_API().list_with_path(user, path=path, project=project_id)
                list_item = json_data['items']
                for item in list_item:
                    file_id = item['fileId']
                    file_name = item['fileName']
                    params = {'file_id': file_id, 'type': 'Original', 'project': project_id}
                    status_code, img = DMS_FILE_API().image(user, params)
                    if status_code == 200:
                        zf.writestr(folder_name + '/' + file_name, img)

            for file in list_file:
                file_id = file['id']
                file_name = file['name']
                params = {'file_id': file_id, 'type': 'Original', 'project': project_id}
                status_code, img = DMS_FILE_API().image(user, params)
                if status_code == 200:
                    zf.writestr(file_name, img)

        return memfile.getvalue()

    def read(self, user, project_id, data):
        filters = DatasetFilterSerializer(data).data
        path = data.get('path')
        # filters['page'] = data.get('page', 1)
        # filters['pageSize'] = data.get('pageSize', 10)
        status_code, json_data = DMS_FILE_API().list_with_path(user, path=path, params=filters,
                                                               project=project_id)
        list_file = json_data.get('items') or []
        json_data['items'] = filter(lambda item: item['fileName'], list_file)

        results = FileOperationDataSerializer(json_data, context={'root': data}).data

        status_code, project_res = DMS_PROJECT_API().detail(user, project_id)
        project_data = project_res['data']
        try:
            all_folders = json.loads(project_data[self.project_folder_field])
        except:
            all_folders = []

        folders = []
        for folder in all_folders:
            if folder['filterPath'] == path:
                folders.append(folder)

        results['files'].extend(folders)
        results.update({"total": json_data['total']})

        return results

    def create(self, user, project_id, data):
        status_code, project_res = DMS_PROJECT_API().detail(user, project_id)
        project_data = project_res['data']

        try:
            folders = json.loads(project_data[self.project_folder_field])
        except:
            folders = []
        
        for folder in folders:
            if folder['name'] == data['name'] and folder['filterPath'] == data['path']:
                return {
                    'cwd': None,
                    'files': None,
                    'error': {
                        'message' : "Folder is existed!"
                    },
                    'details': "Folder is existed!"
                }

        new_folder_data = data['data'][0]
        new_folder_data.update({
            'name': data['name'],
            'filterPath': data['path']
        })

        folders.append(new_folder_data)
        data = {
            self.project_folder_field: json.dumps(folders)
        }
        status_code, json_data = DMS_PROJECT_API().edit(user, project_id, data)

        return {
            'cwd': None,
            'files': folders,
            'error': None,
            'details': None
        }

    def rename(self, user, project_id, data):
        # Only Folder
        if data['data']:
            rename_folder = data['data'][0]
            status_code, project_res = DMS_PROJECT_API().detail(user, project_id)
            project_data = project_res['data']
            try:
                folders = json.loads(project_data[self.project_folder_field])
            except:
                folders = []

            for i, folder in enumerate(folders):
                folder_name = folder['name']
                if folder_name == rename_folder['name'] and folder['filterPath'] == rename_folder['filterPath']:
                    folders[i]['name'] = data['newName']

            status_code, json_data = DMS_PROJECT_API().edit(user, project_id,
                                                            {self.project_folder_field: json.dumps(folders)})

        return {
            'cwd': None,
            'files': data['data'],
            'error': None,
            'details': None
        }

    def recursive_delete(self, user, project_id, folder_list, delete_folders, folder_list_result):
        for folder in folder_list:
            folder_name = folder['name']
            # print(folder_name)
            if folder_name in delete_folders :
                if folder['filterPath'] == delete_folders[folder_name]:
                    data = {
                        "action": "read",
                        "path": "{}{}/".format(folder['filterPath'], folder_name),
                        "showHiddenItems": False,
                        "data": [folder]
                    }
                    response_read = self.read(user, project_id, FileReadSerializer(data).data)
                    # print("====")
                    # print(folder_name)
                    # print(x['files'])
                    # print("====")
                    delete_folders_ = {}
                    for delete_folder_data in response_read['files']:
                        if delete_folder_data['isFile'] == True:
                            status_code, json_data = DMS_FILE_API().delete_temporary_file(user=user, project_id=delete_folder_data['projectId'], file_id=delete_folder_data['fileId'])
                            # print(json_data)
                        delete_folders_[delete_folder_data['name']] = delete_folder_data['filterPath']
                        self.recursive_delete(user, project_id, folder_list, delete_folders_, folder_list_result)

                    folder_list_result.remove(folder)

    def delete(self, user, project_id, data):
        # Only Folder
        status_code, project_res = DMS_PROJECT_API().detail(user, project_id)
        project_data = project_res['data']

        try:
            folders = json.loads(project_data[self.project_folder_field])
        except:
            folders = []

        delete_folders = {}
        for delete_folder_data in data['data']:
            delete_folders[delete_folder_data['name']] = delete_folder_data['filterPath']

        folders_temp = folders.copy()
        self.recursive_delete(user, project_id, folders, delete_folders, folder_list_result=folders_temp)
        status_code, json_data = DMS_PROJECT_API().edit(user, project_id,
                                                        {self.project_folder_field: json.dumps(folders_temp)})

        return {
            'cwd': None,
            'files': data['data'],
            'error': None,
            'details': None
        }

    def move(self, user, project_id, data):
        status_code, project_res = DMS_PROJECT_API().detail(user, project_id)
        project_data = project_res['data']
        
        data_read = {
            "action": "read",
            "path": data['targetPath'],
            "showHiddenItems": False,
            "data": data['targetData']
        }
        target_folder = self.read(user=user, project_id=project_id, data=FileReadSerializer(data_read).data)
        existed_files = []
        for item in target_folder['files']:
            if item['isFile']:
                existed_files.append(item['fileName'])
        # print("++++++++++++++++++")
        # print(existed_files)

        list_folder = []
        list_file = []
        for obj in data['data']:
            if obj['isFile']:
                list_file.append(obj)
            else:
                list_folder.append(obj)

        if list_folder:
            try:
                folders = json.loads(project_data[self.project_folder_field])
            except:
                folders = []

            names = data['names']
            for i, folder in enumerate(folders):
                if folder['name'] in names and folder['filterPath'] == data['path']:
                    folders[i]['filterPath'] = data['targetPath']

            status_code, json_data = DMS_PROJECT_API().edit(user, project_id,
                                                            {self.project_folder_field: json.dumps(folders)})

        if list_file:
            item_ids = []
            for file in list_file:
                if file['fileName'] in existed_files:
                    return {
                        'cwd': None,
                        'files': None,
                        'error': {
                            'message' : "File is existed!"
                        },
                        'details': "File is existed!"
                    }
                item_ids.append(file['fileId'])
            
            status_code, json_data = DMS_FILE_API().update_path_multi(user, item_ids, project_id,
                                                                path=data['targetPath'])

        return {
            'cwd': None,
            'files': FileMoveResultSerializer(data['data'], context={'filterPath': data['targetPath']}, many=True).data,
            'error': None,
            'details': None
        }

    def details(self, user, project_id, data):
        return {
            'cwd': None,
            'files': None,
            'error': None,
            'details': FileDetailResultSerializer(data['data'][-1]).data
        }

    def search(self):
        pass

    def copy(self):
        pass

    def upload(self, user, project_id, file, data):
        status_code, json_data = DMS_PROJECT_DATATYPE_API().list(user, project=project_id)
        data_types = json_data.get('data', [])

        is_upload = False

        try:
            file_extension = file.name.split('.')[-1]
            for data_type in data_types:
                data_type = data_type.lower()
                
                if f".{file_extension}" == data_type:
                    is_upload = True
                    break

        except Exception as e:
            return {
                    'cwd': None,
                    'files': None,
                    'error': {
                        'message' : e
                    },
                    'details': e
                }, 400
                
        if not is_upload:
            return {
                    'cwd': None,
                    'files': None,
                    'error': {
                        'message' : "File format is not valid!"
                    },
                    'details': "File format is not valid!"
                }, 400

        readyToWorkProject = data.get('readyToWorkProject', False)

        # attributes = settings.DEFAULT_VIEW_ATTRIBUTES
        attributes = [
            {'code': 'view_state', 'value': 'New'}, 
            {'code': 'view_priority', 'value': '0'}, 
            {'code': 'view_category', 'value': ''}, 
            {'code': 'view_folder', 'value': '/'}, 
            {'code': 'view_annotation_result', 'value': ''}, 
            {'code': 'view_archive', 'value': 'false'}, 
            {'code': 'view_workflow_step', 'value': 'New'}, 
            {'code': 'view_workflow_step_id', 'value': '00000000-0000-0000-0000-000000000000'}
        ]
        for attr in attributes:
            if attr['code'] == settings.ATTR_VIEW_FOLDER:
                attr['value'] = data['path']
                break

        if readyToWorkProject:
            for attr in attributes:
                if attr['code'] == 'view_state':
                    attr['value'] = data.get('nextWorkFlowState', "New")
                if attr['code'] == 'view_workflow_step':
                    attr['value'] = data.get('nextWorkFlowStep', "New")
                if attr['code'] == 'view_workflow_step_id':
                    attr['value'] = data.get('nextWorkFlowStepId', "00000000-0000-0000-0000-000000000000")

        # data_view = settings.DEFAULT_FILE_FIELDS
        print(f"data_view_settings:----------- {settings.DEFAULT_FILE_FIELDS}")

        data_view = {
            'viewState': "New",
            'priority': '0',
            'pathFolder': '/',
            'isArchive': False,
            'viewWorkFlowStep': "New",
            'viewWorkFlowStepId': "00000000-0000-0000-0000-000000000000"
        }
        data_view['pathFolder'] = data['path']

        if readyToWorkProject:
            data_view = {
                'viewState': data.get('nextWorkFlowState', "New"),
                'viewWorkFlowStep': data.get('nextWorkFlowStep', "New"),
                'viewWorkFlowStepId': data.get('nextWorkFlowStepId', "00000000-0000-0000-0000-000000000000")
            }

        status_code, json_data = DMS_FILE_API().create(user, file, project_id, attributes, data_view)
        if status_code == 200 and json_data is not None:
            if json_data['success']:    
                folder_sync_es_multiple_files.delay(project_master_id=project_id, file_ids=[json_data['data']])

        if status_code == 200 and json_data is not None:
            nextWorkFlowStepIsAutoPrediction = data.get('nextWorkFlowStepIsAutoPrediction', False)
            if nextWorkFlowStepIsAutoPrediction:
                nextStepAfterAutoPrediction = data.get('nextStepAfterAutoPrediction', {})
                status_code_prediction, json_data_prediction = DMS_PROJECT_AIMODEL_PREDICTION_API().post(user=user,
                                                                                                        project=project_id,
                                                                                                        data=[{
                                                                                                            "fileId": json_data['data'],
                                                                                                            "workflowStepId": data.get('nextWorkFlowStepId', "00000000-0000-0000-0000-000000000000"),
                                                                                                            "nextViewState": nextStepAfterAutoPrediction['nextWorkFlowState'],
                                                                                                            "nextWorkFlowStep": nextStepAfterAutoPrediction['nextWorkFlowStep'],
                                                                                                            "nextWorkFlowStepId": nextStepAfterAutoPrediction['nextWorkFlowStepId']
                                                                                                        }])
        return json_data, status_code
    
    def upload_multi_types(self, user, project_id, file, data):
        is_upload = True
                
        if not is_upload:
            return {
                    'cwd': None,
                    'files': None,
                    'error': {
                        'message' : "File format is not valid!"
                    },
                    'details': "File format is not valid!"
                }, 400

        attributes = settings.DEFAULT_VIEW_ATTRIBUTES
        for attr in attributes:
            if attr['code'] == settings.ATTR_VIEW_FOLDER:
                attr['value'] = data['path']
                break

        # data_view = settings.DEFAULT_FILE_FIELDS
        print(f"data_view_settings:----------- {settings.DEFAULT_FILE_FIELDS}")

        data_view = {
            'viewState': "New",
            'priority': '0',
            'pathFolder': '/',
            'isArchive': False,
            'viewWorkFlowStep': "New",
            'viewWorkFlowStepId': "00000000-0000-0000-0000-000000000000"
        }
        data_view['pathFolder'] = data['path']
        data_view['fileVersionId'] = data['fileVersionId']

        status_code, json_data = DMS_FILE_API().create_no_check_datatype(user, file, project_id, attributes, data_view)
        if status_code == 200 and json_data is not None:
            if json_data['success']:    
                folder_sync_es_multiple_files.delay(project_master_id=project_id, file_ids=[json_data['data']])

        return json_data, status_code
