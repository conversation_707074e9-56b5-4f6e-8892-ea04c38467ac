"""This file and its contents are licensed under the Apache License 2.0. Please see the included NOTICE for copyright information and LICENSE for a copy of the license.
"""
from django.urls import include, path
from rest_framework.routers import DefaultRouter

from data_manager import api, views, query

app_name = "data_manager"
# router = DefaultRouter()
# router.register(r"views", api.ViewAPI, basename="view")

urlpatterns = [
    # path('dm/', views.dm_list, name='dm-index'),

    path("api/dm/", api.DataListImportAPI.as_view()),
    path("api/dm/project_avatar", api.DataListImportProjectAvatarAPI.as_view()),
    path("api/dm/data_manager", api.DataListImportNewAPI.as_view()),
    path("api/dm/data_manager_security", api.DataListImportWithSecurityAPI.as_view()),
    path("api/dm/deleted_data_manager", api.DataListDeletedAPI.as_view()),
    path("api/dm/statistic/", api.DataStatisticProjectAPI.as_view()),
    path("api/dm/statistic_classtree/", api.DataStatisticClassTreeProjectAPI.as_view()),
    path("api/dm/list_folder/", api.DataListFoldertAPI.as_view()),
    path("api/dm/image/", api.DataItemImageAPI.as_view()),
    path("api/dm/attributes/", api.DataAttrAPI.as_view()),
    path("api/dm/FileOperations/", api.DataExplorerAPI.as_view()),
    path("api/dm/FileOperationsMultiTypes/", api.DataExplorerMultipleTypesAPI.as_view()),
    path("api/dm/download/", api.DataDownloadAPI.as_view()),

    path("api/dm/zip_csv/csv_file/", api.ImportDataCSVAPI.as_view()),
    path("api/dm/zip_csv/zip_file/", api.ImportDataZipAPI.as_view()),
    path("api/dm/zip_csv/confirm_upload/", api.ConfirmUploadAPI.as_view()),
    path("api/dm/zip_csv/sample_csv/", api.ImportDataGetSampleAPI.as_view()),
    # path("api/dm/cancel_upload/", api.CancelUploadAPI.as_view()),

    path("api/dm/revert/<uuid:pk>/", api.DataItemRevertAPI.as_view()),
    path("api/dm/deletetemporary/<uuid:pk>/", api.DataItemDeleteTemporaryAPI.as_view()),

    path("api/dm/<uuid:pk>/", api.DataItemDetailAPI.as_view()),
    path("api/dm/<uuid:pk>/annotate_history", api.DataAnnotateHistoryDetailAPI.as_view()),
    path("api/dm/<uuid:pk>/attribute_as_file", api.UploadFileAsAttrbAPI.as_view()),
    path("api/dm/cropped_image", api.DataListImportCroppedImageAPI.as_view()),
    path("api/dm/save_as_image", api.DataListImportSaveAsImageAPI.as_view()),
    path("api/dm/update_file_ids", api.DataMultipleItemDetailAPI.as_view()),
    path("api/dm/<uuid:pk>/conversations", api.DataItemConversationAPI.as_view()),
    path("api/dm/<uuid:pk>/conversations/<uuid:conversation_id>", api.DataItemConversationDetailAPI.as_view()),
    path("api/dm/<uuid:pk>/redo_reason", api.DataItemRedoReasonAPI.as_view()),
    path("api/dm/<uuid:pk>/redo_reason/<uuid:redo_reason_id>", api.DataItemRedoReasonDetailAPI.as_view()),
    path("api/dm/<uuid:pk>/reject_reason", api.DataItemRejectReasonAPI.as_view()),
    path("api/dm/<uuid:pk>/reject_reason/<uuid:reject_reason_id>", api.DataItemRejectReasonDetailAPI.as_view()),
    path("api/dm/<uuid:pk>/data_follow", api.DataItemFollowAPI.as_view()),
    # path("api/dm/views/", api.ViewAPI.as_view()),

    path("api/dm/query/", query.DatasetQueryListCreateAPI.as_view()),
    path("api/dm/query/<uuid:query_id>/", query.DatasetQueryDetailAPI.as_view()),
    path("api/dm/query/resources/", query.DatasetQueryResourcesAPI.as_view()),

    path("projects/<uuid:pk>/data/", views.task_page, name='project-data'),
    path("projects/<uuid:pk>/data/import", views.task_page, name='project-import'),
    path("projects/<uuid:pk>/data/export", views.task_page, name='project-export'),

    path('api/projects/<uuid:project_id>/upload_zip', api.UploadLargeZipV2API.as_view(), name='project-upload-zip'),
    path('api/projects/<uuid:project_id>/upload_csv', api.UploadFillCSVDataV2API.as_view(), name='project-upload-csv'),
    path('api/projects/<uuid:project_id>/cancel_zip', api.UploadLargeZipV2CancelAPI.as_view(), name='project-cancel-upload'),
    path('api/projects/<uuid:project_id>/upload_zip_csv', api.UploadZipAndCSVV2API.as_view(), name='project-upload-zip-csv-concurrently'),

    path('api/file_folder_manager/<uuid:project_id>/sync_folder_project', api.FileManagerSyncESProjectAPI.as_view(), name='file-folder-manager-sync-project'),
    path('api/file_folder_manager/<uuid:project_id>/count_files', api.FileManagerCountFilesInFolderAPI.as_view(), name='file-folder-manager-count-files'),
]
