version: "3.9"

services:
  app:
    image: taureau.azurecr.io/tas-prod-public:${IMAGE_VERSION:-}
    container_name: tas-prod-public
    ports:
      - "${TAS_PORT:-8081}:8080"
    depends_on:
      - db
      - redis
    environment:
      - SSL_VERIFY=False
      - DMS_HOST=${DMS_HOST:-*************:8001}
      - AIS_HOST=${AIS_HOST:-https://a40.taureau.ai}
      - MINIO_HOST=${MINIO_HOST:-}
      - NOTISERVICE_HOST=${NOTISERVICE_HOST:-http://*************:8000}
      - DMS_VERSION=2
      - REDIS_SERVER=redis
      - DJANGO_DB=default
      - POSTGRES_DB=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRE_PORT=5432
      - POSTGRE_HOST=db
      - LABEL_STUDIO_HOST=${LABEL_STUDIO_HOST:-}
      - JSON_LOG=1
      - WORKERS_TAS=${WORKERS_TAS:-}
      - DEBUG=0
      - DEBUG_MODAL_EXCEPTIONS=0
    #      - LOG_LEVEL=DEBUG
      - DOCKER_BUILDKIT=1
      - COMPOSE_DOCKER_CLI_BUILD=1
      - LOGS_MANAGEMENT_HOST=${LOGS_MANAGEMENT_HOST:-127.0.0.1}
      - LOGS_MANAGEMENT_PORT=${LOGS_MANAGEMENT_PORT:-9880}
      - LOGS_MANAGEMENT_TAG=${LOGS_MANAGEMENT_FE_TAG:-tas_fe.log}

      - ES_HOST=${ES_HOST:-*************}
      - ES_PORT=${ES_PORT:-9200}
      - ES_SCHEME=${ES_SCHEME:-http}
      - ES_USERNAME=${ES_USERNAME:-elastic}
      - ES_PASSWORD=${ES_PASSWORD:-taureauai}
      - ES_INDEX_ATTRB=${ES_INDEX_ATTRB:-file_annotation_attributes}
      - DMS_MYSQL_HOST=${DMS_MYSQL_HOST:-*************}
      - DMS_MYSQL_PORT=${DMS_MYSQL_PORT:-9336}
      - DMS_MYSQL_USER=${DMS_MYSQL_USER:-root}
      - DMS_MYSQL_PASSWORD=${DMS_MYSQL_PASSWORD:-taureauai}
      - DMS_MYSQL_DATABASE=${DMS_MYSQL_DATABASE:-dms}
      - MINIO_ENDPOINT=*************:9000
      - VIEW_ANNOTATION_RESULT_ATTRB_ID=${VIEW_ANNOTATION_RESULT_ATTRB_ID:-08dc52f5-a655-4e99-8627-3410d87a20d3}
      - VIEW_FOLDER_ATTRB_ID=${VIEW_FOLDER_ATTRB_ID:-08dc52f5-938c-4032-8244-137ee1514996}

      - DATA_VISUALIZATION_HOST=${DATA_VISUALIZATION_HOST:-https://platform-fiftyone.allby.ai}
      - DATA_VISUALIZATION_FIFTYONE_PORT=${DATA_VISUALIZATION_FIFTYONE_PORT:-443}
      - USER_DMS_ID_DEFAULT=4f847b1e-ea93-4aa1-910d-00f422d644e3
    networks:
      - app_network
      - redis_network
      - db_network
      # - dms_v2_minio_network
      # - dms_v2_web_network
    volumes:
      - ${LS_DATA_DIR:-./ls-data}:/label-studio/data:rw
    command: label-studio-uvicorn
    # logging:
    #   driver: "fluentd"
    #   options:
    #     fluentd-address: ${LOGS_MANAGEMENT_HOST:-localhost}:24224
    #     tag: ${LOGS_MANAGEMENT_BE_TAG:-tas_be.log}
  db:
    image: postgres:11.5
    container_name: tas-db-prod-public
    restart: on-failure
    volumes:
      - postgres_data_backup:/var/lib/postgresql/data
    networks:
      - db_network
    environment:
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"
  redis:
    image: redis
    container_name: tas-redis-prod-public
    volumes:
      - ${REDIS_DATA_DIR:-./redis-data}:/data
    restart: on-failure
    networks:
      - redis_network
    ports:
      - "6380:6379"

volumes:
  postgres_data_backup: { }

networks:
  # dms_v2_web_network:
  #   external: true
  # dms_v2_minio_network:
  #   external: true
  app_network:
    driver: bridge
  redis_network:
    driver: bridge
  db_network:
    driver: bridge